#!/bin/bash

# WriterPro AI Service 部署脚本
# 用于在香港服务器上部署AI服务

set -e

echo "🚀 开始部署 WriterPro AI Service..."

# 检查Node.js版本
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，正在安装..."
    curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
    sudo yum install -y nodejs
fi

NODE_VERSION=$(node --version)
echo "✅ Node.js 版本: $NODE_VERSION"

# 检查PM2
if ! command -v pm2 &> /dev/null; then
    echo "📦 安装 PM2..."
    npm install -g pm2
fi

# 创建应用目录
APP_DIR="/opt/writerpro-ai-service"
sudo mkdir -p $APP_DIR
sudo chown -R $USER:$USER $APP_DIR

# 复制文件到应用目录
echo "📁 复制应用文件..."
cp -r ./* $APP_DIR/

# 进入应用目录
cd $APP_DIR

# 安装依赖
echo "📦 安装依赖包..."
npm install --production

# 创建日志目录
sudo mkdir -p /var/log/writerpro
sudo chown -R $USER:$USER /var/log/writerpro

# 创建环境变量文件
if [ ! -f .env ]; then
    echo "⚙️ 创建环境变量文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件并设置正确的配置值"
fi

# 创建PM2配置文件
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'writerpro-ai-service',
    script: 'src/server.js',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3002
    },
    error_file: '/var/log/writerpro/ai-service-error.log',
    out_file: '/var/log/writerpro/ai-service-out.log',
    log_file: '/var/log/writerpro/ai-service-combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF

# 设置防火墙规则
echo "🔥 配置防火墙..."
sudo firewall-cmd --permanent --add-port=3002/tcp
sudo firewall-cmd --reload

echo "✅ 部署完成！"
echo ""
echo "📋 下一步操作："
echo "1. 编辑配置文件: nano $APP_DIR/.env"
echo "2. 启动服务: pm2 start ecosystem.config.js"
echo "3. 查看状态: pm2 status"
echo "4. 查看日志: pm2 logs writerpro-ai-service"
echo ""
echo "🌐 服务将在端口 3002 上运行"
