/**
 * 日志工具 - 统一的日志记录
 */

const winston = require('winston');
const path = require('path');

// 日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// 控制台格式
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let msg = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
      msg += ` ${JSON.stringify(meta)}`;
    }
    return msg;
  })
);

// 创建logger实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'writerpro-ai-service' },
  transports: [
    // 控制台输出
    new winston.transports.Console({
      format: consoleFormat
    })
  ]
});

// 生产环境添加文件日志
if (process.env.NODE_ENV === 'production') {
  const logDir = path.dirname(process.env.LOG_FILE_PATH || '/var/log/writerpro-ai-service.log');
  
  // 添加文件传输
  logger.add(new winston.transports.File({
    filename: process.env.LOG_FILE_PATH || '/var/log/writerpro-ai-service.log',
    maxsize: 10 * 1024 * 1024, // 10MB
    maxFiles: 5,
    tailable: true
  }));

  // 错误日志单独文件
  logger.add(new winston.transports.File({
    filename: path.join(logDir, 'error.log'),
    level: 'error',
    maxsize: 10 * 1024 * 1024,
    maxFiles: 5,
    tailable: true
  }));
}

module.exports = logger;
