/**
 * Google AI服务 - 处理与Google API的交互
 */

const axios = require('axios');
const logger = require('../utils/logger');

class GoogleAIService {
  constructor() {
    this.apiKey = process.env.GOOGLE_API_KEY;
    this.baseURL = 'https://generativelanguage.googleapis.com/v1beta';
    this.testMode = process.env.TEST_MODE === 'true';

    if (!this.apiKey && !this.testMode) {
      logger.warn('Google API key not configured');
    }

    if (this.testMode) {
      logger.info('Google AI Service running in TEST MODE');
    }
  }

  /**
   * 优化文本内容
   * @param {string} text - 原始文本
   * @param {Object} options - 优化选项
   * @returns {Object} 优化结果
   */
  async optimizeText(text, options = {}) {
    try {
      const {
        language = 'zh-CN',
        style = 'professional',
        length = 'medium'
      } = options;

      // 构建优化提示词
      const prompt = this.buildOptimizationPrompt(text, { language, style, length });

      // 调用Google Gemini API
      const response = await this.callGeminiAPI(prompt);

      // 解析响应
      const optimizedText = this.parseGeminiResponse(response);

      // 分析改进点
      const improvements = this.analyzeImprovements(text, optimizedText);

      return {
        optimizedText,
        improvements,
        metadata: {
          model: 'gemini-pro',
          language,
          style,
          length
        }
      };

    } catch (error) {
      logger.error('Google AI text optimization failed', {
        error: error.message,
        textLength: text.length
      });
      throw new Error(`AI optimization failed: ${error.message}`);
    }
  }

  /**
   * 构建优化提示词 - 与后端保持一致
   * @param {string} text - 原始文本
   * @param {Object} options - 选项
   * @returns {string} 提示词
   */
  buildOptimizationPrompt(text, options) {
    const { style } = options;

    // 使用与后端完全一致的提示词模板
    const PROMPT_TEMPLATES = {
      academic: "请用人类的表达语气重写下面这段话成中文，要求每句话不能出现'我们、首先、其次、然后、最后、另外、同时、通过、无论、随着、此外、也将、将为、例如、比如、将成为、总的来说、总之、总而言之、以上、综上'等词语。要求发送一段输出为一段",
      creative: "请将以下文本改写得更有创意和吸引力，使用生动的描述和修辞手法，同时保持原意不变。避免产生机器生成的特征。文本：",
      business: "请将以下文本改写成正式的商务风格，使用专业的商务用语，同时保持原意不变。避免产生机器生成的特征。文本：",
      professional: "请将以下文本改写成正式的商务风格，使用专业的商务用语，同时保持原意不变。避免产生机器生成的特征。文本：" // 默认使用business模板
    };

    // 根据样式选择提示词模板，默认使用academic
    const template = PROMPT_TEMPLATES[style] || PROMPT_TEMPLATES.academic;

    return template + text;
  }

  /**
   * 调用Gemini API
   * @param {string} prompt - 提示词
   * @returns {Object} API响应
   */
  async callGeminiAPI(prompt) {
    // 测试模式：返回模拟响应
    if (this.testMode) {
      logger.info('TEST MODE: Returning mock response');
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟延迟

      return {
        candidates: [{
          content: {
            parts: [{
              text: `[测试模式] 这是优化后的文本：${prompt.substring(prompt.lastIndexOf('原文：') + 3).trim()}`
            }]
          }
        }]
      };
    }

    try {
      const url = `${this.baseURL}/models/gemini-2.5-flash:generateContent?key=${this.apiKey}`;

      const requestData = {
        contents: [{
          parts: [{
            text: prompt
          }]
        }],
        generationConfig: {
          temperature: 0.9 // 与后端保持一致
        }
      };

      const response = await axios.post(url, requestData, {
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        },
        timeout: 30000, // 30秒超时
        responseType: 'json',
        responseEncoding: 'utf8' // 明确指定响应编码
      });

      return response.data;

    } catch (error) {
      if (error.response) {
        logger.error('Gemini API error', {
          status: error.response.status,
          data: error.response.data
        });
        throw new Error(`Gemini API error: ${error.response.status}`);
      } else if (error.request) {
        logger.error('Gemini API network error', {
          message: error.message
        });
        throw new Error('Network error when calling Gemini API');
      } else {
        logger.error('Gemini API request setup error', {
          message: error.message
        });
        throw new Error('Failed to setup Gemini API request');
      }
    }
  }

  /**
   * 解析Gemini响应
   * @param {Object} response - API响应
   * @returns {string} 优化后的文本
   */
  parseGeminiResponse(response) {
    try {
      if (response.candidates && response.candidates.length > 0) {
        const candidate = response.candidates[0];
        if (candidate.content && candidate.content.parts && candidate.content.parts.length > 0) {
          return candidate.content.parts[0].text.trim();
        }
      }
      
      throw new Error('Invalid response format from Gemini API');
    } catch (error) {
      logger.error('Failed to parse Gemini response', {
        error: error.message,
        response: JSON.stringify(response)
      });
      throw new Error('Failed to parse AI response');
    }
  }

  /**
   * 分析改进点
   * @param {string} originalText - 原始文本
   * @param {string} optimizedText - 优化后文本
   * @returns {Array} 改进点列表
   */
  analyzeImprovements(originalText, optimizedText) {
    const improvements = [];

    // 长度变化
    if (optimizedText.length > originalText.length * 1.1) {
      improvements.push('扩展了内容表达');
    } else if (optimizedText.length < originalText.length * 0.9) {
      improvements.push('简化了表达方式');
    }

    // 基本改进点（可以根据需要扩展更复杂的分析）
    improvements.push('改善了语法和用词');
    improvements.push('提高了可读性');
    improvements.push('优化了表达流畅度');

    return improvements;
  }

  /**
   * 检查Google AI服务连接状态
   * @returns {boolean} 连接状态
   */
  async checkConnection() {
    try {
      if (!this.apiKey) {
        return false;
      }

      // 发送一个简单的测试请求
      const testPrompt = 'Hello';
      await this.callGeminiAPI(testPrompt);
      return true;
    } catch (error) {
      logger.warn('Google AI service connection check failed', {
        error: error.message
      });
      return false;
    }
  }
}

module.exports = new GoogleAIService();
