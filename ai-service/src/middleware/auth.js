/**
 * 认证中间件 - 验证来自中国服务器的请求
 */

const logger = require('../utils/logger');

/**
 * API密钥认证中间件
 */
function authMiddleware(req, res, next) {
  try {
    const apiKey = req.headers['x-api-key'] || req.headers['authorization'];
    const expectedKey = process.env.API_SECRET_KEY;

    if (!expectedKey) {
      logger.error('API_SECRET_KEY not configured');
      return res.status(500).json({
        success: false,
        error: 'Server configuration error'
      });
    }

    if (!apiKey) {
      logger.warn('Missing API key in request', {
        ip: req.ip,
        path: req.path
      });
      return res.status(401).json({
        success: false,
        error: 'Missing API key'
      });
    }

    // 支持Bearer token格式
    const cleanKey = apiKey.replace('Bearer ', '');

    if (cleanKey !== expectedKey) {
      logger.warn('Invalid API key', {
        ip: req.ip,
        path: req.path,
        providedKey: cleanKey.substring(0, 10) + '...' // 只记录前10个字符
      });
      return res.status(401).json({
        success: false,
        error: 'Invalid API key'
      });
    }

    // 认证成功，继续处理请求
    next();

  } catch (error) {
    logger.error('Authentication middleware error', {
      error: error.message,
      stack: error.stack
    });
    
    res.status(500).json({
      success: false,
      error: 'Authentication error'
    });
  }
}

module.exports = authMiddleware;
