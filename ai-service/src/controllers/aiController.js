/**
 * AI控制器 - 处理文本优化请求
 */

const { v4: uuidv4 } = require('uuid');
const googleAIService = require('../services/googleAIService');
const logger = require('../utils/logger');

class AIController {
  /**
   * 优化文本
   * @param {Object} requestData - 请求数据
   * @returns {Object} 优化结果
   */
  async optimizeText(requestData) {
    const startTime = Date.now();
    const requestId = uuidv4();

    try {
      const { text, options = {}, metadata = {} } = requestData;

      logger.info('Starting text optimization', {
        requestId,
        textLength: text.length,
        options,
        userId: metadata.userId
      });

      // 调用Google AI服务进行文本优化
      const optimizationResult = await googleAIService.optimizeText(text, options);

      const processingTime = (Date.now() - startTime) / 1000;

      const result = {
        success: true,
        data: {
          optimizedText: optimizationResult.optimizedText,
          improvements: optimizationResult.improvements || [],
          processingTime: processingTime,
          originalLength: text.length,
          optimizedLength: optimizationResult.optimizedText.length
        },
        metadata: {
          requestId,
          timestamp: new Date().toISOString(),
          processingTime: processingTime,
          version: '1.0.0'
        }
      };

      logger.info('Text optimization completed successfully', {
        requestId,
        processingTime,
        originalLength: text.length,
        optimizedLength: optimizationResult.optimizedText.length
      });

      return result;

    } catch (error) {
      const processingTime = (Date.now() - startTime) / 1000;

      logger.error('Text optimization failed', {
        requestId,
        error: error.message,
        processingTime,
        stack: error.stack
      });

      // 返回错误响应
      return {
        success: false,
        error: {
          message: 'Text optimization failed',
          code: 'OPTIMIZATION_ERROR',
          details: process.env.NODE_ENV === 'development' ? error.message : undefined
        },
        metadata: {
          requestId,
          timestamp: new Date().toISOString(),
          processingTime: processingTime
        }
      };
    }
  }

  /**
   * 获取服务状态
   * @returns {Object} 服务状态信息
   */
  async getServiceStatus() {
    try {
      // 检查Google AI服务连接状态
      const googleStatus = await googleAIService.checkConnection();

      return {
        success: true,
        data: {
          status: 'healthy',
          services: {
            googleAI: googleStatus ? 'connected' : 'disconnected'
          },
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      logger.error('Service status check failed', {
        error: error.message
      });

      return {
        success: false,
        error: {
          message: 'Service status check failed',
          code: 'STATUS_CHECK_ERROR'
        }
      };
    }
  }
}

module.exports = new AIController();
