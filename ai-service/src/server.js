/**
 * WriterPro AI Service - Hong Kong Server
 * 专门处理AI文本优化请求的独立服务
 */

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { body, validationResult } = require('express-validator');
const logger = require('./utils/logger');
const aiController = require('./controllers/aiController');
const authMiddleware = require('./middleware/auth');

const app = express();
const PORT = process.env.PORT || 3002;

// 安全中间件
app.use(helmet());

// CORS配置 - 只允许中国服务器访问
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3001'],
  credentials: true,
  methods: ['POST', 'GET', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-API-Key']
};
app.use(cors(corsOptions));

// 请求解析
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 速率限制
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15分钟
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // 限制每个IP 100次请求
  message: {
    error: 'Too many requests from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
});
app.use('/api/', limiter);

// 请求日志
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.path}`, {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    timestamp: new Date().toISOString()
  });
  next();
});

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'WriterPro AI Service',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

// API路由
app.post('/api/optimize-text', 
  authMiddleware,
  [
    body('text').isString().isLength({ min: 1, max: 50000 }).withMessage('Text must be between 1 and 50000 characters'),
    body('options.language').optional().isString().withMessage('Language must be a string'),
    body('options.style').optional().isString().withMessage('Style must be a string'),
    body('options.length').optional().isString().withMessage('Length must be a string'),
  ],
  async (req, res) => {
    try {
      // 验证请求
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
      }

      const result = await aiController.optimizeText(req.body);
      
      logger.info('Text optimization completed', {
        textLength: req.body.text.length,
        processingTime: result.metadata.processingTime
      });

      res.json(result);
    } catch (error) {
      logger.error('Text optimization failed', {
        error: error.message,
        stack: error.stack
      });

      res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: 'Text optimization failed'
      });
    }
  }
);

// 错误处理中间件
app.use((error, req, res, next) => {
  logger.error('Unhandled error', {
    error: error.message,
    stack: error.stack,
    path: req.path,
    method: req.method
  });

  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

// 启动服务器
app.listen(PORT, '0.0.0.0', () => {
  logger.info(`WriterPro AI Service started on port ${PORT}`, {
    environment: process.env.NODE_ENV,
    timestamp: new Date().toISOString()
  });
});

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

module.exports = app;
