            if ((payment && payment.type === 'alipay') || (!payment && refundData.refundMethod === 'alipay')) {
                // 支付宝退款
                console.log('开始处理支付宝退款...');
                
                const AlipayRefundService = require('../services/alipayRefundService');
                const alipayRefundService = new AlipayRefundService();

                const refundParams = {
                    amount: parseFloat(amount),
                    reason: reason || '管理员发起退款',
                    refundId: refund.id
                };

                if (payment) {
                    // 注册用户的支付宝退款
                    refundParams.orderId = payment.orderId;
                    refundParams.paymentId = payment.id;
                } else {
                    // 临时用户的支付宝退款
                    refundParams.tempOrderId = tempOrder.id;
                }

                const refundResult = await alipayRefundService.processRefund(refundParams);
                
                if (refundResult.success) {
                    console.log('支付宝退款成功:', refundResult);
                } else {
                    throw new Error(refundResult.message || '支付宝退款失败');
                }
            } else {
                // 微信支付退款（包括临时订单）
                console.log('处理微信支付退款...');
                await prisma.refund.update({
                    where: { id: refund.id },
                    data: {
                        status: 'completed',
                        processedAt: new Date()
                    }
                });
            }
