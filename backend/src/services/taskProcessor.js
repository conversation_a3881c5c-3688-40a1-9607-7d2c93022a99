const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const fs = require('fs');
const path = require('path');
const mammoth = require('mammoth');
const os = require('os');
const { spawn } = require('child_process');
const aiServiceClient = require('./aiServiceClient'); // 使用英国AI服务

const taskQueue = [];
const taskStatus = {}; // Stores { taskId: { status: 'pending'|'processing'|'completed'|'failed', result: any, error: any } }
let isProcessing = false;

// 🐍 Python文档解析功能开关
const USE_PYTHON_PARSER = true; // 强制启用（调试模式）

// 🧹 内存管理：定期清理过期的文档结构信息
if (!global.documentStructuresCleanupStarted) {
    global.documentStructuresCleanupStarted = true;
    global.documentStructures = global.documentStructures || {};

    setInterval(() => {
        const now = Date.now();
        const maxAge = 24 * 60 * 60 * 1000; // 24小时
        let cleanedCount = 0;

        Object.keys(global.documentStructures).forEach(key => {
            const structure = global.documentStructures[key];
            if (structure && structure.timestamp) {
                let shouldClean = false;
                let maxAgeForKey = maxAge;

                if (key.startsWith('doc_')) {
                    // doc_${documentId} 格式：保留7天（永久文档结构）
                    maxAgeForKey = 7 * 24 * 60 * 60 * 1000;
                    shouldClean = (now - structure.timestamp > maxAgeForKey);
                } else {
                    // taskId 格式：保留24小时（临时任务结构）
                    shouldClean = (now - structure.timestamp > maxAge);
                }

                if (shouldClean) {
                    delete global.documentStructures[key];
                    cleanedCount++;
                }
            }
        });

        if (cleanedCount > 0) {
        }
    }, 60 * 60 * 1000); // 每小时检查一次

}

// 🐍 Python文档解析函数
async function parseDocumentStructure(filePath, taskId) {
    return new Promise((resolve, reject) => {
        const pythonScript = path.join(__dirname, '../utils/documentParser.py');
        const python = spawn('python3', [pythonScript, filePath]);

        let output = '';
        let error = '';

        python.stdout.on('data', (data) => {
            output += data.toString();
        });

        python.stderr.on('data', (data) => {
            error += data.toString();
        });

        python.on('close', (code) => {
            if (code === 0) {
                try {
                    const result = JSON.parse(output);
                    if (result.success) {
                        resolve(result);
                    } else {
                        reject(new Error(`Python解析失败: ${result.error}`));
                    }
                } catch (parseError) {
                    reject(new Error(`Python输出解析失败: ${parseError.message}`));
                }
            } else {
                reject(new Error(`Python脚本执行失败: ${error}`));
            }
        });
    });
}

// 🧠 智能服务器配置检测
function detectServerCapabilities() {
    const cpuCount = os.cpus().length;
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const memoryGB = Math.round(totalMemory / (1024 * 1024 * 1024));
    const platform = os.platform();
    const arch = os.arch();


    return {
        cpuCount,
        totalMemoryGB: memoryGB,
        freeMemoryGB: Math.round(freeMemory / (1024 * 1024 * 1024)),
        platform,
        arch
    };
}

// 🎯 智能并发配置计算
function calculateOptimalConcurrency(serverInfo) {
    const { cpuCount, totalMemoryGB, freeMemoryGB } = serverInfo;

    // 基于CPU的并发计算（保守系数0.8，避免CPU过载）
    const cpuBasedConcurrency = Math.floor(cpuCount * 2.5 * 0.8); // 每核心2.5个任务

    // 基于内存的并发计算（每任务预估200MB）
    const memoryBasedConcurrency = Math.floor((freeMemoryGB * 1024) / 200);

    // 取较小值确保稳定性
    const maxConcurrentTasks = Math.min(cpuBasedConcurrency, memoryBasedConcurrency, 20); // 最大不超过20

    // API并发数（通常为任务并发数的70-80%）
    const maxApiCalls = Math.floor(maxConcurrentTasks * 0.75);

    // 队列大小（基于内存容量）
    const queueSize = Math.min(totalMemoryGB * 10, 200); // 每GB内存支持10个队列任务

    // 速率限制（基于并发数动态调整）
    const rateLimit = maxConcurrentTasks > 10 ? 50 : 100; // 高并发时更短间隔

    const config = {
        MAX_CONCURRENT_TASKS: Math.max(maxConcurrentTasks, 3), // 最少3个
        MAX_GOOGLE_API_CALLS: Math.max(maxApiCalls, 2), // 最少2个
        QUEUE_SIZE_LIMIT: Math.max(queueSize, 20), // 最少20个
        RATE_LIMIT_DELAY: rateLimit,
    };


    return config;
}

// 🚀 初始化智能并发配置
const serverInfo = detectServerCapabilities();
const CONCURRENCY_CONFIG = calculateOptimalConcurrency(serverInfo);

// 并发状态跟踪
let currentConcurrentTasks = 0;
let currentApiCalls = 0;
let lastApiCallTime = 0;

// 🔍 性能监控数据
let performanceMetrics = {
    taskCompletionTimes: [],
    apiResponseTimes: [],
    errorCount: 0,
    totalTasks: 0,
    lastAdjustmentTime: Date.now(),
    cpuUsageHistory: [],
    memoryUsageHistory: []
};

// 📊 实时性能监控
function updatePerformanceMetrics(taskTime, apiTime, isError = false) {
    performanceMetrics.totalTasks++;

    if (isError) {
        performanceMetrics.errorCount++;
    } else {
        performanceMetrics.taskCompletionTimes.push(taskTime);
        if (apiTime) performanceMetrics.apiResponseTimes.push(apiTime);
    }

    // 只保留最近100个记录
    if (performanceMetrics.taskCompletionTimes.length > 100) {
        performanceMetrics.taskCompletionTimes = performanceMetrics.taskCompletionTimes.slice(-100);
    }
    if (performanceMetrics.apiResponseTimes.length > 100) {
        performanceMetrics.apiResponseTimes = performanceMetrics.apiResponseTimes.slice(-100);
    }
}

// 🎛️ 动态并发调整
function adjustConcurrencyIfNeeded() {
    const now = Date.now();
    const timeSinceLastAdjustment = now - performanceMetrics.lastAdjustmentTime;

    // 每5分钟检查一次
    if (timeSinceLastAdjustment < 5 * 60 * 1000) return;

    const avgTaskTime = performanceMetrics.taskCompletionTimes.length > 0
        ? performanceMetrics.taskCompletionTimes.reduce((a, b) => a + b, 0) / performanceMetrics.taskCompletionTimes.length
        : 0;

    const errorRate = performanceMetrics.totalTasks > 0
        ? performanceMetrics.errorCount / performanceMetrics.totalTasks
        : 0;

    const avgApiTime = performanceMetrics.apiResponseTimes.length > 0
        ? performanceMetrics.apiResponseTimes.reduce((a, b) => a + b, 0) / performanceMetrics.apiResponseTimes.length
        : 0;


    let shouldAdjust = false;
    let newMaxTasks = CONCURRENCY_CONFIG.MAX_CONCURRENT_TASKS;
    let newMaxApi = CONCURRENCY_CONFIG.MAX_GOOGLE_API_CALLS;

    // 如果错误率过高（>10%），降低并发
    if (errorRate > 0.1) {
        newMaxTasks = Math.max(Math.floor(CONCURRENCY_CONFIG.MAX_CONCURRENT_TASKS * 0.8), 2);
        newMaxApi = Math.max(Math.floor(CONCURRENCY_CONFIG.MAX_GOOGLE_API_CALLS * 0.8), 1);
        shouldAdjust = true;
    }
    // 如果API响应时间过长（>60秒），降低API并发
    else if (avgApiTime > 60000) {
        newMaxApi = Math.max(Math.floor(CONCURRENCY_CONFIG.MAX_GOOGLE_API_CALLS * 0.7), 1);
        shouldAdjust = true;
    }
    // 如果性能良好且错误率低（<2%），可以适当提升并发
    else if (errorRate < 0.02 && avgTaskTime < 45000 && avgApiTime < 30000) {
        const serverInfo = detectServerCapabilities();
        const optimalConfig = calculateOptimalConcurrency(serverInfo);

        if (CONCURRENCY_CONFIG.MAX_CONCURRENT_TASKS < optimalConfig.MAX_CONCURRENT_TASKS) {
            newMaxTasks = Math.min(CONCURRENCY_CONFIG.MAX_CONCURRENT_TASKS + 1, optimalConfig.MAX_CONCURRENT_TASKS);
            newMaxApi = Math.min(CONCURRENCY_CONFIG.MAX_GOOGLE_API_CALLS + 1, optimalConfig.MAX_GOOGLE_API_CALLS);
            shouldAdjust = true;
        }
    }

    if (shouldAdjust) {
        CONCURRENCY_CONFIG.MAX_CONCURRENT_TASKS = newMaxTasks;
        CONCURRENCY_CONFIG.MAX_GOOGLE_API_CALLS = newMaxApi;
        performanceMetrics.lastAdjustmentTime = now;

        // 重置性能指标
        performanceMetrics.taskCompletionTimes = [];
        performanceMetrics.apiResponseTimes = [];
        performanceMetrics.errorCount = 0;
        performanceMetrics.totalTasks = 0;
    }
}

// 提示词模板已移至英国AI服务器，统一管理
// Google API相关常量已移除，现在统一通过英国AI服务调用

async function processDocumentTask(task) {
    const { taskId, file, userId, isTempUser } = task;

    try {
        taskStatus[taskId] = { status: 'processing', message: '正在处理文档...' };

        const decodedFileName = decodeURIComponent(escape(file.originalname));

        // 立即返回基本信息，异步提取文本
        taskStatus[taskId] = {
            status: 'completed',
            result: {
                documentId: Date.now(),
                fileName: decodedFileName,
                text: '正在提取文档内容，请稍候...', // 占位文本
                paragraphs: ['正在提取文档内容，请稍候...'],
                wordCount: 0,
                paragraphCount: 1
            }
        };


        // 异步提取文本内容并处理所有后续操作
        setImmediate(async () => {
            try {
                let text = '';

                // 🔧 先保存文件到服务器，获取filePath
                const uploadsDir = path.join(__dirname, '../../uploads');
                if (!fs.existsSync(uploadsDir)) {
                    fs.mkdirSync(uploadsDir, { recursive: true });
                }
                const fileExtension = path.extname(file.originalname);
                const newFileName = `${Date.now()}${fileExtension}`;
                const filePath = path.join(uploadsDir, newFileName);
                fs.writeFileSync(filePath, file.buffer);

                if (file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
                    // 🐍 尝试使用Python解析器（排除标题）
                    if (USE_PYTHON_PARSER) {
                        try {
                            const parseResult = await parseDocumentStructure(filePath, taskId);
                            if (parseResult.success && parseResult.content.length > 0) {
                                // 使用Python解析的正文内容（已排除标题）
                                text = parseResult.content.join('\n');

                                // 🔧 保存Python解析的完整结构信息（用于前端显示）
                                global.documentStructures = global.documentStructures || {};

                                const structureInfo = {
                                    fullStructure: parseResult.details.all_paragraphs,
                                    titleInfo: parseResult.details.titles_only,
                                    contentInfo: parseResult.details.content_only,
                                    statistics: parseResult.statistics,
                                    timestamp: Date.now() // 添加时间戳用于清理
                                };

                                global.documentStructures[taskId] = structureInfo;
                            } else {
                                throw new Error('Python解析返回空内容');
                            }
                        } catch (parseError) {
                            // 回退到mammoth方案
                            const result = await mammoth.extractRawText({ buffer: file.buffer });
                            text = result.value;
                        }
                    } else {
                        // 使用原有mammoth方案
                        const result = await mammoth.extractRawText({ buffer: file.buffer });
                        text = result.value;
                    }
                } else if (file.mimetype === 'text/plain') {
                    text = file.buffer.toString('utf8');
                } else {
                    try {
                        text = file.buffer.toString('utf8');
                    } catch (error) {
                        text = '文档格式不支持，请上传.txt或.docx文件';
                    }
                }

                const wordCount = text.replace(/\s+/g, '').length;

                // 根据用户类型保存到不同的数据库表
                let documentRecord = null;

                if (isTempUser) {
                    // 临时用户：保存到TempDocument表
                    try {
                        // 智能文件名处理：确保保存正确的文件名信息
                        const nameWithoutExt = decodedFileName.replace(/\.[^/.]+$/, '');
                        const optimizedFileName = `${nameWithoutExt}+优化版.docx`;

                        documentRecord = await prisma.tempDocument.create({
                            data: {
                                tempUserId: userId,
                                fileName: optimizedFileName,
                                originalName: decodedFileName,
                                serviceType: 'ai_optimization',
                                status: 'completed',
                                filePath: `uploads/${newFileName}`,
                                expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天过期
                            }
                        });
                } else {
                    // 正式用户：保存到Document表

                    // 安全地转换userId - 支持临时用户ID
                    let numericUserId;
                    try {
                        // 如果是临时用户ID（以temp_开头），跳过数字转换
                        if (userId && userId.toString().startsWith('temp_')) {
                            numericUserId = null; // 强制作为临时用户处理
                        } else {
                            numericUserId = parseInt(userId);

                            if (isNaN(numericUserId) || numericUserId <= 0) {
                                throw new Error(`无效的用户ID: ${userId}`);
                            }
                        }
                    } catch (parseError) {
                        numericUserId = null;
                    }

                    // 验证用户是否存在
                    let userExists = null;
                    if (numericUserId) {
                        userExists = await prisma.user.findUnique({
                            where: { id: numericUserId },
                            select: { id: true, username: true, email: true }
                        });
                    }

                    if (!userExists || !numericUserId) {
                        // 作为临时用户处理
                        try {
                            // 安全处理userId，确保不为undefined
                            const safeUserId = userId ? userId.toString() : `temp_${Date.now()}`;
                            // 智能文件名处理：确保保存正确的文件名信息
                            const nameWithoutExt = decodedFileName.replace(/\.[^/.]+$/, '');
                            const optimizedFileName = `${nameWithoutExt}+优化版.docx`;

                            documentRecord = await prisma.tempDocument.create({
                                data: {
                                    tempUserId: safeUserId,
                                    fileName: optimizedFileName,
                                    originalName: decodedFileName,
                                    serviceType: 'ai_optimization',
                                    status: 'completed',
                                    filePath: `uploads/${newFileName}`,
                                    expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天过期
                                }
                            });
                        } catch (tempError) {
                            // 即使保存失败，也不影响文档处理，创建一个虚拟记录
                            documentRecord = { id: Date.now() };
                        }
                    } else {
                        try {
                            // 智能文件名处理：确保保存正确的文件名信息
                            const nameWithoutExt = decodedFileName.replace(/\.[^/.]+$/, '');
                            const optimizedFileName = `${nameWithoutExt}+优化版.docx`;

                            documentRecord = await prisma.document.create({
                                data: {
                                    title: decodedFileName, // 保持原有逻辑
                                    content: text,
                                    userId: numericUserId,
                                    // 添加文件名相关字段
                                    fileName: optimizedFileName,
                                    originalName: decodedFileName,
                                    serviceType: 'ai_optimization',
                                    status: 'completed'
                                },
                            });

                            // 🎯 关键修改：将文档结构信息也保存到documentId，供内容优化使用
                            const structureInfo = global.documentStructures && global.documentStructures[taskId];
                            if (structureInfo) {
                                global.documentStructures[`doc_${documentRecord.id}`] = structureInfo;
                            }
                        } catch (createError) {
                            // 尝试作为临时用户处理
                            try {
                                const safeUserId = userId ? userId.toString() : `temp_${Date.now()}`;
                                // 智能文件名处理：确保保存正确的文件名信息
                                const nameWithoutExt = decodedFileName.replace(/\.[^/.]+$/, '');
                                const optimizedFileName = `${nameWithoutExt}+优化版.docx`;

                                documentRecord = await prisma.tempDocument.create({
                                    data: {
                                        tempUserId: safeUserId,
                                        fileName: optimizedFileName,
                                        originalName: decodedFileName,
                                        serviceType: 'ai_optimization',
                                        status: 'completed',
                                        filePath: `uploads/${newFileName}`,
                                        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
                                    }
                                });

                                // 🎯 关键修复：临时用户也保存文档结构信息
                                const structureInfo = global.documentStructures && global.documentStructures[taskId];
                                if (structureInfo) {
                                    global.documentStructures[`doc_${documentRecord.id}`] = structureInfo;
                                }
                            } catch (fallbackError) {
                                // 创建虚拟记录避免后续null错误
                                documentRecord = { id: Date.now() };
                            }
                        }
                    }
                }

                // 智能分段处理
                const paragraphs = intelligentSegmentation(text);

                // 更新任务结果为真实内容
                if (taskStatus[taskId] && taskStatus[taskId].result) {
                    // 基础数据（用于AIGC检测）
                    taskStatus[taskId].result.text = text;
                    taskStatus[taskId].result.paragraphs = paragraphs;
                    taskStatus[taskId].result.wordCount = wordCount;
                    taskStatus[taskId].result.documentId = documentRecord.id;
                    taskStatus[taskId].result.paragraphCount = paragraphs.length;

                    // 🔧 添加完整结构信息（用于前端显示）
                    const structureInfo = global.documentStructures && global.documentStructures[taskId];
                    if (structureInfo) {
                        // 构建完整段落结构（包含标题和正文）
                        const fullParagraphs = structureInfo.fullStructure.map(item => ({
                            text: item.text,
                            isTitle: item.is_title,
                            style: item.style,
                            length: item.length
                        }));

                        // 构建完整文本（包含标题）
                        const fullText = structureInfo.fullStructure.map(item => item.text).join('\n');

                        taskStatus[taskId].result.fullText = fullText;
                        taskStatus[taskId].result.fullParagraphs = fullParagraphs;
                        taskStatus[taskId].result.titleCount = structureInfo.statistics.title_paragraphs;
                        taskStatus[taskId].result.contentRatio = structureInfo.statistics.content_ratio;


                        // 🔍 详细调试信息

                        // 显示前3个段落的结构示例
                        fullParagraphs.slice(0, 3).forEach((para, index) => {
                        });

                        // 🔧 清理临时存储（只清理taskId，保留doc_${documentId}）
                        delete global.documentStructures[taskId];
                    }
                }


                // 🔍 最终数据结构调试
                if (taskStatus[taskId] && taskStatus[taskId].result) {
                    const finalResult = taskStatus[taskId].result;
                }

            } catch (error) {

                // 不要将错误信息作为文档内容返回，而是设置任务状态为失败
                taskStatus[taskId] = {
                    status: 'failed',
                    error: '文档处理失败，请重试',
                    message: '文档处理过程中发生错误',
                    // 在开发环境下提供详细错误信息
                    debugInfo: process.env.NODE_ENV === 'development' ? {
                        errorMessage: error.message,
                        errorCode: error.code,
                        errorName: error.name
                    } : undefined
                };
            }
        });
    } catch (error) {
        taskStatus[taskId] = {
            status: 'failed',
            error: '文档处理失败，请重试',
            message: '文档处理过程中发生错误',
            debugInfo: process.env.NODE_ENV === 'development' ? {
                errorMessage: error.message,
                errorCode: error.code,
                errorName: error.name
            } : undefined
        };
    }
}

async function optimizeTextTask(task) {
    const { taskId, text, optimizeType, documentId, userId, isTempUser } = task;

    try {
        // 智能分段处理
        const paragraphs = intelligentSegmentation(text);

        // 更新任务状态，包含段落信息
        taskStatus[taskId] = {
            status: 'processing',
            message: '开始逐段优化...',
            progress: {
                completedParagraphs: 0,
                totalParagraphs: paragraphs.length,
                currentParagraph: 1,
                percentage: 0
            },
            segments: text.length <= 2000 ? [] : undefined // 只有短文本才存储分段结果
        };

        // 逐段优化处理
        const optimizedParagraphs = [];

        for (let i = 0; i < paragraphs.length; i++) {
            const paragraph = paragraphs[i];
            const paragraphIndex = i + 1;


            // 更新当前处理状态
            const percentage = Math.round((i / paragraphs.length) * 100);
            taskStatus[taskId] = {
                ...taskStatus[taskId],
                message: `正在优化第${paragraphIndex}段，共${paragraphs.length}段`,
                progress: {
                    completedParagraphs: i,
                    totalParagraphs: paragraphs.length,
                    currentParagraph: paragraphIndex,
                    percentage: percentage
                }
            };

            try {
                // 调用香港AI服务优化单个段落
                const optimizedParagraph = await callGoogleAPI(paragraph);

                optimizedParagraphs.push(optimizedParagraph);

                // 对于短文本（≤2000字），实现流式显示
                if (text.length <= 2000) {
                    taskStatus[taskId].segments = [...optimizedParagraphs];
                }

                // 更新完成进度
                const completedPercentage = Math.round(((i + 1) / paragraphs.length) * 100);
                taskStatus[taskId].progress = {
                    completedParagraphs: i + 1,
                    totalParagraphs: paragraphs.length,
                    currentParagraph: i + 1 < paragraphs.length ? i + 2 : paragraphs.length,
                    percentage: completedPercentage
                };

            } catch (error) {
                // 单段失败不影响整体，使用原文
                optimizedParagraphs.push(paragraph);
            }

            // 添加小延迟，避免API调用过于频繁
            if (i < paragraphs.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }

        // 合并所有优化后的段落
        const finalOptimizedText = optimizedParagraphs.join('\n\n');

        // 保存优化内容到数据库
        if (documentId) {
            try {
                if (isTempUser) {
                    // 临时用户：尝试更新tempDocument表
                    await prisma.tempDocument.updateMany({
                        where: {
                            tempUserId: userId.toString(),
                            id: parseInt(documentId)
                        },
                        data: {
                            status: 'completed'
                        },
                    });
                } else {
                    // 正式用户：更新document表
                    await prisma.document.update({
                        where: { id: parseInt(documentId) },
                        data: {
                            optimizedContent: finalOptimizedText,
                            status: 'completed'
                        },
                    });
                }
            } catch (updateError) {
                // 文档更新失败不影响优化流程
                console.log(`DEBUG: 文档 ${documentId} 更新失败，但不影响优化流程:`, updateError.message);
            }
        }

        // 更新任务状态为完成
        taskStatus[taskId] = {
            status: 'completed',
            message: '优化完成',
            result: { optimizedText: finalOptimizedText },
            progress: {
                completedParagraphs: paragraphs.length,
                totalParagraphs: paragraphs.length,
                currentParagraph: paragraphs.length,
                percentage: 100
            },
            segments: text.length <= 2000 ? optimizedParagraphs : undefined
        };

    } catch (error) {
        taskStatus[taskId] = { status: 'failed', error: error.message };
    }
}

async function optimizeParagraphTask(task) {
    const { taskId, text, optimizeType, documentId, userId, isTempUser } = task;
    try {
        taskStatus[taskId] = { 
            status: 'processing', 
            message: '正在优化段落...', 
            progress: { completedParagraphs: 0, totalParagraphs: text.split(/\n\n+/).filter(p => p.trim().length > 0).length } 
        };

        // API速率控制
        while (currentApiCalls >= CONCURRENCY_CONFIG.MAX_GOOGLE_API_CALLS) {
            await new Promise(resolve => setTimeout(resolve, 500));
        }

        currentApiCalls++;

        try {

            // 使用英国AI服务客户端统一调用
            const result = await aiServiceClient.optimizeText(text, {
                language: 'zh-CN',
                style: optimizeType === 'academic' ? 'academic' : 'professional',
                length: 'medium'
            });

            currentApiCalls--; // 释放API调用槽位

            const optimizedText = result.optimizedText;

            // Save optimized content to database if documentId is provided
            if (documentId) {
                try {
                    if (isTempUser) {
                        // 临时用户：尝试更新tempDocument表
                        await prisma.tempDocument.updateMany({
                            where: {
                                tempUserId: userId.toString(),
                                id: parseInt(documentId)
                            },
                            data: {
                                status: 'completed'
                            },
                        });
                    } else {
                        // 正式用户：更新document表
                        await prisma.document.update({
                            where: { id: parseInt(documentId) },
                            data: { optimizedContent: optimizedText },
                        });
                    }

                    // 🎯 方案B：立即进行文档结构重组并保存DOCX文件
                    await generateAndSaveStructuredDocx(documentId, optimizedText, taskId);
                } catch (dbUpdateError) {
                    // 数据库更新失败不影响优化流程，但记录日志
                    console.log(`DEBUG: 文档 ${documentId} 更新失败，但继续进行文档重组:`, dbUpdateError.message);

                    // 即使数据库更新失败，仍然尝试文档重组
                    try {
                        await generateAndSaveStructuredDocx(documentId, optimizedText, taskId);
                    } catch (docError) {
                        console.log(`DEBUG: 文档 ${documentId} 重组也失败:`, docError.message);
                    }
                }
            }

            taskStatus[taskId] = { status: 'completed', result: { optimizedText } };
        } catch (aiError) {
            currentApiCalls--; // 释放API调用槽位
            throw aiError;
        }
    } catch (error) {
        // 确保释放API调用槽位
        if (currentApiCalls > 0) {
            currentApiCalls--;
        }
        taskStatus[taskId] = { status: 'failed', error: error.message };
    }
}


async function processNextTask() {
    // 检查是否可以处理更多任务
    if (taskQueue.length === 0 || currentConcurrentTasks >= CONCURRENCY_CONFIG.MAX_CONCURRENT_TASKS) {
        return;
    }

    const task = taskQueue.shift(); // Get the next task from the queue
    const { taskId, taskCategory } = task;

    // 增加并发计数
    currentConcurrentTasks++;

    // 异步处理任务，不阻塞其他任务
    processTaskAsync(task).finally(() => {
        currentConcurrentTasks--;

        // 尝试处理下一个任务
        setTimeout(processNextTask, 100);
    });

    // 立即尝试处理下一个任务（如果还有并发容量）
    setTimeout(processNextTask, 50);
}

async function processTaskAsync(task) {
    const { taskId, taskCategory } = task;
    const taskStartTime = Date.now();

    try {
        if (taskCategory === 'document_upload') {
            await processDocumentTask(task);
        } else if (taskCategory === 'optimize_text') {
            await optimizeTextTask(task);
        } else if (taskCategory === 'optimize_paragraph') {
            await optimizeParagraphTask(task);
        } else {
            taskStatus[taskId] = { status: 'failed', error: '未知任务类别' };
            updatePerformanceMetrics(Date.now() - taskStartTime, 0, true);
            return;
        }

        // 记录成功任务的性能指标
        const taskDuration = Date.now() - taskStartTime;
        updatePerformanceMetrics(taskDuration, 0, false);

        // 定期检查是否需要调整并发配置
        adjustConcurrencyIfNeeded();

    } catch (error) {
        taskStatus[taskId] = { status: 'failed', error: error.message };

        // 记录失败任务的性能指标
        const taskDuration = Date.now() - taskStartTime;
        updatePerformanceMetrics(taskDuration, 0, true);
    }
}

function addTask(taskCategory, data) {
    // 检查队列是否已满
    if (taskQueue.length >= CONCURRENCY_CONFIG.QUEUE_SIZE_LIMIT) {
        throw new Error(`队列已满，当前排队任务: ${taskQueue.length}，请稍后再试`);
    }

    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    taskQueue.push({ taskId, taskCategory, ...data });

    let initialStatus = {
        status: 'pending',
        message: '优化处理中...'
    };

    if (taskCategory === 'optimize_text' || taskCategory === 'optimize_paragraph') {
        // For optimization tasks, initialize progress even when pending
        initialStatus.progress = { completedParagraphs: 0, totalParagraphs: 0 };

        // 预估处理时间
        const textLength = data.text ? data.text.length : 1000;
        const estimatedProcessTime = textLength <= 2000 ? 60 : Math.ceil(textLength / 500) * 30;
        initialStatus.estimatedProcessTime = estimatedProcessTime;
    }

    taskStatus[taskId] = initialStatus;

    // 启动任务处理
    processNextTask();

    return taskId;
}

function getTaskStatus(taskId) {
    return taskStatus[taskId];
}

// 获取系统状态
function getSystemStatus() {
    const currentServerInfo = detectServerCapabilities();
    const avgTaskTime = performanceMetrics.taskCompletionTimes.length > 0
        ? Math.round(performanceMetrics.taskCompletionTimes.reduce((a, b) => a + b, 0) / performanceMetrics.taskCompletionTimes.length / 1000)
        : 0;
    const errorRate = performanceMetrics.totalTasks > 0
        ? Math.round((performanceMetrics.errorCount / performanceMetrics.totalTasks) * 100)
        : 0;

    return {
        serverInfo: {
            cpuCores: currentServerInfo.cpuCount,
            totalMemoryGB: currentServerInfo.totalMemoryGB,
            freeMemoryGB: currentServerInfo.freeMemoryGB,
            platform: currentServerInfo.platform,
            architecture: currentServerInfo.arch
        },
        concurrency: {
            maxConcurrentTasks: CONCURRENCY_CONFIG.MAX_CONCURRENT_TASKS,
            currentConcurrentTasks: currentConcurrentTasks,
            maxGoogleApiCalls: CONCURRENCY_CONFIG.MAX_GOOGLE_API_CALLS,
            currentApiCalls: currentApiCalls,
            isIntelligentMode: true,
            lastAdjustment: new Date(performanceMetrics.lastAdjustmentTime).toISOString()
        },
        queue: {
            queueLength: taskQueue.length,
            maxQueueSize: CONCURRENCY_CONFIG.QUEUE_SIZE_LIMIT,
            queueUtilization: Math.round((taskQueue.length / CONCURRENCY_CONFIG.QUEUE_SIZE_LIMIT) * 100)
        },
        performance: {
            averageTaskTime: avgTaskTime > 0 ? `${avgTaskTime}秒` : '暂无数据',
            errorRate: `${errorRate}%`,
            totalTasksProcessed: performanceMetrics.totalTasks,
            supportedConcurrentUsers: CONCURRENCY_CONFIG.MAX_CONCURRENT_TASKS,
            rateLimit: CONCURRENCY_CONFIG.RATE_LIMIT_DELAY + 'ms',
            configurationMode: '智能自适应'
        },
        status: taskQueue.length >= CONCURRENCY_CONFIG.QUEUE_SIZE_LIMIT ? 'busy' : 'available',
        healthScore: calculateHealthScore()
    };
}

// 🏥 计算系统健康分数
function calculateHealthScore() {
    let score = 100;

    // 基于错误率扣分
    const errorRate = performanceMetrics.totalTasks > 0
        ? performanceMetrics.errorCount / performanceMetrics.totalTasks
        : 0;
    score -= errorRate * 500; // 每1%错误率扣5分

    // 基于队列利用率扣分
    const queueUtilization = taskQueue.length / CONCURRENCY_CONFIG.QUEUE_SIZE_LIMIT;
    if (queueUtilization > 0.8) score -= (queueUtilization - 0.8) * 100; // 超过80%开始扣分

    // 基于并发利用率
    const concurrencyUtilization = currentConcurrentTasks / CONCURRENCY_CONFIG.MAX_CONCURRENT_TASKS;
    if (concurrencyUtilization > 0.9) score -= (concurrencyUtilization - 0.9) * 50; // 超过90%轻微扣分

    // 基于内存使用率
    const currentServerInfo = detectServerCapabilities();
    const memoryUtilization = 1 - (currentServerInfo.freeMemoryGB / currentServerInfo.totalMemoryGB);
    if (memoryUtilization > 0.85) score -= (memoryUtilization - 0.85) * 200; // 超过85%扣分

    return Math.max(Math.round(score), 0);
}

// 获取队列中的任务列表（用于管理）
function getQueueStatus() {
    return {
        pendingTasks: taskQueue.map(task => ({
            taskId: task.taskId,
            category: task.taskCategory,
            addedAt: new Date(parseInt(task.taskId.split('_')[1])),
            estimatedWaitTime: taskStatus[task.taskId]?.estimatedWaitTime || 0
        })),
        processingTasks: Object.keys(taskStatus)
            .filter(taskId => taskStatus[taskId].status === 'processing')
            .map(taskId => ({
                taskId,
                status: taskStatus[taskId],
                startedAt: new Date(parseInt(taskId.split('_')[1]))
            }))
    };
}

/**
 * 调用香港AI服务的辅助函数
 * @param {string} text - 需要优化的文本
 * @returns {string} - 优化后的文本
 */
async function callGoogleAPI(text) {
    try {

        const result = await aiServiceClient.optimizeText(text, {
            language: 'zh-CN',
            style: 'professional',
            length: 'medium'
        });

        return result.optimizedText;

    } catch (error) {
        throw error;
    }
}

/**
 * 智能分段算法：按语义段落分割，不是简单的换行分割
 * @param {string} text - 需要分段的文本
 * @returns {Array} - 分段后的段落数组
 */
function intelligentSegmentation(text) {
    if (!text || text.trim().length === 0) {
        return [];
    }

    // 第一步：按双换行符分割（保留原有段落结构）
    let segments = text.split(/\n\s*\n/).filter(para => para.trim().length > 0);

    // 第二步：处理过长的段落（超过500字的段落进一步分割）
    const maxParagraphLength = 500;
    const finalSegments = [];

    segments.forEach(segment => {
        const trimmedSegment = segment.trim();
        if (trimmedSegment.length <= maxParagraphLength) {
            finalSegments.push(trimmedSegment);
        } else {
            // 对于过长的段落，按句号分割
            const sentences = trimmedSegment.split(/[。！？；]\s*/);
            let currentParagraph = '';

            sentences.forEach((sentence, index) => {
                if (!sentence.trim()) return;

                // 重新添加标点符号（除了最后一个句子）
                const punctuation = index < sentences.length - 1 ?
                    (trimmedSegment.charAt(trimmedSegment.indexOf(sentence) + sentence.length) || '。') : '';
                const fullSentence = sentence.trim() + punctuation;

                // 如果当前段落加上新句子不超过限制，则添加
                if (currentParagraph.length + fullSentence.length <= maxParagraphLength) {
                    currentParagraph += (currentParagraph ? '' : '') + fullSentence;
                } else {
                    // 当前段落已满，保存并开始新段落
                    if (currentParagraph.trim()) {
                        finalSegments.push(currentParagraph.trim());
                    }
                    currentParagraph = fullSentence;
                }
            });

            // 添加最后一个段落
            if (currentParagraph.trim()) {
                finalSegments.push(currentParagraph.trim());
            }
        }
    });

    // 第三步：合并过短的段落（少于50字的段落与下一个段落合并）
    const minParagraphLength = 50;
    const optimizedSegments = [];
    let i = 0;

    while (i < finalSegments.length) {
        let currentSegment = finalSegments[i];

        // 如果当前段落太短且不是最后一个段落，尝试与下一个合并
        while (i + 1 < finalSegments.length &&
               currentSegment.length < minParagraphLength &&
               currentSegment.length + finalSegments[i + 1].length <= maxParagraphLength) {
            currentSegment += '\n\n' + finalSegments[i + 1];
            i++;
        }

        optimizedSegments.push(currentSegment);
        i++;
    }

    return optimizedSegments.filter(segment => segment.trim().length > 0);
}

// 获取所有任务状态（用于AIGC检测API查找文档结构）
function getAllTaskStatus() {
    return taskStatus;
}

// 🎯 方案B完善版：生成并保存结构化DOCX文件
async function generateAndSaveStructuredDocx(documentId, optimizedText, taskId) {
    try {

        // 验证输入参数
        if (!documentId || !optimizedText) {
            return;
        }

        // 获取原始文档结构信息
        const structureInfo = global.documentStructures && global.documentStructures[`doc_${documentId}`];

        if (!structureInfo || !structureInfo.fullStructure || structureInfo.fullStructure.length === 0) {
            return;
        }


        // 导入docx库
        const docx = require('docx');

        // 将优化后的内容按段落分割
        const optimizedParagraphs = optimizedText.split('\n').filter(p => p.trim().length > 0);

        // 重新组装文档：保留原标题，替换正文内容
        const reassembledParagraphs = [];
        let contentIndex = 0;
        let titleCount = 0;
        let contentCount = 0;

        for (const originalPara of structureInfo.fullStructure) {
            if (originalPara.is_title) {
                // 保留原标题
                reassembledParagraphs.push(new docx.Paragraph({
                    children: [new docx.TextRun({
                        text: originalPara.text,
                        bold: true,
                        size: 28
                    })],
                    spacing: { before: 240, after: 120 }
                }));
                titleCount++;
                if (titleCount <= 3) { // 只记录前3个标题
                }
            } else {
                // 使用优化后的内容替换原正文
                if (contentIndex < optimizedParagraphs.length) {
                    const optimizedTextPara = optimizedParagraphs[contentIndex];
                    reassembledParagraphs.push(new docx.Paragraph({
                        children: [new docx.TextRun(optimizedTextPara)],
                        spacing: { after: 200 }
                    }));
                    contentIndex++;
                    contentCount++;
                    if (contentCount <= 3) { // 只记录前3个内容段落
                    }
                }
            }
        }

        // 如果还有剩余的优化内容，追加到文档末尾
        let appendedCount = 0;
        while (contentIndex < optimizedParagraphs.length) {
            const remainingText = optimizedParagraphs[contentIndex];
            reassembledParagraphs.push(new docx.Paragraph({
                children: [new docx.TextRun(remainingText)],
                spacing: { after: 200 }
            }));
            contentIndex++;
            appendedCount++;
        }

        if (appendedCount > 0) {
        }


        // 生成DOCX文档
        const doc = new docx.Document({
            sections: [{
                properties: {},
                children: reassembledParagraphs.length > 0 ? reassembledParagraphs : [
                    new docx.Paragraph({
                        children: [new docx.TextRun(optimizedText)]
                    })
                ]
            }]
        });

        const buffer = await docx.Packer.toBuffer(doc);

        // 保存文件到服务器
        const uploadsDir = path.join(__dirname, '../../uploads');
        if (!fs.existsSync(uploadsDir)) {
            fs.mkdirSync(uploadsDir, { recursive: true });
        }

        const fileName = `structured_${documentId}_${Date.now()}.docx`;
        const filePath = path.join(uploadsDir, fileName);

        try {
            fs.writeFileSync(filePath, buffer);
        } catch (writeError) {
            throw writeError;
        }

        // 更新数据库记录
        try {
            const downloadUrl = `/api/documents/download-structured/${documentId}`;

            // 尝试更新document表
            let updateResult = null;
            try {
                updateResult = await prisma.document.update({
                    where: { id: parseInt(documentId) },
                    data: {
                        filePath: filePath,
                        downloadUrl: downloadUrl
                    }
                });
            } catch (docUpdateError) {
                // 如果document表更新失败，尝试tempDocument表
                if (docUpdateError.code === 'P2025') {
                    // 记录不存在，可能是临时用户文档
                    console.log(`文档 ${documentId} 在document表中不存在，跳过数据库更新`);
                } else {
                    throw docUpdateError;
                }
            }

            // 验证更新是否成功（仅当更新成功时）
            if (updateResult) {
                const verifyDoc = await prisma.document.findUnique({
                    where: { id: parseInt(documentId) },
                    select: { id: true, filePath: true, downloadUrl: true }
                });
            }

        } catch (dbError) {
            // 删除已创建的文件
            try {
                fs.unlinkSync(filePath);
            } catch (cleanupError) {
                // 忽略清理错误
            }
            throw dbError;
        }


    } catch (error) {
        // 不抛出错误，避免影响主流程
    }
}

module.exports = {
    addTask,
    getTaskStatus,
    getAllTaskStatus,
    getSystemStatus,
    getQueueStatus,
    CONCURRENCY_CONFIG,
    detectServerCapabilities,
    calculateOptimalConcurrency,
    performanceMetrics
};