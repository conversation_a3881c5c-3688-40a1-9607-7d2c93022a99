# 证书文件目录

此目录用于存放支付所需的证书文件：

## 支付宝证书
1. `private-key.pem` - 应用私钥
2. `alipay-public-key.pem` - 支付宝公钥

## 微信支付证书
1. `apiclient_cert.pem` - 微信支付公钥证书
2. `apiclient_key.pem` - 微信支付私钥证书

## 如何获取证书

1. 前往[支付宝开放平台](https://open.alipay.com/)
2. 登录您的开发者账户
3. 创建应用并获取AppID
4. 使用[支付宝开放平台秘钥工具](https://opendocs.alipay.com/common/02kipk)生成RSA密钥对
5. 将生成的私钥保存为`private-key.pem`
6. 将应用公钥上传到支付宝开放平台
7. 从支付宝开放平台下载支付宝公钥，保存为`alipay-public-key.pem`

## 注意事项

- 请妥善保管私钥，不要泄露给他人
- 确保证书格式为PKCS1（如果使用支付宝密钥工具生成的是PKCS8格式，请使用工具转换）
- 证书文件不应提交到版本控制系统中，请将此目录添加到.gitignore文件 