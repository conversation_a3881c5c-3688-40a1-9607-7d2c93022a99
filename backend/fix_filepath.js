const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

async function fixFilePaths() {
    try {
        console.log('🔧 开始修复文档filePath字段...');
        
        // 检查uploads目录中的structured文件
        const uploadsDir = path.join(__dirname, 'uploads');
        const files = fs.readdirSync(uploadsDir).filter(file => file.startsWith('structured_'));
        
        console.log(`📁 找到 ${files.length} 个重组文件:`, files);
        
        for (const file of files) {
            // 从文件名提取文档ID: structured_491_1754375924137.docx -> 491
            const match = file.match(/structured_(\d+)_\d+\.docx/);
            if (match) {
                const documentId = parseInt(match[1]);
                const filePath = path.join(uploadsDir, file);
                const downloadUrl = `/api/documents/download-structured/${documentId}`;
                
                console.log(`\n🔧 处理文档 ${documentId}:`);
                console.log(`   文件: ${file}`);
                console.log(`   路径: ${filePath}`);
                
                // 检查文件是否存在
                if (fs.existsSync(filePath)) {
                    const stats = fs.statSync(filePath);
                    console.log(`   大小: ${Math.round(stats.size / 1024)}KB`);
                    
                    // 更新数据库
                    try {
                        const updateResult = await prisma.document.update({
                            where: { id: documentId },
                            data: {
                                filePath: filePath,
                                downloadUrl: downloadUrl
                            }
                        });
                        
                        console.log(`   ✅ 数据库更新成功`);
                        
                        // 验证更新
                        const verifyDoc = await prisma.document.findUnique({
                            where: { id: documentId },
                            select: { id: true, title: true, filePath: true, downloadUrl: true }
                        });
                        
                        console.log(`   📊 验证结果:`);
                        console.log(`      标题: ${verifyDoc.title}`);
                        console.log(`      filePath: ${verifyDoc.filePath}`);
                        console.log(`      downloadUrl: ${verifyDoc.downloadUrl}`);
                        
                    } catch (dbError) {
                        console.error(`   ❌ 数据库更新失败: ${dbError.message}`);
                    }
                } else {
                    console.log(`   ❌ 文件不存在: ${filePath}`);
                }
            }
        }
        
        console.log('\n🎉 filePath修复完成！');
        
    } catch (error) {
        console.error('❌ 修复过程出错:', error);
    } finally {
        await prisma.$disconnect();
    }
}

// 执行修复
fixFilePaths();
