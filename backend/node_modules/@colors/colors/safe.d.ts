// Type definitions for Colors.js 1.2
// Project: https://github.com/Marak/colors.js
// Definitions by: <PERSON> <https://github.com/Bartvds>, <PERSON><PERSON> <https://github.com/staeke>
// Definitions: https://github.com/Marak/colors.js

export const enabled: boolean;
export function enable(): void;
export function disable(): void;
export function setTheme(theme: any): void;

export function strip(str: string): string;
export function stripColors(str: string): string;

export function black(str: string): string;
export function red(str: string): string;
export function green(str: string): string;
export function yellow(str: string): string;
export function blue(str: string): string;
export function magenta(str: string): string;
export function cyan(str: string): string;
export function white(str: string): string;
export function gray(str: string): string;
export function grey(str: string): string;

export function brightRed(str: string): string;
export function brightGreen(str: string): string;
export function brightYellow(str: string): string;
export function brightBlue(str: string): string;
export function brightMagenta(str: string): string;
export function brightCyan(str: string): string;
export function brightWhite(str: string): string;

export function bgBlack(str: string): string;
export function bgRed(str: string): string;
export function bgGreen(str: string): string;
export function bgYellow(str: string): string;
export function bgBlue(str: string): string;
export function bgMagenta(str: string): string;
export function bgCyan(str: string): string;
export function bgWhite(str: string): string;

export function bgBrightRed(str: string): string;
export function bgBrightGreen(str: string): string;
export function bgBrightYellow(str: string): string;
export function bgBrightBlue(str: string): string;
export function bgBrightMagenta(str: string): string;
export function bgBrightCyan(str: string): string;
export function bgBrightWhite(str: string): string;

export function reset(str: string): string;
export function bold(str: string): string;
export function dim(str: string): string;
export function italic(str: string): string;
export function underline(str: string): string;
export function inverse(str: string): string;
export function hidden(str: string): string;
export function strikethrough(str: string): string;

export function rainbow(str: string): string;
export function zebra(str: string): string;
export function america(str: string): string;
export function trap(str: string): string;
export function random(str: string): string;
export function zalgo(str: string): string;
