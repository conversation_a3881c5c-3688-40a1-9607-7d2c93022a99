#!/usr/bin/env python3
"""
安全清理JavaScript文件中的控制台输出
只移除console.log/error/warn/info语句，保持所有业务逻辑不变
"""

import re
import sys

def clean_console_statements(content):
    """
    安全地移除控制台输出语句，保持业务逻辑完整
    """
    lines = content.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # 检查是否是纯粹的控制台输出行
        stripped = line.strip()
        
        # 匹配各种控制台输出模式
        console_patterns = [
            r'^\s*console\.(log|error|warn|info)\s*\(',
            r'^\s*debugLog\s*\(',
        ]
        
        is_console_line = False
        for pattern in console_patterns:
            if re.match(pattern, line):
                is_console_line = True
                break
        
        if is_console_line:
            # 检查是否是多行控制台语句的开始
            if line.count('(') > line.count(')'):
                # 多行语句，需要找到结束
                paren_count = line.count('(') - line.count(')')
                # 跳过这行，继续处理后续行直到括号平衡
                continue
            else:
                # 单行语句，直接跳过
                continue
        
        # 处理包含控制台输出的复合语句
        # 例如: .catch(console.error) 或 updateUIBasedOnAuthState().catch(console.error)
        if 'console.error' in line and '.catch(' in line:
            # 替换为空的catch
            line = re.sub(r'\.catch\s*\(\s*console\.error\s*\)', '.catch(() => {})', line)
        
        # 保留所有非控制台输出的行
        cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 clean_console.py <javascript_file>")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 清理控制台输出
        cleaned_content = clean_console_statements(content)
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        print(f"✅ 已清理 {file_path} 中的控制台输出")
        
    except Exception as e:
        print(f"❌ 处理文件失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
