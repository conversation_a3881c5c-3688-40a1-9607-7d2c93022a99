// 全局变量
let currentTab = 'google-api';
let googleApiUsageChart = null;
let googleApiCostChart = null;
let apiTrendChart = null;
let apiDistributionChart = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 初始化选项卡
    initTabs();
    
    // 初始化图表
    initCharts();
    
    // 绑定事件处理器
    bindEventHandlers();
});

// 初始化选项卡
function initTabs() {
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // 移除所有选项卡的活动状态
            tabs.forEach(t => t.classList.remove('active'));
            
            // 设置当前选项卡为活动状态
            tab.classList.add('active');
            
            // 获取选项卡对应的内容ID
            const tabId = tab.dataset.tab;
            currentTab = tabId;
            
            // 隐藏所有选项卡内容
            const tabPanes = document.querySelectorAll('.tab-pane');
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            // 显示当前选项卡内容
            document.getElementById(tabId).classList.add('active');
        });
    });
}

// 初始化图表
function initCharts() {
    // 初始化谷歌API使用图表
    initGoogleApiUsageChart();
    
    // 初始化谷歌API费用图表
    initGoogleApiCostChart();
    
    // 初始化API趋势图表（仅在API使用统计选项卡存在时）
    if (document.getElementById('apiTrendChart')) {
        initApiTrendChart();
    }
    
    // 初始化API分布图表（仅在API使用统计选项卡存在时）
    if (document.getElementById('apiDistributionChart')) {
        initApiDistributionChart();
    }
}

// 初始化谷歌API使用图表
function initGoogleApiUsageChart() {
    const ctx = document.getElementById('googleApiUsageChart');
    if (!ctx) return;
    
    // 获取最近30天的日期
    const dates = getLast30Days();
    
    // 模拟数据
    const translationData = generateRandomData(30, 100, 500);
    const documentData = generateRandomData(30, 50, 300);
    const visionData = generateRandomData(30, 20, 150);
    
    googleApiUsageChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [
                {
                    label: 'Translation API',
                    data: translationData,
                    borderColor: '#1890ff',
                    backgroundColor: 'rgba(24, 144, 255, 0.1)',
                    tension: 0.4,
                    fill: true
                },
                {
                    label: 'Document AI',
                    data: documentData,
                    borderColor: '#52c41a',
                    backgroundColor: 'rgba(82, 196, 26, 0.1)',
                    tension: 0.4,
                    fill: true
                },
                {
                    label: 'Vision API',
                    data: visionData,
                    borderColor: '#faad14',
                    backgroundColor: 'rgba(250, 173, 20, 0.1)',
                    tension: 0.4,
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'API调用次数'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            }
        }
    });
}

// 初始化谷歌API费用图表
function initGoogleApiCostChart() {
    const ctx = document.getElementById('googleApiCostChart');
    if (!ctx) return;
    
    // 获取最近6个月的月份
    const months = getLast6Months();
    
    // 模拟数据
    const translationCost = [42.56, 38.45, 45.78, 52.30, 60.15, 65.42];
    const documentCost = [35.20, 40.15, 38.90, 45.60, 48.75, 53.20];
    const visionCost = [25.45, 22.80, 26.35, 30.45, 34.20, 39.21];
    
    googleApiCostChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: months,
            datasets: [
                {
                    label: 'Translation API',
                    data: translationCost,
                    backgroundColor: 'rgba(24, 144, 255, 0.8)',
                    borderWidth: 0
                },
                {
                    label: 'Document AI',
                    data: documentCost,
                    backgroundColor: 'rgba(82, 196, 26, 0.8)',
                    borderWidth: 0
                },
                {
                    label: 'Vision API',
                    data: visionCost,
                    backgroundColor: 'rgba(250, 173, 20, 0.8)',
                    borderWidth: 0
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return `${context.dataset.label}: $${context.raw.toFixed(2)}`;
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: '费用 ($)'
                    },
                    ticks: {
                        callback: function(value) {
                            return '$' + value;
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

// 初始化API趋势图表
function initApiTrendChart() {
    const ctx = document.getElementById('apiTrendChart');
    if (!ctx) return;
    
    // 获取最近30天的日期
    const dates = getLast30Days();
    
    // 模拟数据
    const totalCalls = generateRandomData(30, 2000, 5000);
    const successCalls = totalCalls.map(value => value * (0.95 + Math.random() * 0.04)); // 95-99% 成功率
    const errorCalls = totalCalls.map((value, index) => value - successCalls[index]);
    
    apiTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: dates,
            datasets: [
                {
                    label: '总调用',
                    data: totalCalls,
                    borderColor: '#1890ff',
                    backgroundColor: 'rgba(24, 144, 255, 0.1)',
                    tension: 0.4,
                    fill: true,
                    yAxisID: 'y'
                },
                {
                    label: '成功调用',
                    data: successCalls,
                    borderColor: '#52c41a',
                    backgroundColor: 'transparent',
                    borderDash: [5, 5],
                    tension: 0.4,
                    fill: false,
                    yAxisID: 'y'
                },
                {
                    label: '错误调用',
                    data: errorCalls,
                    borderColor: '#ff4d4f',
                    backgroundColor: 'transparent',
                    tension: 0.4,
                    fill: false,
                    yAxisID: 'y1'
                }
            ]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                }
            },
            scales: {
                y: {
                    type: 'linear',
                    position: 'left',
                    title: {
                        display: true,
                        text: 'API调用次数'
                    },
                    beginAtZero: true
                },
                y1: {
                    type: 'linear',
                    position: 'right',
                    title: {
                        display: true,
                        text: '错误调用次数'
                    },
                    beginAtZero: true,
                    grid: {
                        drawOnChartArea: false
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            interaction: {
                mode: 'index',
                intersect: false
            }
        }
    });
}

// 初始化API分布图表
function initApiDistributionChart() {
    const ctx = document.getElementById('apiDistributionChart');
    if (!ctx) return;
    
    // 模拟数据
    const data = {
        labels: [
            '/api/documents/analyze',
            '/api/documents/translate',
            '/api/users/auth',
            '/api/payment/process',
            '/api/documents/ocr',
            '其他'
        ],
        datasets: [{
            data: [25, 20, 30, 10, 8, 7],
            backgroundColor: [
                '#1890ff',
                '#52c41a',
                '#faad14',
                '#13c2c2',
                '#f759ab',
                '#999999'
            ],
            hoverOffset: 4
        }]
    };
    
    apiDistributionChart = new Chart(ctx, {
        type: 'doughnut',
        data: data,
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const label = context.label;
                            const value = context.raw;
                            return `${label}: ${value}%`;
                        }
                    }
                }
            },
            cutout: '60%'
        }
    });
}

// 绑定事件处理器
function bindEventHandlers() {
    // 添加API密钥按钮
    const addApiKeyBtn = document.getElementById('addApiKeyBtn');
    if (addApiKeyBtn) {
        addApiKeyBtn.addEventListener('click', () => {
            openApiKeyModal();
        });
    }
    
    // 关闭模态框按钮
    const closeModal = document.getElementById('closeModal');
    if (closeModal) {
        closeModal.addEventListener('click', () => {
            closeApiKeyModal();
        });
    }
    
    // 取消按钮
    const cancelBtn = document.getElementById('cancelBtn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            closeApiKeyModal();
        });
    }
    
    // 保存API密钥按钮
    const saveApiKeyBtn = document.getElementById('saveApiKeyBtn');
    if (saveApiKeyBtn) {
        saveApiKeyBtn.addEventListener('click', () => {
            saveApiKey();
        });
    }
    
    // 密码可见性切换
    const toggleButtons = document.querySelectorAll('.toggle-visibility');
    toggleButtons.forEach(button => {
        button.addEventListener('click', () => {
            togglePasswordVisibility(button);
        });
    });
    
    // 复制按钮
    const copyButtons = document.querySelectorAll('.copy-btn');
    copyButtons.forEach(button => {
        button.addEventListener('click', () => {
            copyToClipboard(button);
        });
    });
    
    // 创建客户端API密钥按钮
    const createClientApiBtn = document.getElementById('createClientApiBtn');
    if (createClientApiBtn) {
        createClientApiBtn.addEventListener('click', () => {
            alert('打开创建客户端API密钥对话框');
        });
    }
    
    // 模态框背景点击关闭
    const modalBackdrop = document.querySelector('.modal-backdrop');
    if (modalBackdrop) {
        modalBackdrop.addEventListener('click', () => {
            closeApiKeyModal();
        });
    }
}

// 打开API密钥模态框
function openApiKeyModal() {
    const modal = document.getElementById('apiKeyModal');
    if (modal) {
        modal.classList.add('active');
    }
}

// 关闭API密钥模态框
function closeApiKeyModal() {
    const modal = document.getElementById('apiKeyModal');
    if (modal) {
        modal.classList.remove('active');
    }
}

// 保存API密钥
function saveApiKey() {
    // 获取表单数据
    const apiName = document.getElementById('apiName').value;
    const apiKey = document.getElementById('apiKey').value;
    const projectId = document.getElementById('projectId').value;
    const apiLimit = document.getElementById('apiLimit').value;
    const apiStatus = document.getElementById('apiStatus').value;
    
    // 验证表单
    if (!apiName || !apiKey || !projectId) {
        alert('请填写必填字段');
        return;
    }
    
    // 模拟保存API密钥
    console.log('保存API密钥:', {
        name: apiName,
        key: apiKey,
        projectId,
        limit: apiLimit,
        status: apiStatus
    });
    
    // 关闭模态框
    closeApiKeyModal();
    
    // 显示成功消息
    alert(`API密钥 "${apiName}" 已保存`);
    
    // 刷新页面或更新UI
    location.reload();
}

// 切换密码可见性
function togglePasswordVisibility(button) {
    const isVisible = button.dataset.visible === 'true';
    const maskedField = button.closest('.masked-field');
    const maskedValue = maskedField.querySelector('.masked-value');
    
    if (isVisible) {
        // 隐藏密码
        maskedValue.textContent = '●●●●●●●●●●●●●●●●●●●●●●●';
        button.innerHTML = '<i class="bi bi-eye"></i>';
        button.dataset.visible = 'false';
    } else {
        // 显示密码（这里使用模拟值，实际应用中应该从后端获取真实值）
        maskedValue.textContent = 'sk_test_aBcDeFgHiJkLmNoPqRsTuVwXyZ123456789';
        button.innerHTML = '<i class="bi bi-eye-slash"></i>';
        button.dataset.visible = 'true';
    }
}

// 复制到剪贴板
function copyToClipboard(button) {
    const maskedField = button.closest('.masked-field');
    const maskedValue = maskedField.querySelector('.masked-value');
    
    // 如果密码是隐藏的，先将其显示（为了复制真实值）
    const toggleButton = maskedField.querySelector('.toggle-visibility');
    const isVisible = toggleButton.dataset.visible === 'true';
    
    let textToCopy = maskedValue.textContent;
    
    if (!isVisible) {
        // 这里应该从后端获取真实值，而不是使用模拟值
        textToCopy = 'sk_test_aBcDeFgHiJkLmNoPqRsTuVwXyZ123456789';
    }
    
    // 创建一个临时输入框用于复制
    const tempInput = document.createElement('input');
    tempInput.value = textToCopy;
    document.body.appendChild(tempInput);
    tempInput.select();
    document.execCommand('copy');
    document.body.removeChild(tempInput);
    
    // 显示复制成功提示
    alert('已复制到剪贴板');
}

// 获取最近30天的日期
function getLast30Days() {
    const result = [];
    const today = new Date();
    
    for (let i = 29; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        result.push(formatDate(date));
    }
    
    return result;
}

// 获取最近6个月的月份
function getLast6Months() {
    const result = [];
    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();
    
    for (let i = 5; i >= 0; i--) {
        let month = currentMonth - i;
        let year = currentYear;
        
        if (month < 0) {
            month += 12;
            year -= 1;
        }
        
        const monthName = new Date(year, month, 1).toLocaleString('zh-CN', { month: 'short' });
        result.push(monthName);
    }
    
    return result;
}

// 生成随机数据
function generateRandomData(length, min, max) {
    return Array.from({ length }, () => Math.floor(min + Math.random() * (max - min)));
}

// 格式化日期
function formatDate(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}/${day}`;
} 