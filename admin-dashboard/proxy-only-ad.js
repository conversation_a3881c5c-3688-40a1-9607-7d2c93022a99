/**
 * 代理服务器 - 只处理 /ad 路径，其他请求转发到原始服务器
 * 这样可以保留您原有的前端页面
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 8080; // 使用不同端口避免冲突
const ORIGINAL_SERVER = 'http://localhost:3000'; // 您原始的前端服务器

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.ico': 'image/x-icon'
};

function getContentType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

// 处理管理后台API请求
function handleAdminApi(req, res, pathname) {
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    
    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }
    
    if (pathname === '/ad/api/auth/login' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => { body += chunk.toString(); });
        req.on('end', () => {
            try {
                const { username, password } = JSON.parse(body);
                if (username === 'admin' && password === 'admin123') {
                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        requiresTwoFactor: true,
                        message: '请输入双因素认证码'
                    }));
                } else {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '用户名或密码错误'
                    }));
                }
            } catch (error) {
                res.writeHead(400);
                res.end(JSON.stringify({ success: false, message: '请求格式错误' }));
            }
        });
        return;
    }
    
    if (pathname === '/ad/api/auth/verify-2fa' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => { body += chunk.toString(); });
        req.on('end', () => {
            try {
                const { code } = JSON.parse(body);
                if (code === '101010') {
                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        user: {
                            id: '1',
                            username: 'admin',
                            name: '系统管理员',
                            email: '<EMAIL>',
                            role: 'admin'
                        }
                    }));
                } else {
                    res.writeHead(401);
                    res.end(JSON.stringify({ success: false, message: '验证码错误' }));
                }
            } catch (error) {
                res.writeHead(400);
                res.end(JSON.stringify({ success: false, message: '请求格式错误' }));
            }
        });
        return;
    }
    
    if (pathname === '/ad/api/auth/resend-2fa' && req.method === 'POST') {
        res.writeHead(200);
        res.end(JSON.stringify({
            success: true,
            message: '验证码已重新发送'
        }));
        return;
    }
    
    // 其他API请求返回404
    res.writeHead(404);
    res.end(JSON.stringify({ success: false, message: 'API接口不存在' }));
}

// 处理管理后台静态文件
function handleAdminStatic(req, res, pathname) {
    let filePath;
    
    if (pathname === '/ad' || pathname === '/ad/') {
        filePath = path.join(__dirname, 'index.html');
    } else if (pathname.startsWith('/ad/')) {
        const actualPath = pathname.substring(4); // 移除 /ad 前缀
        
        // 页面映射
        const pageMap = {
            'dashboard': 'dashboard.html',
            'users': 'users.html',
            'agents': 'agents.html',
            'commission': 'commission.html',
            'pricing': 'pricing.html',
            'roles': 'roles.html',
            'finance': 'finance.html',
            'analytics': 'analytics.html',
            'reports': 'reports.html',
            'settings': 'settings.html',
            'content': 'content.html',
            'api-docs': 'api-docs.html',
            'marketing': 'marketing.html',
            'security': 'security.html',
            'monitor': 'monitor.html',
            'logs': 'logs.html',
            'tasks': 'tasks.html',
            'notifications': 'notifications.html',
            'backup': 'backup.html'
        };
        
        if (pageMap[actualPath]) {
            filePath = path.join(__dirname, pageMap[actualPath]);
        } else {
            // 静态资源文件
            filePath = path.join(__dirname, actualPath);
        }
    }
    
    fs.readFile(filePath, (err, data) => {
        if (err) {
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end(`
                <h1>404 - 文件不存在</h1>
                <p><a href="http://localhost:3000">返回主网站</a> | <a href="/ad">进入管理后台</a></p>
            `);
        } else {
            const contentType = getContentType(filePath);
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(data);
        }
    });
}

// 创建代理服务器
const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    
    // 只处理 /ad 开头的请求
    if (pathname.startsWith('/ad')) {
        // 处理API请求
        if (pathname.startsWith('/ad/api/')) {
            return handleAdminApi(req, res, pathname);
        }
        
        // 处理静态文件请求
        return handleAdminStatic(req, res, pathname);
    }
    
    // 其他请求返回重定向提示
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>管理后台代理服务器</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                .btn { display: inline-block; padding: 15px 30px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
                .btn:hover { background: #0056b3; }
                .btn.secondary { background: #6c757d; }
                .btn.secondary:hover { background: #545b62; }
                .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🔧 管理后台代理服务器</h1>
                <p>这是管理后台的代理服务器，运行在端口 ${PORT}。</p>
                <p>您的原始前端页面仍然在端口 3000 正常运行。</p>
                
                <div style="margin: 20px 0;">
                    <a href="http://localhost:3000" class="btn secondary">🏠 访问主网站 (端口3000)</a>
                    <a href="/ad" class="btn">🔧 进入管理后台</a>
                </div>
                
                <div class="info">
                    <h3>📋 访问信息</h3>
                    <p><strong>主网站:</strong> <a href="http://localhost:3000">http://localhost:3000</a></p>
                    <p><strong>管理后台:</strong> <a href="http://localhost:${PORT}/ad">http://localhost:${PORT}/ad</a></p>
                    <p><strong>登录信息:</strong> admin / admin123 / 101010</p>
                </div>
                
                <div class="info" style="background: #fff3cd; border: 1px solid #ffeaa7;">
                    <h3>⚠️ 重要说明</h3>
                    <p>管理后台现在运行在独立端口 ${PORT}，不会影响您原有的前端应用。</p>
                    <p>如果您希望将管理后台集成到端口 3000 的 /ad 路径下，请联系开发人员进行配置。</p>
                </div>
            </div>
        </body>
        </html>
    `);
});

// 启动服务器
server.listen(PORT, () => {
    console.log('========================================');
    console.log('🔧 管理后台代理服务器启动成功');
    console.log(`📱 主网站 (您的原始页面): http://localhost:3000`);
    console.log(`🔧 管理后台: http://localhost:${PORT}/ad`);
    console.log('👤 登录信息: admin / admin123 / 101010');
    console.log('========================================');
    console.log('');
    console.log('💡 提示:');
    console.log('- 您的原始前端页面仍在 http://localhost:3000 正常运行');
    console.log('- 管理后台现在在独立端口运行，不会影响您的主网站');
    console.log('- 按 Ctrl+C 停止管理后台服务器');
    console.log('========================================');
});

module.exports = server;
