<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专家工作台 - WriterPro</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="css/ui-enhancements.css">
    <style>
        .work-item {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        .work-item:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .content-editor {
            min-height: 300px;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            background: #f8f9fa;
        }
        .time-tracker {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
        }
        .status-badge {
            font-size: 0.9em;
            padding: 6px 12px;
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="bi bi-person-workspace"></i>
                    <span>专家工作台</span>
                </div>
                <button class="menu-toggle">
                    <i class="bi bi-list"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="active"><a href="#"><i class="bi bi-house"></i><span>工作台</span></a></li>
                    <li><a href="#" onclick="showMyWorkOrders()"><i class="bi bi-clipboard-check"></i><span>我的工单</span></a></li>
                    <li><a href="#" onclick="showWorkHistory()"><i class="bi bi-clock-history"></i><span>工作历史</span></a></li>
                    <li><a href="#" onclick="showProfile()"><i class="bi bi-person"></i><span>个人资料</span></a></li>
                    <li><a href="#" onclick="showSettings()"><i class="bi bi-gear"></i><span>设置</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-header">
                <div class="header-left">
                    <button class="menu-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <h1>专家工作台</h1>
                </div>
                
                <div class="header-right">
                    <div class="time-tracker me-3">
                        <div class="d-flex align-items-center">
                            <i class="bi bi-clock me-2"></i>
                            <div>
                                <div class="fw-bold" id="currentTime">00:00:00</div>
                                <small>今日工作时间</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="user-menu">
                        <button class="user-btn">
                            <i class="bi bi-person-circle"></i>
                            <span id="expertName">专家</span>
                            <i class="bi bi-chevron-down"></i>
                        </button>
                        <div class="dropdown-menu">
                            <a href="#" class="dropdown-item" onclick="showProfile()">
                                <i class="bi bi-person"></i>
                                <span>个人资料</span>
                            </a>
                            <a href="#" class="dropdown-item" onclick="showSettings()">
                                <i class="bi bi-gear"></i>
                                <span>设置</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="#" class="dropdown-item" onclick="updateStatus('offline')">
                                <i class="bi bi-box-arrow-right"></i>
                                <span>下线</span>
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 状态卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-clipboard-check"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="currentOrders">0</h3>
                                <p>当前工单</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="completedToday">0</h3>
                                <p>今日完成</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-clock"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="avgTime">0h</h3>
                                <p>平均用时</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-star"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="rating">5.0</h3>
                                <p>客户评分</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 状态控制 -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h5 class="mb-0">工作状态</h5>
                                <p class="text-muted mb-0">当前状态: <span class="badge bg-success" id="currentStatus">在线</span></p>
                            </div>
                            <div class="col-md-6 text-end">
                                <div class="btn-group">
                                    <button class="btn btn-success" onclick="updateStatus('active')">
                                        <i class="bi bi-play-circle"></i> 上线
                                    </button>
                                    <button class="btn btn-warning" onclick="updateStatus('busy')">
                                        <i class="bi bi-pause-circle"></i> 忙碌
                                    </button>
                                    <button class="btn btn-secondary" onclick="updateStatus('offline')">
                                        <i class="bi bi-stop-circle"></i> 下线
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 待处理工单 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">待处理工单</h5>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshWorkOrders()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="workOrdersList">
                            <div class="text-center py-4">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">加载中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 工单处理模态框 -->
    <div class="modal fade" id="workOrderModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">工单处理</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>原始内容</h6>
                            <div class="content-editor" id="originalContent" style="background: #fff; border-color: #ccc;">
                                <!-- 原始内容 -->
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>优化内容</h6>
                            <textarea class="form-control content-editor" id="optimizedContent" placeholder="请在此输入优化后的内容..."></textarea>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6>工作日志</h6>
                            <textarea class="form-control" id="workLog" rows="3" placeholder="记录本次工作内容..."></textarea>
                        </div>
                        <div class="col-md-6">
                            <h6>工作时间</h6>
                            <div class="input-group">
                                <input type="number" class="form-control" id="hoursSpent" placeholder="小时" step="0.5" min="0">
                                <span class="input-group-text">小时</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-warning" onclick="saveProgress()">保存进度</button>
                    <button type="button" class="btn btn-success" onclick="submitWork()">提交完成</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/toast-notifications.js"></script>
    <script src="js/expert-workspace.js"></script>
</body>
</html>
