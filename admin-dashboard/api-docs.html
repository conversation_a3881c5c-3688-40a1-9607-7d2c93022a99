<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API文档 - WriterPro管理后台</title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/ui-enhancements.css">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- 引入Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- 引入Prism.js用于代码高亮 -->
    <link href="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/themes/prism.min.css" rel="stylesheet">
    <style>
        .api-sidebar {
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        .api-endpoint {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            margin-bottom: 1.5rem;
            background-color: #ffffff;
        }
        .api-endpoint-header {
            background-color: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            border-radius: 0.375rem 0.375rem 0 0;
        }
        .api-method {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 0.25rem;
            font-weight: bold;
            font-size: 0.875rem;
            margin-right: 0.5rem;
        }
        .api-method.get { background-color: #d1ecf1; color: #0c5460; }
        .api-method.post { background-color: #d4edda; color: #155724; }
        .api-method.put { background-color: #fff3cd; color: #856404; }
        .api-method.delete { background-color: #f8d7da; color: #721c24; }
        .api-path {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #495057;
        }
        .api-content {
            padding: 1.5rem;
        }
        .param-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .response-example {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-top: 1rem;
        }
        .api-nav {
            background-color: #f8f9fa;
            border-radius: 0.375rem;
            padding: 1rem;
        }
        .api-nav .nav-link {
            color: #495057;
            padding: 0.5rem 0.75rem;
            border-radius: 0.25rem;
            margin-bottom: 0.25rem;
        }
        .api-nav .nav-link:hover {
            background-color: #e9ecef;
            color: #0d6efd;
        }
        .api-nav .nav-link.active {
            background-color: #0d6efd;
            color: #ffffff;
        }
        .api-section {
            margin-bottom: 3rem;
        }
        .api-test-form {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-top: 1rem;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="logo">
                <h1>WriterPro</h1>
                <span>管理后台</span>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li><a href="dashboard.html"><i class="bi bi-speedometer2"></i><span data-i18n="common.dashboard">仪表盘</span></a></li>
                    <li><a href="users.html"><i class="bi bi-people"></i><span data-i18n="common.users">用户管理</span></a></li>
                    <li><a href="agents.html"><i class="bi bi-person-badge"></i><span data-i18n="common.agents">代理商管理</span></a></li>
                    <li><a href="commission.html"><i class="bi bi-currency-dollar"></i><span>佣金管理</span></a></li>
                    <li><a href="pricing.html"><i class="bi bi-tags"></i><span>价格管理</span></a></li>
                    <li><a href="roles.html"><i class="bi bi-shield-check"></i><span data-i18n="common.roles">角色管理</span></a></li>
                    <li><a href="finance.html"><i class="bi bi-currency-dollar"></i><span data-i18n="common.finance">财务管理</span></a></li>
                    <li><a href="settings.html"><i class="bi bi-gear"></i><span data-i18n="common.settings">系统设置</span></a></li>
                    <li><a href="content.html"><i class="bi bi-file-text"></i><span data-i18n="common.content">内容管理</span></a></li>
                    <li class="active"><a href="api-docs.html"><i class="bi bi-code-slash"></i><span>API文档</span></a></li>
                    <li><a href="marketing.html"><i class="bi bi-megaphone"></i><span data-i18n="common.marketing">营销工具</span></a></li>
                    <li><a href="security.html"><i class="bi bi-shield-lock"></i><span data-i18n="common.security">安全中心</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 头部 -->
            <header class="main-header">
                <div class="header-left">
                    <button class="menu-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <div class="header-title">
                        <h2>API文档</h2>
                        <p>WriterPro管理后台API接口文档和使用指南</p>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-actions">
                        <button class="action-btn theme-toggle" id="themeToggle" title="切换主题">
                            <i class="bi bi-moon"></i>
                        </button>
                        
                        <button class="action-btn">
                            <i class="bi bi-bell"></i>
                            <span class="badge">5</span>
                        </button>
                        
                        <div class="user-menu">
                            <button class="user-btn">
                                <div class="user-avatar">A</div>
                                <span>管理员</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <div class="user-dropdown">
                                <a href="#"><i class="bi bi-person"></i>个人资料</a>
                                <a href="#"><i class="bi bi-gear"></i>设置</a>
                                <a href="index.html"><i class="bi bi-box-arrow-right"></i>退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 内容区域 -->
            <div class="content-area">
                <div class="row">
                    <!-- API导航 -->
                    <div class="col-md-3">
                        <div class="api-sidebar">
                            <div class="api-nav">
                                <h6 class="mb-3">API导航</h6>
                                <nav class="nav flex-column">
                                    <a class="nav-link active" href="#overview">概览</a>
                                    <a class="nav-link" href="#authentication">认证</a>
                                    <a class="nav-link" href="#users">用户管理</a>
                                    <a class="nav-link" href="#agents">代理商管理</a>
                                    <a class="nav-link" href="#commission">佣金管理</a>
                                    <a class="nav-link" href="#pricing">价格管理</a>
                                    <a class="nav-link" href="#finance">财务管理</a>
                                    <a class="nav-link" href="#content">内容管理</a>
                                    <a class="nav-link" href="#marketing">营销工具</a>
                                    <a class="nav-link" href="#security">安全中心</a>
                                    <a class="nav-link" href="#monitor">系统监控</a>
                                    <a class="nav-link" href="#analytics">数据分析</a>
                                    <a class="nav-link" href="#errors">错误代码</a>
                                    <a class="nav-link" href="#sdk">SDK下载</a>
                                </nav>
                            </div>
                        </div>
                    </div>

                    <!-- API文档内容 -->
                    <div class="col-md-9">
                        <!-- 概览 -->
                        <section id="overview" class="api-section">
                            <h3>API概览</h3>
                            <p>WriterPro管理后台提供完整的RESTful API，支持所有管理功能的程序化访问。</p>
                            
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">基础信息</h6>
                                            <ul class="list-unstyled">
                                                <li><strong>Base URL:</strong> <code>https://api.writerpro.com/v1</code></li>
                                                <li><strong>协议:</strong> HTTPS</li>
                                                <li><strong>格式:</strong> JSON</li>
                                                <li><strong>编码:</strong> UTF-8</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">限制</h6>
                                            <ul class="list-unstyled">
                                                <li><strong>请求频率:</strong> 1000次/小时</li>
                                                <li><strong>并发连接:</strong> 10个</li>
                                                <li><strong>请求大小:</strong> 最大10MB</li>
                                                <li><strong>超时时间:</strong> 30秒</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <h5>快速开始</h5>
                            <div class="code-block">
                                <pre><code class="language-bash"># 获取访问令牌
curl -X POST https://api.writerpro.com/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# 使用令牌访问API
curl -X GET https://api.writerpro.com/v1/users \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"</code></pre>
                            </div>
                        </section>

                        <!-- 认证 -->
                        <section id="authentication" class="api-section">
                            <h3>认证</h3>
                            <p>所有API请求都需要进行身份认证。WriterPro使用JWT（JSON Web Token）进行认证。</p>

                            <!-- 登录接口 -->
                            <div class="api-endpoint">
                                <div class="api-endpoint-header">
                                    <span class="api-method post">POST</span>
                                    <span class="api-path">/auth/login</span>
                                    <span class="ms-auto text-muted">用户登录</span>
                                </div>
                                <div class="api-content">
                                    <h6>请求参数</h6>
                                    <table class="table table-sm param-table">
                                        <thead>
                                            <tr>
                                                <th>参数名</th>
                                                <th>类型</th>
                                                <th>必填</th>
                                                <th>说明</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>email</td>
                                                <td>string</td>
                                                <td>是</td>
                                                <td>用户邮箱</td>
                                            </tr>
                                            <tr>
                                                <td>password</td>
                                                <td>string</td>
                                                <td>是</td>
                                                <td>用户密码</td>
                                            </tr>
                                        </tbody>
                                    </table>

                                    <h6>响应示例</h6>
                                    <div class="code-block">
                                        <pre><code class="language-json">{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "role": "admin",
      "name": "管理员"
    },
    "expiresIn": 3600
  }
}</code></pre>
                                    </div>

                                    <!-- API测试表单 -->
                                    <div class="api-test-form">
                                        <h6>API测试</h6>
                                        <form id="loginTestForm">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <input type="email" class="form-control mb-2" placeholder="邮箱" id="testEmail">
                                                </div>
                                                <div class="col-md-6">
                                                    <input type="password" class="form-control mb-2" placeholder="密码" id="testPassword">
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-primary btn-sm" onclick="testLogin()">测试登录</button>
                                        </form>
                                        <div id="loginTestResult" class="mt-2"></div>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <!-- 用户管理API -->
                        <section id="users" class="api-section">
                            <h3>用户管理</h3>
                            <p>用户管理相关的API接口，包括用户的增删改查、状态管理等功能。</p>

                            <!-- 获取用户列表 -->
                            <div class="api-endpoint">
                                <div class="api-endpoint-header">
                                    <span class="api-method get">GET</span>
                                    <span class="api-path">/users</span>
                                    <span class="ms-auto text-muted">获取用户列表</span>
                                </div>
                                <div class="api-content">
                                    <h6>查询参数</h6>
                                    <table class="table table-sm param-table">
                                        <thead>
                                            <tr>
                                                <th>参数名</th>
                                                <th>类型</th>
                                                <th>必填</th>
                                                <th>说明</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>page</td>
                                                <td>integer</td>
                                                <td>否</td>
                                                <td>页码，默认1</td>
                                            </tr>
                                            <tr>
                                                <td>limit</td>
                                                <td>integer</td>
                                                <td>否</td>
                                                <td>每页数量，默认20</td>
                                            </tr>
                                            <tr>
                                                <td>search</td>
                                                <td>string</td>
                                                <td>否</td>
                                                <td>搜索关键词</td>
                                            </tr>
                                            <tr>
                                                <td>status</td>
                                                <td>string</td>
                                                <td>否</td>
                                                <td>用户状态：active, inactive</td>
                                            </tr>
                                        </tbody>
                                    </table>

                                    <h6>响应示例</h6>
                                    <div class="code-block">
                                        <pre><code class="language-json">{
  "success": true,
  "data": {
    "users": [
      {
        "id": "user_123",
        "username": "john_doe",
        "email": "<EMAIL>",
        "status": "active",
        "createdAt": "2024-01-01T00:00:00Z",
        "lastLoginAt": "2024-01-10T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  }
}</code></pre>
                                    </div>
                                </div>
                            </div>
                        </section>

                        <!-- 更多API部分将在下一部分添加 -->
                        <div id="moreApiSections">
                            <!-- 动态加载更多API文档 -->
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 引入JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/prismjs@1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="js/bilingual-init.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/language-switcher.js"></script>
    <script src="js/mobile-nav.js"></script>
    <script src="js/ui-enhancements.js"></script>
    <script src="js/api-docs.js"></script>
</body>
</html>
