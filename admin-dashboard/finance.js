// 全局变量
let currentPage = 1;
const pageSize = 10;
let totalPages = 1;
let revenueChart = null;
let revenuePieChart = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 初始化日期选择器
    initDateRangePicker();
    
    // 初始化图表
    initCharts();
    
    // 加载数据
    loadFinancialData();
    loadTransactions();
    loadRefunds();
    loadConsumptionAnalysis();

    // 初始化分析图表
    initAnalyticsCharts();

    // 绑定事件处理器
    bindEventHandlers();
});

// 初始化日期选择器
function initDateRangePicker() {
    $('#dateRange').daterangepicker({
        startDate: moment().subtract(29, 'days'),
        endDate: moment(),
        ranges: {
            '今天': [moment(), moment()],
            '昨天': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            '最近7天': [moment().subtract(6, 'days'), moment()],
            '最近30天': [moment().subtract(29, 'days'), moment()],
            '本月': [moment().startOf('month'), moment().endOf('month')],
            '上月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        },
        locale: {
            format: 'YYYY-MM-DD',
            separator: ' 至 ',
            applyLabel: '确定',
            cancelLabel: '取消',
            fromLabel: '从',
            toLabel: '至',
            customRangeLabel: '自定义',
            weekLabel: '周',
            daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'],
            monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月']
        }
    }, (start, end) => {
        loadFinancialData(start.format('YYYY-MM-DD'), end.format('YYYY-MM-DD'));
        loadTransactions();
    });
}

// 初始化图表
function initCharts() {
    // 收入趋势图
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: '收入',
                    data: [],
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    fill: true
                },
                {
                    label: '支出',
                    data: [],
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    fill: true
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top'
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: value => `¥${value}`
                    }
                }
            }
        }
    });

    // 收入构成饼图
    const pieCtx = document.getElementById('revenuePieChart').getContext('2d');
    revenuePieChart = new Chart(pieCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#0d6efd',
                    '#20c997',
                    '#ffc107',
                    '#dc3545',
                    '#6610f2'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right'
                }
            }
        }
    });
}

// 加载财务数据
async function loadFinancialData(startDate, endDate) {
    try {
        const response = await fetch(`/api/finance/statistics?startDate=${startDate}&endDate=${endDate}`);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || '加载财务数据失败');
        }
        
        // 更新统计卡片
        updateStatisticsCards(data);
        
        // 更新图表
        updateCharts(data);
    } catch (error) {
        console.error('加载财务数据失败:', error);
        showToast('错误', error.message);
    }
}

// 更新统计卡片
function updateStatisticsCards(data) {
    document.getElementById('totalRevenue').textContent = formatCurrency(data.totalRevenue);
    document.getElementById('totalExpense').textContent = formatCurrency(data.totalExpense);
    document.getElementById('netProfit').textContent = formatCurrency(data.netProfit);
    document.getElementById('orderCount').textContent = data.orderCount;
    
    // 更新增长率
    updateGrowthRate('revenueGrowth', data.revenueGrowth);
    updateGrowthRate('expenseGrowth', data.expenseGrowth);
    updateGrowthRate('profitGrowth', data.profitGrowth);
    updateGrowthRate('orderGrowth', data.orderGrowth);
}

// 更新增长率显示
function updateGrowthRate(elementId, growth) {
    const element = document.getElementById(elementId);
    const value = growth > 0 ? `+${growth}%` : `${growth}%`;
    element.textContent = value;
    element.className = growth >= 0 ? 'growth-positive' : 'growth-negative';
}

// 更新图表
function updateCharts(data) {
    // 更新收入趋势图
    revenueChart.data.labels = data.timeline;
    revenueChart.data.datasets[0].data = data.revenueData;
    revenueChart.data.datasets[1].data = data.expenseData;
    revenueChart.update();
    
    // 更新收入构成饼图
    revenuePieChart.data.labels = data.revenueComposition.map(item => item.name);
    revenuePieChart.data.datasets[0].data = data.revenueComposition.map(item => item.value);
    revenuePieChart.update();
}

// 加载交易记录
async function loadTransactions() {
    try {
        const dateRange = $('#dateRange').val().split(' 至 ');
        const startDate = dateRange[0];
        const endDate = dateRange[1];
        
        const response = await fetch(`/api/finance/transactions?page=${currentPage}&pageSize=${pageSize}&startDate=${startDate}&endDate=${endDate}`);
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || '加载交易记录失败');
        }
        
        renderTransactionsTable(data.transactions);
        renderPagination(data.total);
    } catch (error) {
        console.error('加载交易记录失败:', error);
        showToast('错误', error.message);
    }
}

// 渲染交易记录表格
function renderTransactionsTable(transactions) {
    const tbody = document.getElementById('transactionTableBody');
    tbody.innerHTML = '';
    
    transactions.forEach(transaction => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td><span class="transaction-id">${transaction.orderId}</span></td>
            <td>${transaction.user.username}</td>
            <td><span class="transaction-type type-${transaction.type}">${getTransactionType(transaction.type)}</span></td>
            <td><span class="transaction-amount amount-${transaction.type === 'income' ? 'positive' : 'negative'}">
                ${formatCurrency(transaction.amount)}
            </span></td>
            <td><span class="status-badge status-${transaction.status}">${getStatusText(transaction.status)}</span></td>
            <td>${formatDate(transaction.createdAt)}</td>
            <td>
                <button class="btn btn-sm btn-outline-info" onclick="viewTransactionDetails(${transaction.id})">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
        tbody.appendChild(tr);
    });
}

// 渲染分页
function renderPagination(total) {
    totalPages = Math.ceil(total / pageSize);
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';
    
    // 上一页按钮
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `
        <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
            <i class="bi bi-chevron-left"></i>
        </a>
    `;
    pagination.appendChild(prevLi);
    
    // 页码按钮
    for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${currentPage === i ? 'active' : ''}`;
        li.innerHTML = `
            <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
        `;
        pagination.appendChild(li);
    }
    
    // 下一页按钮
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `
        <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
            <i class="bi bi-chevron-right"></i>
        </a>
    `;
    pagination.appendChild(nextLi);
}

// 切换页码
function changePage(page) {
    if (page < 1 || page > totalPages) return;
    currentPage = page;
    loadTransactions();
}

// 查看交易详情
async function viewTransactionDetails(transactionId) {
    try {
        const response = await fetch(`/api/finance/transactions/${transactionId}`);
        const transaction = await response.json();
        
        if (!response.ok) {
            throw new Error(transaction.message || '加载交易详情失败');
        }
        
        // 填充基本信息
        document.getElementById('detailOrderId').textContent = transaction.orderId;
        document.getElementById('detailUser').textContent = transaction.user.username;
        document.getElementById('detailAmount').textContent = formatCurrency(transaction.amount);
        document.getElementById('detailStatus').textContent = getStatusText(transaction.status);
        
        // 填充支付信息
        document.getElementById('detailPaymentMethod').textContent = getPaymentMethod(transaction.paymentMethod);
        document.getElementById('detailPaymentTime').textContent = formatDate(transaction.paymentTime);
        document.getElementById('detailTransactionId').textContent = transaction.transactionId;
        
        // 渲染订单明细
        renderOrderItems(transaction.items);
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById('transactionModal'));
        modal.show();
    } catch (error) {
        console.error('加载交易详情失败:', error);
        showToast('错误', error.message);
    }
}

// 渲染订单明细
function renderOrderItems(items) {
    const tbody = document.getElementById('detailItems');
    tbody.innerHTML = '';
    
    items.forEach(item => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${item.name}</td>
            <td>${item.quantity}</td>
            <td>${formatCurrency(item.price)}</td>
            <td>${formatCurrency(item.price * item.quantity)}</td>
        `;
        tbody.appendChild(tr);
    });
}

// 导出报表
async function exportReport(format) {
    try {
        const dateRange = $('#dateRange').val().split(' 至 ');
        const startDate = dateRange[0];
        const endDate = dateRange[1];
        
        const response = await fetch(`/api/finance/export?format=${format}&startDate=${startDate}&endDate=${endDate}`);
        const blob = await response.blob();
        
        if (!response.ok) {
            throw new Error('导出报表失败');
        }
        
        // 下载文件
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `财务报表_${startDate}_${endDate}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    } catch (error) {
        console.error('导出报表失败:', error);
        showToast('错误', error.message);
    }
}

// 绑定事件处理器
function bindEventHandlers() {
    // 导出按钮点击事件
    document.getElementById('exportBtn').addEventListener('click', () => {
        const options = document.createElement('div');
        options.className = 'export-options';
        options.innerHTML = `
            <a class="dropdown-item" href="#" onclick="exportReport('excel')">
                <i class="bi bi-file-excel"></i> 导出为Excel
            </a>
            <a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                <i class="bi bi-file-pdf"></i> 导出为PDF
            </a>
        `;
        
        const exportBtn = document.getElementById('exportBtn');
        exportBtn.parentNode.appendChild(options);
        options.classList.add('show');
        
        // 点击其他地方关闭下拉菜单
        document.addEventListener('click', function closeOptions(e) {
            if (!options.contains(e.target) && !exportBtn.contains(e.target)) {
                options.classList.remove('show');
                document.removeEventListener('click', closeOptions);
                setTimeout(() => options.remove(), 300);
            }
        });
    });
}

// 格式化货币
function formatCurrency(amount) {
    return `¥${amount.toFixed(2)}`;
}

// 格式化日期
function formatDate(dateString) {
    return new Date(dateString).toLocaleString('zh-CN');
}

// 获取交易类型文本
function getTransactionType(type) {
    const typeMap = {
        income: '收入',
        expense: '支出'
    };
    return typeMap[type] || type;
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        success: '成功',
        pending: '处理中',
        failed: '失败'
    };
    return statusMap[status] || status;
}

// 获取支付方式文本
function getPaymentMethod(method) {
    const methodMap = {
        alipay: '支付宝',
        wechat: '微信支付',
        balance: '余额支付'
    };
    return methodMap[method] || method;
}

// 显示提示消息
function showToast(title, message) {
    // 这里可以使用你喜欢的提示框组件
    alert(`${title}: ${message}`);
}

// 加载退款记录
async function loadRefunds() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/finance/refunds', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                renderRefundsTable(data.data);
                updateRefundStats(data.stats);
            }
        }
    } catch (error) {
        console.error('加载退款记录失败:', error);
        // 使用模拟数据
        renderRefundsTable([
            {
                _id: 'refund-001',
                refundId: 'REF-2024-001',
                orderId: 'ORD-2024-001',
                userId: 'user-001',
                amount: 299,
                reason: '服务不满意',
                status: 'pending',
                createdAt: new Date()
            }
        ]);
        updateRefundStats({
            pendingRefunds: 5,
            totalRefundAmount: 1250,
            refundRate: 3.2,
            avgRefundTime: 2
        });
    }
}

// 渲染退款表格
function renderRefundsTable(refunds) {
    const tbody = document.getElementById('refundTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    refunds.forEach(refund => {
        const statusBadge = getRefundStatusBadge(refund.status);
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${refund.refundId}</td>
            <td>${refund.orderId}</td>
            <td>${refund.userId}</td>
            <td>¥${refund.amount.toFixed(2)}</td>
            <td>${refund.reason}</td>
            <td>${statusBadge}</td>
            <td>${formatDateTime(refund.createdAt)}</td>
            <td>
                ${refund.status === 'pending' ? `
                    <button class="btn btn-sm btn-success me-1" onclick="processRefund('${refund._id}', 'approve')">
                        <i class="bi bi-check"></i> 批准
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="processRefund('${refund._id}', 'reject')">
                        <i class="bi bi-x"></i> 拒绝
                    </button>
                ` : '-'}
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 获取退款状态徽章
function getRefundStatusBadge(status) {
    const badges = {
        'pending': '<span class="badge bg-warning">待处理</span>',
        'approved': '<span class="badge bg-info">已批准</span>',
        'rejected': '<span class="badge bg-danger">已拒绝</span>',
        'completed': '<span class="badge bg-success">已完成</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">未知</span>';
}

// 更新退款统计
function updateRefundStats(stats) {
    const elements = {
        'pendingRefunds': stats.pendingRefunds,
        'totalRefundAmount': '¥' + stats.totalRefundAmount.toLocaleString(),
        'refundRate': stats.refundRate + '%',
        'avgRefundTime': stats.avgRefundTime + '天'
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

// 处理退款申请
async function processRefund(refundId, action) {
    try {
        const reason = action === 'reject' ? prompt('请输入拒绝原因:') : '';
        if (action === 'reject' && !reason) return;

        const token = localStorage.getItem('token');
        const response = await fetch(`/api/finance/refunds/${refundId}/process`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ action, reason })
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                showToast('成功', data.message);
                loadRefunds(); // 重新加载退款列表
            } else {
                showToast('错误', data.message);
            }
        }
    } catch (error) {
        console.error('处理退款申请失败:', error);
        showToast('错误', '处理退款申请失败');
    }
}

// 加载消费分析数据
async function loadConsumptionAnalysis() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/finance/consumption-analysis', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                updateConsumptionMetrics(data.data);
            }
        }
    } catch (error) {
        console.error('加载消费分析失败:', error);
        // 使用模拟数据
        updateConsumptionMetrics({
            avgOrderValue: 245,
            customerLifetimeValue: 1250,
            repeatPurchaseRate: 35.8,
            churnRate: 15.2
        });
    }
}

// 更新消费指标
function updateConsumptionMetrics(data) {
    const elements = {
        'avgOrderValue': '¥' + data.avgOrderValue,
        'customerLifetimeValue': '¥' + data.customerLifetimeValue,
        'repeatPurchaseRate': data.repeatPurchaseRate + '%',
        'churnRate': data.churnRate + '%'
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

// 初始化分析图表
function initAnalyticsCharts() {
    // 检查是否已加载ECharts
    if (typeof echarts === 'undefined') {
        console.warn('ECharts未加载，跳过图表初始化');
        return;
    }

    // 延迟初始化图表，确保DOM元素已渲染
    setTimeout(() => {
        initConsumptionHabitsChart();
        initProductRevenueChart();
        initRegionConsumptionChart();
        initConversionFunnelChart();
    }, 100);
}

// 初始化用户消费习惯图表
function initConsumptionHabitsChart() {
    const chartElement = document.getElementById('consumptionHabitsChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c}% ({d}%)'
        },
        series: [{
            name: '消费习惯',
            type: 'pie',
            radius: '60%',
            data: [
                { value: 65, name: '订阅用户', itemStyle: { color: '#1890ff' } },
                { value: 25, name: '一次性购买', itemStyle: { color: '#52c41a' } },
                { value: 10, name: '升级用户', itemStyle: { color: '#faad14' } }
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}

// 初始化产品收入占比图表
function initProductRevenueChart() {
    const chartElement = document.getElementById('productRevenueChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'item'
        },
        series: [{
            name: '产品收入',
            type: 'pie',
            radius: ['40%', '70%'],
            data: [
                { value: 45, name: '基础版', itemStyle: { color: '#52c41a' } },
                { value: 35, name: '专业版', itemStyle: { color: '#1890ff' } },
                { value: 20, name: '企业版', itemStyle: { color: '#faad14' } }
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}

// 初始化地域消费分布图表
function initRegionConsumptionChart() {
    const chartElement = document.getElementById('regionConsumptionChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'value'
        },
        yAxis: {
            type: 'category',
            data: ['华东', '华北', '华南', '西南', '其他']
        },
        series: [{
            name: '消费金额',
            type: 'bar',
            data: [35000, 25000, 20000, 12000, 8000],
            itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                    { offset: 0, color: '#1890ff' },
                    { offset: 1, color: '#52c41a' }
                ])
            }
        }]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}

// 初始化付费转化漏斗图表
function initConversionFunnelChart() {
    const chartElement = document.getElementById('conversionFunnelChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c}'
        },
        series: [{
            name: '转化漏斗',
            type: 'funnel',
            left: '10%',
            top: 60,
            bottom: 60,
            width: '80%',
            min: 0,
            max: 100,
            minSize: '0%',
            maxSize: '100%',
            sort: 'descending',
            gap: 2,
            label: {
                show: true,
                position: 'inside'
            },
            labelLine: {
                length: 10,
                lineStyle: {
                    width: 1,
                    type: 'solid'
                }
            },
            itemStyle: {
                borderColor: '#fff',
                borderWidth: 1
            },
            emphasis: {
                label: {
                    fontSize: 20
                }
            },
            data: [
                { value: 100, name: '访问用户' },
                { value: 30, name: '注册用户' },
                { value: 12, name: '试用用户' },
                { value: 4.8, name: '付费用户' }
            ]
        }]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}