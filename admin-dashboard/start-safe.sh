#!/bin/bash

# 安全启动脚本 - 不影响您原有的前端页面
echo "🛡️  安全启动 WriterPro 管理后台"
echo "========================================"
echo "✅ 保护您原有的前端页面不受影响"
echo

# 进入正确目录
if [ ! -f "index.html" ]; then
    if [ -d "admin-dashboard" ]; then
        echo "📁 进入 admin-dashboard 目录..."
        cd admin-dashboard
    else
        echo "❌ 错误：找不到 admin-dashboard 目录"
        exit 1
    fi
fi

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 需要安装 Node.js"
    echo "请访问 https://nodejs.org 下载安装"
    exit 1
fi

echo "✅ Node.js 检查通过"

# 检查端口3000是否有服务运行
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "✅ 检测到端口3000有服务运行（您的原始前端）"
    echo "🛡️  管理后台将使用端口8080，不会影响您的前端"
else
    echo "⚠️  端口3000没有检测到服务"
    echo "💡 如果您有前端应用，请先启动它"
fi

# 停止端口8080的占用（如果有）
if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "🔄 清理端口8080..."
    lsof -ti:8080 | xargs kill -9 2>/dev/null || true
    sleep 1
fi

echo "🚀 启动管理后台到端口8080..."
echo

# 自动打开浏览器
echo "🌐 3秒后自动打开管理后台..."
if command -v open &> /dev/null; then
    (sleep 3 && open "http://localhost:8080/ad") &
elif command -v xdg-open &> /dev/null; then
    (sleep 3 && xdg-open "http://localhost:8080/ad") &
fi

# 启动代理服务器
node proxy-only-ad.js
