<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专家订单管理 - WriterPro 管理后台</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="css/ui-enhancements.css">
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="bi bi-pen"></i>
                    <span>WriterPro</span>
                </div>
                <button class="menu-toggle">
                    <i class="bi bi-list"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="dashboard.html"><i class="bi bi-grid"></i><span>仪表盘</span></a></li>
                    <li><a href="users.html"><i class="bi bi-people"></i><span>用户管理</span></a></li>
                    <li class="active"><a href="workorders.html"><i class="bi bi-clipboard-check"></i><span>订单管理</span></a></li>
                    <li><a href="experts.html"><i class="bi bi-person-badge"></i><span>专家管理</span></a></li>
                    <li><a href="finance.html"><i class="bi bi-cash-stack"></i><span>财务统计</span></a></li>
                    <li><a href="settings.html"><i class="bi bi-gear"></i><span>系统设置</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-header">
                <div class="header-left">
                    <button class="menu-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <h1>专家订单管理</h1>
                </div>
                
                <div class="header-right">
                    <div class="user-menu">
                        <button class="user-btn">
                            <i class="bi bi-person-circle"></i>
                            <span>管理员</span>
                            <i class="bi bi-chevron-down"></i>
                        </button>
                        <div class="dropdown-menu">
                            <a href="#" class="dropdown-item">
                                <i class="bi bi-person"></i>
                                <span>个人资料</span>
                            </a>
                            <a href="#" class="dropdown-item">
                                <i class="bi bi-gear"></i>
                                <span>设置</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="index.html" class="dropdown-item">
                                <i class="bi bi-box-arrow-right"></i>
                                <span>退出登录</span>
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 筛选和搜索 -->
                <div class="content-header">
                    <div class="row">
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter">
                                <option value="">全部状态</option>
                                <option value="pending">待分配</option>
                                <option value="assigned">已分配</option>
                                <option value="in_progress">进行中</option>
                                <option value="review">审核中</option>
                                <option value="completed">已完成</option>
                                <option value="cancelled">已取消</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="serviceTypeFilter">
                                <option value="">全部服务</option>
                                <option value="ai_optimization">AI优化</option>
                                <option value="expert_optimization">专家优化</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="priorityFilter">
                                <option value="">全部优先级</option>
                                <option value="low">低</option>
                                <option value="normal">普通</option>
                                <option value="high">高</option>
                                <option value="urgent">紧急</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索工单...">
                                <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-clipboard-check"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalWorkOrders">0</h3>
                                <p>总工单数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-clock"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="pendingWorkOrders">0</h3>
                                <p>待处理</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-gear"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="inProgressWorkOrders">0</h3>
                                <p>进行中</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="completedWorkOrders">0</h3>
                                <p>已完成</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 工单列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">专家订单列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>订单ID</th>
                                        <th>文档描述</th>
                                        <th>客户</th>
                                        <th>服务类型</th>
                                        <th>状态</th>
                                        <th>优先级</th>
                                        <th>专家</th>
                                        <th>金额</th>
                                        <th>创建时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="workOrdersTable">
                                    <tr>
                                        <td colspan="10" class="text-center">
                                            <div class="spinner-border" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <nav aria-label="订单分页">
                            <ul class="pagination justify-content-center" id="pagination">
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 工单详情模态框 -->
    <div class="modal fade" id="workOrderModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">工单详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="workOrderDetails">
                    <!-- 工单详情内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="assignExpertBtn">分配专家</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/workorders.js"></script>
</body>
</html>
