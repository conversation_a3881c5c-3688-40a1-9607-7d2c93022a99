<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>备份恢复 - WriterPro管理后台</title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/ui-enhancements.css">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- 引入Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .backup-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background-color: #ffffff;
            transition: all 0.2s ease;
        }
        .backup-card:hover {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-color: #0d6efd;
        }
        .backup-card.success {
            border-left: 4px solid #198754;
        }
        .backup-card.failed {
            border-left: 4px solid #dc3545;
        }
        .backup-card.running {
            border-left: 4px solid #0dcaf0;
        }
        .backup-progress {
            height: 0.5rem;
            border-radius: 0.25rem;
            margin-top: 0.5rem;
        }
        .storage-usage {
            background: linear-gradient(90deg, #198754 0%, #ffc107 70%, #dc3545 90%);
            height: 1rem;
            border-radius: 0.5rem;
            position: relative;
        }
        .storage-indicator {
            position: absolute;
            top: 0;
            height: 100%;
            width: 2px;
            background-color: #000;
            border-radius: 1px;
        }
        .backup-type-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-right: 1rem;
        }
        .backup-type-icon.full {
            background-color: #0d6efd;
        }
        .backup-type-icon.incremental {
            background-color: #198754;
        }
        .backup-type-icon.differential {
            background-color: #ffc107;
            color: #000;
        }
        .schedule-item {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 0.75rem;
            background-color: #f8f9fa;
        }
        .schedule-item.active {
            border-color: #198754;
            background-color: #f8fff9;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="logo">
                <h1>WriterPro</h1>
                <span>管理后台</span>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li><a href="dashboard.html"><i class="bi bi-speedometer2"></i><span data-i18n="common.dashboard">仪表盘</span></a></li>
                    <li><a href="users.html"><i class="bi bi-people"></i><span data-i18n="common.users">用户管理</span></a></li>
                    <li><a href="agents.html"><i class="bi bi-person-badge"></i><span data-i18n="common.agents">代理商管理</span></a></li>
                    <li><a href="roles.html"><i class="bi bi-shield-check"></i><span data-i18n="common.roles">角色管理</span></a></li>
                    <li><a href="finance.html"><i class="bi bi-currency-dollar"></i><span data-i18n="common.finance">财务管理</span></a></li>
                    <li><a href="settings.html"><i class="bi bi-gear"></i><span data-i18n="common.settings">系统设置</span></a></li>
                    <li><a href="content.html"><i class="bi bi-file-text"></i><span data-i18n="common.content">内容管理</span></a></li>
                    <li><a href="api.html"><i class="bi bi-code-slash"></i><span data-i18n="common.api">API配置</span></a></li>
                    <li><a href="marketing.html"><i class="bi bi-megaphone"></i><span data-i18n="common.marketing">营销工具</span></a></li>
                    <li><a href="security.html"><i class="bi bi-shield-lock"></i><span data-i18n="common.security">安全中心</span></a></li>
                    <li><a href="monitor.html"><i class="bi bi-activity"></i><span data-i18n="common.monitor">系统监控</span></a></li>
                    <li><a href="analytics.html"><i class="bi bi-graph-up"></i><span>数据分析</span></a></li>
                    <li><a href="reports.html"><i class="bi bi-file-earmark-text"></i><span>报表中心</span></a></li>
                    <li><a href="logs.html"><i class="bi bi-journal-text"></i><span>系统日志</span></a></li>
                    <li><a href="tasks.html"><i class="bi bi-list-task"></i><span>任务管理</span></a></li>
                    <li><a href="notifications.html"><i class="bi bi-bell"></i><span>通知中心</span></a></li>
                    <li class="active"><a href="backup.html"><i class="bi bi-shield-check"></i><span>备份恢复</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 头部 -->
            <header class="main-header">
                <div class="header-left">
                    <button class="menu-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <div class="header-title">
                        <h2>备份恢复</h2>
                        <p>数据备份和恢复管理</p>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-actions">
                        <button class="action-btn theme-toggle" id="themeToggle" title="切换主题">
                            <i class="bi bi-moon"></i>
                        </button>
                        
                        <button class="action-btn">
                            <i class="bi bi-bell"></i>
                            <span class="badge">5</span>
                        </button>
                        
                        <div class="user-menu">
                            <button class="user-btn">
                                <div class="user-avatar">A</div>
                                <span>管理员</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <div class="user-dropdown">
                                <a href="#"><i class="bi bi-person"></i>个人资料</a>
                                <a href="#"><i class="bi bi-gear"></i>设置</a>
                                <a href="index.html"><i class="bi bi-box-arrow-right"></i>退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 备份统计 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-primary">
                                <i class="bi bi-archive"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="totalBackups">0</h3>
                                <p>总备份数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-success">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="successfulBackups">0</h3>
                                <p>成功备份</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-info">
                                <i class="bi bi-hdd"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="totalSize">0 GB</h3>
                                <p>总大小</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-warning">
                                <i class="bi bi-clock"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="lastBackup">-</h3>
                                <p>最后备份</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 存储使用情况 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">存储使用情况</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="storage-usage">
                                    <div class="storage-indicator" id="storageIndicator" style="left: 65%;"></div>
                                </div>
                                <div class="d-flex justify-content-between mt-2">
                                    <small class="text-success">0 GB</small>
                                    <small class="text-warning">70%</small>
                                    <small class="text-danger">100 GB</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-end">
                                    <h4 id="storageUsed">65 GB</h4>
                                    <p class="text-muted">已使用 / 100 GB</p>
                                    <button class="btn btn-outline-warning btn-sm" id="cleanupBtn">
                                        <i class="bi bi-trash"></i> 清理旧备份
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">快速操作</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <button class="btn btn-primary w-100 mb-2" id="fullBackupBtn">
                                            <i class="bi bi-archive-fill"></i>
                                            <div>完整备份</div>
                                            <small>备份所有数据</small>
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-success w-100 mb-2" id="incrementalBackupBtn">
                                            <i class="bi bi-plus-circle"></i>
                                            <div>增量备份</div>
                                            <small>备份变更数据</small>
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-warning w-100 mb-2" id="restoreBtn">
                                            <i class="bi bi-arrow-clockwise"></i>
                                            <div>数据恢复</div>
                                            <small>从备份恢复</small>
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-info w-100 mb-2" id="scheduleBtn">
                                            <i class="bi bi-calendar-check"></i>
                                            <div>备份计划</div>
                                            <small>设置自动备份</small>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 备份列表和计划 -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">备份历史</h5>
                                <div class="d-flex gap-2">
                                    <select class="form-select form-select-sm" id="backupFilter" style="width: auto;">
                                        <option value="">全部类型</option>
                                        <option value="full">完整备份</option>
                                        <option value="incremental">增量备份</option>
                                        <option value="differential">差异备份</option>
                                    </select>
                                    <button class="btn btn-outline-secondary btn-sm" id="refreshBackupsBtn">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="backupsList" style="max-height: 500px; overflow-y: auto;">
                                    <!-- 备份列表将在这里动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">备份计划</h5>
                                <button class="btn btn-primary btn-sm" id="addScheduleBtn">
                                    <i class="bi bi-plus"></i> 添加
                                </button>
                            </div>
                            <div class="card-body">
                                <div id="schedulesList">
                                    <!-- 备份计划将在这里动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 备份配置模态框 -->
    <div class="modal fade" id="backupModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="backupModalTitle">创建备份</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="backupForm">
                        <div class="mb-3">
                            <label class="form-label">备份名称</label>
                            <input type="text" class="form-control" id="backupName" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">备份类型</label>
                            <select class="form-select" id="backupType" required>
                                <option value="full">完整备份</option>
                                <option value="incremental">增量备份</option>
                                <option value="differential">差异备份</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">备份内容</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="backupDatabase" checked>
                                <label class="form-check-label" for="backupDatabase">数据库</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="backupFiles" checked>
                                <label class="form-check-label" for="backupFiles">文件系统</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="backupConfig">
                                <label class="form-check-label" for="backupConfig">配置文件</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="backupLogs">
                                <label class="form-check-label" for="backupLogs">日志文件</label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">压缩级别</label>
                            <select class="form-select" id="compressionLevel">
                                <option value="none">无压缩</option>
                                <option value="low">低压缩</option>
                                <option value="medium" selected>中等压缩</option>
                                <option value="high">高压缩</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">备份描述</label>
                            <textarea class="form-control" id="backupDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="startBackupBtn">开始备份</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 恢复模态框 -->
    <div class="modal fade" id="restoreModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">数据恢复</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>警告：</strong>数据恢复将覆盖现有数据，请确保已做好准备。
                    </div>
                    
                    <form id="restoreForm">
                        <div class="mb-3">
                            <label class="form-label">选择备份</label>
                            <select class="form-select" id="restoreBackup" required>
                                <option value="">请选择要恢复的备份</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">恢复选项</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="restoreDatabase" checked>
                                <label class="form-check-label" for="restoreDatabase">恢复数据库</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="restoreFiles" checked>
                                <label class="form-check-label" for="restoreFiles">恢复文件</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="restoreConfig">
                                <label class="form-check-label" for="restoreConfig">恢复配置</label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">恢复模式</label>
                            <select class="form-select" id="restoreMode">
                                <option value="full">完整恢复</option>
                                <option value="partial">部分恢复</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirmRestore" required>
                                <label class="form-check-label" for="confirmRestore">
                                    我确认要执行此恢复操作
                                </label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="startRestoreBtn">开始恢复</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 备份计划模态框 -->
    <div class="modal fade" id="scheduleModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">备份计划</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="scheduleForm">
                        <div class="mb-3">
                            <label class="form-label">计划名称</label>
                            <input type="text" class="form-control" id="scheduleName" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">备份类型</label>
                            <select class="form-select" id="scheduleBackupType">
                                <option value="full">完整备份</option>
                                <option value="incremental">增量备份</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">执行频率</label>
                            <select class="form-select" id="scheduleFrequency">
                                <option value="daily">每日</option>
                                <option value="weekly">每周</option>
                                <option value="monthly">每月</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">执行时间</label>
                            <input type="time" class="form-control" id="scheduleTime" value="02:00">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">保留天数</label>
                            <input type="number" class="form-control" id="retentionDays" value="30" min="1">
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="scheduleEnabled" checked>
                                <label class="form-check-label" for="scheduleEnabled">启用此计划</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveScheduleBtn">保存计划</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/bilingual-init.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/language-switcher.js"></script>
    <script src="js/mobile-nav.js"></script>
    <script src="js/ui-enhancements.js"></script>
    <script src="js/backup.js"></script>
</body>
</html>
