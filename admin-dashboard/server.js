const express = require('express');
const path = require('path');
const session = require('express-session');
const MongoStore = require('connect-mongo');
const mongoose = require('mongoose');
const dotenv = require('dotenv');

// 加载环境变量
dotenv.config();

// 导入数据库连接
const { connectDB, isUsingMemoryStore } = require('./config/db');
const dashboardController = require('./controllers/dashboardController');
const fileController = require('../backend/src/controllers/fileController');
const reportsController = require('./controllers/reportsController');
const emailService = require('../backend/src/services/emailService');

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 8080;

// 连接数据库
connectDB().then(() => {
    console.log('数据库连接已初始化');
    
    // 配置会话存储
    let sessionConfig = {
        secret: process.env.SESSION_SECRET || 'writerpro_secret_key',
        resave: false,
        saveUninitialized: false,
        cookie: {
            secure: process.env.NODE_ENV === 'production',
            maxAge: 14 * 24 * 60 * 60 * 1000 // 14天
        }
    };
    
    // 如果不是使用内存存储，则使用MongoDB存储会话
    if (!isUsingMemoryStore()) {
        sessionConfig.store = MongoStore.create({
            mongoUrl: process.env.MONGODB_URI || 'mongodb://localhost:27017/writerpro_admin',
            ttl: 14 * 24 * 60 * 60 // 14天
        });
    }
    
    // 配置中间件
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    app.use(session(sessionConfig));
    
    // 提供静态文件
    app.use(express.static(__dirname));
    
    // API路由
    app.use('/api', require('./routes/api'));

    // 仪表盘API路由
    app.get('/ad/api/dashboard/stats', dashboardController.getStats);
    app.get('/ad/api/dashboard/users', dashboardController.getUsers);
    app.get('/ad/api/dashboard/orders', dashboardController.getOrders);
    app.get('/ad/api/dashboard/payments', dashboardController.getPayments);

    // 工单管理API路由
    app.get('/ad/api/dashboard/workorders', dashboardController.getWorkOrders);
    app.get('/ad/api/dashboard/experts', dashboardController.getExperts);

    // 文件上传API路由
    app.post('/ad/api/workorders/:workOrderId/attachments',
        fileController.getUploadMiddleware('file'),
        fileController.uploadWorkOrderAttachment
    );
    app.post('/ad/api/workorders/:workOrderId/attachments/multiple',
        fileController.getMultipleUploadMiddleware('files', 5),
        fileController.uploadMultipleFiles
    );
    app.get('/ad/api/attachments/:id/download', fileController.downloadAttachment);
    app.delete('/ad/api/attachments/:id', fileController.deleteAttachment);
    app.get('/ad/api/workorders/:workOrderId/attachments', fileController.getWorkOrderAttachments);

    // 报表API路由
    app.get('/ad/api/reports/business', reportsController.getBusinessOverview);
    app.get('/ad/api/reports/financial', reportsController.getFinancialReport);
    app.get('/ad/api/reports/user', reportsController.getUserBehaviorReport);
    app.get('/ad/api/reports/expert', reportsController.getExpertPerformanceReport);
    app.get('/ad/api/reports/export', reportsController.exportReport);

    // 邮件通知API路由
    app.post('/ad/api/notifications/email', async (req, res) => {
        try {
            const { to, subject, message, type = 'info' } = req.body;
            const result = await emailService.sendSystemNotification(to, subject, message, type);
            res.json(result);
        } catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    });
    
    // 页面路由
    app.get('/', (req, res) => {
        res.sendFile(path.join(__dirname, 'index.html'));
    });
    
    app.get('/dashboard', (req, res) => {
        res.sendFile(path.join(__dirname, 'dashboard.html'));
    });
    
    app.get('/security', (req, res) => {
        res.sendFile(path.join(__dirname, 'security.html'));
    });
    
    app.get('/content', (req, res) => {
        res.sendFile(path.join(__dirname, 'content.html'));
    });
    
    app.get('/api-config', (req, res) => {
        res.sendFile(path.join(__dirname, 'api.html'));
    });
    
    app.get('/marketing', (req, res) => {
        res.sendFile(path.join(__dirname, 'marketing.html'));
    });
    
    app.get('/users', (req, res) => {
        res.sendFile(path.join(__dirname, 'users.html'));
    });
    
    app.get('/agents', (req, res) => {
        res.sendFile(path.join(__dirname, 'agents.html'));
    });
    
    app.get('/roles', (req, res) => {
        res.sendFile(path.join(__dirname, 'roles.html'));
    });
    
    app.get('/finance', (req, res) => {
        res.sendFile(path.join(__dirname, 'finance.html'));
    });
    
    app.get('/monitor', (req, res) => {
        res.sendFile(path.join(__dirname, 'monitor.html'));
    });
    
    app.get('/settings', (req, res) => {
        res.sendFile(path.join(__dirname, 'settings.html'));
    });

    app.get('/login-logs', (req, res) => {
        res.sendFile(path.join(__dirname, 'login-logs.html'));
    });

    // 捕获所有其他请求并返回到主页
    app.get('*', (req, res) => {
        res.redirect('/');
    });
    
    // 启动服务器
    app.listen(PORT, () => {
        console.log(`管理后台服务器运行在端口 ${PORT}`);
        console.log(`访问 http://localhost:${PORT} 查看管理后台`);
        console.log(`使用 ${isUsingMemoryStore() ? '内存数据存储' : 'MongoDB数据库'}`);
    });
}); 