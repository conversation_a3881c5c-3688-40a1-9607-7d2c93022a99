<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>通知中心 - WriterPro管理后台</title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/ui-enhancements.css">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- 引入Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .notification-item {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 0.75rem;
            background-color: #ffffff;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .notification-item:hover {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-color: #0d6efd;
        }
        .notification-item.unread {
            border-left: 4px solid #0d6efd;
            background-color: #f8f9ff;
        }
        .notification-item.read {
            opacity: 0.8;
        }
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }
        .notification-icon.info {
            background-color: #0dcaf0;
        }
        .notification-icon.success {
            background-color: #198754;
        }
        .notification-icon.warning {
            background-color: #ffc107;
            color: #000;
        }
        .notification-icon.error {
            background-color: #dc3545;
        }
        .notification-icon.system {
            background-color: #6c757d;
        }
        .notification-time {
            font-size: 0.875rem;
            color: #6c757d;
        }
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background-color: #dc3545;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .notification-settings-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        .template-editor {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            background-color: #f8f9fa;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="logo">
                <h1>WriterPro</h1>
                <span>管理后台</span>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li><a href="dashboard.html"><i class="bi bi-speedometer2"></i><span data-i18n="common.dashboard">仪表盘</span></a></li>
                    <li><a href="users.html"><i class="bi bi-people"></i><span data-i18n="common.users">用户管理</span></a></li>
                    <li><a href="agents.html"><i class="bi bi-person-badge"></i><span data-i18n="common.agents">代理商管理</span></a></li>
                    <li><a href="roles.html"><i class="bi bi-shield-check"></i><span data-i18n="common.roles">角色管理</span></a></li>
                    <li><a href="finance.html"><i class="bi bi-currency-dollar"></i><span data-i18n="common.finance">财务管理</span></a></li>
                    <li><a href="settings.html"><i class="bi bi-gear"></i><span data-i18n="common.settings">系统设置</span></a></li>
                    <li><a href="content.html"><i class="bi bi-file-text"></i><span data-i18n="common.content">内容管理</span></a></li>
                    <li><a href="api.html"><i class="bi bi-code-slash"></i><span data-i18n="common.api">API配置</span></a></li>
                    <li><a href="marketing.html"><i class="bi bi-megaphone"></i><span data-i18n="common.marketing">营销工具</span></a></li>
                    <li><a href="security.html"><i class="bi bi-shield-lock"></i><span data-i18n="common.security">安全中心</span></a></li>
                    <li><a href="monitor.html"><i class="bi bi-activity"></i><span data-i18n="common.monitor">系统监控</span></a></li>
                    <li><a href="analytics.html"><i class="bi bi-graph-up"></i><span>数据分析</span></a></li>
                    <li><a href="reports.html"><i class="bi bi-file-earmark-text"></i><span>报表中心</span></a></li>
                    <li><a href="logs.html"><i class="bi bi-journal-text"></i><span>系统日志</span></a></li>
                    <li><a href="tasks.html"><i class="bi bi-list-task"></i><span>任务管理</span></a></li>
                    <li class="active"><a href="notifications.html"><i class="bi bi-bell"></i><span>通知中心</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 头部 -->
            <header class="main-header">
                <div class="header-left">
                    <button class="menu-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <div class="header-title">
                        <h2>通知中心</h2>
                        <p>系统通知和消息推送管理</p>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-actions">
                        <button class="action-btn theme-toggle" id="themeToggle" title="切换主题">
                            <i class="bi bi-moon"></i>
                        </button>
                        
                        <button class="action-btn position-relative">
                            <i class="bi bi-bell"></i>
                            <span class="notification-badge" id="notificationBadge">5</span>
                        </button>
                        
                        <div class="user-menu">
                            <button class="user-btn">
                                <div class="user-avatar">A</div>
                                <span>管理员</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <div class="user-dropdown">
                                <a href="#"><i class="bi bi-person"></i>个人资料</a>
                                <a href="#"><i class="bi bi-gear"></i>设置</a>
                                <a href="index.html"><i class="bi bi-box-arrow-right"></i>退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 通知统计 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-primary">
                                <i class="bi bi-bell"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="totalNotifications">0</h3>
                                <p>总通知数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-info">
                                <i class="bi bi-envelope"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="unreadNotifications">0</h3>
                                <p>未读通知</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-success">
                                <i class="bi bi-send"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="sentToday">0</h3>
                                <p>今日发送</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="pendingNotifications">0</h3>
                                <p>待发送</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 通知控制面板 -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">通知列表</h5>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary" id="createNotificationBtn">
                                        <i class="bi bi-plus"></i> 创建通知
                                    </button>
                                    <button class="btn btn-outline-secondary" id="markAllReadBtn">
                                        <i class="bi bi-check-all"></i> 全部已读
                                    </button>
                                    <button class="btn btn-outline-danger" id="clearAllBtn">
                                        <i class="bi bi-trash"></i> 清空
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- 过滤器 -->
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <select class="form-select" id="typeFilter">
                                            <option value="">全部类型</option>
                                            <option value="info">信息</option>
                                            <option value="success">成功</option>
                                            <option value="warning">警告</option>
                                            <option value="error">错误</option>
                                            <option value="system">系统</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="statusFilter">
                                            <option value="">全部状态</option>
                                            <option value="unread">未读</option>
                                            <option value="read">已读</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" id="searchInput" placeholder="搜索通知内容...">
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-primary w-100" id="searchBtn">
                                            <i class="bi bi-search"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- 通知列表 -->
                                <div id="notificationsList" style="max-height: 600px; overflow-y: auto;">
                                    <!-- 通知项将在这里动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">通知设置</h5>
                            </div>
                            <div class="card-body">
                                <div class="notification-settings-card">
                                    <h6>推送设置</h6>
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="enablePush" checked>
                                        <label class="form-check-label" for="enablePush">启用推送通知</label>
                                    </div>
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="enableEmail" checked>
                                        <label class="form-check-label" for="enableEmail">启用邮件通知</label>
                                    </div>
                                    <div class="form-check form-switch mb-2">
                                        <input class="form-check-input" type="checkbox" id="enableSms">
                                        <label class="form-check-label" for="enableSms">启用短信通知</label>
                                    </div>
                                </div>
                                
                                <div class="notification-settings-card">
                                    <h6>通知频率</h6>
                                    <select class="form-select mb-2" id="notificationFrequency">
                                        <option value="realtime">实时</option>
                                        <option value="hourly">每小时</option>
                                        <option value="daily">每日</option>
                                        <option value="weekly">每周</option>
                                    </select>
                                </div>
                                
                                <div class="notification-settings-card">
                                    <h6>免打扰时间</h6>
                                    <div class="row">
                                        <div class="col-6">
                                            <label class="form-label">开始时间</label>
                                            <input type="time" class="form-control" id="quietStart" value="22:00">
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">结束时间</label>
                                            <input type="time" class="form-control" id="quietEnd" value="08:00">
                                        </div>
                                    </div>
                                </div>
                                
                                <button class="btn btn-primary w-100" id="saveSettingsBtn">保存设置</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 创建通知模态框 -->
    <div class="modal fade" id="notificationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建通知</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="notificationForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">通知类型</label>
                                <select class="form-select" id="notificationType" required>
                                    <option value="">请选择</option>
                                    <option value="info">信息</option>
                                    <option value="success">成功</option>
                                    <option value="warning">警告</option>
                                    <option value="error">错误</option>
                                    <option value="system">系统</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">优先级</label>
                                <select class="form-select" id="notificationPriority">
                                    <option value="low">低</option>
                                    <option value="normal" selected>普通</option>
                                    <option value="high">高</option>
                                    <option value="urgent">紧急</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <label class="form-label">通知标题</label>
                            <input type="text" class="form-control" id="notificationTitle" required>
                        </div>
                        
                        <div class="mt-3">
                            <label class="form-label">通知内容</label>
                            <textarea class="form-control" id="notificationContent" rows="4" required></textarea>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">目标用户</label>
                                <select class="form-select" id="targetUsers">
                                    <option value="all">所有用户</option>
                                    <option value="admins">管理员</option>
                                    <option value="users">普通用户</option>
                                    <option value="agents">代理商</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">发送方式</label>
                                <div class="mt-2">
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="sendPush" checked>
                                        <label class="form-check-label" for="sendPush">推送</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="sendEmail">
                                        <label class="form-check-label" for="sendEmail">邮件</label>
                                    </div>
                                    <div class="form-check form-check-inline">
                                        <input class="form-check-input" type="checkbox" id="sendSms">
                                        <label class="form-check-label" for="sendSms">短信</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">发送时间</label>
                                <select class="form-select" id="sendTime">
                                    <option value="now">立即发送</option>
                                    <option value="scheduled">定时发送</option>
                                </select>
                            </div>
                            <div class="col-md-6" id="scheduledTimeSection" style="display: none;">
                                <label class="form-label">定时时间</label>
                                <input type="datetime-local" class="form-control" id="scheduledTime">
                            </div>
                        </div>
                        
                        <div class="mt-3" id="customUsersSection" style="display: none;">
                            <label class="form-label">自定义用户列表</label>
                            <textarea class="form-control" id="customUsersList" rows="3" placeholder="请输入用户ID，用逗号分隔"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="sendNotificationBtn">发送通知</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知详情模态框 -->
    <div class="modal fade" id="notificationDetailModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">通知详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>类型:</strong>
                            <p id="detailType"></p>
                        </div>
                        <div class="col-md-6">
                            <strong>优先级:</strong>
                            <p id="detailPriority"></p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>发送时间:</strong>
                            <p id="detailTime"></p>
                        </div>
                        <div class="col-md-6">
                            <strong>状态:</strong>
                            <p id="detailStatus"></p>
                        </div>
                    </div>
                    <div class="mt-3">
                        <strong>标题:</strong>
                        <p id="detailTitle"></p>
                    </div>
                    <div class="mt-3">
                        <strong>内容:</strong>
                        <p id="detailContent"></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="markReadBtn">标记为已读</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/bilingual-init.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/language-switcher.js"></script>
    <script src="js/mobile-nav.js"></script>
    <script src="js/ui-enhancements.js"></script>
    <script src="js/notifications.js"></script>
</body>
</html>
