<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WriterPro 管理后台</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="css/ui-enhancements.css">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <span class="logo-icon"><i class="bi bi-pen"></i></span>
                    <h1>WriterPro</h1>
                </div>
                <h2 data-i18n="login.title">管理员登录</h2>
                <p class="subtitle" data-i18n="login.subtitle">登录以访问管理功能</p>
                
                <!-- 语言切换器 -->
                <div id="language-switcher" class="language-switcher"></div>
            </div>
            
            <div class="login-form">
                <div class="form-group">
                    <label for="username" data-i18n="login.username">用户名</label>
                    <div class="input-with-icon">
                        <i class="bi bi-person"></i>
                        <input type="text" id="username" data-i18n-placeholder="login.usernamePlaceholder" placeholder="请输入管理员用户名">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password" data-i18n="login.password">密码</label>
                    <div class="input-with-icon">
                        <i class="bi bi-lock"></i>
                        <input type="password" id="password" data-i18n-placeholder="login.passwordPlaceholder" placeholder="请输入密码">
                    </div>
                </div>
                
                <div class="form-options">
                    <div class="remember-me">
                        <input type="checkbox" id="remember">
                        <label for="remember" data-i18n="login.remember">记住我</label>
                    </div>
                    <a href="#" class="forgot-password" data-i18n="login.forgotPassword">忘记密码?</a>
                </div>
                
                <button id="loginBtn" class="login-btn" data-i18n="login.login">登录</button>
                
                <div class="login-footer">
                    <p data-i18n="login.securityTip">安全提示: 请勿在公共场所登录管理后台</p>
                </div>
            </div>
        </div>
        
        <div class="login-info">
            <div class="login-info-content">
                <h2>WriterPro 管理系统</h2>
                <p data-i18n="login.infoDesc">全面的管理功能，助力业务增长</p>
                <ul class="feature-list">
                    <li><i class="bi bi-check-circle"></i> <span data-i18n="login.feature1">用户管理与数据分析</span></li>
                    <li><i class="bi bi-check-circle"></i> <span data-i18n="login.feature2">代理招商与佣金设置</span></li>
                    <li><i class="bi bi-check-circle"></i> <span data-i18n="login.feature3">财务统计与支付管理</span></li>
                    <li><i class="bi bi-check-circle"></i> <span data-i18n="login.feature4">系统设置与内容管理</span></li>
                </ul>
            </div>
        </div>
    </div>

    <div id="two-factor-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 data-i18n="login.twoFactorTitle">双因素认证</h3>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <p data-i18n="login.twoFactorDesc">请输入您手机上的验证码</p>
                <div class="otp-input-container">
                    <input type="text" class="otp-input" maxlength="1" autofocus>
                    <input type="text" class="otp-input" maxlength="1">
                    <input type="text" class="otp-input" maxlength="1">
                    <input type="text" class="otp-input" maxlength="1">
                    <input type="text" class="otp-input" maxlength="1">
                    <input type="text" class="otp-input" maxlength="1">
                </div>
                <p class="otp-timer"><span data-i18n="login.codeValidTime">验证码有效期</span>: <span id="countdown">60</span><span data-i18n="login.seconds">秒</span></p>
                <button class="verify-btn" data-i18n="login.verify">验证</button>
                <button class="resend-btn" data-i18n="login.resend">重新发送</button>
            </div>
        </div>
    </div>

    <!-- 引入i18n脚本 -->
    <script src="js/i18n.js"></script>
    
    <!-- 引入登录脚本 -->
    <script src="login.js"></script>
</body>
</html> 