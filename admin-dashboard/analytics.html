<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析 - WriterPro管理后台</title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/ui-enhancements.css">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- 引入Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="logo">
                <h1>WriterPro</h1>
                <span>管理后台</span>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li><a href="dashboard.html"><i class="bi bi-speedometer2"></i><span data-i18n="common.dashboard">仪表盘</span></a></li>
                    <li><a href="users.html"><i class="bi bi-people"></i><span data-i18n="common.users">用户管理</span></a></li>
                    <li><a href="agents.html"><i class="bi bi-person-badge"></i><span data-i18n="common.agents">代理商管理</span></a></li>
                    <li><a href="roles.html"><i class="bi bi-shield-check"></i><span data-i18n="common.roles">角色管理</span></a></li>
                    <li><a href="finance.html"><i class="bi bi-currency-dollar"></i><span data-i18n="common.finance">财务管理</span></a></li>
                    <li><a href="settings.html"><i class="bi bi-gear"></i><span data-i18n="common.settings">系统设置</span></a></li>
                    <li><a href="content.html"><i class="bi bi-file-text"></i><span data-i18n="common.content">内容管理</span></a></li>
                    <li><a href="api.html"><i class="bi bi-code-slash"></i><span data-i18n="common.api">API配置</span></a></li>
                    <li><a href="marketing.html"><i class="bi bi-megaphone"></i><span data-i18n="common.marketing">营销工具</span></a></li>
                    <li><a href="security.html"><i class="bi bi-shield-lock"></i><span data-i18n="common.security">安全中心</span></a></li>
                    <li><a href="monitor.html"><i class="bi bi-activity"></i><span data-i18n="common.monitor">系统监控</span></a></li>
                    <li class="active"><a href="analytics.html"><i class="bi bi-graph-up"></i><span>数据分析</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 头部 -->
            <header class="main-header">
                <div class="header-left">
                    <button class="menu-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <div class="header-title">
                        <h2>数据分析</h2>
                        <p>深度数据洞察和预测分析</p>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-actions">
                        <button class="action-btn theme-toggle" id="themeToggle" title="切换主题">
                            <i class="bi bi-moon"></i>
                        </button>
                        
                        <button class="action-btn">
                            <i class="bi bi-bell"></i>
                            <span class="badge">5</span>
                        </button>
                        
                        <div class="user-menu">
                            <button class="user-btn">
                                <div class="user-avatar">A</div>
                                <span>管理员</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <div class="user-dropdown">
                                <a href="#"><i class="bi bi-person"></i>个人资料</a>
                                <a href="#"><i class="bi bi-gear"></i>设置</a>
                                <a href="index.html"><i class="bi bi-box-arrow-right"></i>退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 分析控制面板 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">分析控制面板</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">时间范围</label>
                                <select class="form-select" id="periodSelect">
                                    <option value="7d">最近7天</option>
                                    <option value="30d" selected>最近30天</option>
                                    <option value="90d">最近90天</option>
                                    <option value="1y">最近1年</option>
                                    <option value="custom">自定义</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">分析类型</label>
                                <select class="form-select" id="analysisType">
                                    <option value="comprehensive">综合分析</option>
                                    <option value="user_behavior">用户行为</option>
                                    <option value="revenue">收入分析</option>
                                    <option value="agent_performance">代理商绩效</option>
                                    <option value="marketing">营销效果</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">数据粒度</label>
                                <select class="form-select" id="granularity">
                                    <option value="day">按天</option>
                                    <option value="week">按周</option>
                                    <option value="month">按月</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary" id="analyzeBtn">
                                        <i class="bi bi-play-fill"></i> 开始分析
                                    </button>
                                    <button class="btn btn-outline-secondary" id="exportBtn">
                                        <i class="bi bi-download"></i> 导出
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 关键指标卡片 -->
                <div class="row mb-4" id="metricsCards">
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-primary">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="totalUsers">-</h3>
                                <p>总用户数</p>
                                <span class="metric-change positive" id="userGrowth">+0%</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-success">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="totalRevenue">-</h3>
                                <p>总收入</p>
                                <span class="metric-change positive" id="revenueGrowth">+0%</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-warning">
                                <i class="bi bi-person-badge"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="totalAgents">-</h3>
                                <p>代理商数</p>
                                <span class="metric-change positive" id="agentGrowth">+0%</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-info">
                                <i class="bi bi-graph-up"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="conversionRate">-</h3>
                                <p>转化率</p>
                                <span class="metric-change positive" id="conversionGrowth">+0%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">趋势分析</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="trendChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">分布分析</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="distributionChart" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 预测分析 -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">预测分析</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="prediction-card">
                                            <h6>用户增长预测</h6>
                                            <div class="prediction-value">
                                                <span id="userPrediction">-</span>
                                                <small class="text-muted">下月预计</small>
                                            </div>
                                            <div class="prediction-confidence">
                                                <span>置信度: </span>
                                                <span id="userConfidence">-</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="prediction-card">
                                            <h6>收入预测</h6>
                                            <div class="prediction-value">
                                                <span id="revenuePrediction">-</span>
                                                <small class="text-muted">下月预计</small>
                                            </div>
                                            <div class="prediction-confidence">
                                                <span>置信度: </span>
                                                <span id="revenueConfidence">-</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="prediction-card">
                                            <h6>流失率预测</h6>
                                            <div class="prediction-value">
                                                <span id="churnPrediction">-</span>
                                                <small class="text-muted">预计流失率</small>
                                            </div>
                                            <div class="prediction-risk">
                                                <span>风险等级: </span>
                                                <span id="churnRisk" class="badge">-</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细分析表格 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">详细分析数据</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="analyticsTable">
                                <thead>
                                    <tr id="analyticsTableHeader">
                                        <!-- 动态生成表头 -->
                                    </tr>
                                </thead>
                                <tbody id="analyticsTableBody">
                                    <!-- 动态生成表格内容 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 自定义时间范围模态框 -->
    <div class="modal fade" id="customDateModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">自定义时间范围</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <label class="form-label">开始日期</label>
                            <input type="date" class="form-control" id="startDate">
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">结束日期</label>
                            <input type="date" class="form-control" id="endDate">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="applyCustomDate">应用</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/bilingual-init.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/language-switcher.js"></script>
    <script src="js/mobile-nav.js"></script>
    <script src="js/ui-enhancements.js"></script>
    <script src="js/analytics.js"></script>
</body>
</html>
