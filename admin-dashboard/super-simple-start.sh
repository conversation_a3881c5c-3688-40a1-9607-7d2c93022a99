#!/bin/bash

# 超级简化启动脚本 - 零依赖，一键启动
echo "🚀 WriterPro 管理后台 - 超级简化启动"
echo "========================================"

# 自动进入正确目录
if [ ! -f "index.html" ]; then
    if [ -d "admin-dashboard" ]; then
        cd admin-dashboard
    else
        echo "❌ 错误：找不到admin-dashboard目录"
        echo "请确保在正确的目录中运行此脚本"
        exit 1
    fi
fi

echo "✅ 目录检查完成"

# 停止占用端口的进程
echo "🔄 清理端口..."
lsof -ti:3000 | xargs kill -9 2>/dev/null || true
sleep 1

# 创建最简单的服务器
echo "📝 创建服务器..."
cat > temp-server.js << 'EOF'
const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
    const url = req.url;
    
    // 主页
    if (url === '/') {
        res.writeHead(200, {'Content-Type': 'text/html'});
        res.end(`
<!DOCTYPE html>
<html>
<head><title>主网站</title>
<style>
body{font-family:Arial;margin:40px;background:#f5f5f5}
.container{max-width:600px;margin:0 auto;background:white;padding:30px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1)}
.btn{display:inline-block;padding:15px 30px;background:#007bff;color:white;text-decoration:none;border-radius:5px;margin:10px 0}
.btn:hover{background:#0056b3}
.info{background:#e7f3ff;padding:15px;border-radius:5px;margin:20px 0}
</style>
</head>
<body>
<div class="container">
<h1>🎉 主网站</h1>
<p>管理后台已集成到 /ad 路径</p>
<a href="/ad" class="btn">🔧 进入管理后台</a>
<div class="info">
<h3>登录信息</h3>
<p><strong>地址:</strong> http://localhost:3000/ad</p>
<p><strong>用户名:</strong> admin</p>
<p><strong>密码:</strong> admin123</p>
<p><strong>验证码:</strong> 101010</p>
</div>
</div>
</body>
</html>
        `);
        return;
    }
    
    // 管理后台
    if (url === '/ad' || url === '/ad/') {
        fs.readFile('index.html', (err, data) => {
            if (err) {
                res.writeHead(404);
                res.end('File not found');
            } else {
                res.writeHead(200, {'Content-Type': 'text/html'});
                res.end(data);
            }
        });
        return;
    }
    
    // API - 登录
    if (url === '/ad/api/auth/login' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', () => {
            try {
                const {username, password} = JSON.parse(body);
                res.writeHead(200, {'Content-Type': 'application/json'});
                if (username === 'admin' && password === 'admin123') {
                    res.end('{"success":true,"requiresTwoFactor":true,"message":"请输入验证码"}');
                } else {
                    res.end('{"success":false,"message":"用户名或密码错误"}');
                }
            } catch(e) {
                res.writeHead(400, {'Content-Type': 'application/json'});
                res.end('{"success":false,"message":"请求错误"}');
            }
        });
        return;
    }
    
    // API - 验证码
    if (url === '/ad/api/auth/verify-2fa' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => body += chunk);
        req.on('end', () => {
            try {
                const {code} = JSON.parse(body);
                res.writeHead(200, {'Content-Type': 'application/json'});
                if (code === '101010') {
                    res.end('{"success":true,"user":{"id":"1","username":"admin","name":"管理员","role":"admin"}}');
                } else {
                    res.end('{"success":false,"message":"验证码错误"}');
                }
            } catch(e) {
                res.writeHead(400, {'Content-Type': 'application/json'});
                res.end('{"success":false,"message":"请求错误"}');
            }
        });
        return;
    }
    
    // 静态文件
    if (url.startsWith('/ad/')) {
        const filePath = url.substring(4) || 'index.html';
        const pages = {
            'dashboard': 'dashboard.html',
            'users': 'users.html', 
            'agents': 'agents.html',
            'commission': 'commission.html',
            'pricing': 'pricing.html',
            'roles': 'roles.html',
            'finance': 'finance.html',
            'analytics': 'analytics.html',
            'reports': 'reports.html',
            'settings': 'settings.html',
            'content': 'content.html',
            'api-docs': 'api-docs.html',
            'marketing': 'marketing.html',
            'security': 'security.html',
            'monitor': 'monitor.html',
            'logs': 'logs.html',
            'tasks': 'tasks.html',
            'notifications': 'notifications.html',
            'backup': 'backup.html'
        };
        
        const actualFile = pages[filePath] || filePath;
        
        fs.readFile(actualFile, (err, data) => {
            if (err) {
                res.writeHead(404);
                res.end('File not found');
            } else {
                const ext = path.extname(actualFile);
                const contentType = {
                    '.html': 'text/html',
                    '.js': 'text/javascript', 
                    '.css': 'text/css',
                    '.json': 'application/json'
                }[ext] || 'text/plain';
                
                res.writeHead(200, {'Content-Type': contentType});
                res.end(data);
            }
        });
        return;
    }
    
    // 404
    res.writeHead(404);
    res.end('Page not found');
});

server.listen(3000, () => {
    console.log('========================================');
    console.log('🚀 服务器运行在端口 3000');
    console.log('📱 主网站: http://localhost:3000');
    console.log('🔧 管理后台: http://localhost:3000/ad');
    console.log('👤 登录: admin / admin123 / 101010');
    console.log('========================================');
});
EOF

echo "✅ 服务器创建完成"

# 自动打开浏览器
echo "🌐 3秒后自动打开浏览器..."
if command -v open &> /dev/null; then
    (sleep 3 && open "http://localhost:3000/ad") &
elif command -v xdg-open &> /dev/null; then
    (sleep 3 && xdg-open "http://localhost:3000/ad") &
fi

# 启动服务器
echo "🚀 启动服务器..."
echo
node temp-server.js

# 清理临时文件
echo
echo "🧹 清理临时文件..."
rm -f temp-server.js
echo "✅ 完成"
