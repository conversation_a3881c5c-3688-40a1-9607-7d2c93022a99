#!/bin/bash

# WriterPro 管理后台启动脚本 (Linux/macOS)

echo "========================================"
echo "    WriterPro 管理后台启动脚本"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "[错误] Python未安装"
    echo "请先安装Python: https://www.python.org/downloads/"
    exit 1
fi

# 确定Python命令
if command -v python3 &> /dev/null; then
    PYTHON_CMD="python3"
else
    PYTHON_CMD="python"
fi

# 显示Python版本
echo "[信息] 检测到Python版本:"
$PYTHON_CMD --version

# 检查当前目录
echo "[信息] 当前目录: $(pwd)"
echo

# 检查关键文件是否存在
if [ ! -f "index.html" ]; then
    echo "[错误] 未找到index.html文件"
    echo "请确保在admin-dashboard目录中运行此脚本"
    exit 1
fi

echo "[信息] 文件检查通过"
echo

# 检查端口是否被占用
PORT=3000
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "[警告] 端口3000被占用，将尝试使用端口8080"
    PORT=8080
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
        echo "[警告] 端口8080也被占用，将使用随机端口"
        PORT=0
    fi
else
    echo "[信息] 端口3000可用"
fi

echo
echo "[信息] 启动HTTP服务器..."
echo "[信息] 服务器端口: $PORT"
if [ $PORT -ne 0 ]; then
    echo "[信息] 访问地址: http://localhost:$PORT"
fi
echo
echo "========================================"
echo "    服务器启动中..."
echo "    按 Ctrl+C 停止服务器"
echo "========================================"
echo

# 自动打开浏览器（可选）
if command -v open &> /dev/null; then
    # macOS
    sleep 2 && open "http://localhost:$PORT" &
elif command -v xdg-open &> /dev/null; then
    # Linux
    sleep 2 && xdg-open "http://localhost:$PORT" &
fi

# 启动服务器
if [ $PORT -eq 0 ]; then
    $PYTHON_CMD -m http.server
else
    $PYTHON_CMD -m http.server $PORT
fi

# 如果服务器停止
echo
echo "[信息] 服务器已停止"
