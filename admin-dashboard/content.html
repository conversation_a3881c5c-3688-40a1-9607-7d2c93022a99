<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>内容管理 - WriterPro 管理后台</title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="content.css">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- 引入富文本编辑器 -->
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <span class="logo-icon"><i class="bi bi-pen"></i></span>
                    <h1>WriterPro</h1>
                </div>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="bi bi-grid"></i>
                            <span>仪表盘</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="bi bi-people"></i>
                            <span>用户管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="agents.html">
                            <i class="bi bi-briefcase"></i>
                            <span>代理管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="finance.html">
                            <i class="bi bi-cash-stack"></i>
                            <span>财务统计</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="bi bi-gear"></i>
                            <span>系统设置</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="content.html">
                            <i class="bi bi-file-text"></i>
                            <span>内容管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="api.html">
                            <i class="bi bi-code-square"></i>
                            <span>API配置</span>
                        </a>
                    </li>
                    <li>
                        <a href="marketing.html">
                            <i class="bi bi-megaphone"></i>
                            <span>营销工具</span>
                        </a>
                    </li>
                    <li>
                        <a href="security.html">
                            <i class="bi bi-shield-check"></i>
                            <span>安全中心</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="header">
                <div class="header-left">
                    <button class="menu-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <h2>内容管理</h2>
                </div>
                
                <div class="header-right">
                    <div class="search-bar">
                        <i class="bi bi-search"></i>
                        <input type="text" placeholder="搜索内容...">
                    </div>
                    
                    <div class="header-actions">
                        <button class="action-btn">
                            <i class="bi bi-bell"></i>
                            <span class="badge">3</span>
                        </button>
                        
                        <div class="user-dropdown">
                            <button class="user-btn">
                                <div class="avatar">A</div>
                                <span>管理员</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            
                            <div class="dropdown-menu">
                                <a href="profile.html" class="dropdown-item">
                                    <i class="bi bi-person"></i>
                                    <span>个人资料</span>
                                </a>
                                <a href="#" class="dropdown-item">
                                    <i class="bi bi-gear"></i>
                                    <span>设置</span>
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="login.html" class="dropdown-item">
                                    <i class="bi bi-box-arrow-right"></i>
                                    <span>退出登录</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 页面内容 -->
            <div class="content">
                <div class="content-header">
                    <div class="content-title">
                        <h3>内容管理</h3>
                        <p>管理系统中的各类内容和协议</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-primary" id="createContentBtn">
                            <i class="bi bi-plus"></i>
                            <span>新建内容</span>
                        </button>
                    </div>
                </div>

                <!-- 选项卡导航 -->
                <div class="tabs">
                    <div class="tab active" data-tab="agreements">协议管理</div>
                    <div class="tab" data-tab="announcements">公告管理</div>
                    <div class="tab" data-tab="qrcodes">客服二维码</div>
                    <div class="tab" data-tab="pages">页面内容</div>
                </div>

                <!-- 选项卡内容 -->
                <div class="tab-content">
                    <!-- 协议管理 -->
                    <div class="tab-pane active" id="agreements">
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>协议名称</th>
                                                <th>最后更新</th>
                                                <th>版本</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>用户注册协议</td>
                                                <td>2025-07-05</td>
                                                <td>v2.1</td>
                                                <td><span class="status active">已启用</span></td>
                                                <td class="actions">
                                                    <button class="btn-icon" title="编辑">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn-icon" title="历史版本">
                                                        <i class="bi bi-clock-history"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>服务条款</td>
                                                <td>2025-06-20</td>
                                                <td>v1.8</td>
                                                <td><span class="status active">已启用</span></td>
                                                <td class="actions">
                                                    <button class="btn-icon" title="编辑">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn-icon" title="历史版本">
                                                        <i class="bi bi-clock-history"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>隐私政策</td>
                                                <td>2025-07-01</td>
                                                <td>v2.3</td>
                                                <td><span class="status active">已启用</span></td>
                                                <td class="actions">
                                                    <button class="btn-icon" title="编辑">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn-icon" title="历史版本">
                                                        <i class="bi bi-clock-history"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 公告管理 -->
                    <div class="tab-pane" id="announcements">
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>标题</th>
                                                <th>发布时间</th>
                                                <th>结束时间</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>系统升级通知</td>
                                                <td>2025-07-08</td>
                                                <td>2025-07-15</td>
                                                <td><span class="status active">已发布</span></td>
                                                <td class="actions">
                                                    <button class="btn-icon" title="编辑">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn-icon" title="下架">
                                                        <i class="bi bi-x-circle"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>新功能上线公告</td>
                                                <td>2025-07-01</td>
                                                <td>2025-07-10</td>
                                                <td><span class="status active">已发布</span></td>
                                                <td class="actions">
                                                    <button class="btn-icon" title="编辑">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn-icon" title="下架">
                                                        <i class="bi bi-x-circle"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>端午节活动公告</td>
                                                <td>2025-06-05</td>
                                                <td>2025-06-12</td>
                                                <td><span class="status inactive">已结束</span></td>
                                                <td class="actions">
                                                    <button class="btn-icon" title="查看">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                    <button class="btn-icon" title="删除">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 客服二维码 -->
                    <div class="tab-pane" id="qrcodes">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>客服二维码设置</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="qrcodeForm">
                                            <div class="form-group">
                                                <label>客服类型</label>
                                                <select class="form-control" id="qrcodeType">
                                                    <option value="wechat">微信客服</option>
                                                    <option value="qq">QQ客服</option>
                                                    <option value="telegram">Telegram客服</option>
                                                    <option value="custom">自定义</option>
                                                </select>
                                            </div>
                                            <div class="form-group">
                                                <label>客服名称</label>
                                                <input type="text" class="form-control" id="qrcodeName" placeholder="如：在线客服">
                                            </div>
                                            <div class="form-group">
                                                <label>联系方式</label>
                                                <input type="text" class="form-control" id="qrcodeContact" placeholder="微信号/QQ号等">
                                            </div>
                                            <div class="form-group">
                                                <label>二维码图片</label>
                                                <input type="file" class="form-control" id="qrcodeImage" accept="image/*">
                                                <small class="text-muted">建议尺寸：200x200像素</small>
                                            </div>
                                            <div class="form-group">
                                                <label>服务时间</label>
                                                <input type="text" class="form-control" id="serviceTime" placeholder="如：9:00-18:00">
                                            </div>
                                            <div class="form-group">
                                                <label class="checkbox-label">
                                                    <input type="checkbox" id="qrcodeEnabled" checked>
                                                    <span>启用此客服</span>
                                                </label>
                                            </div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="bi bi-check"></i> 保存设置
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>当前客服二维码</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        <div id="qrcodePreview">
                                            <img src="https://via.placeholder.com/200x200?text=暂无二维码" alt="客服二维码" class="qrcode-image">
                                            <h6>在线客服</h6>
                                            <p class="text-muted">服务时间：9:00-18:00</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- 客服统计 -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5>客服统计</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="stats-grid">
                                            <div class="stat-item">
                                                <h4 id="todayConsultations">0</h4>
                                                <span>今日咨询</span>
                                            </div>
                                            <div class="stat-item">
                                                <h4 id="totalConsultations">0</h4>
                                                <span>总咨询数</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 页面内容 -->
                    <div class="tab-pane" id="pages">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>页面列表</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="page-list" id="pagesList">
                                            <!-- 页面列表将通过JavaScript动态加载 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-9">
                                <div class="card">
                                    <div class="card-header">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h5 id="currentPageTitle">选择页面进行编辑</h5>
                                            <div class="page-actions" style="display: none;">
                                                <button class="btn btn-success" id="savePage">
                                                    <i class="bi bi-check"></i> 保存
                                                </button>
                                                <button class="btn btn-outline-secondary" id="previewPage">
                                                    <i class="bi bi-eye"></i> 预览
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div id="pageEditor" style="height: 500px;">
                                            <p>请从左侧选择要编辑的页面</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 模态框 -->
    <!-- 添加协议模态框 -->
    <div class="modal" id="addAgreementModal">
        <div class="modal-content">
            <div class="modal-header">
                <h5>添加协议</h5>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addAgreementForm">
                    <div class="form-group">
                        <label>协议名称</label>
                        <input type="text" class="form-control" id="agreementName" required>
                    </div>
                    <div class="form-group">
                        <label>协议类型</label>
                        <select class="form-control" id="agreementType" required>
                            <option value="">选择类型</option>
                            <option value="terms">服务条款</option>
                            <option value="privacy">隐私政策</option>
                            <option value="user">用户协议</option>
                            <option value="payment">支付协议</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>版本号</label>
                        <input type="text" class="form-control" id="agreementVersion" placeholder="如：v1.0">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveAgreement">保存</button>
            </div>
        </div>
    </div>

    <!-- 添加公告模态框 -->
    <div class="modal" id="addAnnouncementModal">
        <div class="modal-content">
            <div class="modal-header">
                <h5>发布公告</h5>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="addAnnouncementForm">
                    <div class="form-group">
                        <label>公告标题</label>
                        <input type="text" class="form-control" id="announcementTitle" required>
                    </div>
                    <div class="form-group">
                        <label>公告类型</label>
                        <select class="form-control" id="announcementType" required>
                            <option value="">选择类型</option>
                            <option value="system">系统公告</option>
                            <option value="maintenance">维护公告</option>
                            <option value="feature">功能更新</option>
                            <option value="promotion">活动推广</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>优先级</label>
                        <select class="form-control" id="announcementPriority">
                            <option value="low">低</option>
                            <option value="normal" selected>普通</option>
                            <option value="high">高</option>
                            <option value="urgent">紧急</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>开始时间</label>
                        <input type="datetime-local" class="form-control" id="announcementStartTime">
                    </div>
                    <div class="form-group">
                        <label>结束时间</label>
                        <input type="datetime-local" class="form-control" id="announcementEndTime">
                    </div>
                    <div class="form-group">
                        <label>公告内容</label>
                        <div id="announcementEditor" style="height: 200px;"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveAnnouncement">发布公告</button>
            </div>
        </div>
    </div>
                                            <p>最后更新: 2025-06-15</p>
                                            <div class="qrcode-actions">
                                                <button class="btn btn-sm btn-outline">更新</button>
                                                <button class="btn btn-sm btn-outline">下载</button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="qrcode-item">
                                        <div class="qrcode-preview">
                                            <img src="https://via.placeholder.com/200x200?text=QQ+Group" alt="QQ群">
                                        </div>
                                        <div class="qrcode-info">
                                            <h4>QQ用户群</h4>
                                            <p>最后更新: 2025-07-01</p>
                                            <div class="qrcode-actions">
                                                <button class="btn btn-sm btn-outline">更新</button>
                                                <button class="btn btn-sm btn-outline">下载</button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="qrcode-item">
                                        <div class="qrcode-preview add-qrcode">
                                            <i class="bi bi-plus-lg"></i>
                                            <span>添加二维码</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 页面内容 -->
                    <div class="tab-pane" id="pages">
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="data-table">
                                        <thead>
                                            <tr>
                                                <th>页面名称</th>
                                                <th>路径</th>
                                                <th>最后更新</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>首页</td>
                                                <td>/</td>
                                                <td>2025-07-05</td>
                                                <td><span class="status active">已发布</span></td>
                                                <td class="actions">
                                                    <button class="btn-icon" title="编辑">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn-icon" title="预览">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>关于我们</td>
                                                <td>/about</td>
                                                <td>2025-06-20</td>
                                                <td><span class="status active">已发布</span></td>
                                                <td class="actions">
                                                    <button class="btn-icon" title="编辑">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn-icon" title="预览">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>联系我们</td>
                                                <td>/contact</td>
                                                <td>2025-06-15</td>
                                                <td><span class="status draft">草稿</span></td>
                                                <td class="actions">
                                                    <button class="btn-icon" title="编辑">
                                                        <i class="bi bi-pencil"></i>
                                                    </button>
                                                    <button class="btn-icon" title="预览">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 内容编辑模态框 -->
    <div class="modal" id="contentModal">
        <div class="modal-backdrop"></div>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">编辑内容</h4>
                    <button class="close-btn" id="closeModal">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="contentTitle">标题</label>
                        <input type="text" id="contentTitle" class="form-control" placeholder="输入标题">
                    </div>
                    <div class="form-group">
                        <label for="editor">内容</label>
                        <div id="editor" style="height: 300px;"></div>
                    </div>
                    <div class="form-group">
                        <label for="contentStatus">状态</label>
                        <select id="contentStatus" class="form-control">
                            <option value="published">发布</option>
                            <option value="draft">草稿</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="cancelBtn">取消</button>
                    <button class="btn btn-primary" id="saveContentBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <script src="js/content.js"></script>
    <script src="content.js"></script>
</body>
</html> 