// 全局变量
let performanceChart;
let currentPage = 1;
let pageSize = 10;
let totalPages = 1;
let searchKeyword = '';
let statusFilter = '';

// 页面加载完成后执行
$(document).ready(function() {
    // 检查登录状态和权限
    checkAuth();
    
    // 初始化图表
    initPerformanceChart();
    
    // 加载代理商列表
    loadAgents();

    // 加载代理统计
    loadAgentStats();

    // 加载上级代理商选项
    loadParentAgents();

    // 加载代理级别
    loadAgentLevels();

    // 加载佣金设置
    loadCommissionSettings();

    // 加载招商设置
    loadRecruitmentSettings();

    // 绑定事件处理器
    bindEventHandlers();

    // 初始化分析图表
    initAnalyticsCharts();
});

// 检查用户登录状态和权限
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = 'login.html';
        return;
    }
    
    // 检查是否有代理商管理权限
    $.ajax({
        url: '/api/auth/check-permission',
        headers: { 'Authorization': `Bearer ${token}` },
        data: { permission: 'agent.view' },
        method: 'POST',
        success: function(response) {
            if (!response.hasPermission) {
                alert('您没有访问此页面的权限');
                window.location.href = 'dashboard.html';
            }
        },
        error: function() {
            alert('权限验证失败');
            window.location.href = 'login.html';
        }
    });
}

// 初始化业绩图表
function initPerformanceChart() {
    performanceChart = new ApexCharts(document.querySelector("#performanceChart"), {
        chart: {
            type: 'area',
            height: 300,
            toolbar: {
                show: false
            }
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        series: [{
            name: '业绩金额',
            data: []
        }],
        colors: ['#0d6efd'],
        fill: {
            type: 'gradient',
            gradient: {
                opacityFrom: 0.6,
                opacityTo: 0.1
            }
        },
        xaxis: {
            type: 'datetime'
        },
        yaxis: {
            labels: {
                formatter: function(val) {
                    return '¥' + val.toFixed(2);
                }
            }
        },
        tooltip: {
            x: {
                format: 'yyyy/MM/dd'
            },
            y: {
                formatter: function(val) {
                    return '¥' + val.toFixed(2);
                }
            }
        }
    });
    performanceChart.render();
}

// 加载代理商列表
function loadAgents() {
    const token = localStorage.getItem('token');
    
    $.ajax({
        url: '/api/agents',
        headers: { 'Authorization': `Bearer ${token}` },
        data: {
            page: currentPage,
            pageSize,
            search: searchKeyword,
            status: statusFilter
        },
        method: 'GET',
        success: function(response) {
            const agents = response.data;
            totalPages = Math.ceil(response.total / pageSize);
            
            renderAgentTable(agents);
            renderPagination();
        },
        error: function() {
            alert('加载代理商列表失败');
        }
    });
}

// 渲染代理商表格
function renderAgentTable(agents) {
    const tbody = $('#agentTableBody');
    tbody.empty();
    
    agents.forEach(agent => {
        const tr = $('<tr>');
        tr.html(`
            <td>${agent.id}</td>
            <td>
                ${agent.name}
                ${agent.level > 1 ? `<span class="agent-level">L${agent.level}</span>` : ''}
            </td>
            <td>${agent.contactPerson}</td>
            <td>${agent.phone}</td>
            <td>
                <span class="status-badge status-${agent.status}">
                    ${getStatusText(agent.status)}
                </span>
            </td>
            <td>${agent.subAgentCount}</td>
            <td>${agent.userCount}</td>
            <td>
                <div class="commission-rate" title="${agent.commissionRate}%">
                    <div class="commission-rate-bar" style="width: ${agent.commissionRate}%"></div>
                </div>
            </td>
            <td class="agent-actions">
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewPerformance(${agent.id})">
                        <i class="bi bi-graph-up"></i>
                    </button>
                    <button class="btn btn-outline-secondary" onclick="editAgent(${agent.id})">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteAgent(${agent.id})">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        `);
        tbody.append(tr);
    });
}

// 渲染分页
function renderPagination() {
    const pagination = $('#pagination');
    pagination.empty();
    
    // 上一页
    pagination.append(`
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>
        </li>
    `);
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        if (
            i === 1 || // 第一页
            i === totalPages || // 最后一页
            (i >= currentPage - 2 && i <= currentPage + 2) // 当前页附近的页码
        ) {
            pagination.append(`
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `);
        } else if (
            i === currentPage - 3 || // 省略号位置
            i === currentPage + 3
        ) {
            pagination.append(`
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            `);
        }
    }
    
    // 下一页
    pagination.append(`
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>
        </li>
    `);
}

// 加载上级代理商选项
function loadParentAgents() {
    const token = localStorage.getItem('token');
    
    $.ajax({
        url: '/api/agents/parents',
        headers: { 'Authorization': `Bearer ${token}` },
        method: 'GET',
        success: function(response) {
            const agents = response.data;
            const select = $('select[name="parentId"]');
            
            agents.forEach(agent => {
                select.append(`<option value="${agent.id}">${agent.name}</option>`);
            });
        }
    });
}

// 绑定事件处理器
function bindEventHandlers() {
    // 搜索
    $('#searchBtn').click(function() {
        searchKeyword = $('#searchInput').val().trim();
        currentPage = 1;
        loadAgents();
    });
    
    // 状态筛选
    $('#statusFilter').change(function() {
        statusFilter = $(this).val();
        currentPage = 1;
        loadAgents();
    });
    
    // 添加代理商
    $('#addAgentSubmit').click(function() {
        const form = $('#addAgentForm');
        const data = {
            name: form.find('[name="name"]').val(),
            contactPerson: form.find('[name="contactPerson"]').val(),
            phone: form.find('[name="phone"]').val(),
            email: form.find('[name="email"]').val(),
            commissionRate: parseFloat(form.find('[name="commissionRate"]').val()),
            parentId: form.find('[name="parentId"]').val() || null,
            address: form.find('[name="address"]').val(),
            remarks: form.find('[name="remarks"]').val()
        };
        
        const token = localStorage.getItem('token');
        $.ajax({
            url: '/api/agents',
            headers: { 'Authorization': `Bearer ${token}` },
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function() {
                $('#addAgentModal').modal('hide');
                form[0].reset();
                loadAgents();
                alert('代理商添加成功');
            },
            error: function(xhr) {
                alert(xhr.responseJSON?.message || '代理商添加失败');
            }
        });
    });
    
    // 导出数据
    $('#exportBtn').click(function() {
        const token = localStorage.getItem('token');
        
        $.ajax({
            url: '/api/agents/export',
            headers: { 'Authorization': `Bearer ${token}` },
            data: {
                search: searchKeyword,
                status: statusFilter
            },
            method: 'GET',
            xhrFields: {
                responseType: 'blob'
            },
            success: function(blob) {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `agents-${new Date().toISOString()}.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                a.remove();
            },
            error: function() {
                alert('导出数据失败');
            }
        });
    });
}

// 查看业绩
function viewPerformance(agentId) {
    const token = localStorage.getItem('token');
    
    $.ajax({
        url: `/api/agents/${agentId}/performance`,
        headers: { 'Authorization': `Bearer ${token}` },
        method: 'GET',
        success: function(response) {
            const data = response.data;
            
            // 更新业绩数据
            $('#monthlyPerformance').text('¥' + data.monthly.toFixed(2));
            $('#totalPerformance').text('¥' + data.total.toFixed(2));
            $('#pendingCommission').text('¥' + data.pendingCommission.toFixed(2));
            
            // 更新业绩图表
            performanceChart.updateSeries([{
                name: '业绩金额',
                data: data.trend
            }]);
            
            // 更新业绩明细表格
            const tbody = $('#performanceTableBody');
            tbody.empty();
            
            data.details.forEach(detail => {
                const tr = $('<tr>');
                tr.html(`
                    <td>${new Date(detail.date).toLocaleDateString()}</td>
                    <td class="amount">¥${detail.amount.toFixed(2)}</td>
                    <td class="amount">¥${detail.commission.toFixed(2)}</td>
                    <td>${detail.settled ? '已结算' : '未结算'}</td>
                `);
                tbody.append(tr);
            });
            
            $('#performanceModal').modal('show');
        },
        error: function() {
            alert('加载业绩数据失败');
        }
    });
}

// 编辑代理商
function editAgent(agentId) {
    const token = localStorage.getItem('token');
    
    $.ajax({
        url: `/api/agents/${agentId}`,
        headers: { 'Authorization': `Bearer ${token}` },
        method: 'GET',
        success: function(response) {
            const agent = response.data;
            const form = $('#editAgentForm');
            
            form.find('[name="agentId"]').val(agent.id);
            form.find('[name="name"]').val(agent.name);
            form.find('[name="contactPerson"]').val(agent.contactPerson);
            form.find('[name="phone"]').val(agent.phone);
            form.find('[name="email"]').val(agent.email);
            form.find('[name="commissionRate"]').val(agent.commissionRate);
            form.find('[name="status"]').val(agent.status);
            form.find('[name="address"]').val(agent.address);
            form.find('[name="remarks"]').val(agent.remarks);
            
            $('#editAgentModal').modal('show');
        },
        error: function() {
            alert('加载代理商信息失败');
        }
    });
}

// 删除代理商
function deleteAgent(agentId) {
    if (!confirm('确定要删除这个代理商吗？')) {
        return;
    }
    
    const token = localStorage.getItem('token');
    $.ajax({
        url: `/api/agents/${agentId}`,
        headers: { 'Authorization': `Bearer ${token}` },
        method: 'DELETE',
        success: function() {
            loadAgents();
            alert('代理商删除成功');
        },
        error: function(xhr) {
            alert(xhr.responseJSON?.message || '代理商删除失败');
        }
    });
}

// 切换页码
function changePage(page) {
    if (page < 1 || page > totalPages) {
        return;
    }
    currentPage = page;
    loadAgents();
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'active': '活跃',
        'pending': '待审核',
        'suspended': '已暂停'
    };
    return statusMap[status] || status;
}

// 格式化金额
function formatAmount(amount) {
    return '¥' + amount.toFixed(2);
}

// 加载代理统计
function loadAgentStats() {
    const token = localStorage.getItem('token');

    $.ajax({
        url: '/api/agents',
        headers: { 'Authorization': `Bearer ${token}` },
        method: 'GET',
        success: function(response) {
            if (response.success && response.stats) {
                updateAgentStatsCards(response.stats);
            }
        },
        error: function() {
            // 使用模拟数据
            updateAgentStatsCards({
                total: 156,
                active: 128,
                pending: 12,
                suspended: 16
            });
        }
    });
}

// 更新代理统计卡片
function updateAgentStatsCards(stats) {
    $('#totalAgentsCount').text(stats.total || 0);
    $('#activeAgentsCount').text(stats.active || 0);
    $('#pendingAgentsCount').text(stats.pending || 0);

    // 计算总佣金（模拟数据）
    const totalCommission = (stats.active || 0) * 1250;
    $('#totalCommission').text('¥' + totalCommission.toLocaleString());

    // 计算增长率（模拟数据）
    const growthRate = 8.5;
    const growthElement = $('#agentGrowth');
    growthElement.text((growthRate >= 0 ? '+' : '') + growthRate + '%');
    growthElement.removeClass('text-success text-danger');
    growthElement.addClass(growthRate >= 0 ? 'text-success' : 'text-danger');
}

// 加载代理级别
function loadAgentLevels() {
    const token = localStorage.getItem('token');

    $.ajax({
        url: '/api/agents/levels',
        headers: { 'Authorization': `Bearer ${token}` },
        method: 'GET',
        success: function(response) {
            if (response.success) {
                renderAgentLevelsTable(response.data);
            }
        },
        error: function() {
            console.error('加载代理级别失败');
            // 使用模拟数据
            renderAgentLevelsTable([
                {
                    _id: 'level-bronze',
                    name: '铜牌代理',
                    code: 'BRONZE',
                    minPerformance: 0,
                    commissionRate: 5,
                    benefits: '基础代理权益，享受5%佣金',
                    status: 'active'
                },
                {
                    _id: 'level-silver',
                    name: '银牌代理',
                    code: 'SILVER',
                    minPerformance: 10000,
                    commissionRate: 8,
                    benefits: '银牌代理权益，享受8%佣金，优先客服支持',
                    status: 'active'
                },
                {
                    _id: 'level-gold',
                    name: '金牌代理',
                    code: 'GOLD',
                    minPerformance: 50000,
                    commissionRate: 12,
                    benefits: '金牌代理权益，享受12%佣金，专属客服，营销支持',
                    status: 'active'
                }
            ]);
        }
    });
}

// 渲染代理级别表格
function renderAgentLevelsTable(levels) {
    const tbody = $('#agentLevelsTableBody');
    tbody.empty();

    levels.forEach(level => {
        const statusBadge = level.status === 'active'
            ? '<span class="badge bg-success">启用</span>'
            : '<span class="badge bg-secondary">禁用</span>';

        const row = `
            <tr>
                <td>${level.name}</td>
                <td><code>${level.code}</code></td>
                <td>¥${level.minPerformance.toLocaleString()}</td>
                <td>${level.commissionRate}%</td>
                <td>${level.benefits}</td>
                <td>${statusBadge}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editAgentLevel('${level._id}')">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteAgentLevel('${level._id}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 加载佣金设置
function loadCommissionSettings() {
    const token = localStorage.getItem('token');

    $.ajax({
        url: '/api/agents/commission-settings',
        headers: { 'Authorization': `Bearer ${token}` },
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateCommissionForm(response.data);
            }
        },
        error: function() {
            console.error('加载佣金设置失败');
            // 使用默认值
            updateCommissionForm({
                level1Commission: 10,
                level2Commission: 5,
                level3Commission: 2,
                settlementPeriod: 'monthly',
                minWithdraw: 100,
                withdrawFee: 0.5
            });
        }
    });
}

// 更新佣金设置表单
function updateCommissionForm(settings) {
    $('#level1Commission').val(settings.level1Commission || 10);
    $('#level2Commission').val(settings.level2Commission || 5);
    $('#level3Commission').val(settings.level3Commission || 2);
    $('#settlementPeriod').val(settings.settlementPeriod || 'monthly');
    $('#minWithdraw').val(settings.minWithdraw || 100);
    $('#withdrawFee').val(settings.withdrawFee || 0.5);
}

// 加载招商设置
function loadRecruitmentSettings() {
    const token = localStorage.getItem('token');

    $.ajax({
        url: '/api/agents/recruitment-settings',
        headers: { 'Authorization': `Bearer ${token}` },
        method: 'GET',
        success: function(response) {
            if (response.success) {
                updateRecruitmentForm(response.data);
            }
        },
        error: function() {
            console.error('加载招商设置失败');
        }
    });
}

// 更新招商设置表单
function updateRecruitmentForm(settings) {
    $(`input[name="approvalProcess"][value="${settings.approvalProcess}"]`).prop('checked', true);
    $('#applicationRequirements').val(settings.applicationRequirements || '');
    $('#contractTemplate').val(settings.contractTemplate || '');
}

// 初始化分析图表
function initAnalyticsCharts() {
    // 检查是否已加载ECharts
    if (typeof echarts === 'undefined') {
        console.warn('ECharts未加载，跳过图表初始化');
        return;
    }

    // 延迟初始化图表，确保DOM元素已渲染
    setTimeout(() => {
        initAgentPerformanceChart();
        initRegionDistributionChart();
        initCommissionTrendChart();
    }, 100);
}

// 初始化代理业绩排行图表
function initAgentPerformanceChart() {
    const chartElement = document.getElementById('agentPerformanceChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'value'
        },
        yAxis: {
            type: 'category',
            data: ['北京科技', '上海商贸', '深圳创新', '广州贸易', '杭州网络']
        },
        series: [{
            name: '业绩',
            type: 'bar',
            data: [125000, 68000, 45000, 32000, 28000],
            itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                    { offset: 0, color: '#1890ff' },
                    { offset: 1, color: '#52c41a' }
                ])
            }
        }]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}

// 初始化区域分布图表
function initRegionDistributionChart() {
    const chartElement = document.getElementById('regionDistributionChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
            name: '区域分布',
            type: 'pie',
            radius: '60%',
            data: [
                { value: 45, name: '华东地区', itemStyle: { color: '#1890ff' } },
                { value: 32, name: '华北地区', itemStyle: { color: '#52c41a' } },
                { value: 28, name: '华南地区', itemStyle: { color: '#faad14' } },
                { value: 25, name: '西南地区', itemStyle: { color: '#ff4d4f' } },
                { value: 26, name: '其他地区', itemStyle: { color: '#722ed1' } }
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}

// 初始化佣金趋势图表
function initCommissionTrendChart() {
    const chartElement = document.getElementById('commissionTrendChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: ['佣金支出', '代理收入']
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: '¥{value}'
            }
        },
        series: [
            {
                name: '佣金支出',
                type: 'line',
                smooth: true,
                data: [15000, 18000, 22000, 25000, 28000, 32000, 35000, 38000, 42000, 45000, 48000, 52000],
                lineStyle: { color: '#ff4d4f' },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(255, 77, 79, 0.3)' },
                        { offset: 1, color: 'rgba(255, 77, 79, 0.1)' }
                    ])
                }
            },
            {
                name: '代理收入',
                type: 'line',
                smooth: true,
                data: [180000, 220000, 280000, 320000, 350000, 420000, 480000, 520000, 580000, 620000, 680000, 750000],
                lineStyle: { color: '#52c41a' },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(82, 196, 26, 0.3)' },
                        { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }
                    ])
                }
            }
        ]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}

// ==================== 多级代理商体系功能 ====================

// 代理商等级配置
const AGENT_LEVELS = {
    1: { name: '初级代理', commission: 0.05, color: '#52c41a', maxSubAgents: 5 },
    2: { name: '中级代理', commission: 0.08, color: '#1890ff', maxSubAgents: 20 },
    3: { name: '高级代理', commission: 0.12, color: '#722ed1', maxSubAgents: 50 },
    4: { name: '区域代理', commission: 0.15, color: '#fa8c16', maxSubAgents: 100 },
    5: { name: '总代理', commission: 0.20, color: '#f5222d', maxSubAgents: null }
};

// 搜索代理商层级结构
function searchAgentHierarchy() {
    const searchTerm = $('#hierarchySearch').val().trim();
    if (!searchTerm) {
        showNotification('请输入代理商姓名或ID', 'warning');
        return;
    }

    // 模拟搜索代理商
    const mockAgent = {
        id: 'AG001',
        name: searchTerm,
        level: 3,
        status: 'active'
    };

    loadAgentHierarchy(mockAgent.id);
}

// 加载代理商层级结构
function loadAgentHierarchy(agentId) {
    showLoading('#hierarchyContainer');

    // 模拟API调用
    setTimeout(() => {
        const hierarchyData = generateMockHierarchy(agentId);
        renderHierarchyTree(hierarchyData);
        updateHierarchyStats(hierarchyData);
    }, 1000);
}

// 生成模拟层级数据
function generateMockHierarchy(agentId) {
    return {
        agent: {
            id: agentId,
            name: '张三',
            level: 3,
            levelName: AGENT_LEVELS[3].name,
            status: 'active',
            joinDate: '2023-01-15',
            totalSales: 150000,
            teamSize: 25
        },
        uplineChain: [
            { id: 'AG_UP1', name: '李四', level: 4, levelName: AGENT_LEVELS[4].name },
            { id: 'AG_UP2', name: '王五', level: 5, levelName: AGENT_LEVELS[5].name }
        ],
        downlineTree: [
            {
                id: 'AG_DOWN1',
                name: '赵六',
                level: 2,
                levelName: AGENT_LEVELS[2].name,
                status: 'active',
                children: [
                    { id: 'AG_DOWN1_1', name: '孙七', level: 1, levelName: AGENT_LEVELS[1].name, status: 'active', children: [] },
                    { id: 'AG_DOWN1_2', name: '周八', level: 1, levelName: AGENT_LEVELS[1].name, status: 'active', children: [] }
                ]
            },
            {
                id: 'AG_DOWN2',
                name: '吴九',
                level: 2,
                levelName: AGENT_LEVELS[2].name,
                status: 'active',
                children: [
                    { id: 'AG_DOWN2_1', name: '郑十', level: 1, levelName: AGENT_LEVELS[1].name, status: 'inactive', children: [] }
                ]
            }
        ],
        teamStats: {
            totalMembers: 25,
            activeMembers: 23,
            levelDistribution: { 1: 15, 2: 8, 3: 2 },
            totalSales: 500000,
            totalCommission: 50000
        }
    };
}

// 渲染层级结构树
function renderHierarchyTree(data) {
    const container = $('#hierarchyContainer');

    const html = `
        <div class="hierarchy-tree">
            <!-- 上级代理商链 -->
            ${data.uplineChain.length > 0 ? `
                <div class="upline-chain mb-4">
                    <h6 class="text-muted mb-3">上级代理商链</h6>
                    <div class="d-flex align-items-center">
                        ${data.uplineChain.reverse().map((agent, index) => `
                            <div class="agent-node upline" data-agent-id="${agent.id}">
                                <div class="agent-card level-${agent.level}">
                                    <div class="agent-name">${agent.name}</div>
                                    <div class="agent-level">${agent.levelName}</div>
                                </div>
                            </div>
                            ${index < data.uplineChain.length - 1 ? '<i class="bi bi-arrow-right mx-2"></i>' : ''}
                        `).join('')}
                        <i class="bi bi-arrow-right mx-2"></i>
                    </div>
                </div>
            ` : ''}

            <!-- 当前代理商 -->
            <div class="current-agent mb-4">
                <div class="agent-node current" data-agent-id="${data.agent.id}">
                    <div class="agent-card level-${data.agent.level} current-agent-card">
                        <div class="agent-avatar">
                            <i class="bi bi-person-circle"></i>
                        </div>
                        <div class="agent-info">
                            <div class="agent-name">${data.agent.name}</div>
                            <div class="agent-level">${data.agent.levelName}</div>
                            <div class="agent-stats">
                                <small>团队: ${data.agent.teamSize}人 | 销售: ¥${formatNumber(data.agent.totalSales)}</small>
                            </div>
                        </div>
                        <div class="agent-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="showAgentDetails('${data.agent.id}')">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-success" onclick="upgradeAgentLevel('${data.agent.id}')">
                                <i class="bi bi-arrow-up"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 下级代理商树 -->
            ${data.downlineTree.length > 0 ? `
                <div class="downline-tree">
                    <h6 class="text-muted mb-3">下级代理商</h6>
                    <div class="tree-container">
                        ${renderDownlineNodes(data.downlineTree)}
                    </div>
                </div>
            ` : '<div class="text-center text-muted py-3">暂无下级代理商</div>'}
        </div>
    `;

    container.html(html);

    // 绑定点击事件
    container.find('.agent-node').click(function() {
        const agentId = $(this).data('agent-id');
        showAgentHierarchyModal(agentId);
    });
}

// 渲染下级代理商节点
function renderDownlineNodes(nodes, level = 0) {
    return nodes.map(node => `
        <div class="tree-node" style="margin-left: ${level * 30}px;">
            <div class="agent-node downline" data-agent-id="${node.id}">
                <div class="agent-card level-${node.level} ${node.status === 'inactive' ? 'inactive' : ''}">
                    <div class="d-flex align-items-center">
                        <div class="agent-info flex-grow-1">
                            <div class="agent-name">${node.name}</div>
                            <div class="agent-level">${node.levelName}</div>
                        </div>
                        <div class="agent-status">
                            <span class="badge bg-${node.status === 'active' ? 'success' : 'secondary'}">
                                ${node.status === 'active' ? '活跃' : '非活跃'}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            ${node.children && node.children.length > 0 ? renderDownlineNodes(node.children, level + 1) : ''}
        </div>
    `).join('');
}

// 更新层级统计
function updateHierarchyStats(data) {
    $('#totalAgentsCount').text(data.teamStats.totalMembers);
    $('#maxHierarchyLevel').text(Math.max(...Object.keys(data.teamStats.levelDistribution)));
    $('#activeAgentsCount').text(data.teamStats.activeMembers);
    $('#newAgentsCount').text(Math.floor(Math.random() * 10) + 1);
}

// 显示代理商层级详情模态框
function showAgentHierarchyModal(agentId) {
    // 模拟获取代理商详情
    const agentData = generateMockHierarchy(agentId);

    // 渲染上级代理商链
    const uplineHtml = agentData.uplineChain.map(agent => `
        <div class="hierarchy-item">
            <div class="d-flex align-items-center mb-2">
                <div class="level-badge level-${agent.level}">${agent.level}</div>
                <div class="ms-2">
                    <div class="fw-bold">${agent.name}</div>
                    <small class="text-muted">${agent.levelName}</small>
                </div>
            </div>
        </div>
    `).join('');

    // 渲染下级代理商
    const downlineHtml = agentData.downlineTree.map(agent => `
        <div class="hierarchy-item">
            <div class="d-flex align-items-center justify-content-between mb-2">
                <div class="d-flex align-items-center">
                    <div class="level-badge level-${agent.level}">${agent.level}</div>
                    <div class="ms-2">
                        <div class="fw-bold">${agent.name}</div>
                        <small class="text-muted">${agent.levelName}</small>
                    </div>
                </div>
                <span class="badge bg-${agent.status === 'active' ? 'success' : 'secondary'}">
                    ${agent.status === 'active' ? '活跃' : '非活跃'}
                </span>
            </div>
            ${agent.children && agent.children.length > 0 ? `
                <div class="ms-4">
                    ${agent.children.map(child => `
                        <div class="d-flex align-items-center mb-1">
                            <div class="level-badge level-${child.level}">${child.level}</div>
                            <div class="ms-2">
                                <small>${child.name} - ${child.levelName}</small>
                            </div>
                        </div>
                    `).join('')}
                </div>
            ` : ''}
        </div>
    `).join('');

    // 渲染团队统计
    const statsHtml = `
        <div class="row">
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">${agentData.teamStats.totalMembers}</div>
                    <div class="stat-label">团队总人数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">${agentData.teamStats.activeMembers}</div>
                    <div class="stat-label">活跃成员</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">¥${formatNumber(agentData.teamStats.totalSales)}</div>
                    <div class="stat-label">团队销售额</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-item">
                    <div class="stat-value">¥${formatNumber(agentData.teamStats.totalCommission)}</div>
                    <div class="stat-label">团队佣金</div>
                </div>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-12">
                <h6>等级分布</h6>
                <div class="level-distribution">
                    ${Object.entries(agentData.teamStats.levelDistribution).map(([level, count]) => `
                        <div class="d-flex justify-content-between mb-1">
                            <span>${AGENT_LEVELS[level].name}</span>
                            <span class="fw-bold">${count}人</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;

    $('#uplineChain').html(uplineHtml || '<div class="text-muted">无上级代理商</div>');
    $('#downlineTree').html(downlineHtml || '<div class="text-muted">无下级代理商</div>');
    $('#teamStats').html(statsHtml);

    $('#agentHierarchyModal').modal('show');
}

// 升级代理商等级
function upgradeAgentLevel(agentId) {
    // 显示升级确认对话框
    if (confirm('确定要升级此代理商等级吗？')) {
        // 模拟升级API调用
        showLoading();

        setTimeout(() => {
            hideLoading();
            showNotification('代理商等级升级成功', 'success');
            // 重新加载层级结构
            loadAgentHierarchy(agentId);
        }, 1000);
    }
}

// 显示代理商详情
function showAgentDetails(agentId) {
    // 这里可以跳转到代理商详情页面或显示详情模态框
    console.log('显示代理商详情:', agentId);
}

// 显示加载状态
function showLoading(selector = 'body') {
    const loadingHtml = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <div class="mt-2">加载中...</div>
        </div>
    `;

    if (selector === 'body') {
        // 全屏加载
        $('body').append('<div class="loading-overlay"><div class="loading-content">' + loadingHtml + '</div></div>');
    } else {
        $(selector).html(loadingHtml);
    }
}

// 隐藏加载状态
function hideLoading() {
    $('.loading-overlay').remove();
}

// 格式化数字
function formatNumber(num) {
    return num.toLocaleString('zh-CN');
}

// ==================== 招商管理功能 ====================

let selectedApplications = [];
let currentApplicationId = null;

// 加载招商申请列表
function loadRecruitmentApplications(page = 1) {
    const status = $('#applicationStatusFilter').val();
    const source = $('#applicationSourceFilter').val();

    // 模拟API调用
    const mockApplications = generateMockApplications();

    // 过滤数据
    let filteredApplications = mockApplications;
    if (status) {
        filteredApplications = filteredApplications.filter(app => app.status === status);
    }
    if (source) {
        filteredApplications = filteredApplications.filter(app => app.source === source);
    }

    renderApplicationsTable(filteredApplications);
    updateRecruitmentStats(mockApplications);
}

// 生成模拟申请数据
function generateMockApplications() {
    const applications = [];
    const names = ['张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十'];
    const regions = ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '武汉'];
    const sources = ['website', 'referral', 'phone', 'exhibition'];
    const statuses = ['pending', 'approved', 'rejected'];

    for (let i = 0; i < 20; i++) {
        applications.push({
            id: `APP_${Date.now()}_${i}`,
            name: names[Math.floor(Math.random() * names.length)],
            phone: `138${Math.floor(Math.random() * *********).toString().padStart(8, '0')}`,
            email: `user${i}@example.com`,
            company: `公司${i + 1}`,
            region: regions[Math.floor(Math.random() * regions.length)],
            expectedLevel: Math.floor(Math.random() * 3) + 1,
            source: sources[Math.floor(Math.random() * sources.length)],
            status: statuses[Math.floor(Math.random() * statuses.length)],
            submittedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
            experience: '有相关行业经验',
            investmentCapacity: Math.floor(Math.random() * 500000) + 50000,
            businessPlan: '计划在本地区开展业务...'
        });
    }

    return applications;
}

// 渲染申请表格
function renderApplicationsTable(applications) {
    const tbody = $('#recruitmentApplicationsTable');
    tbody.empty();

    if (applications.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="9" class="text-center text-muted">暂无申请记录</td>
            </tr>
        `);
        return;
    }

    applications.forEach(app => {
        const statusClass = getStatusClass(app.status);
        const statusText = getStatusText(app.status);
        const sourceText = getSourceText(app.source);

        tbody.append(`
            <tr>
                <td>
                    <input type="checkbox" class="application-checkbox" value="${app.id}"
                           onchange="updateSelectedApplications()">
                </td>
                <td>
                    <div>
                        <div class="fw-bold">${app.name}</div>
                        <small class="text-muted">${app.company || '个人'}</small>
                    </div>
                </td>
                <td>
                    <div>
                        <div>${app.phone}</div>
                        <small class="text-muted">${app.email}</small>
                    </div>
                </td>
                <td>
                    <span class="badge bg-info">等级${app.expectedLevel}</span>
                </td>
                <td>${app.region}</td>
                <td>
                    <span class="badge bg-secondary">${sourceText}</span>
                </td>
                <td>
                    <small>${formatDateTime(app.submittedAt)}</small>
                </td>
                <td>
                    <span class="badge bg-${statusClass}">${statusText}</span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="showApplicationDetail('${app.id}')">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${app.status === 'pending' ? `
                            <button class="btn btn-outline-success" onclick="quickReview('${app.id}', 'approve')">
                                <i class="bi bi-check"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="quickReview('${app.id}', 'reject')">
                                <i class="bi bi-x"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `);
    });
}

// 更新招商统计
function updateRecruitmentStats(applications) {
    const pending = applications.filter(app => app.status === 'pending').length;
    const thisMonth = new Date();
    thisMonth.setDate(1);

    const approvedThisMonth = applications.filter(app =>
        app.status === 'approved' && new Date(app.submittedAt) >= thisMonth
    ).length;

    const rejectedThisMonth = applications.filter(app =>
        app.status === 'rejected' && new Date(app.submittedAt) >= thisMonth
    ).length;

    const totalThisMonth = applications.filter(app =>
        new Date(app.submittedAt) >= thisMonth
    ).length;

    const conversionRate = totalThisMonth > 0 ?
        (approvedThisMonth / totalThisMonth * 100).toFixed(1) : 0;

    $('#pendingApplicationsCount').text(pending);
    $('#approvedThisMonthCount').text(approvedThisMonth);
    $('#rejectedThisMonthCount').text(rejectedThisMonth);
    $('#conversionRate').text(conversionRate + '%');
}

// 显示申请详情
function showApplicationDetail(applicationId) {
    currentApplicationId = applicationId;

    // 模拟获取申请详情
    const application = {
        id: applicationId,
        name: '张三',
        phone: '***********',
        email: '<EMAIL>',
        company: '某某科技有限公司',
        region: '北京',
        expectedLevel: 2,
        experience: '有5年相关行业经验，曾在多家公司担任销售经理',
        investmentCapacity: 200000,
        businessPlan: '计划在北京地区开展业务，预计第一年发展50个客户，实现销售额500万元...',
        referralCode: 'REF123456',
        source: 'website',
        status: 'pending',
        submittedAt: new Date()
    };

    const detailHtml = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>姓名:</td><td>${application.name}</td></tr>
                    <tr><td>电话:</td><td>${application.phone}</td></tr>
                    <tr><td>邮箱:</td><td>${application.email}</td></tr>
                    <tr><td>公司:</td><td>${application.company || '个人'}</td></tr>
                    <tr><td>地区:</td><td>${application.region}</td></tr>
                    <tr><td>期望等级:</td><td>等级${application.expectedLevel}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>申请信息</h6>
                <table class="table table-sm">
                    <tr><td>申请来源:</td><td>${getSourceText(application.source)}</td></tr>
                    <tr><td>推荐码:</td><td>${application.referralCode || '无'}</td></tr>
                    <tr><td>投资能力:</td><td>¥${formatNumber(application.investmentCapacity)}</td></tr>
                    <tr><td>申请时间:</td><td>${formatDateTime(application.submittedAt)}</td></tr>
                    <tr><td>状态:</td><td><span class="badge bg-${getStatusClass(application.status)}">${getStatusText(application.status)}</span></td></tr>
                </table>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-12">
                <h6>从业经验</h6>
                <p class="text-muted">${application.experience}</p>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-12">
                <h6>商业计划</h6>
                <p class="text-muted">${application.businessPlan}</p>
            </div>
        </div>
    `;

    $('#applicationDetailContent').html(detailHtml);
    $('#applicationDetailModal').modal('show');
}

// 快速审核
function quickReview(applicationId, action) {
    const actionText = action === 'approve' ? '通过' : '拒绝';

    if (confirm(`确定要${actionText}这个申请吗？`)) {
        // 模拟API调用
        showLoading();

        setTimeout(() => {
            hideLoading();
            showNotification(`申请已${actionText}`, 'success');
            loadRecruitmentApplications();
        }, 1000);
    }
}

// 审核申请（从详情模态框）
function reviewApplication(action) {
    if (!currentApplicationId) return;

    const reason = prompt('请输入审核说明（可选）:');

    // 模拟API调用
    showLoading();

    setTimeout(() => {
        hideLoading();
        $('#applicationDetailModal').modal('hide');
        showNotification(`申请已${action === 'approve' ? '通过' : '拒绝'}`, 'success');
        loadRecruitmentApplications();
    }, 1000);
}

// 更新选中的申请
function updateSelectedApplications() {
    selectedApplications = [];
    $('.application-checkbox:checked').each(function() {
        selectedApplications.push($(this).val());
    });

    $('#selectedApplicationsCount').text(selectedApplications.length);
}

// 切换全选
function toggleSelectAllApplications() {
    const isChecked = $('#selectAllApplications').prop('checked');
    $('.application-checkbox').prop('checked', isChecked);
    updateSelectedApplications();
}

// 显示批量审核模态框
function showBatchReviewModal() {
    if (selectedApplications.length === 0) {
        showNotification('请先选择要审核的申请', 'warning');
        return;
    }

    $('#batchReviewModal').modal('show');
}

// 执行批量审核
function executeBatchReview() {
    const action = $('#batchReviewAction').val();
    const reason = $('#batchReviewReason').val();

    if (!reason.trim()) {
        showNotification('请输入审核说明', 'warning');
        return;
    }

    // 模拟API调用
    showLoading();

    setTimeout(() => {
        hideLoading();
        $('#batchReviewModal').modal('hide');
        showNotification(`批量${action === 'approve' ? '通过' : '拒绝'}成功`, 'success');
        selectedApplications = [];
        $('#selectAllApplications').prop('checked', false);
        loadRecruitmentApplications();
    }, 1500);
}

// 复制到剪贴板
function copyToClipboard(selector) {
    const element = $(selector)[0];
    element.select();
    element.setSelectionRange(0, 99999);
    document.execCommand('copy');
    showNotification('已复制到剪贴板', 'success');
}

// 下载二维码
function downloadQRCode() {
    // 模拟下载二维码
    showNotification('二维码下载成功', 'success');
}

// 获取状态样式类
function getStatusClass(status) {
    const classes = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger'
    };
    return classes[status] || 'secondary';
}

// 获取状态文本
function getStatusText(status) {
    const texts = {
        'pending': '待审核',
        'approved': '已通过',
        'rejected': '已拒绝'
    };
    return texts[status] || status;
}

// 获取来源文本
function getSourceText(source) {
    const texts = {
        'website': '官网',
        'referral': '推荐',
        'phone': '电话',
        'exhibition': '展会'
    };
    return texts[source] || source;
}

// 格式化日期时间
function formatDateTime(dateString) {
    return new Date(dateString).toLocaleString('zh-CN');
}

// 显示通知
function showNotification(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    $('body').append(alert);

    setTimeout(() => {
        alert.remove();
    }, 3000);
}