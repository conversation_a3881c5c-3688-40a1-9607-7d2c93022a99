// 全局变量
let editor;
let currentTab = 'agreements';
let currentEditItem = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 初始化选项卡
    initTabs();
    
    // 初始化富文本编辑器
    initEditor();
    
    // 绑定事件处理器
    bindEventHandlers();
});

// 初始化选项卡
function initTabs() {
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // 移除所有选项卡的活动状态
            tabs.forEach(t => t.classList.remove('active'));
            
            // 设置当前选项卡为活动状态
            tab.classList.add('active');
            
            // 获取选项卡对应的内容ID
            const tabId = tab.dataset.tab;
            currentTab = tabId;
            
            // 隐藏所有选项卡内容
            const tabPanes = document.querySelectorAll('.tab-pane');
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            // 显示当前选项卡内容
            document.getElementById(tabId).classList.add('active');
        });
    });
}

// 初始化富文本编辑器
function initEditor() {
    const toolbarOptions = [
        ['bold', 'italic', 'underline', 'strike'],
        ['blockquote', 'code-block'],
        [{ 'header': 1 }, { 'header': 2 }],
        [{ 'list': 'ordered'}, { 'list': 'bullet' }],
        [{ 'script': 'sub'}, { 'script': 'super' }],
        [{ 'indent': '-1'}, { 'indent': '+1' }],
        [{ 'direction': 'rtl' }],
        [{ 'size': ['small', false, 'large', 'huge'] }],
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'font': [] }],
        [{ 'align': [] }],
        ['clean'],
        ['link', 'image']
    ];

    editor = new Quill('#editor', {
        modules: {
            toolbar: toolbarOptions
        },
        theme: 'snow'
    });
}

// 绑定事件处理器
function bindEventHandlers() {
    // 新建内容按钮
    document.getElementById('createContentBtn').addEventListener('click', () => {
        openContentModal();
    });
    
    // 关闭模态框按钮
    document.getElementById('closeModal').addEventListener('click', () => {
        closeContentModal();
    });
    
    // 取消按钮
    document.getElementById('cancelBtn').addEventListener('click', () => {
        closeContentModal();
    });
    
    // 保存内容按钮
    document.getElementById('saveContentBtn').addEventListener('click', () => {
        saveContent();
    });
    
    // 编辑按钮事件委托
    document.addEventListener('click', (e) => {
        if (e.target.closest('.btn-icon') && e.target.closest('.btn-icon').title === '编辑') {
            const row = e.target.closest('tr');
            if (row) {
                const title = row.cells[0].textContent;
                openContentModal(title);
            }
        }
    });
    
    // 添加二维码点击事件
    const addQrcode = document.querySelector('.add-qrcode');
    if (addQrcode) {
        addQrcode.addEventListener('click', () => {
            alert('上传新的二维码');
        });
    }
    
    // 二维码更新按钮事件委托
    document.addEventListener('click', (e) => {
        if (e.target.closest('.btn') && e.target.closest('.btn').textContent === '更新') {
            const qrcodeItem = e.target.closest('.qrcode-item');
            if (qrcodeItem) {
                const title = qrcodeItem.querySelector('h4').textContent;
                alert(`更新${title}二维码`);
            }
        }
    });
    
    // 模态框背景点击关闭
    document.querySelector('.modal-backdrop').addEventListener('click', () => {
        closeContentModal();
    });
}

// 打开内容编辑模态框
function openContentModal(title = '') {
    const modal = document.getElementById('contentModal');
    modal.classList.add('active');
    
    // 设置模态框标题
    const modalTitle = document.querySelector('.modal-title');
    
    if (title) {
        // 编辑现有内容
        modalTitle.textContent = `编辑 - ${title}`;
        document.getElementById('contentTitle').value = title;
        
        // 根据不同的选项卡加载不同的内容
        switch (currentTab) {
            case 'agreements':
                loadAgreementContent(title);
                break;
            case 'announcements':
                loadAnnouncementContent(title);
                break;
            case 'pages':
                loadPageContent(title);
                break;
        }
    } else {
        // 创建新内容
        modalTitle.textContent = '创建新内容';
        document.getElementById('contentTitle').value = '';
        editor.setText('');
        document.getElementById('contentStatus').value = 'draft';
    }
}

// 关闭内容编辑模态框
function closeContentModal() {
    const modal = document.getElementById('contentModal');
    modal.classList.remove('active');
    currentEditItem = null;
}

// 保存内容
function saveContent() {
    const title = document.getElementById('contentTitle').value;
    const content = editor.root.innerHTML;
    const status = document.getElementById('contentStatus').value;
    
    if (!title) {
        alert('请输入标题');
        return;
    }
    
    // 根据不同的选项卡保存不同类型的内容
    switch (currentTab) {
        case 'agreements':
            saveAgreement(title, content, status);
            break;
        case 'announcements':
            saveAnnouncement(title, content, status);
            break;
        case 'pages':
            savePage(title, content, status);
            break;
        case 'qrcodes':
            saveQrcode(title, content);
            break;
    }
    
    // 关闭模态框
    closeContentModal();
    
    // 显示成功消息
    showToast('保存成功', `内容"${title}"已保存`);
}

// 加载协议内容
function loadAgreementContent(title) {
    // 模拟API调用获取协议内容
    // 实际应用中应该从后端获取数据
    const content = `<h2>${title}</h2>
<p>这是${title}的内容。在实际应用中，这里应该是从后端API获取的真实内容。</p>
<p>本协议的最新版本为v2.1，更新于2025年7月5日。</p>
<h3>一、总则</h3>
<p>1.1 本协议是用户与WriterPro平台之间关于用户使用WriterPro平台服务所订立的协议。</p>
<p>1.2 用户在使用WriterPro提供的各项服务之前，应仔细阅读本协议。</p>
<h3>二、用户权利与义务</h3>
<p>2.1 用户有权利...</p>
<p>2.2 用户有义务...</p>`;
    
    editor.root.innerHTML = content;
    document.getElementById('contentStatus').value = 'published';
    
    currentEditItem = {
        title,
        type: 'agreement',
        version: 'v2.1',
        updatedAt: '2025-07-05',
        status: 'published'
    };
}

// 加载公告内容
function loadAnnouncementContent(title) {
    // 模拟API调用获取公告内容
    const content = `<h2>${title}</h2>
<p>亲爱的用户：</p>
<p>我们将于2025年7月10日凌晨2:00-4:00进行系统升级维护，期间服务可能会出现短暂中断，给您带来的不便敬请谅解。</p>
<p>升级内容：</p>
<ol>
  <li>优化文档处理速度，提升30%处理效率</li>
  <li>新增批量处理功能</li>
  <li>修复已知的几个小bug</li>
</ol>
<p>感谢您的支持与理解！</p>
<p>WriterPro团队</p>`;
    
    editor.root.innerHTML = content;
    document.getElementById('contentStatus').value = 'published';
    
    currentEditItem = {
        title,
        type: 'announcement',
        startDate: '2025-07-08',
        endDate: '2025-07-15',
        status: 'published'
    };
}

// 加载页面内容
function loadPageContent(title) {
    // 模拟API调用获取页面内容
    const content = `<h1>${title}</h1>
<p>这是${title}的页面内容。在实际应用中，这里应该是从后端API获取的真实内容。</p>
<p>WriterPro是一款专业的文档处理工具，可以帮助用户快速生成、编辑和优化各类文档。</p>
<h2>我们的优势</h2>
<ul>
  <li>智能文档生成</li>
  <li>多语言支持</li>
  <li>专业排版</li>
  <li>云端存储</li>
</ul>`;
    
    editor.root.innerHTML = content;
    
    // 根据不同的页面设置不同的状态
    if (title === '联系我们') {
        document.getElementById('contentStatus').value = 'draft';
    } else {
        document.getElementById('contentStatus').value = 'published';
    }
    
    currentEditItem = {
        title,
        type: 'page',
        path: title === '首页' ? '/' : `/${title.toLowerCase().replace(/\s+/g, '-')}`,
        updatedAt: '2025-07-05',
        status: title === '联系我们' ? 'draft' : 'published'
    };
}

// 保存协议
function saveAgreement(title, content, status) {
    console.log('保存协议:', { title, content, status });
    // 实际应用中应该调用API保存数据
    // 模拟成功保存后的UI更新
    
    // 如果是编辑现有协议
    if (currentEditItem && currentEditItem.type === 'agreement') {
        // 更新表格中的对应行
        const rows = document.querySelectorAll('#agreements table tbody tr');
        for (const row of rows) {
            if (row.cells[0].textContent === currentEditItem.title) {
                row.cells[0].textContent = title;
                row.cells[1].textContent = getCurrentDate();
                // 版本号自增
                const version = currentEditItem.version;
                const newVersion = incrementVersion(version);
                row.cells[2].textContent = newVersion;
                
                const statusSpan = row.cells[3].querySelector('.status');
                statusSpan.textContent = status === 'published' ? '已启用' : '草稿';
                statusSpan.className = `status ${status === 'published' ? 'active' : 'draft'}`;
                break;
            }
        }
    } else {
        // 添加新协议到表格
        const tbody = document.querySelector('#agreements table tbody');
        const newRow = document.createElement('tr');
        newRow.innerHTML = `
            <td>${title}</td>
            <td>${getCurrentDate()}</td>
            <td>v1.0</td>
            <td><span class="status ${status === 'published' ? 'active' : 'draft'}">${status === 'published' ? '已启用' : '草稿'}</span></td>
            <td class="actions">
                <button class="btn-icon" title="编辑">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn-icon" title="历史版本">
                    <i class="bi bi-clock-history"></i>
                </button>
            </td>
        `;
        tbody.appendChild(newRow);
    }
}

// 保存公告
function saveAnnouncement(title, content, status) {
    console.log('保存公告:', { title, content, status });
    // 实际应用中应该调用API保存数据
    
    // 如果是编辑现有公告
    if (currentEditItem && currentEditItem.type === 'announcement') {
        // 更新表格中的对应行
        const rows = document.querySelectorAll('#announcements table tbody tr');
        for (const row of rows) {
            if (row.cells[0].textContent === currentEditItem.title) {
                row.cells[0].textContent = title;
                // 其他字段保持不变
                
                const statusSpan = row.cells[3].querySelector('.status');
                statusSpan.textContent = status === 'published' ? '已发布' : '草稿';
                statusSpan.className = `status ${status === 'published' ? 'active' : 'draft'}`;
                break;
            }
        }
    } else {
        // 添加新公告到表格
        const tbody = document.querySelector('#announcements table tbody');
        const newRow = document.createElement('tr');
        const today = getCurrentDate();
        const endDate = getDateAfterDays(7); // 默认7天后结束
        
        newRow.innerHTML = `
            <td>${title}</td>
            <td>${today}</td>
            <td>${endDate}</td>
            <td><span class="status ${status === 'published' ? 'active' : 'draft'}">${status === 'published' ? '已发布' : '草稿'}</span></td>
            <td class="actions">
                <button class="btn-icon" title="编辑">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn-icon" title="下架">
                    <i class="bi bi-x-circle"></i>
                </button>
            </td>
        `;
        tbody.appendChild(newRow);
    }
}

// 保存页面内容
function savePage(title, content, status) {
    console.log('保存页面:', { title, content, status });
    // 实际应用中应该调用API保存数据
    
    // 如果是编辑现有页面
    if (currentEditItem && currentEditItem.type === 'page') {
        // 更新表格中的对应行
        const rows = document.querySelectorAll('#pages table tbody tr');
        for (const row of rows) {
            if (row.cells[0].textContent === currentEditItem.title) {
                row.cells[0].textContent = title;
                // 路径可能需要更新
                row.cells[1].textContent = `/${title.toLowerCase().replace(/\s+/g, '-')}`;
                row.cells[2].textContent = getCurrentDate();
                
                const statusSpan = row.cells[3].querySelector('.status');
                statusSpan.textContent = status === 'published' ? '已发布' : '草稿';
                statusSpan.className = `status ${status === 'published' ? 'active' : 'draft'}`;
                break;
            }
        }
    } else {
        // 添加新页面到表格
        const tbody = document.querySelector('#pages table tbody');
        const newRow = document.createElement('tr');
        const path = `/${title.toLowerCase().replace(/\s+/g, '-')}`;
        
        newRow.innerHTML = `
            <td>${title}</td>
            <td>${path}</td>
            <td>${getCurrentDate()}</td>
            <td><span class="status ${status === 'published' ? 'active' : 'draft'}">${status === 'published' ? '已发布' : '草稿'}</span></td>
            <td class="actions">
                <button class="btn-icon" title="编辑">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn-icon" title="预览">
                    <i class="bi bi-eye"></i>
                </button>
            </td>
        `;
        tbody.appendChild(newRow);
    }
}

// 保存二维码
function saveQrcode(title, imageUrl) {
    console.log('保存二维码:', { title, imageUrl });
    // 实际应用中应该调用API保存数据
    alert('二维码保存功能尚未实现');
}

// 工具函数：获取当前日期字符串
function getCurrentDate() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 工具函数：获取指定天数后的日期
function getDateAfterDays(days) {
    const date = new Date();
    date.setDate(date.getDate() + days);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// 工具函数：增加版本号
function incrementVersion(version) {
    // 假设版本号格式为 "vX.Y"
    const match = version.match(/v(\d+)\.(\d+)/);
    if (match) {
        const major = parseInt(match[1]);
        const minor = parseInt(match[2]) + 1;
        return `v${major}.${minor}`;
    }
    return version;
}

// 显示提示消息
function showToast(title, message) {
    // 如果页面中有Toast组件，可以使用它
    console.log(`${title}: ${message}`);
    alert(`${title}: ${message}`);
} 