@echo off
setlocal enabledelayedexpansion

echo 🤖 WriterPro 管理后台全自动启动脚本
echo ========================================
echo.

:: 检查并进入正确目录
:check_directory
echo [信息] 检查当前目录...

if exist "index.html" (
    echo [成功] 已在admin-dashboard目录中
    goto check_nodejs
)

if exist "admin-dashboard" (
    echo [信息] 进入admin-dashboard目录...
    cd admin-dashboard
    if exist "index.html" (
        echo [成功] 成功进入admin-dashboard目录
        goto check_nodejs
    )
)

echo [错误] 未找到admin-dashboard目录或index.html文件
echo [错误] 请确保在包含admin-dashboard的目录中运行此脚本
pause
exit /b 1

:check_nodejs
echo [信息] 检查Node.js...

node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [成功] Node.js已安装
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo [信息] Node.js版本: !NODE_VERSION!
    goto stop_processes
) else (
    echo [错误] Node.js未安装
    echo [信息] 请访问 https://nodejs.org 下载安装Node.js
    pause
    exit /b 1
)

:stop_processes
echo [信息] 检查端口占用情况...

:: 停止端口3000的进程
netstat -ano | findstr :3000 >nul 2>&1
if %errorlevel% equ 0 (
    echo [警告] 端口3000被占用，正在停止相关进程...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do (
        taskkill /F /PID %%a >nul 2>&1
    )
    timeout /t 2 >nul
)

:: 停止端口8080的进程
netstat -ano | findstr :8080 >nul 2>&1
if %errorlevel% equ 0 (
    echo [警告] 端口8080被占用，正在停止相关进程...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080') do (
        taskkill /F /PID %%a >nul 2>&1
    )
    timeout /t 2 >nul
)

echo [成功] 端口清理完成

:create_server
if not exist "simple-port-3000.js" (
    echo [信息] 创建服务器文件...
    
    echo const http = require('http'); > simple-port-3000.js
    echo const fs = require('fs'); >> simple-port-3000.js
    echo const path = require('path'); >> simple-port-3000.js
    echo const url = require('url'); >> simple-port-3000.js
    echo. >> simple-port-3000.js
    echo const PORT = 3000; >> simple-port-3000.js
    echo. >> simple-port-3000.js
    echo const server = http.createServer((req, res) =^> { >> simple-port-3000.js
    echo   const parsedUrl = url.parse(req.url, true); >> simple-port-3000.js
    echo   let pathname = parsedUrl.pathname; >> simple-port-3000.js
    echo. >> simple-port-3000.js
    echo   if (pathname === '/') { >> simple-port-3000.js
    echo     res.writeHead(200, { 'Content-Type': 'text/html' }); >> simple-port-3000.js
    echo     res.end(`^<!DOCTYPE html^>^<html^>^<head^>^<title^>主网站^</title^>^<style^>body{font-family:Arial,sans-serif;margin:40px;background:#f5f5f5}.container{max-width:800px;margin:0 auto;background:white;padding:40px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1)}.admin-link{display:inline-block;padding:15px 30px;background:#007bff;color:white;text-decoration:none;border-radius:5px;margin-top:20px;font-weight:bold}.admin-link:hover{background:#0056b3}.info{background:#e7f3ff;padding:15px;border-radius:5px;margin-top:20px}^</style^>^</head^>^<body^>^<div class="container"^>^<h1^>🎉 欢迎访问主网站^</h1^>^<p^>管理后台已成功集成到 /ad 路径下。^</p^>^<a href="/ad" class="admin-link"^>🔧 进入管理后台^</a^>^<div class="info"^>^<h3^>📋 登录信息^</h3^>^<p^>^<strong^>管理后台:^</strong^> ^<code^>http://localhost:3000/ad^</code^>^</p^>^<p^>^<strong^>用户名:^</strong^> ^<code^>admin^</code^>^</p^>^<p^>^<strong^>密码:^</strong^> ^<code^>admin123^</code^>^</p^>^<p^>^<strong^>验证码:^</strong^> ^<code^>101010^</code^>^</p^>^</div^>^</div^>^</body^>^</html^>`); >> simple-port-3000.js
    echo     return; >> simple-port-3000.js
    echo   } >> simple-port-3000.js
    echo. >> simple-port-3000.js
    echo   if (pathname === '/ad' ^|^| pathname === '/ad/') { >> simple-port-3000.js
    echo     fs.readFile('index.html', (err, data) =^> { >> simple-port-3000.js
    echo       if (err) { >> simple-port-3000.js
    echo         res.writeHead(404); >> simple-port-3000.js
    echo         res.end('File not found'); >> simple-port-3000.js
    echo       } else { >> simple-port-3000.js
    echo         res.writeHead(200, { 'Content-Type': 'text/html' }); >> simple-port-3000.js
    echo         res.end(data); >> simple-port-3000.js
    echo       } >> simple-port-3000.js
    echo     }); >> simple-port-3000.js
    echo     return; >> simple-port-3000.js
    echo   } >> simple-port-3000.js
    echo. >> simple-port-3000.js
    echo   res.writeHead(404); >> simple-port-3000.js
    echo   res.end('Page not found'); >> simple-port-3000.js
    echo }); >> simple-port-3000.js
    echo. >> simple-port-3000.js
    echo server.listen(PORT, () =^> { >> simple-port-3000.js
    echo   console.log('========================================'); >> simple-port-3000.js
    echo   console.log('🚀 服务器运行在端口', PORT); >> simple-port-3000.js
    echo   console.log('📱 主网站: http://localhost:' + PORT); >> simple-port-3000.js
    echo   console.log('🔧 管理后台: http://localhost:' + PORT + '/ad'); >> simple-port-3000.js
    echo   console.log('👤 登录信息: admin / admin123 / 101010'); >> simple-port-3000.js
    echo   console.log('========================================'); >> simple-port-3000.js
    echo }); >> simple-port-3000.js
    
    echo [成功] 服务器文件创建完成
)

:start_server
echo.
echo [成功] 所有检查完成，准备启动服务器...
echo.

:: 自动打开浏览器
echo [信息] 3秒后自动打开浏览器...
start "" "http://localhost:3000/ad"

:: 启动服务器
echo [信息] 启动服务器...
echo.
echo ========================================
echo    服务器启动中...
echo    按 Ctrl+C 停止服务器
echo ========================================
echo.

node simple-port-3000.js

:: 如果服务器停止
echo.
echo [信息] 服务器已停止
pause
