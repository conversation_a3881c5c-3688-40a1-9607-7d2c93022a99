/* 全局样式 */
:root {
    --primary-color: #1677ff;
    --primary-hover: #4096ff;
    --secondary-color: #52c41a;
    --danger-color: #ff4d4f;
    --warning-color: #faad14;
    --text-color: #262626;
    --text-secondary: #595959;
    --text-light: #8c8c8c;
    --border-color: #d9d9d9;
    --bg-light: #f5f5f5;
    --bg-white: #ffffff;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    --radius: 6px;
    --transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    color: var(--text-color);
    background-color: var(--bg-light);
    line-height: 1.5;
}

/* 登录页样式 */
.login-container {
    display: flex;
    min-height: 100vh;
    background-color: var(--bg-light);
}

.login-card {
    flex: 1;
    max-width: 480px;
    padding: 40px;
    background-color: var(--bg-white);
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
}

.login-info {
    flex: 1;
    background: linear-gradient(135deg, #1677ff 0%, #0958d9 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
}

.login-info-content {
    max-width: 500px;
}

.login-info h2 {
    font-size: 32px;
    margin-bottom: 16px;
}

.login-info p {
    font-size: 16px;
    margin-bottom: 32px;
    opacity: 0.9;
}

.feature-list {
    list-style-type: none;
}

.feature-list li {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    font-size: 16px;
}

.feature-list i {
    margin-right: 12px;
    color: var(--secondary-color);
    font-size: 20px;
}

.login-header {
    margin-bottom: 40px;
    text-align: center;
    position: relative;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
}

.logo-icon {
    width: 48px;
    height: 48px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 24px;
}

.logo h1 {
    font-size: 28px;
    font-weight: 600;
    color: var(--primary-color);
}

.login-header h2 {
    font-size: 24px;
    margin-bottom: 8px;
}

.subtitle {
    color: var(--text-light);
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.input-with-icon {
    position: relative;
}

.input-with-icon i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.input-with-icon input {
    width: 100%;
    padding: 12px 12px 12px 40px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    font-size: 16px;
    transition: var(--transition);
}

.input-with-icon input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    outline: none;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    margin-right: 8px;
}

.forgot-password {
    color: var(--primary-color);
    text-decoration: none;
}

.forgot-password:hover {
    text-decoration: underline;
}

.login-btn {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius);
    font-size: 16px;
    cursor: pointer;
    transition: var(--transition);
}

.login-btn:hover {
    background-color: var(--primary-hover);
}

.login-footer {
    margin-top: 24px;
    text-align: center;
    color: var(--text-light);
    font-size: 14px;
}

/* 双因素认证模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--bg-white);
    border-radius: var(--radius);
    width: 100%;
    max-width: 400px;
    box-shadow: var(--shadow);
    overflow: hidden;
}

.modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    font-size: 18px;
    margin: 0;
    font-weight: 600;
}

.close-modal {
    font-size: 24px;
    cursor: pointer;
    color: var(--text-light);
}

.close-modal:hover {
    color: var(--text-color);
}

.modal-body {
    padding: 24px;
}

.modal-body p {
    margin-bottom: 16px;
}

.otp-input-container {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
}

.otp-input {
    width: 40px;
    height: 48px;
    text-align: center;
    font-size: 18px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
}

.otp-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    outline: none;
}

.otp-timer {
    text-align: center;
    margin-bottom: 24px;
    color: var(--text-secondary);
}

.verify-btn {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius);
    font-size: 16px;
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 16px;
}

.verify-btn:hover {
    background-color: var(--primary-hover);
}

.resend-btn {
    width: 100%;
    padding: 12px;
    background-color: var(--bg-white);
    color: var(--primary-color);
    border: 1px solid var(--primary-color);
    border-radius: var(--radius);
    font-size: 16px;
    cursor: pointer;
    transition: var(--transition);
}

.resend-btn:hover {
    background-color: rgba(24, 144, 255, 0.1);
}

/* 语言切换器样式 */
.language-switcher {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 100;
}

.language-dropdown {
    position: relative;
    min-width: 100px;
    cursor: pointer;
}

.language-selected {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background-color: var(--bg-light);
    border-radius: var(--radius);
    font-size: 14px;
    border: 1px solid var(--border-color);
}

.language-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--bg-white);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    margin-top: 4px;
    display: none;
    z-index: 1000;
    border: 1px solid var(--border-color);
}

.language-dropdown.active .language-options {
    display: block;
}

.language-option {
    padding: 8px 12px;
    font-size: 14px;
    transition: var(--transition);
}

.language-option:hover {
    background-color: var(--bg-light);
}

.language-option.active {
    background-color: #e6f7ff;
    color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 992px) {
    .login-container {
        flex-direction: column;
    }
    
    .login-card {
        max-width: 100%;
        padding: 40px 20px;
    }
    
    .login-info {
        display: none;
    }
} 