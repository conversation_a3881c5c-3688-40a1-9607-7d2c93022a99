# WriterPro 管理后台系统

## 🚀 快速启动

### 方法一：启动到端口3000/ad路径（推荐）

#### Windows用户：
```bash
# 双击运行或在命令行执行
start-port-3000.bat
```

#### Linux/macOS用户：
```bash
# 给脚本执行权限
chmod +x start-port-3000.sh

# 运行脚本
./start-port-3000.sh
```

#### 使用npm命令：
```bash
# 启动到端口3000/ad路径
npm start

# 或者
npm run start:3000
```

### 方法二：启动到独立端口8080

#### Windows用户：
```bash
# 双击运行或在命令行执行
start-server.bat

# 或者一键启动并打开浏览器
start-with-browser.bat
```

#### Linux/macOS用户：
```bash
# 给脚本执行权限
chmod +x start-server.sh

# 运行脚本
./start-server.sh
```

### 方法二：使用npm命令

```bash
# 安装依赖（首次运行）
npm install

# 启动服务器
npm start

# 或者启动并自动打开浏览器
npm run start:node
```

### 方法三：手动启动

```bash
# Python方式
python -m http.server 3000

# Node.js方式
npx http-server -p 3000 -o
```

## 🔐 登录信息

### 默认管理员账号
- **邮箱**: `<EMAIL>`
- **密码**: `admin123`
- **权限**: 超级管理员

### 其他测试账号
- **财务管理员**: `<EMAIL>` / `finance123`
- **代理管理员**: `<EMAIL>` / `agent123`

## 🌐 访问地址

### 端口3000/ad路径（推荐）：
- **管理后台**: http://localhost:3000/ad
- **主网站**: http://localhost:3000

### 独立端口8080：
- **管理后台**: http://localhost:8080
- **备用地址**: http://127.0.0.1:8080

## 📋 系统要求

- Python 3.6+ 或 Node.js 12+
- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 网络连接（用于加载CDN资源）

## 🎯 主要功能

### 核心管理
- 📊 仪表盘 - 数据概览
- 👥 用户管理 - 用户增删改查
- 🏢 代理商管理 - 多级代理体系
- 💰 佣金管理 - 佣金计算结算
- 💳 价格管理 - 产品价格配置
- 🛡️ 角色管理 - 权限控制

### 高级功能
- 📈 数据分析 - 高级数据分析
- 📋 报表中心 - 自定义报表
- 🔧 系统设置 - 参数配置
- 📝 内容管理 - 文章媒体管理
- 🔔 通知中心 - 消息推送
- 🛡️ 安全中心 - 安全策略

### 运营工具
- 📊 系统监控 - 性能监控
- 📜 系统日志 - 日志查看
- ⚙️ 任务管理 - 定时任务
- 💾 备份恢复 - 数据备份
- 🎯 营销工具 - 营销活动
- 📚 API文档 - 接口文档

## 🛠️ 故障排除

### 端口被占用
脚本会自动检测并使用其他可用端口

### Python未安装
下载安装：https://www.python.org/downloads/

### 权限问题
Linux/macOS用户可能需要：
```bash
sudo python -m http.server 3000
```

## 📞 技术支持

如有问题请联系：
- 邮箱：<EMAIL>
- 文档：http://localhost:3000/api-docs.html

## 📄 许可证

MIT License
