/* 营销工具页面样式 */

/* 选项卡样式 */
.tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 20px;
}

.tab {
    padding: 12px 20px;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    position: relative;
    transition: all 0.3s ease;
}

.tab:hover {
    color: #1890ff;
}

.tab.active {
    color: #1890ff;
}

.tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #1890ff;
}

/* 选项卡内容 */
.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 卡片样式 */
.card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.card-body {
    padding: 20px;
}

/* 操作栏 */
.action-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.filter-group {
    display: flex;
    gap: 10px;
}

/* 表格样式 */
.table {
    width: 100%;
    border-collapse: collapse;
}

.table th {
    background-color: #f9f9f9;
    padding: 12px 15px;
    text-align: left;
    font-weight: 500;
    color: #333;
    border-bottom: 1px solid #e0e0e0;
    white-space: nowrap;
}

.table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e0e0e0;
}

.table tr:hover {
    background-color: #f5f5f5;
}

.table-responsive {
    overflow-x: auto;
}

/* 状态标签 */
.status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status.active {
    background-color: rgba(82, 196, 26, 0.1);
    color: #52c41a;
}

.status.pending {
    background-color: rgba(250, 173, 20, 0.1);
    color: #faad14;
}

.status.expired {
    background-color: rgba(144, 147, 153, 0.1);
    color: #909399;
}

/* 按钮样式 */
.btn {
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    border: none;
}

.btn-primary {
    background-color: #1890ff;
    color: #fff;
}

.btn-primary:hover {
    background-color: #40a9ff;
}

.btn-outline {
    background-color: #fff;
    color: #666;
    border: 1px solid #d9d9d9;
}

.btn-outline:hover {
    color: #1890ff;
    border-color: #1890ff;
}

.btn-secondary {
    background-color: #f0f0f0;
    color: #666;
}

.btn-secondary:hover {
    background-color: #e0e0e0;
}

/* 表格操作按钮 */
.table-actions {
    display: flex;
    gap: 5px;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: none;
    background-color: transparent;
    color: #666;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-icon:hover {
    background-color: #f0f0f0;
    color: #1890ff;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s;
}

.form-control:focus {
    border-color: #1890ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

.input-group {
    display: flex;
}

.input-group .form-control {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    flex: 1;
}

.input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.input-suffix {
    position: relative;
    display: flex;
    align-items: center;
}

.input-suffix .form-control {
    padding-right: 40px;
    width: 100%;
}

.input-suffix .suffix {
    position: absolute;
    right: 12px;
    color: #999;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.modal.active {
    display: block;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-dialog {
    position: relative;
    width: 90%;
    max-width: 600px;
    margin: 50px auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    margin: 0;
    font-size: 18px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 活动卡片样式 */
.campaign-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.campaign-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
}

.campaign-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
}

.campaign-header {
    height: 120px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.campaign-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.1), rgba(0,0,0,0.5));
    display: flex;
    align-items: flex-end;
    padding: 15px;
}

.campaign-title {
    color: #fff;
    margin: 0;
    font-size: 18px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.campaign-body {
    padding: 15px;
}

.campaign-info {
    margin-bottom: 15px;
}

.campaign-dates {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 14px;
    margin-bottom: 5px;
}

.campaign-dates i {
    margin-right: 5px;
}

.campaign-stats {
    display: flex;
    gap: 15px;
    margin-top: 15px;
}

.stat {
    flex: 1;
    text-align: center;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
}

.stat-value {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #666;
}

.campaign-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .action-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .filter-group {
        flex-wrap: wrap;
    }
    
    .form-row {
        flex-direction: column;
        gap: 20px;
    }
    
    .modal-dialog {
        width: 95%;
        margin: 20px auto;
    }
    
    .campaign-grid {
        grid-template-columns: 1fr;
    }
} 