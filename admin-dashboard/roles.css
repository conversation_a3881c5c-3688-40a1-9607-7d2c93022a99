.permission-tree {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 1rem;
}

.permission-tree ul {
    list-style: none;
    padding-left: 1.5rem;
}

.permission-tree > ul {
    padding-left: 0;
}

.permission-tree li {
    margin: 0.5rem 0;
}

.permission-group {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.permission-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.permission-item .form-check {
    margin: 0;
}

.table td {
    vertical-align: middle;
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.role-actions {
    white-space: nowrap;
}

.role-actions .btn {
    margin-right: 0.25rem;
}

.role-actions .btn:last-child {
    margin-right: 0;
}

.modal-lg {
    max-width: 800px;
}

@media (max-width: 768px) {
    .permission-tree {
        max-height: 300px;
    }
    
    .table-responsive {
        margin-bottom: 1rem;
    }
    
    .role-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
    }
    
    .role-actions .btn {
        margin-right: 0;
    }
}

/* 权限树的展开/折叠图标 */
.permission-group .toggle-icon {
    cursor: pointer;
    margin-right: 0.5rem;
}

/* 权限项的悬停效果 */
.permission-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 0.25rem;
}

/* 权限描述的提示框样式 */
.permission-description {
    color: #6c757d;
    font-size: 0.875rem;
    margin-left: 1.75rem;
}

/* 权限组的折叠动画 */
.permission-group-content {
    transition: max-height 0.3s ease-out;
    overflow: hidden;
}

/* 禁用状态的样式 */
.permission-item.disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

/* 搜索框样式 */
.permission-search {
    margin-bottom: 1rem;
}

.permission-search input {
    width: 100%;
    padding: 0.375rem 0.75rem;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
}

/* 权限统计标签 */
.permission-count {
    font-size: 0.875rem;
    color: #6c757d;
    margin-left: 0.5rem;
}

/* 角色卡片样式（用于小屏幕） */
@media (max-width: 768px) {
    .role-card {
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .role-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;
    }

    .role-card-title {
        font-weight: 500;
        margin: 0;
    }

    .role-card-body {
        margin-bottom: 0.5rem;
    }

    .role-card-footer {
        display: flex;
        justify-content: flex-end;
        gap: 0.5rem;
    }
} 