/* 统计卡片样式 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
}

.stat-icon i {
    font-size: 24px;
    color: white;
}

.stat-icon:nth-child(1) {
    background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
}

.stat-icon:nth-child(2) {
    background: linear-gradient(135deg, #4ECDC4, #45B7AF);
}

.stat-icon:nth-child(3) {
    background: linear-gradient(135deg, #FFD93D, #FFE566);
}

.stat-icon:nth-child(4) {
    background: linear-gradient(135deg, #6C5CE7, #8983F7);
}

.stat-info {
    flex: 1;
}

.stat-info h3 {
    margin: 0 0 5px 0;
    font-size: 14px;
    color: #666;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

/* 用户列表部分 */
.users-section {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h2 {
    margin: 0;
    font-size: 20px;
    color: #333;
}

.header-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.filters {
    display: flex;
    gap: 10px;
}

.filters select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    color: #666;
}

/* 表格样式 */
.table-container {
    overflow-x: auto;
    margin-bottom: 20px;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background: #f8f9fa;
    color: #666;
    font-weight: 600;
}

.data-table tbody tr:hover {
    background: #f8f9fa;
}

.data-table td:last-child {
    white-space: nowrap;
}

/* 状态标签 */
.status-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-active {
    background: #E3F2FD;
    color: #2196F3;
}

.status-inactive {
    background: #FFF3E0;
    color: #FF9800;
}

.status-blocked {
    background: #FFEBEE;
    color: #F44336;
}

/* 分页控件 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-page {
    width: 32px;
    height: 32px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.btn-page:hover {
    background: #f8f9fa;
}

.btn-page:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

#page-numbers {
    display: flex;
    gap: 5px;
}

.page-number {
    width: 32px;
    height: 32px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.page-number.active {
    background: #2196F3;
    color: white;
    border-color: #2196F3;
}

.page-size {
    margin-left: 20px;
}

.page-size select {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #666;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    position: relative;
    background: white;
    margin: 50px auto;
    padding: 20px;
    width: 90%;
    max-width: 600px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.modal-header h3 {
    margin: 0;
    color: #333;
}

.close-modal {
    background: none;
    border: none;
    font-size: 24px;
    color: #666;
    cursor: pointer;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #666;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.form-group small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 12px;
}

.modal-footer {
    margin-top: 20px;
    text-align: right;
}

/* 用户详情标签页 */
.user-detail-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.tab-btn {
    padding: 8px 16px;
    border: none;
    background: none;
    color: #666;
    cursor: pointer;
    font-weight: 500;
}

.tab-btn.active {
    color: #2196F3;
    border-bottom: 2px solid #2196F3;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 批量操作模态框 */
.batch-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.batch-options button {
    width: 100%;
    padding: 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.btn-warning {
    background: #FFF3E0;
    color: #FF9800;
}

.btn-danger {
    background: #FFEBEE;
    color: #F44336;
}

.btn-success {
    background: #E8F5E9;
    color: #4CAF50;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .stats-cards {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .header-actions {
        flex-direction: column;
    }

    .filters {
        flex-direction: column;
    }

    .filters select {
        width: 100%;
    }

    .modal-content {
        width: 95%;
        margin: 20px auto;
    }

    .user-detail-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        text-align: center;
        padding: 8px;
    }
} 

.table {
    margin-bottom: 1rem;
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    color: #495057;
    font-weight: 500;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.user-status {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.user-status.active {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.user-status.inactive {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.user-status.blocked {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.action-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    margin-right: 0.5rem;
}

.action-btn:last-child {
    margin-right: 0;
}

.search-filter-row {
    margin-bottom: 1.5rem;
}

.input-group-text {
    background-color: #f8f9fa;
    border-color: #dee2e6;
}

.form-control,
.form-select {
    border-color: #dee2e6;
}

.form-control:focus,
.form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    padding: 0.5rem 0.75rem;
    color: #0d6efd;
    background-color: #fff;
    border: 1px solid #dee2e6;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.pagination-info {
    color: #6c757d;
    font-size: 0.875rem;
}

.pagination-size {
    width: 120px;
}

.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

.daterangepicker {
    border: 1px solid #dee2e6;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.daterangepicker .calendar-table {
    border: none;
}

.daterangepicker td.active {
    background-color: #0d6efd;
}

.daterangepicker td.active:hover {
    background-color: #0b5ed7;
}

@media (max-width: 768px) {
    .search-filter-row > div {
        margin-bottom: 1rem;
    }
    
    .search-filter-row > div:last-child {
        margin-bottom: 0;
    }
    
    .pagination-wrapper {
        flex-direction: column;
        align-items: center;
    }
    
    .pagination-info,
    .pagination,
    .pagination-size {
        margin-bottom: 1rem;
    }
    
    .pagination-size {
        width: 100%;
    }
    
    .table-responsive {
        margin-bottom: 1rem;
        border: 0;
    }
    
    .action-btn {
        padding: 0.375rem 0.75rem;
        font-size: 1rem;
        margin-bottom: 0.5rem;
        width: 100%;
    }
    
    .action-btn:last-child {
        margin-bottom: 0;
    }
} 