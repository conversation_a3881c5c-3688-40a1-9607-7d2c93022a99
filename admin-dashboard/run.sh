#!/bin/bash

# 一键运行脚本 - 最简单的启动方式
echo "🚀 一键启动 WriterPro 管理后台"

# 进入正确目录
[ ! -f "index.html" ] && [ -d "admin-dashboard" ] && cd admin-dashboard

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ 需要安装Node.js"
    echo "请访问 https://nodejs.org 下载安装"
    exit 1
fi

# 停止端口占用
lsof -ti:3000 | xargs kill -9 2>/dev/null || true

# 启动服务器
echo "✅ 启动服务器到 http://localhost:3000/ad"
echo "👤 登录: admin / admin123 / 101010"
echo

# 自动打开浏览器
(sleep 2 && open "http://localhost:3000/ad" 2>/dev/null || xdg-open "http://localhost:3000/ad" 2>/dev/null) &

# 创建并运行服务器
node -e "
const http=require('http'),fs=require('fs'),path=require('path');
http.createServer((req,res)=>{
const u=req.url;
if(u==='/'){res.writeHead(200,{'Content-Type':'text/html'});res.end(\`<!DOCTYPE html><html><head><title>主网站</title><style>body{font-family:Arial;margin:40px;background:#f5f5f5}.container{max-width:600px;margin:0 auto;background:white;padding:30px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1)}.btn{display:inline-block;padding:15px 30px;background:#007bff;color:white;text-decoration:none;border-radius:5px;margin:10px 0}.btn:hover{background:#0056b3}.info{background:#e7f3ff;padding:15px;border-radius:5px;margin:20px 0}</style></head><body><div class=\"container\"><h1>🎉 主网站</h1><p>管理后台已集成到 /ad 路径</p><a href=\"/ad\" class=\"btn\">🔧 进入管理后台</a><div class=\"info\"><h3>登录信息</h3><p><strong>地址:</strong> http://localhost:3000/ad</p><p><strong>用户名:</strong> admin</p><p><strong>密码:</strong> admin123</p><p><strong>验证码:</strong> 101010</p></div></div></body></html>\`);return}
if(u==='/ad'||u==='/ad/'){fs.readFile('index.html',(e,d)=>{if(e){res.writeHead(404);res.end('File not found')}else{res.writeHead(200,{'Content-Type':'text/html'});res.end(d)}});return}
if(u==='/ad/api/auth/login'&&req.method==='POST'){let b='';req.on('data',c=>b+=c);req.on('end',()=>{try{const {username,password}=JSON.parse(b);res.writeHead(200,{'Content-Type':'application/json'});if(username==='admin'&&password==='admin123'){res.end('{\"success\":true,\"requiresTwoFactor\":true,\"message\":\"请输入验证码\"}')}else{res.end('{\"success\":false,\"message\":\"用户名或密码错误\"}')}}catch(e){res.writeHead(400,{'Content-Type':'application/json'});res.end('{\"success\":false,\"message\":\"请求错误\"}')}});return}
if(u==='/ad/api/auth/verify-2fa'&&req.method==='POST'){let b='';req.on('data',c=>b+=c);req.on('end',()=>{try{const {code}=JSON.parse(b);res.writeHead(200,{'Content-Type':'application/json'});if(code==='101010'){res.end('{\"success\":true,\"user\":{\"id\":\"1\",\"username\":\"admin\",\"name\":\"管理员\",\"role\":\"admin\"}}')}else{res.end('{\"success\":false,\"message\":\"验证码错误\"}')}}catch(e){res.writeHead(400,{'Content-Type':'application/json'});res.end('{\"success\":false,\"message\":\"请求错误\"}')}});return}
if(u.startsWith('/ad/')){const f=u.substring(4)||'index.html';const pages={'dashboard':'dashboard.html','users':'users.html','agents':'agents.html','commission':'commission.html','pricing':'pricing.html','roles':'roles.html','finance':'finance.html','analytics':'analytics.html','reports':'reports.html','settings':'settings.html','content':'content.html','api-docs':'api-docs.html','marketing':'marketing.html','security':'security.html','monitor':'monitor.html','logs':'logs.html','tasks':'tasks.html','notifications':'notifications.html','backup':'backup.html'};const af=pages[f]||f;fs.readFile(af,(e,d)=>{if(e){res.writeHead(404);res.end('File not found')}else{const ext=path.extname(af);const ct={'.html':'text/html','.js':'text/javascript','.css':'text/css','.json':'application/json'}[ext]||'text/plain';res.writeHead(200,{'Content-Type':ct});res.end(d)}});return}
res.writeHead(404);res.end('Page not found')
}).listen(3000,()=>{console.log('========================================');console.log('🚀 服务器运行在端口 3000');console.log('📱 主网站: http://localhost:3000');console.log('🔧 管理后台: http://localhost:3000/ad');console.log('👤 登录: admin / admin123 / 101010');console.log('========================================')})
"
