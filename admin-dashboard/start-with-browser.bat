@echo off
echo ========================================
echo    WriterPro 管理后台一键启动
echo ========================================
echo.

:: 检查Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 请先安装Python
    pause
    exit /b 1
)

:: 检查文件
if not exist "index.html" (
    echo [错误] 请在admin-dashboard目录中运行
    pause
    exit /b 1
)

:: 设置端口
set PORT=3000
netstat -an | find "3000" >nul
if %errorlevel% equ 0 (
    set PORT=8080
)

echo [信息] 启动服务器端口: %PORT%
echo [信息] 访问地址: http://localhost:%PORT%
echo.

:: 启动服务器并自动打开浏览器
echo [信息] 正在启动服务器...
start "" "http://localhost:%PORT%"
python -m http.server %PORT%
