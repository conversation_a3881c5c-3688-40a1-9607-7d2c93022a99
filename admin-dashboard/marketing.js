// 全局变量
let currentTab = 'coupons';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 初始化选项卡
    initTabs();
    
    // 绑定事件处理器
    bindEventHandlers();
});

// 初始化选项卡
function initTabs() {
    const tabs = document.querySelectorAll('.tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // 移除所有选项卡的活动状态
            tabs.forEach(t => t.classList.remove('active'));
            
            // 设置当前选项卡为活动状态
            tab.classList.add('active');
            
            // 获取选项卡对应的内容ID
            const tabId = tab.dataset.tab;
            currentTab = tabId;
            
            // 隐藏所有选项卡内容
            const tabPanes = document.querySelectorAll('.tab-pane');
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            // 显示当前选项卡内容
            document.getElementById(tabId).classList.add('active');
        });
    });
}

// 绑定事件处理器
function bindEventHandlers() {
    // 创建优惠券按钮
    const createCouponBtn = document.getElementById('createCouponBtn');
    if (createCouponBtn) {
        createCouponBtn.addEventListener('click', () => {
            openCouponModal();
        });
    }
    
    // 关闭模态框按钮
    const closeModal = document.getElementById('closeModal');
    if (closeModal) {
        closeModal.addEventListener('click', () => {
            closeCouponModal();
        });
    }
    
    // 取消按钮
    const cancelBtn = document.getElementById('cancelBtn');
    if (cancelBtn) {
        cancelBtn.addEventListener('click', () => {
            closeCouponModal();
        });
    }
    
    // 保存优惠券按钮
    const saveCouponBtn = document.getElementById('saveCouponBtn');
    if (saveCouponBtn) {
        saveCouponBtn.addEventListener('click', () => {
            saveCoupon();
        });
    }
    
    // 生成优惠码按钮
    const generateCodeBtn = document.getElementById('generateCodeBtn');
    if (generateCodeBtn) {
        generateCodeBtn.addEventListener('click', () => {
            generateCouponCode();
        });
    }
    
    // 优惠类型变更
    const couponType = document.getElementById('couponType');
    if (couponType) {
        couponType.addEventListener('change', () => {
            updateCouponValueSuffix();
        });
    }
    
    // 模态框背景点击关闭
    const modalBackdrop = document.querySelector('.modal-backdrop');
    if (modalBackdrop) {
        modalBackdrop.addEventListener('click', () => {
            closeCouponModal();
        });
    }
}

// 打开优惠券模态框
function openCouponModal() {
    const modal = document.getElementById('couponModal');
    if (modal) {
        // 重置表单
        const form = modal.querySelector('form');
        if (form) form.reset();
        
        // 设置默认日期
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');
        
        if (startDate && endDate) {
            const today = new Date();
            const nextMonth = new Date();
            nextMonth.setMonth(today.getMonth() + 1);
            
            startDate.valueAsDate = today;
            endDate.valueAsDate = nextMonth;
        }
        
        // 显示模态框
        modal.classList.add('active');
    }
}

// 关闭优惠券模态框
function closeCouponModal() {
    const modal = document.getElementById('couponModal');
    if (modal) {
        modal.classList.remove('active');
    }
}

// 保存优惠券
function saveCoupon() {
    // 获取表单数据
    const couponCode = document.getElementById('couponCode').value;
    const couponName = document.getElementById('couponName').value;
    const couponType = document.getElementById('couponType').value;
    const couponValue = document.getElementById('couponValue').value;
    const couponLimit = document.getElementById('couponLimit').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const couponAmount = document.getElementById('couponAmount').value;
    const couponDescription = document.getElementById('couponDescription').value;
    
    // 验证表单
    if (!couponCode || !couponName || !couponValue) {
        alert('请填写必填字段：优惠码、名称和优惠力度');
        return;
    }
    
    // 模拟保存优惠券
    console.log('保存优惠券:', {
        code: couponCode,
        name: couponName,
        type: couponType,
        value: couponValue,
        limit: couponLimit,
        startDate,
        endDate,
        amount: couponAmount,
        description: couponDescription
    });
    
    // 关闭模态框
    closeCouponModal();
    
    // 显示成功消息
    alert(`优惠券 "${couponCode}" 已创建`);
    
    // 刷新页面或更新UI
    location.reload();
}

// 生成随机优惠码
function generateCouponCode() {
    const couponCode = document.getElementById('couponCode');
    if (couponCode) {
        // 生成8位随机字母数字组合
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let code = '';
        
        for (let i = 0; i < 8; i++) {
            const randomIndex = Math.floor(Math.random() * chars.length);
            code += chars.charAt(randomIndex);
        }
        
        couponCode.value = code;
    }
}

// 更新优惠值后缀
function updateCouponValueSuffix() {
    const couponType = document.getElementById('couponType');
    const inputSuffix = document.querySelector('.input-suffix .suffix');
    
    if (couponType && inputSuffix) {
        const type = couponType.value;
        
        if (type === 'percentage') {
            inputSuffix.textContent = '%';
        } else if (type === 'fixed') {
            inputSuffix.textContent = '¥';
        } else {
            inputSuffix.textContent = '';
        }
    }
}

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
} 