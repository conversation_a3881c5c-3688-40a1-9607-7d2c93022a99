<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务管理 - WriterPro管理后台</title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/ui-enhancements.css">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- 引入Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .task-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
            background-color: #ffffff;
            transition: all 0.2s ease;
        }
        .task-card:hover {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-color: #0d6efd;
        }
        .task-card.running {
            border-left: 4px solid #198754;
            background-color: #f8fff9;
        }
        .task-card.failed {
            border-left: 4px solid #dc3545;
            background-color: #fff8f8;
        }
        .task-card.pending {
            border-left: 4px solid #ffc107;
            background-color: #fffdf5;
        }
        .task-card.completed {
            border-left: 4px solid #6c757d;
            background-color: #f8f9fa;
        }
        .task-status {
            font-size: 0.875rem;
            font-weight: 500;
        }
        .task-progress {
            height: 0.5rem;
            border-radius: 0.25rem;
        }
        .cron-input {
            font-family: 'Courier New', monospace;
        }
        .task-log {
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 0.75rem;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="logo">
                <h1>WriterPro</h1>
                <span>管理后台</span>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li><a href="dashboard.html"><i class="bi bi-speedometer2"></i><span data-i18n="common.dashboard">仪表盘</span></a></li>
                    <li><a href="users.html"><i class="bi bi-people"></i><span data-i18n="common.users">用户管理</span></a></li>
                    <li><a href="agents.html"><i class="bi bi-person-badge"></i><span data-i18n="common.agents">代理商管理</span></a></li>
                    <li><a href="roles.html"><i class="bi bi-shield-check"></i><span data-i18n="common.roles">角色管理</span></a></li>
                    <li><a href="finance.html"><i class="bi bi-currency-dollar"></i><span data-i18n="common.finance">财务管理</span></a></li>
                    <li><a href="settings.html"><i class="bi bi-gear"></i><span data-i18n="common.settings">系统设置</span></a></li>
                    <li><a href="content.html"><i class="bi bi-file-text"></i><span data-i18n="common.content">内容管理</span></a></li>
                    <li><a href="api.html"><i class="bi bi-code-slash"></i><span data-i18n="common.api">API配置</span></a></li>
                    <li><a href="marketing.html"><i class="bi bi-megaphone"></i><span data-i18n="common.marketing">营销工具</span></a></li>
                    <li><a href="security.html"><i class="bi bi-shield-lock"></i><span data-i18n="common.security">安全中心</span></a></li>
                    <li><a href="monitor.html"><i class="bi bi-activity"></i><span data-i18n="common.monitor">系统监控</span></a></li>
                    <li><a href="analytics.html"><i class="bi bi-graph-up"></i><span>数据分析</span></a></li>
                    <li><a href="reports.html"><i class="bi bi-file-earmark-text"></i><span>报表中心</span></a></li>
                    <li><a href="logs.html"><i class="bi bi-journal-text"></i><span>系统日志</span></a></li>
                    <li class="active"><a href="tasks.html"><i class="bi bi-list-task"></i><span>任务管理</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 头部 -->
            <header class="main-header">
                <div class="header-left">
                    <button class="menu-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <div class="header-title">
                        <h2>任务管理</h2>
                        <p>系统任务和定时任务管理</p>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-actions">
                        <button class="action-btn theme-toggle" id="themeToggle" title="切换主题">
                            <i class="bi bi-moon"></i>
                        </button>
                        
                        <button class="action-btn">
                            <i class="bi bi-bell"></i>
                            <span class="badge">5</span>
                        </button>
                        
                        <div class="user-menu">
                            <button class="user-btn">
                                <div class="user-avatar">A</div>
                                <span>管理员</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <div class="user-dropdown">
                                <a href="#"><i class="bi bi-person"></i>个人资料</a>
                                <a href="#"><i class="bi bi-gear"></i>设置</a>
                                <a href="index.html"><i class="bi bi-box-arrow-right"></i>退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 任务统计 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-primary">
                                <i class="bi bi-list-task"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="totalTasks">0</h3>
                                <p>总任务数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-success">
                                <i class="bi bi-play-circle"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="runningTasks">0</h3>
                                <p>运行中</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-warning">
                                <i class="bi bi-clock"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="pendingTasks">0</h3>
                                <p>等待中</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-danger">
                                <i class="bi bi-x-circle"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="failedTasks">0</h3>
                                <p>失败</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 任务控制面板 -->
                <div class="card mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">任务控制面板</h5>
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary" id="createTaskBtn">
                                <i class="bi bi-plus"></i> 创建任务
                            </button>
                            <button class="btn btn-outline-secondary" id="refreshTasksBtn">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">任务状态</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">全部状态</option>
                                    <option value="running">运行中</option>
                                    <option value="pending">等待中</option>
                                    <option value="completed">已完成</option>
                                    <option value="failed">失败</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">任务类型</label>
                                <select class="form-select" id="typeFilter">
                                    <option value="">全部类型</option>
                                    <option value="scheduled">定时任务</option>
                                    <option value="manual">手动任务</option>
                                    <option value="system">系统任务</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">搜索任务</label>
                                <input type="text" class="form-control" id="taskSearch" placeholder="搜索任务名称或描述...">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-outline-primary w-100" id="searchTasksBtn">
                                    <i class="bi bi-search"></i> 搜索
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 任务列表 -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">任务列表</h5>
                            </div>
                            <div class="card-body">
                                <div id="tasksList">
                                    <!-- 任务卡片将在这里动态生成 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 创建/编辑任务模态框 -->
    <div class="modal fade" id="taskModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="taskModalTitle">创建任务</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="taskForm">
                        <input type="hidden" id="taskId">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">任务名称</label>
                                <input type="text" class="form-control" id="taskName" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">任务类型</label>
                                <select class="form-select" id="taskType" required>
                                    <option value="">请选择</option>
                                    <option value="scheduled">定时任务</option>
                                    <option value="manual">手动任务</option>
                                    <option value="system">系统任务</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <label class="form-label">任务描述</label>
                            <textarea class="form-control" id="taskDescription" rows="3"></textarea>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">执行命令/脚本</label>
                                <input type="text" class="form-control" id="taskCommand" placeholder="例如: /usr/bin/python3 /path/to/script.py">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">优先级</label>
                                <select class="form-select" id="taskPriority">
                                    <option value="low">低</option>
                                    <option value="normal" selected>普通</option>
                                    <option value="high">高</option>
                                    <option value="urgent">紧急</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mt-3" id="cronSection" style="display: none;">
                            <div class="col-md-8">
                                <label class="form-label">Cron表达式</label>
                                <input type="text" class="form-control cron-input" id="cronExpression" placeholder="0 0 * * *">
                                <small class="form-text text-muted">格式: 分 时 日 月 周 (例如: 0 2 * * * 表示每天凌晨2点)</small>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">时区</label>
                                <select class="form-select" id="taskTimezone">
                                    <option value="Asia/Shanghai">Asia/Shanghai</option>
                                    <option value="UTC">UTC</option>
                                    <option value="America/New_York">America/New_York</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">超时时间(秒)</label>
                                <input type="number" class="form-control" id="taskTimeout" value="300" min="1">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">最大重试次数</label>
                                <input type="number" class="form-control" id="taskRetries" value="3" min="0">
                            </div>
                        </div>
                        
                        <div class="mt-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="taskEnabled" checked>
                                <label class="form-check-label" for="taskEnabled">启用任务</label>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveTaskBtn">保存任务</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务详情模态框 -->
    <div class="modal fade" id="taskDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">任务详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>基本信息</h6>
                            <table class="table table-sm">
                                <tr><td><strong>任务名称:</strong></td><td id="detailTaskName"></td></tr>
                                <tr><td><strong>任务类型:</strong></td><td id="detailTaskType"></td></tr>
                                <tr><td><strong>状态:</strong></td><td id="detailTaskStatus"></td></tr>
                                <tr><td><strong>优先级:</strong></td><td id="detailTaskPriority"></td></tr>
                                <tr><td><strong>创建时间:</strong></td><td id="detailTaskCreated"></td></tr>
                                <tr><td><strong>最后执行:</strong></td><td id="detailTaskLastRun"></td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>执行信息</h6>
                            <table class="table table-sm">
                                <tr><td><strong>执行命令:</strong></td><td id="detailTaskCommand"></td></tr>
                                <tr><td><strong>Cron表达式:</strong></td><td id="detailTaskCron"></td></tr>
                                <tr><td><strong>执行次数:</strong></td><td id="detailTaskRuns"></td></tr>
                                <tr><td><strong>成功次数:</strong></td><td id="detailTaskSuccess"></td></tr>
                                <tr><td><strong>失败次数:</strong></td><td id="detailTaskFailed"></td></tr>
                                <tr><td><strong>平均耗时:</strong></td><td id="detailTaskAvgTime"></td></tr>
                            </table>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h6>任务描述</h6>
                        <p id="detailTaskDescription"></p>
                    </div>
                    
                    <div class="mt-4">
                        <h6>执行日志</h6>
                        <div class="task-log" id="detailTaskLog">
                            <!-- 任务日志将在这里显示 -->
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="runTaskNowBtn">立即执行</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/bilingual-init.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/language-switcher.js"></script>
    <script src="js/mobile-nav.js"></script>
    <script src="js/ui-enhancements.js"></script>
    <script src="js/tasks.js"></script>
</body>
</html>
