// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalPages = 1;
let users = [];
let selectedUsers = new Set();
let batchOperations = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 初始化批量操作
    initializeBatchOperations();
    // 初始化事件监听
    initializeEventListeners();
    // 加载用户数据
    loadUsers();
    // 加载统计数据
    loadStatistics();
    // 加载用户分析数据
    loadUserAnalytics();
    // 初始化分析图表
    initAnalyticsCharts();
});

// 初始化批量操作
function initializeBatchOperations() {
    // 确保批量操作组件已加载
    if (typeof BatchOperations === 'undefined') {
        console.warn('BatchOperations component not loaded');
        return;
    }

    batchOperations = new BatchOperations({
        tableSelector: '#userTableBody',
        checkboxSelector: '.user-checkbox',
        selectAllSelector: '.select-all',
        batchActionsSelector: '#batchActions',
        onSelectionChange: handleSelectionChange,
        onBatchAction: handleBatchAction
    });

    // 设置批量操作选项
    batchOperations.setBatchActions([
        { key: 'activate', label: '批量激活', icon: 'check-circle' },
        { key: 'deactivate', label: '批量禁用', icon: 'x-circle' },
        { key: 'delete', label: '批量删除', icon: 'trash' },
        { key: 'export', label: '导出选中', icon: 'download' },
        { key: 'resetPassword', label: '重置密码', icon: 'key' },
        { key: 'sendEmail', label: '发送邮件', icon: 'envelope' }
    ]);
}

// 处理选择变化
function handleSelectionChange(selectedIds) {
    selectedUsers = new Set(selectedIds);
    console.log('Selected users:', selectedIds);
}

// 处理批量操作
function handleBatchAction(actionKey, selectedIds) {
    switch (actionKey) {
        case 'activate':
            batchActivateUsers(selectedIds);
            break;
        case 'deactivate':
            batchDeactivateUsers(selectedIds);
            break;
        case 'delete':
            batchDeleteUsers(selectedIds);
            break;
        case 'export':
            exportSelectedUsers(selectedIds);
            break;
        case 'resetPassword':
            batchResetPassword(selectedIds);
            break;
        case 'sendEmail':
            showBatchEmailModal(selectedIds);
            break;
        default:
            console.warn('Unknown batch action:', actionKey);
    }
}

// 批量激活用户
function batchActivateUsers(userIds) {
    batchOperations.showBatchConfirmDialog(
        '激活用户',
        '确定要激活选中的用户吗？',
        async () => {
            try {
                let successCount = 0;

                for (let i = 0; i < userIds.length; i++) {
                    batchOperations.showBatchProgress(userIds.length, i + 1, '正在激活用户...');

                    // 模拟API调用
                    await new Promise(resolve => setTimeout(resolve, 200));

                    const user = users.find(u => u.id === userIds[i]);
                    if (user) {
                        user.status = 'active';
                        successCount++;
                    }
                }

                loadUsers(); // 重新加载用户列表
                batchOperations.clearSelection();
                batchOperations.showNotification(`成功激活 ${successCount} 个用户`, 'success');
            } catch (error) {
                console.error('批量激活用户失败:', error);
                batchOperations.showNotification('批量激活失败', 'error');
            }
        }
    );
}

// 批量禁用用户
function batchDeactivateUsers(userIds) {
    batchOperations.showBatchConfirmDialog(
        '禁用用户',
        '确定要禁用选中的用户吗？禁用后用户将无法登录系统。',
        async () => {
            try {
                let successCount = 0;

                for (let i = 0; i < userIds.length; i++) {
                    batchOperations.showBatchProgress(userIds.length, i + 1, '正在禁用用户...');

                    // 模拟API调用
                    await new Promise(resolve => setTimeout(resolve, 200));

                    const user = users.find(u => u.id === userIds[i]);
                    if (user) {
                        user.status = 'inactive';
                        successCount++;
                    }
                }

                loadUsers(); // 重新加载用户列表
                batchOperations.clearSelection();
                batchOperations.showNotification(`成功禁用 ${successCount} 个用户`, 'success');
            } catch (error) {
                console.error('批量禁用用户失败:', error);
                batchOperations.showNotification('批量禁用失败', 'error');
            }
        }
    );
}

// 批量删除用户
function batchDeleteUsers(userIds) {
    batchOperations.showBatchConfirmDialog(
        '删除用户',
        '确定要删除选中的用户吗？此操作不可撤销！',
        async () => {
            try {
                let successCount = 0;

                for (let i = 0; i < userIds.length; i++) {
                    batchOperations.showBatchProgress(userIds.length, i + 1, '正在删除用户...');

                    // 模拟API调用
                    await new Promise(resolve => setTimeout(resolve, 300));

                    const userIndex = users.findIndex(u => u.id === userIds[i]);
                    if (userIndex !== -1) {
                        users.splice(userIndex, 1);
                        successCount++;
                    }
                }

                loadUsers(); // 重新加载用户列表
                batchOperations.clearSelection();
                batchOperations.showNotification(`成功删除 ${successCount} 个用户`, 'success');
            } catch (error) {
                console.error('批量删除用户失败:', error);
                batchOperations.showNotification('批量删除失败', 'error');
            }
        }
    );
}

// 导出选中用户
function exportSelectedUsers(userIds) {
    try {
        const selectedUserData = users.filter(u => userIds.includes(u.id));

        // 创建CSV内容
        const csvContent = [
            ['ID', '用户名', '邮箱', '状态', '注册时间'].join(','),
            ...selectedUserData.map(user => [
                user.id,
                user.username,
                user.email,
                user.status,
                user.createdAt
            ].join(','))
        ].join('\n');

        // 创建下载链接
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `users_export_${new Date().toISOString().split('T')[0]}.csv`;
        link.click();

        batchOperations.showNotification(`成功导出 ${selectedUserData.length} 个用户数据`, 'success');
    } catch (error) {
        console.error('导出用户数据失败:', error);
        batchOperations.showNotification('导出失败', 'error');
    }
}

// 批量重置密码
function batchResetPassword(userIds) {
    batchOperations.showBatchConfirmDialog(
        '重置密码',
        '确定要重置选中用户的密码吗？新密码将通过邮件发送给用户。',
        async () => {
            try {
                let successCount = 0;

                for (let i = 0; i < userIds.length; i++) {
                    batchOperations.showBatchProgress(userIds.length, i + 1, '正在重置密码...');

                    // 模拟API调用
                    await new Promise(resolve => setTimeout(resolve, 500));

                    const user = users.find(u => u.id === userIds[i]);
                    if (user) {
                        // 模拟密码重置
                        successCount++;
                    }
                }

                batchOperations.clearSelection();
                batchOperations.showNotification(`成功重置 ${successCount} 个用户密码`, 'success');
            } catch (error) {
                console.error('批量重置密码失败:', error);
                batchOperations.showNotification('批量重置密码失败', 'error');
            }
        }
    );
}

// 初始化事件监听器
function initializeEventListeners() {
    // 搜索框
    document.getElementById('search-input').addEventListener('input', debounce(handleSearch, 300));

    // 筛选器
    document.getElementById('role-filter').addEventListener('change', handleFilters);
    document.getElementById('status-filter').addEventListener('change', handleFilters);
    document.getElementById('sort-by').addEventListener('change', handleFilters);

    // 全选checkbox
    document.getElementById('select-all').addEventListener('change', handleSelectAll);

    // 关闭模态框按钮
    document.querySelectorAll('.close-modal').forEach(button => {
        button.addEventListener('click', () => {
            document.querySelectorAll('.modal').forEach(modal => {
                modal.style.display = 'none';
            });
        });
    });
}

// 加载用户数据
async function loadUsers() {
    try {
        const response = await fetch('/api/users?' + new URLSearchParams({
            page: currentPage,
            pageSize: pageSize,
            role: document.getElementById('role-filter').value,
            status: document.getElementById('status-filter').value,
            sortBy: document.getElementById('sort-by').value,
            search: document.getElementById('search-input').value
        }));

        if (!response.ok) throw new Error('Failed to load users');

        const data = await response.json();
        users = data.users;
        totalPages = Math.ceil(data.total / pageSize);

        renderUsers();
        renderPagination();
    } catch (error) {
        showNotification('加载用户数据失败', 'error');
        console.error('Error loading users:', error);
    }
}

// 加载统计数据
async function loadStatistics() {
    try {
        const response = await fetch('/api/users/statistics');
        if (!response.ok) throw new Error('Failed to load statistics');

        const data = await response.json();
        
        document.getElementById('total-users').textContent = data.totalUsers;
        document.getElementById('new-users').textContent = data.newUsers;
        document.getElementById('active-users').textContent = data.activeUsers;
        document.getElementById('total-consumption').textContent = `¥${data.totalConsumption.toFixed(2)}`;
    } catch (error) {
        showNotification('加载统计数据失败', 'error');
        console.error('Error loading statistics:', error);
    }
}

// 渲染用户列表
function renderUsers() {
    const tbody = document.getElementById('users-table-body');
    tbody.innerHTML = '';

    users.forEach(user => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>
                <input type="checkbox" class="user-checkbox" value="${user.id}" 
                    ${selectedUsers.has(user.id) ? 'checked' : ''}>
            </td>
            <td>${user.id}</td>
            <td>${user.username}</td>
            <td>${user.email}</td>
            <td>${formatRole(user.role)}</td>
            <td>${formatDate(user.createdAt)}</td>
            <td>${formatDate(user.lastLoginAt)}</td>
            <td><span class="status-badge status-${user.status.toLowerCase()}">${formatStatus(user.status)}</span></td>
            <td>¥${user.totalConsumption.toFixed(2)}</td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="showUserDetail(${user.id})">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-warning" onclick="showEditUserModal(${user.id})">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.id})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(tr);
    });

    // 更新checkbox事件监听
    document.querySelectorAll('.user-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', handleUserSelect);
    });
}

// 渲染分页
function renderPagination() {
    const pageNumbers = document.getElementById('page-numbers');
    pageNumbers.innerHTML = '';

    // 计算显示的页码范围
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, currentPage + 2);

    // 添加第一页
    if (startPage > 1) {
        pageNumbers.appendChild(createPageButton(1));
        if (startPage > 2) {
            pageNumbers.appendChild(createEllipsis());
        }
    }

    // 添加中间页码
    for (let i = startPage; i <= endPage; i++) {
        pageNumbers.appendChild(createPageButton(i));
    }

    // 添加最后一页
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            pageNumbers.appendChild(createEllipsis());
        }
        pageNumbers.appendChild(createPageButton(totalPages));
    }

    // 更新上一页/下一页按钮状态
    document.querySelector('.btn-page:first-child').disabled = currentPage === 1;
    document.querySelector('.btn-page:last-child').disabled = currentPage === totalPages;
}

// 创建页码按钮
function createPageButton(pageNum) {
    const button = document.createElement('button');
    button.className = `page-number${pageNum === currentPage ? ' active' : ''}`;
    button.textContent = pageNum;
    button.onclick = () => changePage(pageNum);
    return button;
}

// 创建省略号
function createEllipsis() {
    const span = document.createElement('span');
    span.className = 'page-ellipsis';
    span.textContent = '...';
    return span;
}

// 切换页面
function changePage(page) {
    if (page === 'prev') {
        if (currentPage > 1) currentPage--;
    } else if (page === 'next') {
        if (currentPage < totalPages) currentPage++;
    } else {
        currentPage = page;
    }
    loadUsers();
}

// 改变每页显示数量
function changePageSize() {
    pageSize = parseInt(document.getElementById('page-size').value);
    currentPage = 1;
    loadUsers();
}

// 处理搜索
function handleSearch(event) {
    currentPage = 1;
    loadUsers();
}

// 处理筛选
function handleFilters() {
    currentPage = 1;
    loadUsers();
}

// 处理全选
function handleSelectAll(event) {
    const checked = event.target.checked;
    document.querySelectorAll('.user-checkbox').forEach(checkbox => {
        checkbox.checked = checked;
        const userId = parseInt(checkbox.value);
        if (checked) {
            selectedUsers.add(userId);
        } else {
            selectedUsers.delete(userId);
        }
    });
    updateBatchOperations();
}

// 处理单个用户选择
function handleUserSelect(event) {
    const userId = parseInt(event.target.value);
    if (event.target.checked) {
        selectedUsers.add(userId);
    } else {
        selectedUsers.delete(userId);
    }
    updateBatchOperations();
}

// 更新批量操作状态
function updateBatchOperations() {
    const hasSelected = selectedUsers.size > 0;
    document.querySelectorAll('.batch-options button').forEach(button => {
        button.disabled = !hasSelected;
    });
}

// 显示用户详情
async function showUserDetail(userId) {
    try {
        const response = await fetch(`/api/users/${userId}/detail`);
        if (!response.ok) throw new Error('Failed to load user details');

        const user = await response.json();
        
        // 加载基本信息
        document.getElementById('basic-info').innerHTML = `
            <div class="detail-grid">
                <div class="detail-item">
                    <label>用户名</label>
                    <span>${user.username}</span>
                </div>
                <div class="detail-item">
                    <label>邮箱</label>
                    <span>${user.email}</span>
                </div>
                <div class="detail-item">
                    <label>手机号</label>
                    <span>${user.phone || '未设置'}</span>
                </div>
                <div class="detail-item">
                    <label>注册时间</label>
                    <span>${formatDate(user.createdAt)}</span>
                </div>
                <div class="detail-item">
                    <label>最后登录</label>
                    <span>${formatDate(user.lastLoginAt)}</span>
                </div>
                <div class="detail-item">
                    <label>账户余额</label>
                    <span>¥${user.balance.toFixed(2)}</span>
                </div>
            </div>
        `;

        // 显示模态框
        document.getElementById('user-detail-modal').style.display = 'block';
        
        // 默认加载消费记录
        loadUserConsumption(userId);
    } catch (error) {
        showNotification('加载用户详情失败', 'error');
        console.error('Error loading user details:', error);
    }
}

// 加载用户消费记录
async function loadUserConsumption(userId) {
    try {
        const response = await fetch(`/api/users/${userId}/consumption`);
        if (!response.ok) throw new Error('Failed to load consumption history');

        const data = await response.json();
        
        document.getElementById('consumption-history').innerHTML = `
            <table class="data-table">
                <thead>
                    <tr>
                        <th>订单号</th>
                        <th>时间</th>
                        <th>金额</th>
                        <th>类型</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    ${data.map(item => `
                        <tr>
                            <td>${item.orderId}</td>
                            <td>${formatDate(item.createdAt)}</td>
                            <td>¥${item.amount.toFixed(2)}</td>
                            <td>${item.type}</td>
                            <td><span class="status-badge status-${item.status.toLowerCase()}">${item.status}</span></td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;
    } catch (error) {
        showNotification('加载消费记录失败', 'error');
        console.error('Error loading consumption history:', error);
    }
}

// 切换详情标签页
function switchTab(tabName) {
    // 更新标签页按钮状态
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[onclick="switchTab('${tabName}')"]`).classList.add('active');

    // 更新内容显示
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${tabName}-info`).classList.add('active');

    // 根据标签页加载数据
    const userId = getCurrentUserId();
    switch (tabName) {
        case 'consumption':
            loadUserConsumption(userId);
            break;
        case 'login':
            loadUserLoginHistory(userId);
            break;
        case 'operation':
            loadUserOperationHistory(userId);
            break;
    }
}

// 显示添加用户模态框
function showAddUserModal() {
    document.getElementById('modal-title').textContent = '添加用户';
    document.getElementById('user-form').reset();
    document.getElementById('user-modal').style.display = 'block';
}

// 显示编辑用户模态框
async function showEditUserModal(userId) {
    try {
        const response = await fetch(`/api/users/${userId}`);
        if (!response.ok) throw new Error('Failed to load user data');

        const user = await response.json();
        
        document.getElementById('modal-title').textContent = '编辑用户';
        document.getElementById('username').value = user.username;
        document.getElementById('email').value = user.email;
        document.getElementById('role').value = user.role;
        document.getElementById('status').value = user.status;
        
        // 清空密码字段
        document.getElementById('password').value = '';
        
        document.getElementById('user-modal').style.display = 'block';
    } catch (error) {
        showNotification('加载用户数据失败', 'error');
        console.error('Error loading user data:', error);
    }
}

// 保存用户
async function saveUser() {
    const form = document.getElementById('user-form');
    const formData = new FormData(form);
    const userId = getCurrentUserId();

    try {
        const response = await fetch(`/api/users${userId ? `/${userId}` : ''}`, {
            method: userId ? 'PUT' : 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(Object.fromEntries(formData))
        });

        if (!response.ok) throw new Error('Failed to save user');

        showNotification(userId ? '用户更新成功' : '用户创建成功', 'success');
        document.getElementById('user-modal').style.display = 'none';
        loadUsers();
    } catch (error) {
        showNotification('保存用户失败', 'error');
        console.error('Error saving user:', error);
    }
}

// 删除用户
async function deleteUser(userId) {
    if (!confirm('确定要删除这个用户吗？此操作不可撤销。')) return;

    try {
        const response = await fetch(`/api/users/${userId}`, {
            method: 'DELETE'
        });

        if (!response.ok) throw new Error('Failed to delete user');

        showNotification('用户删除成功', 'success');
        loadUsers();
    } catch (error) {
        showNotification('删除用户失败', 'error');
        console.error('Error deleting user:', error);
    }
}

// 批量操作
async function batchOperation(operation) {
    const userIds = Array.from(selectedUsers);
    if (!userIds.length) return;

    try {
        const response = await fetch('/api/users/batch', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                operation,
                userIds
            })
        });

        if (!response.ok) throw new Error('Failed to perform batch operation');

        showNotification('批量操作成功', 'success');
        document.getElementById('batch-modal').style.display = 'none';
        selectedUsers.clear();
        loadUsers();
    } catch (error) {
        showNotification('批量操作失败', 'error');
        console.error('Error performing batch operation:', error);
    }
}

// 导出用户数据
async function exportUsers() {
    const userIds = Array.from(selectedUsers);
    const queryParams = new URLSearchParams({
        role: document.getElementById('role-filter').value,
        status: document.getElementById('status-filter').value,
        search: document.getElementById('search-input').value
    });

    if (userIds.length) {
        queryParams.append('userIds', userIds.join(','));
    }

    try {
        const response = await fetch('/api/users/export?' + queryParams);
        if (!response.ok) throw new Error('Failed to export users');

        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'users.xlsx';
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        showNotification('用户数据导出成功', 'success');
    } catch (error) {
        showNotification('导出用户数据失败', 'error');
        console.error('Error exporting users:', error);
    }
}

// 工具函数
function formatDate(dateString) {
    if (!dateString) return '未知';
    return new Date(dateString).toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

function formatRole(role) {
    const roleMap = {
        'user': '普通用户',
        'vip': 'VIP用户',
        'agent': '代理'
    };
    return roleMap[role] || role;
}

function formatStatus(status) {
    const statusMap = {
        'active': '活跃',
        'inactive': '非活跃',
        'blocked': '已封禁'
    };
    return statusMap[status] || status;
}

function getCurrentUserId() {
    const modalTitle = document.getElementById('modal-title').textContent;
    return modalTitle.startsWith('编辑') ? parseInt(modalTitle.match(/\d+/)[0]) : null;
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showNotification(message, type = 'info') {
    // 实现一个简单的通知系统
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// 加载用户分析数据
async function loadUserAnalytics() {
    try {
        const token = localStorage.getItem('token');

        // 加载用户统计概览
        const statsResponse = await fetch('/api/analytics/user-stats', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (statsResponse.ok) {
            const statsData = await statsResponse.json();
            if (statsData.success) {
                updateUserStatsCards(statsData.data);
            }
        }

        // 加载用户行为数据
        const behaviorResponse = await fetch('/api/analytics/user-behavior', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (behaviorResponse.ok) {
            const behaviorData = await behaviorResponse.json();
            if (behaviorData.success) {
                updateBehaviorStats(behaviorData.data);
            }
        }

    } catch (error) {
        console.error('加载用户分析数据失败:', error);
        // 使用模拟数据
        updateUserStatsCards({
            totalUsers: 1250,
            newUsers: 85,
            activeUsers: 420,
            userGrowthRate: 12.5,
            retentionRate: 68.3
        });

        updateBehaviorStats({
            avgSessionTime: '25分钟',
            pageViews: 15420,
            bounceRate: '32.5%',
            conversionRate: '8.7%'
        });
    }
}

// 更新用户统计卡片
function updateUserStatsCards(data) {
    const elements = {
        'totalUsersCount': data.totalUsers?.toLocaleString() || '0',
        'activeUsersCount': data.activeUsers?.toLocaleString() || '0',
        'newUsersCount': data.newUsers?.toLocaleString() || '0',
        'retentionRate': (data.retentionRate || 0) + '%'
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });

    // 更新增长率显示
    const growthElement = document.getElementById('userGrowth');
    if (growthElement && data.userGrowthRate !== undefined) {
        growthElement.textContent = (data.userGrowthRate >= 0 ? '+' : '') + data.userGrowthRate + '%';
        growthElement.className = data.userGrowthRate >= 0 ? 'text-success' : 'text-danger';
    }
}

// 更新行为统计
function updateBehaviorStats(data) {
    const elements = {
        'avgSessionTime': data.avgSessionTime || '0分钟',
        'pageViews': typeof data.pageViews === 'number' ? data.pageViews.toLocaleString() : data.pageViews || '0',
        'bounceRate': data.bounceRate || '0%',
        'conversionRate': data.conversionRate || '0%'
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

// 初始化分析图表
function initAnalyticsCharts() {
    // 检查是否已加载ECharts
    if (typeof echarts === 'undefined') {
        console.warn('ECharts未加载，跳过图表初始化');
        return;
    }

    // 延迟初始化图表，确保DOM元素已渲染
    setTimeout(() => {
        initUserGrowthChart();
        initUserActivityChart();
        initRetentionChart();
        initUserDistributionChart();
        initBehaviorChart();
    }, 100);
}

// 初始化用户增长趋势图
function initUserGrowthChart() {
    const chartElement = document.getElementById('userGrowthChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'axis'
        },
        xAxis: {
            type: 'category',
            data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            data: [120, 200, 150, 80, 70, 110, 130, 180, 220, 160, 190, 250],
            type: 'line',
            smooth: true,
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                    { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
                ])
            },
            lineStyle: { color: '#1890ff' }
        }]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}

// 初始化用户活跃度图表
function initUserActivityChart() {
    const chartElement = document.getElementById('userActivityChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
            name: '活跃度',
            type: 'pie',
            radius: '60%',
            data: [
                { value: 420, name: '日活跃', itemStyle: { color: '#52c41a' } },
                { value: 280, name: '周活跃', itemStyle: { color: '#1890ff' } },
                { value: 180, name: '月活跃', itemStyle: { color: '#faad14' } },
                { value: 120, name: '不活跃', itemStyle: { color: '#ff4d4f' } }
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}

// 初始化留存分析图表
function initRetentionChart() {
    const chartElement = document.getElementById('retentionChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: ['1天留存', '7天留存', '30天留存']
        },
        xAxis: {
            type: 'category',
            data: ['第1周', '第2周', '第3周', '第4周', '第5周', '第6周', '第7周', '第8周']
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: '{value}%'
            }
        },
        series: [
            {
                name: '1天留存',
                type: 'line',
                data: [85, 82, 88, 90, 87, 85, 89, 91],
                lineStyle: { color: '#52c41a' }
            },
            {
                name: '7天留存',
                type: 'line',
                data: [65, 68, 72, 70, 69, 71, 74, 76],
                lineStyle: { color: '#1890ff' }
            },
            {
                name: '30天留存',
                type: 'line',
                data: [45, 48, 52, 50, 49, 51, 54, 56],
                lineStyle: { color: '#faad14' }
            }
        ]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}

// 初始化用户分布图表
function initUserDistributionChart() {
    const chartElement = document.getElementById('userDistributionChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'item'
        },
        series: [{
            name: '用户状态',
            type: 'pie',
            radius: ['40%', '70%'],
            data: [
                { value: 1050, name: '活跃用户', itemStyle: { color: '#52c41a' } },
                { value: 150, name: '未激活', itemStyle: { color: '#faad14' } },
                { value: 50, name: '已封禁', itemStyle: { color: '#ff4d4f' } }
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}

// 初始化行为分析图表
function initBehaviorChart() {
    const chartElement = document.getElementById('behaviorChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: ['页面访问', '功能使用', '文档创建']
        },
        xAxis: {
            type: 'category',
            data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '页面访问',
                type: 'bar',
                data: [2340, 1890, 2450, 2100, 2680, 1800, 1200],
                itemStyle: { color: '#1890ff' }
            },
            {
                name: '功能使用',
                type: 'bar',
                data: [1200, 980, 1350, 1100, 1480, 900, 600],
                itemStyle: { color: '#52c41a' }
            },
            {
                name: '文档创建',
                type: 'bar',
                data: [680, 520, 750, 620, 820, 450, 280],
                itemStyle: { color: '#faad14' }
            }
        ]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}

// 初始化用户活跃度图表
function initUserActivityChart() {
    const chartElement = document.getElementById('userActivityChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        series: [{
            name: '活跃度',
            type: 'pie',
            radius: '60%',
            data: [
                { value: 420, name: '日活跃', itemStyle: { color: '#52c41a' } },
                { value: 280, name: '周活跃', itemStyle: { color: '#1890ff' } },
                { value: 180, name: '月活跃', itemStyle: { color: '#faad14' } },
                { value: 120, name: '不活跃', itemStyle: { color: '#ff4d4f' } }
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}

// 初始化留存分析图表
function initRetentionChart() {
    const chartElement = document.getElementById('retentionChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: ['1天留存', '7天留存', '30天留存']
        },
        xAxis: {
            type: 'category',
            data: ['第1周', '第2周', '第3周', '第4周', '第5周', '第6周', '第7周', '第8周']
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: '{value}%'
            }
        },
        series: [
            {
                name: '1天留存',
                type: 'line',
                data: [85, 82, 88, 90, 87, 85, 89, 91],
                lineStyle: { color: '#52c41a' }
            },
            {
                name: '7天留存',
                type: 'line',
                data: [65, 68, 72, 70, 69, 71, 74, 76],
                lineStyle: { color: '#1890ff' }
            },
            {
                name: '30天留存',
                type: 'line',
                data: [45, 48, 52, 50, 49, 51, 54, 56],
                lineStyle: { color: '#faad14' }
            }
        ]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}

// 初始化用户分布图表
function initUserDistributionChart() {
    const chartElement = document.getElementById('userDistributionChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'item'
        },
        series: [{
            name: '用户状态',
            type: 'pie',
            radius: ['40%', '70%'],
            data: [
                { value: 1050, name: '活跃用户', itemStyle: { color: '#52c41a' } },
                { value: 150, name: '未激活', itemStyle: { color: '#faad14' } },
                { value: 50, name: '已封禁', itemStyle: { color: '#ff4d4f' } }
            ],
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}

// 初始化行为分析图表
function initBehaviorChart() {
    const chartElement = document.getElementById('behaviorChart');
    if (!chartElement) return;

    const chart = echarts.init(chartElement);
    const option = {
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: ['页面访问', '功能使用', '文档创建']
        },
        xAxis: {
            type: 'category',
            data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '页面访问',
                type: 'bar',
                data: [2340, 1890, 2450, 2100, 2680, 1800, 1200],
                itemStyle: { color: '#1890ff' }
            },
            {
                name: '功能使用',
                type: 'bar',
                data: [1200, 980, 1350, 1100, 1480, 900, 600],
                itemStyle: { color: '#52c41a' }
            },
            {
                name: '文档创建',
                type: 'bar',
                data: [680, 520, 750, 620, 820, 450, 280],
                itemStyle: { color: '#faad14' }
            }
        ]
    };

    chart.setOption(option);
    window.addEventListener('resize', () => chart.resize());
}