// 全局变量
let resourceChart, networkChart;
let cpuTrendChart, memoryTrendChart, userTrendChart, loadTrendChart;
let currentTimeRange = 24; // 默认24小时
let refreshInterval;
let alertConfig = null;
let alerts = [];
let performanceHistory = [];

// 页面加载完成后执行
$(document).ready(function() {
    // 检查登录状态和权限
    checkAuth();
    
    // 初始化图表
    initCharts();
    
    // 加载初始数据
    loadMonitorData();
    
    // 设置自动刷新
    startAutoRefresh();
    
    // 绑定事件处理器
    bindEventHandlers();
});

// 检查用户登录状态和权限
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = 'login.html';
        return;
    }
    
    // 检查是否有监控权限
    $.ajax({
        url: '/api/auth/check-permission',
        headers: { 'Authorization': `Bearer ${token}` },
        data: { permission: 'monitor.view' },
        method: 'POST',
        success: function(response) {
            if (!response.hasPermission) {
                alert('您没有访问此页面的权限');
                window.location.href = 'dashboard.html';
            }
        },
        error: function() {
            alert('权限验证失败');
            window.location.href = 'login.html';
        }
    });
}

// 初始化图表
function initCharts() {
    // CPU趋势小图表
    cpuTrendChart = new ApexCharts(document.querySelector("#cpuTrend"), {
        chart: {
            type: 'line',
            height: 40,
            sparkline: {
                enabled: true
            }
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        colors: ['#28a745'],
        series: [{
            data: []
        }],
        tooltip: {
            fixed: {
                enabled: false
            },
            x: {
                show: false
            },
            marker: {
                show: false
            }
        }
    });
    cpuTrendChart.render();
    
    // 内存趋势小图表
    memoryTrendChart = new ApexCharts(document.querySelector("#memoryTrend"), {
        chart: {
            type: 'line',
            height: 40,
            sparkline: {
                enabled: true
            }
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        colors: ['#17a2b8'],
        series: [{
            data: []
        }],
        tooltip: {
            fixed: {
                enabled: false
            },
            x: {
                show: false
            },
            marker: {
                show: false
            }
        }
    });
    memoryTrendChart.render();
    
    // 用户趋势小图表
    userTrendChart = new ApexCharts(document.querySelector("#userTrend"), {
        chart: {
            type: 'line',
            height: 40,
            sparkline: {
                enabled: true
            }
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        colors: ['#ffc107'],
        series: [{
            data: []
        }],
        tooltip: {
            fixed: {
                enabled: false
            },
            x: {
                show: false
            },
            marker: {
                show: false
            }
        }
    });
    userTrendChart.render();
    
    // 负载趋势小图表
    loadTrendChart = new ApexCharts(document.querySelector("#loadTrend"), {
        chart: {
            type: 'line',
            height: 40,
            sparkline: {
                enabled: true
            }
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        colors: ['#dc3545'],
        series: [{
            data: []
        }],
        tooltip: {
            fixed: {
                enabled: false
            },
            x: {
                show: false
            },
            marker: {
                show: false
            }
        }
    });
    loadTrendChart.render();
    
    // 资源使用趋势图
    resourceChart = new ApexCharts(document.querySelector("#resourceChart"), {
        chart: {
            type: 'line',
            height: 300,
            animations: {
                enabled: true,
                easing: 'linear',
                dynamicAnimation: {
                    speed: 1000
                }
            },
            toolbar: {
                show: false
            }
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        series: [{
            name: 'CPU使用率',
            data: []
        }, {
            name: '内存使用率',
            data: []
        }],
        colors: ['#28a745', '#17a2b8'],
        xaxis: {
            type: 'datetime'
        },
        yaxis: {
            min: 0,
            max: 100,
            labels: {
                formatter: function(val) {
                    return val.toFixed(0) + '%';
                }
            }
        },
        legend: {
            show: true,
            position: 'top'
        },
        tooltip: {
            x: {
                format: 'yyyy/MM/dd HH:mm:ss'
            }
        }
    });
    resourceChart.render();
    
    // 网络流量图
    networkChart = new ApexCharts(document.querySelector("#networkChart"), {
        chart: {
            type: 'area',
            height: 300,
            animations: {
                enabled: true,
                easing: 'linear',
                dynamicAnimation: {
                    speed: 1000
                }
            },
            toolbar: {
                show: false
            }
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        series: [{
            name: '入站流量',
            data: []
        }, {
            name: '出站流量',
            data: []
        }],
        colors: ['#6f42c1', '#fd7e14'],
        fill: {
            type: 'gradient',
            gradient: {
                opacityFrom: 0.6,
                opacityTo: 0.1
            }
        },
        xaxis: {
            type: 'datetime'
        },
        yaxis: {
            labels: {
                formatter: function(val) {
                    return formatBytes(val);
                }
            }
        },
        legend: {
            show: true,
            position: 'top'
        },
        tooltip: {
            x: {
                format: 'yyyy/MM/dd HH:mm:ss'
            },
            y: {
                formatter: function(val) {
                    return formatBytes(val) + '/s';
                }
            }
        }
    });
    networkChart.render();
}

// 加载监控数据
function loadMonitorData() {
    const token = localStorage.getItem('token');
    
    // 加载系统资源使用数据
    $.ajax({
        url: '/api/monitor/resources',
        headers: { 'Authorization': `Bearer ${token}` },
        data: { hours: currentTimeRange },
        method: 'GET',
        success: function(response) {
            updateResourceData(response.data);
        }
    });
    
    // 加载网络流量数据
    $.ajax({
        url: '/api/monitor/network',
        headers: { 'Authorization': `Bearer ${token}` },
        data: { hours: currentTimeRange },
        method: 'GET',
        success: function(response) {
            updateNetworkData(response.data);
        }
    });
    
    // 加载用户活动数据
    $.ajax({
        url: '/api/monitor/user-activity',
        headers: { 'Authorization': `Bearer ${token}` },
        method: 'GET',
        success: function(response) {
            updateUserActivity(response.data);
        }
    });
    
    // 加载系统日志
    $.ajax({
        url: '/api/monitor/system-logs',
        headers: { 'Authorization': `Bearer ${token}` },
        method: 'GET',
        success: function(response) {
            updateSystemLogs(response.data);
        }
    });
}

// 更新资源使用数据
function updateResourceData(data) {
    // 更新CPU使用率
    $('#cpuUsage').text(data.current.cpu.toFixed(1) + '%');
    cpuTrendChart.updateSeries([{
        data: data.cpuTrend
    }]);
    
    // 更新内存使用率
    $('#memoryUsage').text(data.current.memory.toFixed(1) + '%');
    memoryTrendChart.updateSeries([{
        data: data.memoryTrend
    }]);
    
    // 更新在线用户数
    $('#onlineUsers').text(data.current.users);
    userTrendChart.updateSeries([{
        data: data.userTrend
    }]);
    
    // 更新系统负载
    $('#systemLoad').text(data.current.load.toFixed(2));
    loadTrendChart.updateSeries([{
        data: data.loadTrend
    }]);
    
    // 更新资源使用趋势图
    resourceChart.updateSeries([{
        name: 'CPU使用率',
        data: data.cpuHistory
    }, {
        name: '内存使用率',
        data: data.memoryHistory
    }]);
}

// 更新网络流量数据
function updateNetworkData(data) {
    networkChart.updateSeries([{
        name: '入站流量',
        data: data.inbound
    }, {
        name: '出站流量',
        data: data.outbound
    }]);
}

// 更新用户活动列表
function updateUserActivity(activities) {
    const tbody = $('#userActivityTable');
    tbody.empty();
    
    activities.forEach(activity => {
        const tr = $('<tr>');
        tr.html(`
            <td>${activity.username}</td>
            <td>${activity.action}</td>
            <td>${activity.ip}</td>
            <td>${new Date(activity.timestamp).toLocaleString()}</td>
        `);
        tbody.append(tr);
    });
}

// 更新系统日志
function updateSystemLogs(logs) {
    const tbody = $('#systemLogTable');
    tbody.empty();
    
    logs.forEach(log => {
        const tr = $('<tr>');
        tr.html(`
            <td><span class="log-level log-level-${log.level.toLowerCase()}">${log.level}</span></td>
            <td>${log.message}</td>
            <td>${log.source}</td>
            <td>${new Date(log.timestamp).toLocaleString()}</td>
        `);
        tbody.append(tr);
    });
}

// 开始自动刷新
function startAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
    
    refreshInterval = setInterval(loadMonitorData, 30000); // 每30秒刷新一次
}

// 绑定事件处理器
function bindEventHandlers() {
    // 刷新按钮点击事件
    $('#refreshBtn').click(function() {
        loadMonitorData();
    });
    
    // 导出按钮点击事件
    $('#exportBtn').click(function() {
        exportMonitorData();
    });
    
    // 时间范围选择
    $('.dropdown-item').click(function(e) {
        e.preventDefault();
        currentTimeRange = $(this).data('range');
        $('#timeRangeDropdown').text($(this).text());
        loadMonitorData();
    });
}

// 导出监控数据
function exportMonitorData() {
    const token = localStorage.getItem('token');
    
    $.ajax({
        url: '/api/monitor/export',
        headers: { 'Authorization': `Bearer ${token}` },
        data: { hours: currentTimeRange },
        method: 'GET',
        xhrFields: {
            responseType: 'blob'
        },
        success: function(blob) {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `monitor-data-${new Date().toISOString()}.xlsx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            a.remove();
        },
        error: function() {
            alert('导出数据失败');
        }
    });
}

// 格式化字节数
function formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 加载告警配置
function loadAlertConfig() {
    $.ajax({
        url: '/api/monitor/alert-config',
        headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` },
        method: 'GET',
        success: function(response) {
            if (response.success) {
                alertConfig = response.data;
                renderAlertConfig();
            }
        },
        error: function(xhr) {
            console.error('加载告警配置失败:', xhr.responseText);
        }
    });
}

// 渲染告警配置
function renderAlertConfig() {
    if (!alertConfig) return;

    $('#cpuThreshold').val(alertConfig.cpu.threshold);
    $('#cpuEnabled').prop('checked', alertConfig.cpu.enabled);
    $('#memoryThreshold').val(alertConfig.memory.threshold);
    $('#memoryEnabled').prop('checked', alertConfig.memory.enabled);
    $('#diskThreshold').val(alertConfig.disk.threshold);
    $('#diskEnabled').prop('checked', alertConfig.disk.enabled);
    $('#responseTimeThreshold').val(alertConfig.response_time.threshold);
    $('#responseTimeEnabled').prop('checked', alertConfig.response_time.enabled);
    $('#errorRateThreshold').val(alertConfig.error_rate.threshold);
    $('#errorRateEnabled').prop('checked', alertConfig.error_rate.enabled);

    $('#emailNotification').prop('checked', alertConfig.notification.email);
    $('#webhookNotification').prop('checked', alertConfig.notification.webhook);
    $('#smsNotification').prop('checked', alertConfig.notification.sms);

    $('#checkInterval').val(alertConfig.check_interval);
    $('#alertCooldown').val(alertConfig.alert_cooldown);
}

// 保存告警配置
function saveAlertConfig() {
    const config = {
        cpu: {
            threshold: parseInt($('#cpuThreshold').val()),
            enabled: $('#cpuEnabled').prop('checked')
        },
        memory: {
            threshold: parseInt($('#memoryThreshold').val()),
            enabled: $('#memoryEnabled').prop('checked')
        },
        disk: {
            threshold: parseInt($('#diskThreshold').val()),
            enabled: $('#diskEnabled').prop('checked')
        },
        response_time: {
            threshold: parseInt($('#responseTimeThreshold').val()),
            enabled: $('#responseTimeEnabled').prop('checked')
        },
        error_rate: {
            threshold: parseFloat($('#errorRateThreshold').val()),
            enabled: $('#errorRateEnabled').prop('checked')
        },
        notification: {
            email: $('#emailNotification').prop('checked'),
            webhook: $('#webhookNotification').prop('checked'),
            sms: $('#smsNotification').prop('checked')
        },
        check_interval: parseInt($('#checkInterval').val()),
        alert_cooldown: parseInt($('#alertCooldown').val())
    };

    $.ajax({
        url: '/api/monitor/alert-config',
        headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` },
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify(config),
        success: function(response) {
            if (response.success) {
                alertConfig = config;
                showNotification('告警配置保存成功', 'success');
            }
        },
        error: function(xhr) {
            console.error('保存告警配置失败:', xhr.responseText);
            showNotification('保存告警配置失败', 'error');
        }
    });
}

// 加载告警历史
function loadAlertHistory(page = 1) {
    $.ajax({
        url: '/api/monitor/alert-history',
        headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` },
        method: 'GET',
        data: {
            page: page,
            limit: 20,
            level: $('#alertLevelFilter').val(),
            status: $('#alertStatusFilter').val()
        },
        success: function(response) {
            if (response.success) {
                alerts = response.data.alerts;
                renderAlertHistory();
                renderAlertPagination(response.data.pagination);
            }
        },
        error: function(xhr) {
            console.error('加载告警历史失败:', xhr.responseText);
        }
    });
}

// 渲染告警历史
function renderAlertHistory() {
    const tbody = $('#alertHistoryTable tbody');
    tbody.empty();

    if (alerts.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="6" class="text-center text-muted">暂无告警记录</td>
            </tr>
        `);
        return;
    }

    alerts.forEach(alert => {
        const levelClass = getLevelClass(alert.level);
        const statusClass = getStatusClass(alert.status);

        tbody.append(`
            <tr>
                <td><span class="badge bg-${levelClass}">${getLevelText(alert.level)}</span></td>
                <td>${getTypeText(alert.type)}</td>
                <td>${alert.message}</td>
                <td><span class="badge bg-${statusClass}">${getStatusText(alert.status)}</span></td>
                <td>${formatDateTime(alert.createdAt)}</td>
                <td>
                    ${alert.status === 'active' ? `
                        <button class="btn btn-sm btn-success me-1" onclick="handleAlert('${alert.id}', 'acknowledge')">
                            确认
                        </button>
                        <button class="btn btn-sm btn-primary me-1" onclick="handleAlert('${alert.id}', 'resolve')">
                            解决
                        </button>
                        <button class="btn btn-sm btn-secondary" onclick="handleAlert('${alert.id}', 'ignore')">
                            忽略
                        </button>
                    ` : `
                        <span class="text-muted">已处理</span>
                    `}
                </td>
            </tr>
        `);
    });
}

// 处理告警
function handleAlert(alertId, action) {
    const note = prompt('请输入处理说明（可选）:');

    $.ajax({
        url: `/api/monitor/alerts/${alertId}`,
        headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` },
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify({ action, note }),
        success: function(response) {
            if (response.success) {
                showNotification('告警处理成功', 'success');
                loadAlertHistory();
            }
        },
        error: function(xhr) {
            console.error('处理告警失败:', xhr.responseText);
            showNotification('处理告警失败', 'error');
        }
    });
}

// 加载性能历史数据
function loadPerformanceHistory(period = '24h') {
    $.ajax({
        url: '/api/monitor/performance-history',
        headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` },
        method: 'GET',
        data: { period },
        success: function(response) {
            if (response.success) {
                performanceHistory = response.data;
                updatePerformanceTrends();
            }
        },
        error: function(xhr) {
            console.error('加载性能历史失败:', xhr.responseText);
        }
    });
}

// 更新性能趋势图表
function updatePerformanceTrends() {
    if (performanceHistory.length === 0) return;

    const labels = performanceHistory.map(item =>
        new Date(item.timestamp).toLocaleTimeString()
    );
    const cpuData = performanceHistory.map(item => item.cpu);
    const memoryData = performanceHistory.map(item => item.memory);
    const diskData = performanceHistory.map(item => item.disk);

    // 更新CPU趋势图
    if (cpuTrendChart) {
        cpuTrendChart.data.labels = labels;
        cpuTrendChart.data.datasets[0].data = cpuData;
        cpuTrendChart.update();
    }

    // 更新内存趋势图
    if (memoryTrendChart) {
        memoryTrendChart.data.labels = labels;
        memoryTrendChart.data.datasets[0].data = memoryData;
        memoryTrendChart.update();
    }
}

// 检查告警状态
function checkAlertStatus(systemData) {
    if (!alertConfig) return;

    const alerts = [];

    // 检查CPU告警
    if (alertConfig.cpu.enabled && systemData.cpu > alertConfig.cpu.threshold) {
        showAlert('CPU使用率过高', `当前CPU使用率: ${systemData.cpu}%`, 'warning');
    }

    // 检查内存告警
    if (alertConfig.memory.enabled && systemData.memory > alertConfig.memory.threshold) {
        showAlert('内存使用率过高', `当前内存使用率: ${systemData.memory}%`, 'warning');
    }

    // 检查磁盘告警
    if (alertConfig.disk.enabled && systemData.disk > alertConfig.disk.threshold) {
        showAlert('磁盘使用率过高', `当前磁盘使用率: ${systemData.disk}%`, 'danger');
    }
}

// 显示告警通知
function showAlert(title, message, type = 'warning') {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <strong>${title}</strong> ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    $('#alertContainer').prepend(alertHtml);

    // 自动移除告警（10秒后）
    setTimeout(() => {
        $('#alertContainer .alert').first().fadeOut();
    }, 10000);
}

// 辅助函数
function getLevelClass(level) {
    const classes = {
        'low': 'info',
        'medium': 'warning',
        'high': 'danger',
        'critical': 'dark'
    };
    return classes[level] || 'secondary';
}

function getLevelText(level) {
    const texts = {
        'low': '低',
        'medium': '中',
        'high': '高',
        'critical': '严重'
    };
    return texts[level] || level;
}

function getStatusClass(status) {
    const classes = {
        'active': 'danger',
        'acknowledge': 'warning',
        'resolve': 'success',
        'ignore': 'secondary'
    };
    return classes[status] || 'secondary';
}

function getStatusText(status) {
    const texts = {
        'active': '活跃',
        'acknowledge': '已确认',
        'resolve': '已解决',
        'ignore': '已忽略'
    };
    return texts[status] || status;
}

function getTypeText(type) {
    const texts = {
        'cpu': 'CPU',
        'memory': '内存',
        'disk': '磁盘',
        'network': '网络',
        'api': 'API'
    };
    return texts[type] || type;
}

function formatDateTime(dateString) {
    return new Date(dateString).toLocaleString();
}

function showNotification(message, type = 'info') {
    const alertClass = type === 'success' ? 'alert-success' :
                      type === 'error' ? 'alert-danger' :
                      type === 'warning' ? 'alert-warning' : 'alert-info';

    const alert = $(`
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `);

    $('body').append(alert);

    setTimeout(() => {
        alert.remove();
    }, 3000);
}

// 页面卸载时清理
$(window).on('unload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});