/* 安全卡片样式 */
.security-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s, box-shadow 0.3s;
}

.security-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.security-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.security-icon i {
    font-size: 24px;
    color: white;
}

/* 选项卡样式 */
.nav-tabs {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    border: none;
    color: #495057;
    font-weight: 500;
    padding: 10px 15px;
    margin-right: 5px;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #0d6efd;
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    background-color: transparent;
    border-bottom: 2px solid #0d6efd;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

/* 按钮样式 */
.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
}

/* 模态框样式 */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .security-card {
        margin-bottom: 15px;
    }
    
    .btn-toolbar {
        margin-top: 10px;
    }
} 