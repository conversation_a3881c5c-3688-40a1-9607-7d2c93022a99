document.addEventListener('DOMContentLoaded', () => {
    // 初始化i18n
    window.i18n.initI18n();
    
    // 初始化语言切换器
    window.i18n.createLanguageSwitcher('language-switcher');
    
    // API基础URL - 自动适配路径
    const API_BASE_URL = window.location.pathname.startsWith('/adm') ? '/adm' :
                         window.location.pathname.startsWith('/ad') ? '/ad' : '';
    
    // 元素
    const loginBtn = document.getElementById('loginBtn');
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const rememberCheckbox = document.getElementById('remember');
    const twoFactorModal = document.getElementById('two-factor-modal');
    const closeModal = document.querySelector('.close-modal');
    const verifyBtn = document.querySelector('.verify-btn');
    const resendBtn = document.querySelector('.resend-btn');
    const otpInputs = document.querySelectorAll('.otp-input');
    const countdownEl = document.getElementById('countdown');
    
    let countdownInterval;
    let countdownValue = 60;
    
    // 自动填充记住的用户名
    const savedUsername = localStorage.getItem('adminUsername');
    if (savedUsername) {
        usernameInput.value = savedUsername;
        rememberCheckbox.checked = true;
    }
    
    // 登录按钮点击事件
    loginBtn.addEventListener('click', () => {
        const username = usernameInput.value.trim();
        const password = passwordInput.value.trim();
        
        if (!username || !password) {
            showNotification(window.i18n.t('notifications.requiredField'), 'error');
            return;
        }
        
        // 禁用登录按钮，防止重复点击
        loginBtn.disabled = true;
        loginBtn.textContent = window.i18n.t('common.loading');
        
        // 调用登录API
        fetch(`${API_BASE_URL}/api/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ identifier: username, password })
        })
        .then(response => response.json())
        .then(data => {
            // 恢复登录按钮
            loginBtn.disabled = false;
            loginBtn.textContent = window.i18n.t('login.login');
            
            if (data.success) {
                // 记住用户名
                if (rememberCheckbox.checked) {
                    localStorage.setItem('adminUsername', username);
                } else {
                    localStorage.removeItem('adminUsername');
                }
                
                // 如果需要双因素认证
                if (data.requiresTwoFactor) {
                    showTwoFactorModal();
                } else {
                    // 直接登录成功
                    showNotification(window.i18n.t('notifications.loginSuccess'), 'success');
                    setTimeout(() => {
                        window.location.href = './dashboard.html';
                    }, 1500);
                }
            } else {
                showNotification(data.message || window.i18n.t('notifications.loginError'), 'error');
            }
        })
        .catch(error => {
            console.error('登录错误:', error);
            loginBtn.disabled = false;
            loginBtn.textContent = window.i18n.t('login.login');
            
            // 如果API不可用，使用默认登录
            if (username === 'admin' && password === 'admin123') {
                // 记住用户名
                if (rememberCheckbox.checked) {
                    localStorage.setItem('adminUsername', username);
                } else {
                    localStorage.removeItem('adminUsername');
                }
                
                // 显示双因素认证模态框
                showTwoFactorModal();
            } else {
                showNotification(window.i18n.t('notifications.loginError'), 'error');
            }
        });
    });
    
    // 关闭模态框
    closeModal.addEventListener('click', () => {
        hideTwoFactorModal();
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', (e) => {
        if (e.target === twoFactorModal) {
            hideTwoFactorModal();
        }
    });
    
    // OTP输入框自动跳转
    otpInputs.forEach((input, index) => {
        input.addEventListener('input', (e) => {
            if (e.target.value) {
                if (index < otpInputs.length - 1) {
                    otpInputs[index + 1].focus();
                }
            }
        });
        
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Backspace' && !e.target.value && index > 0) {
                otpInputs[index - 1].focus();
            }
        });
    });
    
    // 验证OTP
    verifyBtn.addEventListener('click', () => {
        let code = '';
        otpInputs.forEach(input => {
            code += input.value;
        });
        
        if (code.length !== 6) {
            showNotification(window.i18n.t('notifications.requiredField'), 'error');
            return;
        }
        
        // 禁用验证按钮，防止重复点击
        verifyBtn.disabled = true;
        verifyBtn.textContent = window.i18n.t('common.loading');
        
        // 调用验证API
        fetch(`${API_BASE_URL}/api/auth/verify-2fa`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ code })
        })
        .then(response => response.json())
        .then(data => {
            // 恢复验证按钮
            verifyBtn.disabled = false;
            verifyBtn.textContent = window.i18n.t('login.verify');
            
            if (data.success) {
                showNotification(window.i18n.t('notifications.verifySuccess'), 'success');
                setTimeout(() => {
                    window.location.href = './dashboard.html';
                }, 1500);
            } else {
                showNotification(data.message || window.i18n.t('notifications.verifyError'), 'error');
            }
        })
        .catch(error => {
            console.error('验证错误:', error);
            verifyBtn.disabled = false;
            verifyBtn.textContent = window.i18n.t('login.verify');
            
            // 如果API不可用，使用默认验证
            if (code === '101010') {
                showNotification(window.i18n.t('notifications.verifySuccess'), 'success');
                setTimeout(() => {
                    window.location.href = './dashboard.html';
                }, 1500);
            } else {
                showNotification(window.i18n.t('notifications.verifyError'), 'error');
            }
        });
    });
    
    // 重新发送验证码
    resendBtn.addEventListener('click', () => {
        // 禁用重发按钮
        resendBtn.disabled = true;
        
        // 调用重发API
        fetch(`${API_BASE_URL}/api/auth/resend-2fa`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(window.i18n.t('notifications.codeSent'), 'info');
            } else {
                showNotification(data.message || window.i18n.t('notifications.sendCodeError'), 'error');
            }
        })
        .catch(error => {
            console.error('重发验证码错误:', error);
            showNotification(window.i18n.t('notifications.codeSent'), 'info');
        })
        .finally(() => {
            // 重置倒计时
            resetCountdown();
        });
    });
    
    // 显示双因素认证模态框
    function showTwoFactorModal() {
        twoFactorModal.style.display = 'flex';
        otpInputs[0].focus();
        resetCountdown();
    }
    
    // 隐藏双因素认证模态框
    function hideTwoFactorModal() {
        twoFactorModal.style.display = 'none';
        clearInterval(countdownInterval);
    }
    
    // 重置倒计时
    function resetCountdown() {
        clearInterval(countdownInterval);
        countdownValue = 60;
        countdownEl.textContent = countdownValue;
        resendBtn.disabled = true;
        
        countdownInterval = setInterval(() => {
            countdownValue--;
            countdownEl.textContent = countdownValue;
            
            if (countdownValue <= 0) {
                clearInterval(countdownInterval);
                countdownEl.textContent = window.i18n.t('login.expired');
                resendBtn.disabled = false;
            }
        }, 1000);
    }
    
    // 显示通知
    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 显示通知
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // 自动关闭
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }
    
    // 添加通知样式
    const style = document.createElement('style');
    style.textContent = `
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 24px;
            border-radius: 4px;
            color: white;
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
            z-index: 2000;
        }
        
        .notification.show {
            transform: translateX(0);
        }
        
        .notification.info {
            background-color: #1677ff;
        }
        
        .notification.success {
            background-color: #52c41a;
        }
        
        .notification.error {
            background-color: #ff4d4f;
        }
        
        .notification.warning {
            background-color: #faad14;
        }
    `;
    document.head.appendChild(style);
    
    // 回车键登录
    passwordInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
            loginBtn.click();
        }
    });
}); 