// 全局变量
let currentPage = 1;
let totalPages = 1;
let currentFilters = {};
const pageSize = 20;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 检查权限
    checkAuth();
    
    // 加载数据
    loadLoginLogs();
    loadStatistics();
    
    // 绑定事件处理器
    bindEventHandlers();
});

// 检查用户登录状态和权限
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = 'index.html';
        return;
    }
    
    // 检查是否有查看登录日志的权限
    fetch('/api/auth/check-permission', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ permission: 'security.view' })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.hasPermission) {
            alert('您没有访问此页面的权限');
            window.location.href = 'dashboard.html';
        }
    })
    .catch(error => {
        console.error('权限验证失败:', error);
        window.location.href = 'index.html';
    });
}

// 加载登录日志
async function loadLoginLogs(page = 1, filters = {}) {
    try {
        const token = localStorage.getItem('token');
        const params = new URLSearchParams({
            page: page,
            limit: pageSize,
            ...filters
        });
        
        const response = await fetch(`/api/auth/login-logs?${params}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (!response.ok) {
            throw new Error('获取登录日志失败');
        }
        
        const data = await response.json();
        
        if (data.success) {
            renderLoginLogs(data.data);
            updatePagination(data.pagination);
            currentPage = page;
            totalPages = data.pagination.pages;
        } else {
            throw new Error(data.message || '获取登录日志失败');
        }
        
    } catch (error) {
        console.error('加载登录日志错误:', error);
        showNotification('加载登录日志失败: ' + error.message, 'error');
        
        // 如果API不可用，显示模拟数据
        renderMockData();
    }
}

// 渲染登录日志表格
function renderLoginLogs(logs) {
    const tbody = document.getElementById('logsTableBody');
    
    if (logs.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">暂无登录日志</td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = logs.map(log => `
        <tr>
            <td>${formatDateTime(log.timestamp)}</td>
            <td>
                <div class="user-info">
                    <span class="username">${log.username || '未知用户'}</span>
                    ${log.userId ? `<small class="user-id">ID: ${log.userId}</small>` : ''}
                </div>
            </td>
            <td>
                <span class="ip-address">${log.ip}</span>
            </td>
            <td>
                <div class="device-info">
                    <i class="bi bi-${getDeviceIcon(log.device)}"></i>
                    <span>${log.device}</span>
                </div>
            </td>
            <td>${log.location || '未知'}</td>
            <td>
                <span class="status-badge ${log.success ? 'success' : 'failure'}">
                    <i class="bi bi-${log.success ? 'check-circle' : 'x-circle'}"></i>
                    ${log.success ? '成功' : '失败'}
                </span>
            </td>
            <td>
                ${log.success ? '-' : `<span class="failure-reason">${log.reason || '未知原因'}</span>`}
            </td>
        </tr>
    `).join('');
}

// 获取设备图标
function getDeviceIcon(device) {
    const deviceMap = {
        'Chrome': 'browser-chrome',
        'Firefox': 'browser-firefox',
        'Safari': 'browser-safari',
        'Edge': 'browser-edge',
        'Unknown': 'question-circle'
    };
    return deviceMap[device] || 'question-circle';
}

// 格式化日期时间
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 更新分页信息
function updatePagination(pagination) {
    const { page, limit, total, pages } = pagination;
    
    // 更新分页信息文本
    const start = (page - 1) * limit + 1;
    const end = Math.min(page * limit, total);
    document.getElementById('paginationInfo').textContent = 
        `显示 ${start} - ${end} 条，共 ${total} 条记录`;
    
    // 更新分页按钮状态
    document.getElementById('prevPageBtn').disabled = page <= 1;
    document.getElementById('nextPageBtn').disabled = page >= pages;
    
    // 更新页码
    renderPageNumbers(page, pages);
}

// 渲染页码
function renderPageNumbers(currentPage, totalPages) {
    const pageNumbers = document.getElementById('pageNumbers');
    let html = '';
    
    // 计算显示的页码范围
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, currentPage + 2);
    
    // 如果总页数小于等于5，显示所有页码
    if (totalPages <= 5) {
        startPage = 1;
        endPage = totalPages;
    }
    
    for (let i = startPage; i <= endPage; i++) {
        html += `
            <button class="page-btn ${i === currentPage ? 'active' : ''}" 
                    onclick="goToPage(${i})">${i}</button>
        `;
    }
    
    pageNumbers.innerHTML = html;
}

// 跳转到指定页面
function goToPage(page) {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
        loadLoginLogs(page, currentFilters);
    }
}

// 加载统计数据
async function loadStatistics() {
    try {
        // 这里应该调用实际的API获取统计数据
        // 暂时使用模拟数据
        const stats = {
            successCount: 156,
            failureCount: 12,
            activeUsers: 45,
            lastLogin: new Date().toLocaleString('zh-CN')
        };
        
        document.getElementById('successCount').textContent = stats.successCount;
        document.getElementById('failureCount').textContent = stats.failureCount;
        document.getElementById('activeUsers').textContent = stats.activeUsers;
        document.getElementById('lastLogin').textContent = stats.lastLogin;
        
    } catch (error) {
        console.error('加载统计数据错误:', error);
    }
}

// 渲染模拟数据
function renderMockData() {
    const mockLogs = [
        {
            timestamp: new Date(Date.now() - 1000 * 60 * 10),
            username: 'admin',
            userId: 'admin-user-id',
            ip: '*************',
            device: 'Chrome',
            location: '北京',
            success: true,
            reason: null
        },
        {
            timestamp: new Date(Date.now() - 1000 * 60 * 30),
            username: 'test_user',
            userId: null,
            ip: '*************',
            device: 'Firefox',
            location: '上海',
            success: false,
            reason: '密码错误'
        },
        {
            timestamp: new Date(Date.now() - 1000 * 60 * 60),
            username: 'admin',
            userId: 'admin-user-id',
            ip: '*************',
            device: 'Chrome',
            location: '北京',
            success: true,
            reason: null
        }
    ];
    
    renderLoginLogs(mockLogs);
    updatePagination({
        page: 1,
        limit: pageSize,
        total: mockLogs.length,
        pages: 1
    });
}

// 绑定事件处理器
function bindEventHandlers() {
    // 刷新按钮
    document.getElementById('refreshBtn').addEventListener('click', () => {
        loadLoginLogs(currentPage, currentFilters);
        loadStatistics();
    });
    
    // 导出按钮
    document.getElementById('exportBtn').addEventListener('click', exportLogs);
    
    // 应用筛选按钮
    document.getElementById('applyFilter').addEventListener('click', applyFilters);
    
    // 搜索输入框
    document.getElementById('searchInput').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            applyFilters();
        }
    });
    
    // 分页按钮
    document.getElementById('prevPageBtn').addEventListener('click', () => {
        if (currentPage > 1) {
            goToPage(currentPage - 1);
        }
    });
    
    document.getElementById('nextPageBtn').addEventListener('click', () => {
        if (currentPage < totalPages) {
            goToPage(currentPage + 1);
        }
    });
    
    // 清理日志按钮
    document.getElementById('clearLogsBtn').addEventListener('click', clearLogs);
}

// 应用筛选
function applyFilters() {
    const filters = {};
    
    const searchValue = document.getElementById('searchInput').value.trim();
    if (searchValue) {
        filters.username = searchValue;
    }
    
    const statusValue = document.getElementById('statusFilter').value;
    if (statusValue) {
        filters.success = statusValue;
    }
    
    const timeValue = document.getElementById('timeFilter').value;
    if (timeValue) {
        filters.timeRange = timeValue;
    }
    
    currentFilters = filters;
    loadLoginLogs(1, filters);
}

// 导出日志
function exportLogs() {
    // 这里应该调用后端API导出日志
    showNotification('日志导出功能开发中...', 'info');
}

// 清理日志
function clearLogs() {
    if (confirm('确定要清理所有登录日志吗？此操作不可恢复。')) {
        // 这里应该调用后端API清理日志
        showNotification('日志清理功能开发中...', 'info');
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 显示通知
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // 自动关闭
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
