@echo off
echo ========================================
echo    WriterPro 管理后台启动脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] Python未安装或未添加到PATH环境变量
    echo 请先安装Python: https://www.python.org/downloads/
    pause
    exit /b 1
)

:: 显示Python版本
echo [信息] 检测到Python版本:
python --version

:: 检查当前目录
echo [信息] 当前目录: %cd%
echo.

:: 检查关键文件是否存在
if not exist "index.html" (
    echo [错误] 未找到index.html文件
    echo 请确保在admin-dashboard目录中运行此脚本
    pause
    exit /b 1
)

echo [信息] 文件检查通过
echo.

:: 检查端口是否被占用
echo [信息] 检查端口3000是否可用...
netstat -an | find "3000" >nul
if %errorlevel% equ 0 (
    echo [警告] 端口3000可能被占用，将尝试使用端口8080
    set PORT=8080
) else (
    echo [信息] 端口3000可用
    set PORT=3000
)

echo.
echo [信息] 启动HTTP服务器...
echo [信息] 服务器端口: %PORT%
echo [信息] 访问地址: http://localhost:%PORT%
echo.
echo ========================================
echo    服务器启动中...
echo    按 Ctrl+C 停止服务器
echo ========================================
echo.

:: 启动服务器
python -m http.server %PORT%

:: 如果服务器停止
echo.
echo [信息] 服务器已停止
pause
