/**
 * 简化版服务器 - 将管理后台部署到端口3000/ad路径
 * 不需要额外依赖，只使用Node.js内置模块
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 3000;

// MIME类型映射
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.wav': 'audio/wav',
    '.mp4': 'video/mp4',
    '.woff': 'application/font-woff',
    '.ttf': 'application/font-ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.otf': 'application/font-otf',
    '.wasm': 'application/wasm'
};

// 获取文件的MIME类型
function getContentType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

// 读取文件
function readFile(filePath) {
    return new Promise((resolve, reject) => {
        fs.readFile(filePath, (err, data) => {
            if (err) {
                reject(err);
            } else {
                resolve(data);
            }
        });
    });
}

// 处理API请求
function handleApiRequest(req, res, pathname) {
    res.setHeader('Content-Type', 'application/json');
    
    if (pathname === '/ad/api/auth/login' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        req.on('end', () => {
            try {
                const { username, password } = JSON.parse(body);
                
                if (username === 'admin' && password === 'admin123') {
                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        requiresTwoFactor: true,
                        message: '请输入双因素认证码'
                    }));
                } else {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '用户名或密码错误'
                    }));
                }
            } catch (error) {
                res.writeHead(400);
                res.end(JSON.stringify({
                    success: false,
                    message: '请求格式错误'
                }));
            }
        });
    } else if (pathname === '/ad/api/auth/verify-2fa' && req.method === 'POST') {
        let body = '';
        req.on('data', chunk => {
            body += chunk.toString();
        });
        req.on('end', () => {
            try {
                const { code } = JSON.parse(body);
                
                if (code === '101010') {
                    res.writeHead(200);
                    res.end(JSON.stringify({
                        success: true,
                        user: {
                            id: '1',
                            username: 'admin',
                            name: '系统管理员',
                            email: '<EMAIL>',
                            role: 'admin'
                        }
                    }));
                } else {
                    res.writeHead(401);
                    res.end(JSON.stringify({
                        success: false,
                        message: '验证码错误'
                    }));
                }
            } catch (error) {
                res.writeHead(400);
                res.end(JSON.stringify({
                    success: false,
                    message: '请求格式错误'
                }));
            }
        });
    } else if (pathname === '/ad/api/auth/resend-2fa' && req.method === 'POST') {
        res.writeHead(200);
        res.end(JSON.stringify({
            success: true,
            message: '验证码已重新发送'
        }));
    } else {
        res.writeHead(404);
        res.end(JSON.stringify({
            success: false,
            message: 'API接口不存在'
        }));
    }
}

// 创建HTTP服务器
const server = http.createServer(async (req, res) => {
    const parsedUrl = url.parse(req.url, true);
    let pathname = parsedUrl.pathname;
    
    // 处理API请求
    if (pathname.startsWith('/ad/api/')) {
        return handleApiRequest(req, res, pathname);
    }
    
    // 处理静态文件请求
    let filePath;
    
    if (pathname === '/') {
        // 主网站首页
        res.writeHead(200, { 'Content-Type': 'text/html' });
        res.end(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>主网站</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                    .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                    .admin-link { 
                        display: inline-block; 
                        padding: 15px 30px; 
                        background: #007bff; 
                        color: white; 
                        text-decoration: none; 
                        border-radius: 5px; 
                        margin-top: 20px;
                        font-weight: bold;
                    }
                    .admin-link:hover { background: #0056b3; }
                    .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin-top: 20px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🎉 欢迎访问主网站</h1>
                    <p>这是运行在端口3000的主网站。管理后台已成功集成到 <code>/ad</code> 路径下。</p>
                    <a href="/ad" class="admin-link">🔧 进入管理后台</a>
                    <div class="info">
                        <h3>📋 登录信息</h3>
                        <p><strong>管理后台地址:</strong> <code>http://localhost:3000/ad</code></p>
                        <p><strong>用户名:</strong> <code>admin</code></p>
                        <p><strong>密码:</strong> <code>admin123</code></p>
                        <p><strong>验证码:</strong> <code>101010</code></p>
                    </div>
                </div>
            </body>
            </html>
        `);
        return;
    }
    
    if (pathname === '/ad' || pathname === '/ad/') {
        filePath = path.join(__dirname, 'index.html');
    } else if (pathname.startsWith('/ad/')) {
        // 移除/ad前缀，映射到实际文件
        const actualPath = pathname.substring(3);
        if (actualPath === '/dashboard') {
            filePath = path.join(__dirname, 'dashboard.html');
        } else if (actualPath === '/users') {
            filePath = path.join(__dirname, 'users.html');
        } else if (actualPath === '/agents') {
            filePath = path.join(__dirname, 'agents.html');
        } else if (actualPath === '/commission') {
            filePath = path.join(__dirname, 'commission.html');
        } else if (actualPath === '/pricing') {
            filePath = path.join(__dirname, 'pricing.html');
        } else if (actualPath === '/roles') {
            filePath = path.join(__dirname, 'roles.html');
        } else if (actualPath === '/finance') {
            filePath = path.join(__dirname, 'finance.html');
        } else if (actualPath === '/analytics') {
            filePath = path.join(__dirname, 'analytics.html');
        } else if (actualPath === '/reports') {
            filePath = path.join(__dirname, 'reports.html');
        } else if (actualPath === '/settings') {
            filePath = path.join(__dirname, 'settings.html');
        } else if (actualPath === '/content') {
            filePath = path.join(__dirname, 'content.html');
        } else if (actualPath === '/api-docs') {
            filePath = path.join(__dirname, 'api-docs.html');
        } else if (actualPath === '/marketing') {
            filePath = path.join(__dirname, 'marketing.html');
        } else if (actualPath === '/security') {
            filePath = path.join(__dirname, 'security.html');
        } else if (actualPath === '/monitor') {
            filePath = path.join(__dirname, 'monitor.html');
        } else if (actualPath === '/logs') {
            filePath = path.join(__dirname, 'logs.html');
        } else if (actualPath === '/tasks') {
            filePath = path.join(__dirname, 'tasks.html');
        } else if (actualPath === '/notifications') {
            filePath = path.join(__dirname, 'notifications.html');
        } else if (actualPath === '/backup') {
            filePath = path.join(__dirname, 'backup.html');
        } else {
            // 静态资源文件
            filePath = path.join(__dirname, actualPath);
        }
    } else {
        // 其他路径返回404
        res.writeHead(404, { 'Content-Type': 'text/html' });
        res.end('<h1>404 - 页面不存在</h1><p><a href="/">返回首页</a> | <a href="/ad">进入管理后台</a></p>');
        return;
    }
    
    try {
        const data = await readFile(filePath);
        const contentType = getContentType(filePath);
        res.writeHead(200, { 'Content-Type': contentType });
        res.end(data);
    } catch (error) {
        res.writeHead(404, { 'Content-Type': 'text/html' });
        res.end('<h1>404 - 文件不存在</h1><p><a href="/">返回首页</a> | <a href="/ad">进入管理后台</a></p>');
    }
});

// 启动服务器
server.listen(PORT, () => {
    console.log('========================================');
    console.log('🚀 服务器运行在端口', PORT);
    console.log('📱 主网站: http://localhost:' + PORT);
    console.log('🔧 管理后台: http://localhost:' + PORT + '/ad');
    console.log('👤 登录信息: admin / admin123 / 101010');
    console.log('========================================');
});

module.exports = server;
