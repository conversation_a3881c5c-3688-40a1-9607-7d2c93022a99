{"version": 3, "file": "scrollspy.js", "sources": ["../src/scrollspy.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component.js'\nimport EventHandler from './dom/event-handler.js'\nimport SelectorEngine from './dom/selector-engine.js'\nimport {\n  defineJQueryPlugin, getElement, isDisabled, isVisible\n} from './util/index.js'\n\n/**\n * Constants\n */\n\nconst NAME = 'scrollspy'\nconst DATA_KEY = 'bs.scrollspy'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_ACTIVATE = `activate${EVENT_KEY}`\nconst EVENT_CLICK = `click${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item'\nconst CLASS_NAME_ACTIVE = 'active'\n\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]'\nconst SELECTOR_TARGET_LINKS = '[href]'\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group'\nconst SELECTOR_NAV_LINKS = '.nav-link'\nconst SELECTOR_NAV_ITEMS = '.nav-item'\nconst SELECTOR_LIST_ITEMS = '.list-group-item'\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`\nconst SELECTOR_DROPDOWN = '.dropdown'\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\n\nconst Default = {\n  offset: null, // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null,\n  threshold: [0.1, 0.5, 1]\n}\n\nconst DefaultType = {\n  offset: '(number|null)', // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element',\n  threshold: 'array'\n}\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element\n    this._activeTarget = null\n    this._observer = null\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    }\n    this.refresh() // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables()\n    this._maybeEnableSmoothScroll()\n\n    if (this._observer) {\n      this._observer.disconnect()\n    } else {\n      this._observer = this._getNewObserver()\n    }\n\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section)\n    }\n  }\n\n  dispose() {\n    this._observer.disconnect()\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body\n\n    // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n    config.rootMargin = config.offset ? `${config.offset}px 0px -30%` : config.rootMargin\n\n    if (typeof config.threshold === 'string') {\n      config.threshold = config.threshold.split(',').map(value => Number.parseFloat(value))\n    }\n\n    return config\n  }\n\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK)\n\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash)\n      if (observableSection) {\n        event.preventDefault()\n        const root = this._rootElement || window\n        const height = observableSection.offsetTop - this._element.offsetTop\n        if (root.scrollTo) {\n          root.scrollTo({ top: height, behavior: 'smooth' })\n          return\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height\n      }\n    })\n  }\n\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: this._config.threshold,\n      rootMargin: this._config.rootMargin\n    }\n\n    return new IntersectionObserver(entries => this._observerCallback(entries), options)\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`)\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop\n      this._process(targetElement(entry))\n    }\n\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop\n    this._previousScrollData.parentScrollTop = parentScrollTop\n\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null\n        this._clearActiveClass(targetElement(entry))\n\n        continue\n      }\n\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry)\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return\n        }\n\n        continue\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry)\n      }\n    }\n  }\n\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map()\n    this._observableSections = new Map()\n\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target)\n\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue\n      }\n\n      const observableSection = SelectorEngine.findOne(decodeURI(anchor.hash), this._element)\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(decodeURI(anchor.hash), anchor)\n        this._observableSections.set(anchor.hash, observableSection)\n      }\n    }\n  }\n\n  _process(target) {\n    if (this._activeTarget === target) {\n      return\n    }\n\n    this._clearActiveClass(this._config.target)\n    this._activeTarget = target\n    target.classList.add(CLASS_NAME_ACTIVE)\n    this._activateParents(target)\n\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, { relatedTarget: target })\n  }\n\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE, target.closest(SELECTOR_DROPDOWN))\n        .classList.add(CLASS_NAME_ACTIVE)\n      return\n    }\n\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE)\n      }\n    }\n  }\n\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE)\n\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE}`, parent)\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy)\n\nexport default ScrollSpy\n"], "names": ["NAME", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "EVENT_ACTIVATE", "EVENT_CLICK", "EVENT_LOAD_DATA_API", "CLASS_NAME_DROPDOWN_ITEM", "CLASS_NAME_ACTIVE", "SELECTOR_DATA_SPY", "SELECTOR_TARGET_LINKS", "SELECTOR_NAV_LIST_GROUP", "SELECTOR_NAV_LINKS", "SELECTOR_NAV_ITEMS", "SELECTOR_LIST_ITEMS", "SELECTOR_LINK_ITEMS", "SELECTOR_DROPDOWN", "SELECTOR_DROPDOWN_TOGGLE", "<PERSON><PERSON><PERSON>", "offset", "rootMargin", "smoothScroll", "target", "threshold", "DefaultType", "ScrollSpy", "BaseComponent", "constructor", "element", "config", "_targetLinks", "Map", "_observableSections", "_rootElement", "getComputedStyle", "_element", "overflowY", "_activeTarget", "_observer", "_previousScrollData", "visibleEntryTop", "parentScrollTop", "refresh", "_initializeTargetsAndObservables", "_maybeEnableSmoothScroll", "disconnect", "_getNewObserver", "section", "values", "observe", "dispose", "_configAfterMerge", "getElement", "document", "body", "split", "map", "value", "Number", "parseFloat", "_config", "EventHandler", "off", "on", "event", "observableSection", "get", "hash", "preventDefault", "root", "window", "height", "offsetTop", "scrollTo", "top", "behavior", "scrollTop", "options", "IntersectionObserver", "entries", "_<PERSON><PERSON><PERSON><PERSON>", "targetElement", "entry", "id", "activate", "_process", "documentElement", "userScrollsDown", "isIntersecting", "_clearActiveClass", "entryIsLowerThanPrevious", "targetLinks", "SelectorEngine", "find", "anchor", "isDisabled", "findOne", "decodeURI", "isVisible", "set", "classList", "add", "_activateParents", "trigger", "relatedTarget", "contains", "closest", "listGroup", "parents", "item", "prev", "parent", "remove", "activeNodes", "node", "jQueryInterface", "each", "data", "getOrCreateInstance", "undefined", "startsWith", "TypeError", "spy", "defineJQueryPlugin"], "mappings": ";;;;;;;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;;;EASA;EACA;EACA;;EAEA,MAAMA,IAAI,GAAG,WAAW;EACxB,MAAMC,QAAQ,GAAG,cAAc;EAC/B,MAAMC,SAAS,GAAG,CAAID,CAAAA,EAAAA,QAAQ,CAAE,CAAA;EAChC,MAAME,YAAY,GAAG,WAAW;EAEhC,MAAMC,cAAc,GAAG,CAAWF,QAAAA,EAAAA,SAAS,CAAE,CAAA;EAC7C,MAAMG,WAAW,GAAG,CAAQH,KAAAA,EAAAA,SAAS,CAAE,CAAA;EACvC,MAAMI,mBAAmB,GAAG,CAAA,IAAA,EAAOJ,SAAS,CAAA,EAAGC,YAAY,CAAE,CAAA;EAE7D,MAAMI,wBAAwB,GAAG,eAAe;EAChD,MAAMC,iBAAiB,GAAG,QAAQ;EAElC,MAAMC,iBAAiB,GAAG,wBAAwB;EAClD,MAAMC,qBAAqB,GAAG,QAAQ;EACtC,MAAMC,uBAAuB,GAAG,mBAAmB;EACnD,MAAMC,kBAAkB,GAAG,WAAW;EACtC,MAAMC,kBAAkB,GAAG,WAAW;EACtC,MAAMC,mBAAmB,GAAG,kBAAkB;EAC9C,MAAMC,mBAAmB,GAAG,CAAA,EAAGH,kBAAkB,CAAA,EAAA,EAAKC,kBAAkB,CAAMD,GAAAA,EAAAA,kBAAkB,CAAKE,EAAAA,EAAAA,mBAAmB,CAAE,CAAA;EAC1H,MAAME,iBAAiB,GAAG,WAAW;EACrC,MAAMC,wBAAwB,GAAG,kBAAkB;EAEnD,MAAMC,OAAO,GAAG;EACdC,EAAAA,MAAM,EAAE,IAAI;EAAE;EACdC,EAAAA,UAAU,EAAE,cAAc;EAC1BC,EAAAA,YAAY,EAAE,KAAK;EACnBC,EAAAA,MAAM,EAAE,IAAI;EACZC,EAAAA,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;EACzB,CAAC;EAED,MAAMC,WAAW,GAAG;EAClBL,EAAAA,MAAM,EAAE,eAAe;EAAE;EACzBC,EAAAA,UAAU,EAAE,QAAQ;EACpBC,EAAAA,YAAY,EAAE,SAAS;EACvBC,EAAAA,MAAM,EAAE,SAAS;EACjBC,EAAAA,SAAS,EAAE;EACb,CAAC;;EAED;EACA;EACA;;EAEA,MAAME,SAAS,SAASC,aAAa,CAAC;EACpCC,EAAAA,WAAWA,CAACC,OAAO,EAAEC,MAAM,EAAE;EAC3B,IAAA,KAAK,CAACD,OAAO,EAAEC,MAAM,CAAC;;EAEtB;EACA,IAAA,IAAI,CAACC,YAAY,GAAG,IAAIC,GAAG,EAAE;EAC7B,IAAA,IAAI,CAACC,mBAAmB,GAAG,IAAID,GAAG,EAAE;EACpC,IAAA,IAAI,CAACE,YAAY,GAAGC,gBAAgB,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACC,SAAS,KAAK,SAAS,GAAG,IAAI,GAAG,IAAI,CAACD,QAAQ;MAClG,IAAI,CAACE,aAAa,GAAG,IAAI;MACzB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACC,mBAAmB,GAAG;EACzBC,MAAAA,eAAe,EAAE,CAAC;EAClBC,MAAAA,eAAe,EAAE;OAClB;EACD,IAAA,IAAI,CAACC,OAAO,EAAE,CAAC;EACjB;;EAEA;IACA,WAAWxB,OAAOA,GAAG;EACnB,IAAA,OAAOA,OAAO;EAChB;IAEA,WAAWM,WAAWA,GAAG;EACvB,IAAA,OAAOA,WAAW;EACpB;IAEA,WAAWxB,IAAIA,GAAG;EAChB,IAAA,OAAOA,IAAI;EACb;;EAEA;EACA0C,EAAAA,OAAOA,GAAG;MACR,IAAI,CAACC,gCAAgC,EAAE;MACvC,IAAI,CAACC,wBAAwB,EAAE;MAE/B,IAAI,IAAI,CAACN,SAAS,EAAE;EAClB,MAAA,IAAI,CAACA,SAAS,CAACO,UAAU,EAAE;EAC7B,KAAC,MAAM;EACL,MAAA,IAAI,CAACP,SAAS,GAAG,IAAI,CAACQ,eAAe,EAAE;EACzC;MAEA,KAAK,MAAMC,OAAO,IAAI,IAAI,CAACf,mBAAmB,CAACgB,MAAM,EAAE,EAAE;EACvD,MAAA,IAAI,CAACV,SAAS,CAACW,OAAO,CAACF,OAAO,CAAC;EACjC;EACF;EAEAG,EAAAA,OAAOA,GAAG;EACR,IAAA,IAAI,CAACZ,SAAS,CAACO,UAAU,EAAE;MAC3B,KAAK,CAACK,OAAO,EAAE;EACjB;;EAEA;IACAC,iBAAiBA,CAACtB,MAAM,EAAE;EACxB;EACAA,IAAAA,MAAM,CAACP,MAAM,GAAG8B,mBAAU,CAACvB,MAAM,CAACP,MAAM,CAAC,IAAI+B,QAAQ,CAACC,IAAI;;EAE1D;EACAzB,IAAAA,MAAM,CAACT,UAAU,GAAGS,MAAM,CAACV,MAAM,GAAG,CAAGU,EAAAA,MAAM,CAACV,MAAM,CAAA,WAAA,CAAa,GAAGU,MAAM,CAACT,UAAU;EAErF,IAAA,IAAI,OAAOS,MAAM,CAACN,SAAS,KAAK,QAAQ,EAAE;QACxCM,MAAM,CAACN,SAAS,GAAGM,MAAM,CAACN,SAAS,CAACgC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,KAAK,IAAIC,MAAM,CAACC,UAAU,CAACF,KAAK,CAAC,CAAC;EACvF;EAEA,IAAA,OAAO5B,MAAM;EACf;EAEAe,EAAAA,wBAAwBA,GAAG;EACzB,IAAA,IAAI,CAAC,IAAI,CAACgB,OAAO,CAACvC,YAAY,EAAE;EAC9B,MAAA;EACF;;EAEA;MACAwC,YAAY,CAACC,GAAG,CAAC,IAAI,CAACF,OAAO,CAACtC,MAAM,EAAEjB,WAAW,CAAC;EAElDwD,IAAAA,YAAY,CAACE,EAAE,CAAC,IAAI,CAACH,OAAO,CAACtC,MAAM,EAAEjB,WAAW,EAAEK,qBAAqB,EAAEsD,KAAK,IAAI;EAChF,MAAA,MAAMC,iBAAiB,GAAG,IAAI,CAACjC,mBAAmB,CAACkC,GAAG,CAACF,KAAK,CAAC1C,MAAM,CAAC6C,IAAI,CAAC;EACzE,MAAA,IAAIF,iBAAiB,EAAE;UACrBD,KAAK,CAACI,cAAc,EAAE;EACtB,QAAA,MAAMC,IAAI,GAAG,IAAI,CAACpC,YAAY,IAAIqC,MAAM;UACxC,MAAMC,MAAM,GAAGN,iBAAiB,CAACO,SAAS,GAAG,IAAI,CAACrC,QAAQ,CAACqC,SAAS;UACpE,IAAIH,IAAI,CAACI,QAAQ,EAAE;YACjBJ,IAAI,CAACI,QAAQ,CAAC;EAAEC,YAAAA,GAAG,EAAEH,MAAM;EAAEI,YAAAA,QAAQ,EAAE;EAAS,WAAC,CAAC;EAClD,UAAA;EACF;;EAEA;UACAN,IAAI,CAACO,SAAS,GAAGL,MAAM;EACzB;EACF,KAAC,CAAC;EACJ;EAEAzB,EAAAA,eAAeA,GAAG;EAChB,IAAA,MAAM+B,OAAO,GAAG;QACdR,IAAI,EAAE,IAAI,CAACpC,YAAY;EACvBV,MAAAA,SAAS,EAAE,IAAI,CAACqC,OAAO,CAACrC,SAAS;EACjCH,MAAAA,UAAU,EAAE,IAAI,CAACwC,OAAO,CAACxC;OAC1B;EAED,IAAA,OAAO,IAAI0D,oBAAoB,CAACC,OAAO,IAAI,IAAI,CAACC,iBAAiB,CAACD,OAAO,CAAC,EAAEF,OAAO,CAAC;EACtF;;EAEA;IACAG,iBAAiBA,CAACD,OAAO,EAAE;EACzB,IAAA,MAAME,aAAa,GAAGC,KAAK,IAAI,IAAI,CAACpD,YAAY,CAACoC,GAAG,CAAC,IAAIgB,KAAK,CAAC5D,MAAM,CAAC6D,EAAE,EAAE,CAAC;MAC3E,MAAMC,QAAQ,GAAGF,KAAK,IAAI;QACxB,IAAI,CAAC3C,mBAAmB,CAACC,eAAe,GAAG0C,KAAK,CAAC5D,MAAM,CAACkD,SAAS;EACjE,MAAA,IAAI,CAACa,QAAQ,CAACJ,aAAa,CAACC,KAAK,CAAC,CAAC;OACpC;MAED,MAAMzC,eAAe,GAAG,CAAC,IAAI,CAACR,YAAY,IAAIoB,QAAQ,CAACiC,eAAe,EAAEV,SAAS;MACjF,MAAMW,eAAe,GAAG9C,eAAe,IAAI,IAAI,CAACF,mBAAmB,CAACE,eAAe;EACnF,IAAA,IAAI,CAACF,mBAAmB,CAACE,eAAe,GAAGA,eAAe;EAE1D,IAAA,KAAK,MAAMyC,KAAK,IAAIH,OAAO,EAAE;EAC3B,MAAA,IAAI,CAACG,KAAK,CAACM,cAAc,EAAE;UACzB,IAAI,CAACnD,aAAa,GAAG,IAAI;EACzB,QAAA,IAAI,CAACoD,iBAAiB,CAACR,aAAa,CAACC,KAAK,CAAC,CAAC;EAE5C,QAAA;EACF;EAEA,MAAA,MAAMQ,wBAAwB,GAAGR,KAAK,CAAC5D,MAAM,CAACkD,SAAS,IAAI,IAAI,CAACjC,mBAAmB,CAACC,eAAe;EACnG;QACA,IAAI+C,eAAe,IAAIG,wBAAwB,EAAE;UAC/CN,QAAQ,CAACF,KAAK,CAAC;EACf;UACA,IAAI,CAACzC,eAAe,EAAE;EACpB,UAAA;EACF;EAEA,QAAA;EACF;;EAEA;EACA,MAAA,IAAI,CAAC8C,eAAe,IAAI,CAACG,wBAAwB,EAAE;UACjDN,QAAQ,CAACF,KAAK,CAAC;EACjB;EACF;EACF;EAEAvC,EAAAA,gCAAgCA,GAAG;EACjC,IAAA,IAAI,CAACb,YAAY,GAAG,IAAIC,GAAG,EAAE;EAC7B,IAAA,IAAI,CAACC,mBAAmB,GAAG,IAAID,GAAG,EAAE;EAEpC,IAAA,MAAM4D,WAAW,GAAGC,cAAc,CAACC,IAAI,CAACnF,qBAAqB,EAAE,IAAI,CAACkD,OAAO,CAACtC,MAAM,CAAC;EAEnF,IAAA,KAAK,MAAMwE,MAAM,IAAIH,WAAW,EAAE;EAChC;QACA,IAAI,CAACG,MAAM,CAAC3B,IAAI,IAAI4B,mBAAU,CAACD,MAAM,CAAC,EAAE;EACtC,QAAA;EACF;EAEA,MAAA,MAAM7B,iBAAiB,GAAG2B,cAAc,CAACI,OAAO,CAACC,SAAS,CAACH,MAAM,CAAC3B,IAAI,CAAC,EAAE,IAAI,CAAChC,QAAQ,CAAC;;EAEvF;EACA,MAAA,IAAI+D,kBAAS,CAACjC,iBAAiB,CAAC,EAAE;EAChC,QAAA,IAAI,CAACnC,YAAY,CAACqE,GAAG,CAACF,SAAS,CAACH,MAAM,CAAC3B,IAAI,CAAC,EAAE2B,MAAM,CAAC;UACrD,IAAI,CAAC9D,mBAAmB,CAACmE,GAAG,CAACL,MAAM,CAAC3B,IAAI,EAAEF,iBAAiB,CAAC;EAC9D;EACF;EACF;IAEAoB,QAAQA,CAAC/D,MAAM,EAAE;EACf,IAAA,IAAI,IAAI,CAACe,aAAa,KAAKf,MAAM,EAAE;EACjC,MAAA;EACF;MAEA,IAAI,CAACmE,iBAAiB,CAAC,IAAI,CAAC7B,OAAO,CAACtC,MAAM,CAAC;MAC3C,IAAI,CAACe,aAAa,GAAGf,MAAM;EAC3BA,IAAAA,MAAM,CAAC8E,SAAS,CAACC,GAAG,CAAC7F,iBAAiB,CAAC;EACvC,IAAA,IAAI,CAAC8F,gBAAgB,CAAChF,MAAM,CAAC;MAE7BuC,YAAY,CAAC0C,OAAO,CAAC,IAAI,CAACpE,QAAQ,EAAE/B,cAAc,EAAE;EAAEoG,MAAAA,aAAa,EAAElF;EAAO,KAAC,CAAC;EAChF;IAEAgF,gBAAgBA,CAAChF,MAAM,EAAE;EACvB;MACA,IAAIA,MAAM,CAAC8E,SAAS,CAACK,QAAQ,CAAClG,wBAAwB,CAAC,EAAE;EACvDqF,MAAAA,cAAc,CAACI,OAAO,CAAC/E,wBAAwB,EAAEK,MAAM,CAACoF,OAAO,CAAC1F,iBAAiB,CAAC,CAAC,CAChFoF,SAAS,CAACC,GAAG,CAAC7F,iBAAiB,CAAC;EACnC,MAAA;EACF;MAEA,KAAK,MAAMmG,SAAS,IAAIf,cAAc,CAACgB,OAAO,CAACtF,MAAM,EAAEX,uBAAuB,CAAC,EAAE;EAC/E;EACA;QACA,KAAK,MAAMkG,IAAI,IAAIjB,cAAc,CAACkB,IAAI,CAACH,SAAS,EAAE5F,mBAAmB,CAAC,EAAE;EACtE8F,QAAAA,IAAI,CAACT,SAAS,CAACC,GAAG,CAAC7F,iBAAiB,CAAC;EACvC;EACF;EACF;IAEAiF,iBAAiBA,CAACsB,MAAM,EAAE;EACxBA,IAAAA,MAAM,CAACX,SAAS,CAACY,MAAM,CAACxG,iBAAiB,CAAC;EAE1C,IAAA,MAAMyG,WAAW,GAAGrB,cAAc,CAACC,IAAI,CAAC,CAAGnF,EAAAA,qBAAqB,CAAIF,CAAAA,EAAAA,iBAAiB,CAAE,CAAA,EAAEuG,MAAM,CAAC;EAChG,IAAA,KAAK,MAAMG,IAAI,IAAID,WAAW,EAAE;EAC9BC,MAAAA,IAAI,CAACd,SAAS,CAACY,MAAM,CAACxG,iBAAiB,CAAC;EAC1C;EACF;;EAEA;IACA,OAAO2G,eAAeA,CAACtF,MAAM,EAAE;EAC7B,IAAA,OAAO,IAAI,CAACuF,IAAI,CAAC,YAAY;QAC3B,MAAMC,IAAI,GAAG5F,SAAS,CAAC6F,mBAAmB,CAAC,IAAI,EAAEzF,MAAM,CAAC;EAExD,MAAA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;EAC9B,QAAA;EACF;EAEA,MAAA,IAAIwF,IAAI,CAACxF,MAAM,CAAC,KAAK0F,SAAS,IAAI1F,MAAM,CAAC2F,UAAU,CAAC,GAAG,CAAC,IAAI3F,MAAM,KAAK,aAAa,EAAE;EACpF,QAAA,MAAM,IAAI4F,SAAS,CAAC,CAAoB5F,iBAAAA,EAAAA,MAAM,GAAG,CAAC;EACpD;EAEAwF,MAAAA,IAAI,CAACxF,MAAM,CAAC,EAAE;EAChB,KAAC,CAAC;EACJ;EACF;;EAEA;EACA;EACA;;EAEAgC,YAAY,CAACE,EAAE,CAACO,MAAM,EAAEhE,mBAAmB,EAAE,MAAM;IACjD,KAAK,MAAMoH,GAAG,IAAI9B,cAAc,CAACC,IAAI,CAACpF,iBAAiB,CAAC,EAAE;EACxDgB,IAAAA,SAAS,CAAC6F,mBAAmB,CAACI,GAAG,CAAC;EACpC;EACF,CAAC,CAAC;;EAEF;EACA;EACA;;AAEAC,6BAAkB,CAAClG,SAAS,CAAC;;;;;;;;"}