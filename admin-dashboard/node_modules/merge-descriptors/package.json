{"name": "merge-descriptors", "description": "Merge objects using descriptors", "version": "1.0.3", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com", "twitter": "https://twitter.com/jongleberry"}, "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "MIT", "repository": "sindresorhus/merge-descriptors", "funding": "https://github.com/sponsors/sindresorhus", "devDependencies": {"eslint": "5.9.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.14.0", "eslint-plugin-node": "7.0.1", "eslint-plugin-promise": "4.0.1", "eslint-plugin-standard": "4.0.0", "mocha": "5.2.0", "nyc": "13.1.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "scripts": {"lint": "eslint .", "test": "mocha test/", "test-cov": "nyc --reporter=html --reporter=text npm test"}}