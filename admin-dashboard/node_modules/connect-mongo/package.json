{"name": "connect-mongo", "version": "5.1.0", "description": "MongoDB session store for Express and Connect", "main": "build/main/index.js", "typings": "build/main/index.d.ts", "keywords": ["connect", "mongo", "mongodb", "session", "express"], "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "MC Or <<EMAIL>>"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/jdesboeufs/connect-mongo.git"}, "bugs": {"url": "https://github.com/jdesboeufs/connect-mongo/issues"}, "scripts": {"prebuild": "rm -rf build", "build": "run-p build:*", "build:main": "tsc -p tsconfig.json", "fix": "run-s fix:*", "fix:prettier": "prettier \"src/**/*.ts\" --write", "fix:lint": "eslint --cache src --ext .ts --fix", "test": "run-s build test:*", "test:lint": "eslint --cache src --ext .ts", "test:prettier": "prettier \"src/**/*.ts\" --list-different", "test:unit": "nyc ava", "check-cli": "run-s test diff-integration-tests check-integration-tests", "check-integration-tests": "run-s check-integration-test:*", "diff-integration-tests": "mkdir -p diff && rm -rf diff/test && cp -r test diff/test && rm -rf diff/test/test-*/.git && cd diff && git init --quiet && git add -A && git commit --quiet --no-verify --allow-empty -m 'WIP' && echo '\\n\\nCommitted most recent integration test output in the \"diff\" directory. Review the changes with \"cd diff && git diff HEAD\" or your preferred git diff viewer.'", "watch:build": "tsc -p tsconfig.json -w", "watch:test": "nyc --silent ava --watch", "cov": "run-s build test:unit cov:html cov:lcov && open-cli coverage/index.html", "cov:html": "nyc report --reporter=html", "cov:lcov": "nyc report --reporter=lcov", "cov:send": "run-s cov:lcov && codecov", "cov:check": "nyc report && nyc check-coverage", "doc": "run-s doc:html && open-cli build/docs/index.html", "doc:html": "typedoc src/ --exclude **/*.spec.ts --target ES6 --mode file --out build/docs", "doc:json": "typedoc src/ --exclude **/*.spec.ts --target ES6 --mode file --json build/docs/typedoc.json", "doc:publish": "gh-pages -m \"[ci skip] Updates\" -d build/docs", "version": "standard-version", "reset-hard": "git clean -dfx && git reset --hard && yarn", "prepare-release": "run-s reset-hard test cov:check doc:html version doc:publish"}, "engines": {"node": ">=12.9.0"}, "peerDependencies": {"express-session": "^1.17.1", "mongodb": ">= 5.1.0 < 7"}, "dependencies": {"debug": "^4.3.1", "kruptein": "^3.0.0"}, "devDependencies": {"@ava/typescript": "^1.1.1", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@istanbuljs/nyc-config-typescript": "^1.0.1", "@types/debug": "^4.1.7", "@types/express": "^4.17.13", "@types/express-session": "^1.17.4", "@types/node": "^14.14.20", "@types/supertest": "^2.0.10", "@typescript-eslint/eslint-plugin": "^4.12.0", "@typescript-eslint/parser": "^4.12.0", "ava": "^3.12.1", "codecov": "^3.5.0", "cspell": "^4.1.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^7.17.0", "eslint-config-prettier": "^7.1.0", "eslint-config-prettier-standard": "^3.0.1", "eslint-config-standard": "^16.0.2", "eslint-plugin-eslint-comments": "^3.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^5.0.0", "express": "^4.17.1", "express-session": "^1.17.1", "gh-pages": "^3.1.0", "husky": "4", "lint-staged": "^10.5.4", "mongodb": "^5.1.0", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "open-cli": "^6.0.1", "prettier": "^2.2.1", "standard-version": "^9.0.0", "supertest": "^6.1.3", "ts-node": "^9.0.0", "typedoc": "^0.19.0", "typescript": "^4.0.2"}, "files": ["build/main", "build/module", "!**/*.spec.*", "!**/*.json", "!build/*/test/*", "CHANGELOG.md", "LICENSE", "README.md"], "ava": {"failFast": true, "timeout": "60s", "typescript": {"rewritePaths": {"src/": "build/main/"}}, "files": ["!build/module/**", "!src/test/testHelper.ts"]}, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "prettier": {"singleQuote": true, "semi": false, "trailingComma": "es5"}, "nyc": {"extends": "@istanbuljs/nyc-config-typescript", "exclude": ["**/*.spec.js"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix"]}}