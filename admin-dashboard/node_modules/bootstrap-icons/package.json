{"name": "bootstrap-icons", "version": "1.13.1", "description": "Official open source SVG icon library for Bootstrap", "author": "mdo", "license": "MIT", "homepage": "https://icons.getbootstrap.com/", "repository": {"type": "git", "url": "git+https://github.com/twbs/icons.git"}, "bugs": {"url": "https://github.com/twbs/icons/issues"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/twbs"}, {"type": "opencollective", "url": "https://opencollective.com/bootstrap"}], "keywords": ["bootstrap", "icons", "svg", "font", "sprite", "woff", "woff2"], "style": "font/bootstrap-icons.css", "sass": "font/bootstrap-icons.scss", "files": ["icons/*.svg", "bootstrap-icons.svg", "font", "!.DS_Store"], "hugo-bin": {"buildTags": "extended", "version": "0.134.3"}, "scripts": {"start": "npm run docs-serve", "docs-serve": "hugo server --port 4000 --disableFastRender", "docs-build": "hugo --cleanDestinationDir --printUnusedTemplates", "docs-test": "npm-run-all docs-build docs-test:vnu", "docs-test:vnu": "node build/vnu-jar.mjs", "pages": "node build/build-pages.mjs", "icons": "npm-run-all icons-main --aggregate-output --parallel icons-sprite icons-font", "icons-main": "node build/build-svgs.mjs", "icons-zip": "cross-env-shell \"rm -rf bootstrap-icons-$npm_package_version bootstrap-icons-$npm_package_version.zip && cp -r icons/ bootstrap-icons-$npm_package_version && cp bootstrap-icons.svg bootstrap-icons-$npm_package_version && cp -r font/ bootstrap-icons-$npm_package_version && zip -qr9 bootstrap-icons-$npm_package_version.zip bootstrap-icons-$npm_package_version && rm -rf bootstrap-icons-$npm_package_version\"", "icons-sprite": "svg-sprite --config svg-sprite.json --log=info \"icons/*.svg\"", "icons-font": "npm-run-all icons-font-*", "icons-font-main": "<PERSON>on", "icons-font-min": "cleancss -O1 --format breakWith=lf --with-rebase --output font/bootstrap-icons.min.css font/bootstrap-icons.css", "release": "npm-run-all icons docs-build icons-zip", "release-version": "node build/bump-version.mjs", "netlify": "cross-env-shell HUGO_BASEURL=$DEPLOY_PRIME_URL npm-run-all icons docs-build", "test:fusv": "fusv docs/assets/scss/", "test:eslint": "eslint --cache --cache-location .cache/.eslintcache .", "test:stylelint": "stylelint docs/assets/scss/ --cache --cache-location .cache/.stylelintcache", "test:lockfile-lint": "lockfile-lint --allowed-hosts npm --allowed-schemes https: --empty-hostname false --type npm --path package-lock.json", "test:check-icons": "node build/check-icons.mjs", "test": "npm-run-all --parallel --aggregate-output --continue-on-error test:*"}, "devDependencies": {"@eslint/js": "^9.25.1", "@twbs/fantasticon": "^3.1.0", "autoprefixer": "^10.4.21", "bootstrap": "^5.3.5", "clean-css-cli": "^5.6.3", "clipboard": "^2.0.11", "cross-env": "^7.0.3", "eslint": "^9.25.0", "find-unused-sass-variables": "^6.1.0", "fuse.js": "^7.1.0", "globals": "^14.0.0", "hugo-bin": "^0.144.0", "lockfile-lint": "^4.14.1", "npm-run-all2": "^7.0.2", "picocolors": "^1.1.1", "postcss": "^8.5.3", "postcss-cli": "^11.0.1", "stylelint": "^16.19.1", "stylelint-config-twbs-bootstrap": "^16.0.0", "svg-sprite": "^3.0.0-rc3", "svgo": "^3.3.2", "vnu-jar": "24.10.17"}}