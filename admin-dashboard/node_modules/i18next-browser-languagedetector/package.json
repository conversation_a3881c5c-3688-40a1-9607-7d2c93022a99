{"name": "i18next-browser-languagedetector", "version": "8.2.0", "description": "language detector used in browser environment for i18next", "main": "./dist/cjs/i18nextBrowserLanguageDetector.js", "module": "./dist/esm/i18nextBrowserLanguageDetector.js", "types": "./index.d.mts", "exports": {"./package.json": "./package.json", ".": {"types": {"require": "./index.d.ts", "import": "./index.d.mts"}, "module": "./dist/esm/i18nextBrowserLanguageDetector.js", "import": "./dist/esm/i18nextBrowserLanguageDetector.js", "require": "./dist/cjs/i18nextBrowserLanguageDetector.js", "default": "./dist/esm/i18nextBrowserLanguageDetector.js"}, "./cjs": {"types": "./index.d.ts", "default": "./dist/cjs/i18nextBrowserLanguageDetector.js"}, "./esm": {"types": "./index.d.mts", "default": "./dist/esm/i18nextBrowserLanguageDetector.js"}}, "keywords": ["i18next", "i18next-languageDetector"], "homepage": "https://github.com/i18next/i18next-browser-languageDetector", "bugs": "https://github.com/i18next/i18next-browser-languageDetector/issues", "repository": {"type": "git", "url": "https://github.com/i18next/i18next-browser-languageDetector.git"}, "dependencies": {"@babel/runtime": "^7.23.2"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/plugin-transform-runtime": "^7.23.3", "@babel/preset-env": "^7.23.3", "@babel/eslint-parser": "^7.23.3", "babel-polyfill": "^6.26.0", "babelify": "^10.0.0", "browserify": "17.0.0", "browserify-istanbul": "3.0.1", "chai": "4.3.10", "coveralls": "3.1.1", "cpy-cli": "^5.0.0", "dtslint": "^4.2.1", "eslint": "8.53.0", "eslint-config-airbnb": "19.0.4", "expect.js": "0.3.1", "i18next": "23.7.1", "mkdirp": "3.0.1", "mocha": "10.8.2", "rimraf": "5.0.5", "rollup": "^2.79.1", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-node-resolve": "^14.1.0", "rollup-plugin-terser": "^7.0.2", "tslint": "^5.20.1", "tsd": "0.29.0", "typescript": "5.1.3", "yargs": "17.7.2"}, "scripts": {"lint": "eslint src", "pretest": "npm run lint && npm run test:typescript && npm run test:typescript:noninterop", "test": "npm run build && mocha test -R spec --exit", "test:typescript": "tslint --project tsconfig.json && tsd", "test:typescript:noninterop": "tslint --project tsconfig.nonEsModuleInterop.json", "build": "rimraf dist && rollup -c && echo '{\"type\":\"module\"}' > dist/esm/package.json  && cpy \"./dist/umd/*.js\" ./", "fix_dist_package": "node -e 'console.log(`{\"type\":\"module\",\"version\":\"${process.env.npm_package_version}\"}`)' > dist/esm/package.json", "preversion": "npm run test && npm run build && git push", "postversion": "npm run fix_dist_package && git push && git push --tags"}, "tsd": {"directory": "test/typescript"}, "author": "<PERSON> <<EMAIL>> (https://github.com/jamuhl)", "license": "MIT"}