(window.webpackJsonp=window.webpackJsonp||[]).push([[93],{424:function(r,e,t){"use strict";t.r(e);var o=t(6),a=Object(o.a)({},(function(){var r=this,e=r.$createElement,t=r._self._c||e;return t("ContentSlotsDistributor",{attrs:{"slot-key":r.$parent.slotKey}},[t("h1",{attrs:{id:"interface-pointprefixedhoveroptions"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#interface-pointprefixedhoveroptions"}},[r._v("#")]),r._v(" Interface: PointPrefixedHoverOptions")]),r._v(" "),t("h2",{attrs:{id:"properties"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[r._v("#")]),r._v(" Properties")]),r._v(" "),t("h3",{attrs:{id:"pointhoverbackgroundcolor"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#pointhoverbackgroundcolor"}},[r._v("#")]),r._v(" pointHoverBackgroundColor")]),r._v(" "),t("p",[r._v("• "),t("strong",[r._v("pointHoverBackgroundColor")]),r._v(": "),t("RouterLink",{attrs:{to:"/api/#color"}},[t("code",[r._v("Color")])])],1),r._v(" "),t("p",[r._v("Point background color when hovered.")]),r._v(" "),t("h4",{attrs:{id:"defined-in"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[r._v("#")]),r._v(" Defined in")]),r._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1950",target:"_blank",rel:"noopener noreferrer"}},[r._v("index.esm.d.ts:1950"),t("OutboundLink")],1)]),r._v(" "),t("hr"),r._v(" "),t("h3",{attrs:{id:"pointhoverbordercolor"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#pointhoverbordercolor"}},[r._v("#")]),r._v(" pointHoverBorderColor")]),r._v(" "),t("p",[r._v("• "),t("strong",[r._v("pointHoverBorderColor")]),r._v(": "),t("RouterLink",{attrs:{to:"/api/#color"}},[t("code",[r._v("Color")])])],1),r._v(" "),t("p",[r._v("Point border color when hovered.")]),r._v(" "),t("h4",{attrs:{id:"defined-in-2"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[r._v("#")]),r._v(" Defined in")]),r._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1954",target:"_blank",rel:"noopener noreferrer"}},[r._v("index.esm.d.ts:1954"),t("OutboundLink")],1)]),r._v(" "),t("hr"),r._v(" "),t("h3",{attrs:{id:"pointhoverborderwidth"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#pointhoverborderwidth"}},[r._v("#")]),r._v(" pointHoverBorderWidth")]),r._v(" "),t("p",[r._v("• "),t("strong",[r._v("pointHoverBorderWidth")]),r._v(": "),t("code",[r._v("number")])]),r._v(" "),t("p",[r._v("Border width of point when hovered.")]),r._v(" "),t("h4",{attrs:{id:"defined-in-3"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[r._v("#")]),r._v(" Defined in")]),r._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1958",target:"_blank",rel:"noopener noreferrer"}},[r._v("index.esm.d.ts:1958"),t("OutboundLink")],1)]),r._v(" "),t("hr"),r._v(" "),t("h3",{attrs:{id:"pointhoverradius"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#pointhoverradius"}},[r._v("#")]),r._v(" pointHoverRadius")]),r._v(" "),t("p",[r._v("• "),t("strong",[r._v("pointHoverRadius")]),r._v(": "),t("code",[r._v("number")])]),r._v(" "),t("p",[r._v("The radius of the point when hovered.")]),r._v(" "),t("h4",{attrs:{id:"defined-in-4"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[r._v("#")]),r._v(" Defined in")]),r._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1962",target:"_blank",rel:"noopener noreferrer"}},[r._v("index.esm.d.ts:1962"),t("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=a.exports}}]);