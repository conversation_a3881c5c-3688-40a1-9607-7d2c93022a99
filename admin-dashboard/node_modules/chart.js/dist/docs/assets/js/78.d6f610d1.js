(window.webpackJsonp=window.webpackJsonp||[]).push([[78],{409:function(t,e,a){"use strict";a.r(e);var r=a(6),i=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-legendoptions-ttype"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-legendoptions-ttype"}},[t._v("#")]),t._v(" Interface: LegendOptions<TType>")]),t._v(" "),a("h2",{attrs:{id:"type-parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"align"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#align"}},[t._v("#")]),t._v(" align")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("align")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#align"}},[a("code",[t._v("Align")])])],1),t._v(" "),a("p",[t._v("Alignment of the legend.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" 'center'")]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2274",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2274"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"display"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#display"}},[t._v("#")]),t._v(" display")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("display")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("Is the legend shown?")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" true")]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2264",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2264"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"fullsize"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#fullsize"}},[t._v("#")]),t._v(" fullSize")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("fullSize")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("Marks that this box should take the full width/height of the canvas (moving other boxes). This is unlikely to need to be changed in day-to-day use.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" true")]),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2287",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2287"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"labels"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#labels"}},[t._v("#")]),t._v(" labels")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("labels")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boxHeight")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Height of the coloured box.  "),a("strong",[a("code",[t._v("default")])]),t._v(" fontSize")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boxPadding")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Padding between the color box and the text  "),a("strong",[a("code",[t._v("default")])]),t._v(" 1")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boxWidth")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Width of colored box.  "),a("strong",[a("code",[t._v("default")])]),t._v(" 40")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("color")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[t._v("Color")])])],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Color of label  "),a("strong",[a("code",[t._v("see")])]),t._v(" Defaults.color")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("font")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#scriptableandscriptableoptions"}},[a("code",[t._v("ScriptableAndScriptableOptions")])]),t._v("<"),a("code",[t._v("Partial")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/FontSpec.html"}},[a("code",[t._v("FontSpec")])]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableChartContext.html"}},[a("code",[t._v("ScriptableChartContext")])]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Font of label  "),a("strong",[a("code",[t._v("see")])]),t._v(" Defaults.font")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("padding")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Padding between rows of colored boxes.  "),a("strong",[a("code",[t._v("default")])]),t._v(" 10")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("pointStyle")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#pointstyle"}},[a("code",[t._v("PointStyle")])])],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Override point style for the legend. Only applies if usePointStyle is true")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("textAlign?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#textalign"}},[a("code",[t._v("TextAlign")])])],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Text alignment")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("usePointStyle")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Label style will match corresponding point style (size is based on the minimum value between boxWidth and font.size).  "),a("strong",[a("code",[t._v("default")])]),t._v(" false")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("filter")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("("),a("code",[t._v("item")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/LegendItem.html"}},[a("code",[t._v("LegendItem")])]),t._v(", "),a("code",[t._v("data")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartData.html"}},[a("code",[t._v("ChartData")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">) => "),a("code",[t._v("boolean")])],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("generateLabels")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("("),a("code",[t._v("chart")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">) => "),a("RouterLink",{attrs:{to:"/api/interfaces/LegendItem.html"}},[a("code",[t._v("LegendItem")])]),t._v("[]")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("sort")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("("),a("code",[t._v("a")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/LegendItem.html"}},[a("code",[t._v("LegendItem")])]),t._v(", "),a("code",[t._v("b")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/LegendItem.html"}},[a("code",[t._v("LegendItem")])]),t._v(", "),a("code",[t._v("data")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartData.html"}},[a("code",[t._v("ChartData")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">) => "),a("code",[t._v("number")])],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2306",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2306"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"maxheight"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#maxheight"}},[t._v("#")]),t._v(" maxHeight")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("maxHeight")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Maximum height of the legend, in pixels")]),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2278",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2278"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"maxwidth"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#maxwidth"}},[t._v("#")]),t._v(" maxWidth")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("maxWidth")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Maximum width of the legend, in pixels")]),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2282",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2282"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"position"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#position"}},[t._v("#")]),t._v(" position")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("position")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#layoutposition"}},[a("code",[t._v("LayoutPosition")])])],1),t._v(" "),a("p",[t._v("Position of the legend.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" 'top'")]),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2269",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2269"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"reverse"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#reverse"}},[t._v("#")]),t._v(" reverse")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("reverse")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("Legend will show datasets in reverse order.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" false")]),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2292",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2292"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"rtl"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#rtl"}},[t._v("#")]),t._v(" rtl")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("rtl")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("true for rendering the legends from right to left.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2371",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2371"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"textdirection"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#textdirection"}},[t._v("#")]),t._v(" textDirection")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("textDirection")]),t._v(": "),a("code",[t._v("string")])]),t._v(" "),a("p",[t._v("This will force the text direction 'rtl' or 'ltr' on the canvas for rendering the legend, regardless of the css specified on the canvas")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" canvas' default")]),t._v(" "),a("h4",{attrs:{id:"defined-in-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2376",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2376"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"title"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#title"}},[t._v("#")]),t._v(" title")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("title")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-2"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("color")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[t._v("Color")])])],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Color of title  "),a("strong",[a("code",[t._v("see")])]),t._v(" Defaults.color")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("display")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Is the legend title displayed.  "),a("strong",[a("code",[t._v("default")])]),t._v(" false")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("font")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#scriptableandscriptableoptions"}},[a("code",[t._v("ScriptableAndScriptableOptions")])]),t._v("<"),a("code",[t._v("Partial")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/FontSpec.html"}},[a("code",[t._v("FontSpec")])]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableChartContext.html"}},[a("code",[t._v("ScriptableChartContext")])]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("see Fonts")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("padding?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("position")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"start"')]),t._v(" | "),a("code",[t._v('"end"')]),t._v(" | "),a("code",[t._v('"center"')])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("text")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("string")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The string title.")])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2378",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2378"),a("OutboundLink")],1)]),t._v(" "),a("h2",{attrs:{id:"methods"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),a("h3",{attrs:{id:"onclick"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#onclick"}},[t._v("#")]),t._v(" onClick")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("onClick")]),t._v("("),a("code",[t._v("e")]),t._v(", "),a("code",[t._v("legendItem")]),t._v(", "),a("code",[t._v("legend")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("A callback that is called when a click event is registered on a label item.")]),t._v(" "),a("h4",{attrs:{id:"parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("e")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ChartEvent.html"}},[a("code",[t._v("ChartEvent")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("legendItem")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/LegendItem.html"}},[a("code",[t._v("LegendItem")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("legend")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/LegendElement.html"}},[a("code",[t._v("LegendElement")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2296",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2296"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"onhover"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#onhover"}},[t._v("#")]),t._v(" onHover")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("onHover")]),t._v("("),a("code",[t._v("e")]),t._v(", "),a("code",[t._v("legendItem")]),t._v(", "),a("code",[t._v("legend")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("A callback that is called when a 'mousemove' event is registered on top of a label item")]),t._v(" "),a("h4",{attrs:{id:"parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-2"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("e")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ChartEvent.html"}},[a("code",[t._v("ChartEvent")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("legendItem")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/LegendItem.html"}},[a("code",[t._v("LegendItem")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("legend")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/LegendElement.html"}},[a("code",[t._v("LegendElement")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2300",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2300"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"onleave"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#onleave"}},[t._v("#")]),t._v(" onLeave")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("onLeave")]),t._v("("),a("code",[t._v("e")]),t._v(", "),a("code",[t._v("legendItem")]),t._v(", "),a("code",[t._v("legend")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("A callback that is called when a 'mousemove' event is registered outside of a previously hovered label item.")]),t._v(" "),a("h4",{attrs:{id:"parameters-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-3"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("e")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ChartEvent.html"}},[a("code",[t._v("ChartEvent")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("legendItem")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/LegendItem.html"}},[a("code",[t._v("LegendItem")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("legend")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/LegendElement.html"}},[a("code",[t._v("LegendElement")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-3"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2304",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2304"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=i.exports}}]);