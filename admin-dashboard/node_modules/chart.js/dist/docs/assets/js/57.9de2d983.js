(window.webpackJsonp=window.webpackJsonp||[]).push([[57],{388:function(t,e,r){"use strict";r.r(e);var a=r(6),s=Object(a.a)({},(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[r("h1",{attrs:{id:"interface-datasetcontrollerchartcomponent"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-datasetcontrollerchartcomponent"}},[t._v("#")]),t._v(" Interface: DatasetControllerChartComponent")]),t._v(" "),r("h2",{attrs:{id:"hierarchy"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),r("ul",[r("li",[r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[r("code",[t._v("ChartComponent")])])],1),t._v(" "),r("p",[t._v("↳ "),r("strong",[r("code",[t._v("DatasetControllerChartComponent")])])])])]),t._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),r("h3",{attrs:{id:"defaultroutes"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defaultroutes"}},[t._v("#")]),t._v(" defaultRoutes")]),t._v(" "),r("p",[t._v("• "),r("code",[t._v("Optional")]),t._v(" "),r("strong",[t._v("defaultRoutes")]),t._v(": "),r("code",[t._v("Object")])]),t._v(" "),r("h4",{attrs:{id:"index-signature"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#index-signature"}},[t._v("#")]),t._v(" Index signature")]),t._v(" "),r("p",[t._v("▪ [property: "),r("code",[t._v("string")]),t._v("]: "),r("code",[t._v("string")])]),t._v(" "),r("h4",{attrs:{id:"inherited-from"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[t._v("ChartComponent")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html#defaultroutes"}},[t._v("defaultRoutes")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1414",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1414"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"defaults"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defaults"}},[t._v("#")]),t._v(" defaults")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("defaults")]),t._v(": "),r("code",[t._v("Object")])]),t._v(" "),r("h4",{attrs:{id:"type-declaration"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),r("table",[r("thead",[r("tr",[r("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),r("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),r("tbody",[r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("dataElementType?")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("string")]),t._v(" | "),r("code",[t._v("false")])])]),t._v(" "),r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("datasetElementType?")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("string")]),t._v(" | "),r("code",[t._v("false")])])])])]),t._v(" "),r("h4",{attrs:{id:"overrides"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#overrides"}},[t._v("#")]),t._v(" Overrides")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[t._v("ChartComponent")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html#defaults"}},[t._v("defaults")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L651",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:651"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"id"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#id"}},[t._v("#")]),t._v(" id")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("id")]),t._v(": "),r("code",[t._v("string")])]),t._v(" "),r("h4",{attrs:{id:"inherited-from-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[t._v("ChartComponent")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html#id"}},[t._v("id")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1412",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1412"),r("OutboundLink")],1)]),t._v(" "),r("h2",{attrs:{id:"methods"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),r("h3",{attrs:{id:"afterregister"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#afterregister"}},[t._v("#")]),t._v(" afterRegister")]),t._v(" "),r("p",[t._v("▸ "),r("code",[t._v("Optional")]),t._v(" "),r("strong",[t._v("afterRegister")]),t._v("(): "),r("code",[t._v("void")])]),t._v(" "),r("h4",{attrs:{id:"returns"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),r("p",[r("code",[t._v("void")])]),t._v(" "),r("h4",{attrs:{id:"inherited-from-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-3"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[t._v("ChartComponent")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html#afterregister"}},[t._v("afterRegister")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1417",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1417"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"afterunregister"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#afterunregister"}},[t._v("#")]),t._v(" afterUnregister")]),t._v(" "),r("p",[t._v("▸ "),r("code",[t._v("Optional")]),t._v(" "),r("strong",[t._v("afterUnregister")]),t._v("(): "),r("code",[t._v("void")])]),t._v(" "),r("h4",{attrs:{id:"returns-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),r("p",[r("code",[t._v("void")])]),t._v(" "),r("h4",{attrs:{id:"inherited-from-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-4"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[t._v("ChartComponent")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html#afterunregister"}},[t._v("afterUnregister")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-5"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1419",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1419"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"beforeregister"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#beforeregister"}},[t._v("#")]),t._v(" beforeRegister")]),t._v(" "),r("p",[t._v("▸ "),r("code",[t._v("Optional")]),t._v(" "),r("strong",[t._v("beforeRegister")]),t._v("(): "),r("code",[t._v("void")])]),t._v(" "),r("h4",{attrs:{id:"returns-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#returns-3"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),r("p",[r("code",[t._v("void")])]),t._v(" "),r("h4",{attrs:{id:"inherited-from-5"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-5"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[t._v("ChartComponent")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html#beforeregister"}},[t._v("beforeRegister")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-6"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1416",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1416"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"beforeunregister"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#beforeunregister"}},[t._v("#")]),t._v(" beforeUnregister")]),t._v(" "),r("p",[t._v("▸ "),r("code",[t._v("Optional")]),t._v(" "),r("strong",[t._v("beforeUnregister")]),t._v("(): "),r("code",[t._v("void")])]),t._v(" "),r("h4",{attrs:{id:"returns-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#returns-4"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),r("p",[r("code",[t._v("void")])]),t._v(" "),r("h4",{attrs:{id:"inherited-from-6"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-6"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[t._v("ChartComponent")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html#beforeunregister"}},[t._v("beforeUnregister")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-7"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1418",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1418"),r("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);