(window.webpackJsonp=window.webpackJsonp||[]).push([[98],{429:function(t,e,r){"use strict";r.r(e);var a=r(6),o=Object(a.a)({},(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[r("h1",{attrs:{id:"interface-polarareacontrollerdatasetoptions"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-polarareacontrollerdatasetoptions"}},[t._v("#")]),t._v(" Interface: PolarAreaControllerDatasetOptions")]),t._v(" "),r("h2",{attrs:{id:"hierarchy"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),r("ul",[r("li",[r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[r("code",[t._v("DoughnutControllerDatasetOptions")])])],1),t._v(" "),r("p",[t._v("↳ "),r("strong",[r("code",[t._v("PolarAreaControllerDatasetOptions")])])])])]),t._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),r("h3",{attrs:{id:"angle"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#angle"}},[t._v("#")]),t._v(" angle")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("angle")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Arc angle to cover. - for polar only")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" circumference / (arc count)")]),t._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L370",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:370"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"animation"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#animation"}},[t._v("#")]),t._v(" animation")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("animation")]),t._v(": "),r("code",[t._v("false")]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/#animationspec"}},[r("code",[t._v("AnimationSpec")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v("> & { "),r("code",[t._v("onComplete?")]),t._v(": ("),r("code",[t._v("event")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/interfaces/AnimationEvent.html"}},[r("code",[t._v("AnimationEvent")])]),t._v(") => "),r("code",[t._v("void")]),t._v(" ; "),r("code",[t._v("onProgress?")]),t._v(": ("),r("code",[t._v("event")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/interfaces/AnimationEvent.html"}},[r("code",[t._v("AnimationEvent")])]),t._v(") => "),r("code",[t._v("void")]),t._v("  }")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#animation"}},[t._v("animation")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1640",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1640"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"animations"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#animations"}},[t._v("#")]),t._v(" animations")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("animations")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#animationsspec"}},[r("code",[t._v("AnimationsSpec")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v(">")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#animations"}},[t._v("animations")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1650",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1650"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"backgroundcolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#backgroundcolor"}},[t._v("#")]),t._v(" backgroundColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("backgroundColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-3"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#backgroundcolor"}},[t._v("backgroundColor")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1696",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1696"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderalign"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderalign"}},[t._v("#")]),t._v(" borderAlign")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderAlign")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v('"center"')]),t._v(" | "),r("code",[t._v('"inner"')]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Arc stroke alignment.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-4"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#borderalign"}},[t._v("borderAlign")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-5"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1732",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1732"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"bordercolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#bordercolor"}},[t._v("#")]),t._v(" borderColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-5"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-5"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#bordercolor"}},[t._v("borderColor")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-6"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1695",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1695"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderjoinstyle"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderjoinstyle"}},[t._v("#")]),t._v(" borderJoinStyle")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderJoinStyle")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("CanvasLineJoin")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Line join style. See MDN. Default is 'round' when "),r("code",[t._v("borderAlign")]),t._v(" is 'inner', else 'bevel'.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-6"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-6"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#borderjoinstyle"}},[t._v("borderJoinStyle")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-7"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1737",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1737"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderradius"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderradius"}},[t._v("#")]),t._v(" borderRadius")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderRadius")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/ArcBorderRadius.html"}},[r("code",[t._v("ArcBorderRadius")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Sets the border radius for arcs")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-7"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-7"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#borderradius"}},[t._v("borderRadius")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-8"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1743",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1743"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderwidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderwidth"}},[t._v("#")]),t._v(" borderWidth")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderWidth")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-8"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-8"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#borderwidth"}},[t._v("borderWidth")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-9"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1694",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1694"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"circular"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#circular"}},[t._v("#")]),t._v(" circular")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("circular")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("boolean")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("If false, Arc will be flat.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" true")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-9"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-9"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#circular"}},[t._v("circular")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-10"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1754",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1754"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"circumference"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#circumference"}},[t._v("#")]),t._v(" circumference")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("circumference")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Sweep to allow arcs to cover.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 360")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-10"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-10"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#circumference"}},[t._v("circumference")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-11"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L250",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:250"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"clip"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#clip"}},[t._v("#")]),t._v(" clip")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("clip")]),t._v(": "),r("code",[t._v("number")]),t._v(" | "),r("code",[t._v("false")]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[r("code",[t._v("ChartArea")])])],1),t._v(" "),r("p",[t._v("How to clip relative to chartArea. Positive value allows overflow, negative value clips that many pixels inside chartArea. 0 = clip at chartArea. Clipping can also be configured per side: clip: {left: 5, top: false, right: -2, bottom: 0}")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-11"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-11"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#clip"}},[t._v("clip")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-12"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L70",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:70"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hidden"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hidden"}},[t._v("#")]),t._v(" hidden")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hidden")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[t._v("Configures the visibility state of the dataset. Set it to true, to hide the dataset from the chart.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" false")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-12"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-12"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#hidden"}},[t._v("hidden")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-13"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L88",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:88"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hoverbackgroundcolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hoverbackgroundcolor"}},[t._v("#")]),t._v(" hoverBackgroundColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hoverBackgroundColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-13"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-13"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#hoverbackgroundcolor"}},[t._v("hoverBackgroundColor")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-14"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1702",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1702"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hoverbordercolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hoverbordercolor"}},[t._v("#")]),t._v(" hoverBorderColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hoverBorderColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-14"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-14"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#hoverbordercolor"}},[t._v("hoverBorderColor")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-15"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-15"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1701",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1701"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hoverborderwidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hoverborderwidth"}},[t._v("#")]),t._v(" hoverBorderWidth")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hoverBorderWidth")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-15"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-15"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#hoverborderwidth"}},[t._v("hoverBorderWidth")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-16"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-16"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1700",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1700"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hoveroffset"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hoveroffset"}},[t._v("#")]),t._v(" hoverOffset")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hoverOffset")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-16"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-16"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#hoveroffset"}},[t._v("hoverOffset")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-17"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-17"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1758",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1758"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"indexaxis"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#indexaxis"}},[t._v("#")]),t._v(" indexAxis")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("indexAxis")]),t._v(": "),r("code",[t._v('"x"')]),t._v(" | "),r("code",[t._v('"y"')])]),t._v(" "),r("p",[t._v("The base axis of the chart. 'x' for vertical charts and 'y' for horizontal charts.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'x'")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-17"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-17"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#indexaxis"}},[t._v("indexAxis")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-18"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-18"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L66",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:66"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"label"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#label"}},[t._v("#")]),t._v(" label")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("label")]),t._v(": "),r("code",[t._v("string")])]),t._v(" "),r("p",[t._v("The label for the dataset which appears in the legend and tooltips.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-18"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-18"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#label"}},[t._v("label")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-19"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-19"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L74",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:74"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"normalized"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#normalized"}},[t._v("#")]),t._v(" normalized")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("normalized")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[t._v("Chart.js is fastest if you provide data with indices that are unique, sorted, and consistent across datasets and provide the normalized: true option to let Chart.js know that you have done so.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-19"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-19"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#normalized"}},[t._v("normalized")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-20"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-20"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L58",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:58"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"offset"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#offset"}},[t._v("#")]),t._v(" offset")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("offset")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Arc offset (in pixels).")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-20"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-20"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#offset"}},[t._v("offset")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-21"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-21"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L255",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:255"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"order"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#order"}},[t._v("#")]),t._v(" order")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("order")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("The drawing order of dataset. Also affects order for stacking, tooltip and legend.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-21"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-21"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#order"}},[t._v("order")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-22"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-22"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L78",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:78"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"parsing"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#parsing"}},[t._v("#")]),t._v(" parsing")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("parsing")]),t._v(": "),r("code",[t._v("false")]),t._v(" | { [key: string]: "),r("code",[t._v("string")]),t._v(";  }")]),t._v(" "),r("p",[t._v("How to parse the dataset. The parsing can be disabled by specifying parsing: false at chart options or dataset. If parsing is disabled, data must be sorted and in the formats the associated chart type and scales use internally.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-22"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-22"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#parsing"}},[t._v("parsing")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-23"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-23"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L49",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:49"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"rotation"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#rotation"}},[t._v("#")]),t._v(" rotation")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("rotation")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Starting angle to draw this dataset from.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-23"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-23"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#rotation"}},[t._v("rotation")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-24"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-24"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L261",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:261"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"spacing"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#spacing"}},[t._v("#")]),t._v(" spacing")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("spacing")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Similar to the "),r("code",[t._v("offset")]),t._v(" option, but applies to all arcs. This can be used to to add spaces\nbetween arcs")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-24"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-24"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#spacing"}},[t._v("spacing")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-25"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-25"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L274",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:274"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"stack"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#stack"}},[t._v("#")]),t._v(" stack")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("stack")]),t._v(": "),r("code",[t._v("string")])]),t._v(" "),r("p",[t._v("The ID of the group to which this dataset belongs to (when stacked, each group will be a separate stack).")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-25"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-25"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#stack"}},[t._v("stack")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-26"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-26"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L83",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:83"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"transitions"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#transitions"}},[t._v("#")]),t._v(" transitions")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("transitions")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#transitionsspec"}},[r("code",[t._v("TransitionsSpec")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v(">")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-26"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-26"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#transitions"}},[t._v("transitions")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-27"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-27"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1651",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1651"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"weight"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#weight"}},[t._v("#")]),t._v(" weight")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("weight")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("The relative thickness of the dataset. Providing a value for weight will cause the pie or doughnut dataset to be drawn with a thickness relative to the sum of all the dataset weight values.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 1")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-27"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-27"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html#weight"}},[t._v("weight")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-28"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-28"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L267",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:267"),r("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=o.exports}}]);