(window.webpackJsonp=window.webpackJsonp||[]).push([[69],{400:function(e,t,r){"use strict";r.r(t);var a=r(6),s=Object(a.a)({},(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("ContentSlotsDistributor",{attrs:{"slot-key":e.$parent.slotKey}},[r("h1",{attrs:{id:"interface-filleroptions"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-filleroptions"}},[e._v("#")]),e._v(" Interface: FillerOptions")]),e._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[e._v("#")]),e._v(" Properties")]),e._v(" "),r("h3",{attrs:{id:"drawtime"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#drawtime"}},[e._v("#")]),e._v(" drawTime")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("drawTime")]),e._v(": "),r("code",[e._v('"beforeDatasetDraw"')]),e._v(" | "),r("code",[e._v('"beforeDatasetsDraw"')])]),e._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2134",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2134"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"propagate"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#propagate"}},[e._v("#")]),e._v(" propagate")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("propagate")]),e._v(": "),r("code",[e._v("boolean")])]),e._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2135",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2135"),r("OutboundLink")],1)])])}),[],!1,null,null,null);t.default=s.exports}}]);