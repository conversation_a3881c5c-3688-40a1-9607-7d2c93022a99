(window.webpackJsonp=window.webpackJsonp||[]).push([[180],{510:function(t,r,e){"use strict";e.r(r);var s=e(6),a=Object(s.a)({},(function(){var t=this,r=t.$createElement,e=t._self._c||r;return e("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[e("h2",{attrs:{id:"npm"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#npm"}},[t._v("#")]),t._v(" npm")]),t._v(" "),e("p",[e("a",{attrs:{href:"https://npmjs.com/package/chart.js",target:"_blank",rel:"noopener noreferrer"}},[e("img",{attrs:{src:"https://img.shields.io/npm/v/chart.js.svg?style=flat-square&maxAge=600",alt:"npm"}}),e("OutboundLink")],1),t._v(" "),e("a",{attrs:{href:"https://npmjs.com/package/chart.js",target:"_blank",rel:"noopener noreferrer"}},[e("img",{attrs:{src:"https://img.shields.io/npm/dm/chart.js.svg?style=flat-square&maxAge=600",alt:"npm"}}),e("OutboundLink")],1)]),t._v(" "),e("div",{staticClass:"language-sh extra-class"},[e("pre",{pre:!0,attrs:{class:"language-sh"}},[e("code",[e("span",{pre:!0,attrs:{class:"token function"}},[t._v("npm")]),t._v(" "),e("span",{pre:!0,attrs:{class:"token function"}},[t._v("install")]),t._v(" chart.js\n")])])]),e("h2",{attrs:{id:"cdn"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#cdn"}},[t._v("#")]),t._v(" CDN")]),t._v(" "),e("h3",{attrs:{id:"cdnjs"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#cdnjs"}},[t._v("#")]),t._v(" CDNJS")]),t._v(" "),e("p",[e("a",{attrs:{href:"https://cdnjs.com/libraries/Chart.js",target:"_blank",rel:"noopener noreferrer"}},[e("img",{attrs:{src:"https://img.shields.io/cdnjs/v/Chart.js.svg?style=flat-square&maxAge=600",alt:"cdnjs"}}),e("OutboundLink")],1)]),t._v(" "),e("p",[t._v("Chart.js built files are available on "),e("a",{attrs:{href:"https://cdnjs.com/",target:"_blank",rel:"noopener noreferrer"}},[t._v("CDNJS"),e("OutboundLink")],1),t._v(":")]),t._v(" "),e("p",[e("a",{attrs:{href:"https://cdnjs.com/libraries/Chart.js",target:"_blank",rel:"noopener noreferrer"}},[t._v("https://cdnjs.com/libraries/Chart.js"),e("OutboundLink")],1)]),t._v(" "),e("h3",{attrs:{id:"jsdelivr"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#jsdelivr"}},[t._v("#")]),t._v(" jsDelivr")]),t._v(" "),e("p",[e("a",{attrs:{href:"https://cdn.jsdelivr.net/npm/chart.js@latest/dist/",target:"_blank",rel:"noopener noreferrer"}},[e("img",{attrs:{src:"https://img.shields.io/npm/v/chart.js.svg?label=jsdelivr&style=flat-square&maxAge=600",alt:"jsdelivr"}}),e("OutboundLink")],1),t._v(" "),e("a",{attrs:{href:"https://www.jsdelivr.com/package/npm/chart.js",target:"_blank",rel:"noopener noreferrer"}},[e("img",{attrs:{src:"https://data.jsdelivr.com/v1/package/npm/chart.js/badge",alt:"jsdelivr hits"}}),e("OutboundLink")],1)]),t._v(" "),e("p",[t._v("Chart.js built files are also available through "),e("a",{attrs:{href:"https://www.jsdelivr.com/",target:"_blank",rel:"noopener noreferrer"}},[t._v("jsDelivr"),e("OutboundLink")],1),t._v(":")]),t._v(" "),e("p",[e("a",{attrs:{href:"https://www.jsdelivr.com/package/npm/chart.js?path=dist",target:"_blank",rel:"noopener noreferrer"}},[t._v("https://www.jsdelivr.com/package/npm/chart.js?path=dist"),e("OutboundLink")],1)]),t._v(" "),e("h2",{attrs:{id:"github"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#github"}},[t._v("#")]),t._v(" Github")]),t._v(" "),e("p",[e("a",{attrs:{href:"https://github.com/chartjs/Chart.js/releases/latest",target:"_blank",rel:"noopener noreferrer"}},[e("img",{attrs:{src:"https://img.shields.io/github/release/chartjs/Chart.js.svg?style=flat-square&maxAge=600",alt:"github"}}),e("OutboundLink")],1)]),t._v(" "),e("p",[t._v("You can download the latest version of "),e("a",{attrs:{href:"https://github.com/chartjs/Chart.js/releases/latest",target:"_blank",rel:"noopener noreferrer"}},[t._v("Chart.js on GitHub"),e("OutboundLink")],1),t._v(".")]),t._v(" "),e("p",[t._v("If you download or clone the repository, you must "),e("RouterLink",{attrs:{to:"/developers/contributing.html#building-and-testing"}},[t._v("build")]),t._v(" Chart.js to generate the dist files. Chart.js no longer comes with prebuilt release versions, so an alternative option to downloading the repo is "),e("strong",[t._v("strongly")]),t._v(" advised.")],1)])}),[],!1,null,null,null);r.default=a.exports}}]);