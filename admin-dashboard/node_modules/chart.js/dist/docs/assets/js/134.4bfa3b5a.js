(window.webpackJsonp=window.webpackJsonp||[]).push([[134],{463:function(t,e,o){"use strict";o.r(e);var a=o(6),v=Object(a.a)({},(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[o("h1",{attrs:{id:"logarithmic-axis"}},[o("a",{staticClass:"header-anchor",attrs:{href:"#logarithmic-axis"}},[t._v("#")]),t._v(" Logarithmic Axis")]),t._v(" "),o("p",[t._v("The logarithmic scale is used to chart numerical data. It can be placed on either the x or y-axis. As the name suggests, logarithmic interpolation is used to determine where a value lies on the axis.")]),t._v(" "),o("h2",{attrs:{id:"configuration-options"}},[o("a",{staticClass:"header-anchor",attrs:{href:"#configuration-options"}},[t._v("#")]),t._v(" Configuration Options")]),t._v(" "),o("h3",{attrs:{id:"common-options-to-all-cartesian-axes"}},[o("a",{staticClass:"header-anchor",attrs:{href:"#common-options-to-all-cartesian-axes"}},[t._v("#")]),t._v(" Common options to all cartesian axes")]),t._v(" "),o("p",[t._v("Namespace: "),o("code",[t._v("options.scales[scaleId]")])]),t._v(" "),o("table",[o("thead",[o("tr",[o("th",[t._v("Name")]),t._v(" "),o("th",[t._v("Type")]),t._v(" "),o("th",[t._v("Default")]),t._v(" "),o("th",[t._v("Description")])])]),t._v(" "),o("tbody",[o("tr",[o("td",[o("code",[t._v("bounds")])]),t._v(" "),o("td",[o("code",[t._v("string")])]),t._v(" "),o("td",[o("code",[t._v("'ticks'")])]),t._v(" "),o("td",[t._v("Determines the scale bounds. "),o("RouterLink",{attrs:{to:"/axes/cartesian/#scale-bounds"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("position")])]),t._v(" "),o("td",[o("code",[t._v("string")]),t._v(" | "),o("code",[t._v("object")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Position of the axis. "),o("RouterLink",{attrs:{to:"/axes/cartesian/#axis-position"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("stack")])]),t._v(" "),o("td",[o("code",[t._v("string")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Stack group. Axes at the same "),o("code",[t._v("position")]),t._v(" with same "),o("code",[t._v("stack")]),t._v(" are stacked.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("stackWeight")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td",[t._v("1")]),t._v(" "),o("td",[t._v("Weight of the scale in stack group. Used to determine the amount of allocated space for the scale within the group.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("axis")])]),t._v(" "),o("td",[o("code",[t._v("string")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Which type of axis this is. Possible values are: "),o("code",[t._v("'x'")]),t._v(", "),o("code",[t._v("'y'")]),t._v(". If not set, this is inferred from the first character of the ID which should be "),o("code",[t._v("'x'")]),t._v(" or "),o("code",[t._v("'y'")]),t._v(".")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("offset")])]),t._v(" "),o("td",[o("code",[t._v("boolean")])]),t._v(" "),o("td",[o("code",[t._v("false")])]),t._v(" "),o("td",[t._v("If true, extra space is added to the both edges and the axis is scaled to fit into the chart area. This is set to "),o("code",[t._v("true")]),t._v(" for a bar chart by default.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("title")])]),t._v(" "),o("td",[o("code",[t._v("object")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Scale title configuration. "),o("RouterLink",{attrs:{to:"/axes/labelling.html#scale-title-configuration"}},[t._v("more...")])],1)])])]),t._v(" "),o("h3",{attrs:{id:"common-options-to-all-axes"}},[o("a",{staticClass:"header-anchor",attrs:{href:"#common-options-to-all-axes"}},[t._v("#")]),t._v(" Common options to all axes")]),t._v(" "),o("p",[t._v("Namespace: "),o("code",[t._v("options.scales[scaleId]")])]),t._v(" "),o("table",[o("thead",[o("tr",[o("th",[t._v("Name")]),t._v(" "),o("th",[t._v("Type")]),t._v(" "),o("th",[t._v("Default")]),t._v(" "),o("th",[t._v("Description")])])]),t._v(" "),o("tbody",[o("tr",[o("td",[o("code",[t._v("type")])]),t._v(" "),o("td",[o("code",[t._v("string")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Type of scale being employed. Custom scales can be created and registered with a string key. This allows changing the type of an axis for a chart.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("alignToPixels")])]),t._v(" "),o("td",[o("code",[t._v("boolean")])]),t._v(" "),o("td",[o("code",[t._v("false")])]),t._v(" "),o("td",[t._v("Align pixel values to device pixels.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("backgroundColor")])]),t._v(" "),o("td",[o("RouterLink",{attrs:{to:"/general/colors.html"}},[o("code",[t._v("Color")])])],1),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Background color of the scale area.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("display")])]),t._v(" "),o("td",[o("code",[t._v("boolean")]),t._v("|"),o("code",[t._v("string")])]),t._v(" "),o("td",[o("code",[t._v("true")])]),t._v(" "),o("td",[t._v("Controls the axis global visibility (visible when "),o("code",[t._v("true")]),t._v(", hidden when "),o("code",[t._v("false")]),t._v("). When "),o("code",[t._v("display: 'auto'")]),t._v(", the axis is visible only if at least one associated dataset is visible.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("grid")])]),t._v(" "),o("td",[o("code",[t._v("object")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Grid line configuration. "),o("RouterLink",{attrs:{to:"/axes/styling.html#grid-line-configuration"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("min")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("User defined minimum number for the scale, overrides minimum value from data. "),o("RouterLink",{attrs:{to:"/axes/#axis-range-settings"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("max")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("User defined maximum number for the scale, overrides maximum value from data. "),o("RouterLink",{attrs:{to:"/axes/#axis-range-settings"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("reverse")])]),t._v(" "),o("td",[o("code",[t._v("boolean")])]),t._v(" "),o("td",[o("code",[t._v("false")])]),t._v(" "),o("td",[t._v("Reverse the scale.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("stacked")])]),t._v(" "),o("td",[o("code",[t._v("boolean")]),t._v("|"),o("code",[t._v("string")])]),t._v(" "),o("td",[o("code",[t._v("false")])]),t._v(" "),o("td",[t._v("Should the data be stacked. "),o("RouterLink",{attrs:{to:"/axes/#stacking"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("suggestedMax")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Adjustment used when calculating the maximum data value. "),o("RouterLink",{attrs:{to:"/axes/#axis-range-settings"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("suggestedMin")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Adjustment used when calculating the minimum data value. "),o("RouterLink",{attrs:{to:"/axes/#axis-range-settings"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("ticks")])]),t._v(" "),o("td",[o("code",[t._v("object")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Tick configuration. "),o("RouterLink",{attrs:{to:"/axes/#tick-configuration"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("weight")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td",[o("code",[t._v("0")])]),t._v(" "),o("td",[t._v("The weight used to sort the axis. Higher weights are further away from the chart area.")])])])]),t._v(" "),o("h2",{attrs:{id:"tick-configuration"}},[o("a",{staticClass:"header-anchor",attrs:{href:"#tick-configuration"}},[t._v("#")]),t._v(" Tick Configuration")]),t._v(" "),o("h3",{attrs:{id:"logarithmic-axis-specific-options"}},[o("a",{staticClass:"header-anchor",attrs:{href:"#logarithmic-axis-specific-options"}},[t._v("#")]),t._v(" Logarithmic Axis specific options")]),t._v(" "),o("p",[t._v("Namespace: "),o("code",[t._v("options.scales[scaleId]")])]),t._v(" "),o("table",[o("thead",[o("tr",[o("th",[t._v("Name")]),t._v(" "),o("th",[t._v("Type")]),t._v(" "),o("th",[t._v("Default")]),t._v(" "),o("th",[t._v("Description")])])]),t._v(" "),o("tbody",[o("tr",[o("td",[o("code",[t._v("format")])]),t._v(" "),o("td",[o("code",[t._v("object")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("The "),o("a",{attrs:{href:"https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/NumberFormat",target:"_blank",rel:"noopener noreferrer"}},[o("code",[t._v("Intl.NumberFormat")]),o("OutboundLink")],1),t._v(" options used by the default label formatter")])])])]),t._v(" "),o("h3",{attrs:{id:"common-tick-options-to-all-cartesian-axes"}},[o("a",{staticClass:"header-anchor",attrs:{href:"#common-tick-options-to-all-cartesian-axes"}},[t._v("#")]),t._v(" Common tick options to all cartesian axes")]),t._v(" "),o("p",[t._v("Namespace: "),o("code",[t._v("options.scales[scaleId].ticks")])]),t._v(" "),o("table",[o("thead",[o("tr",[o("th",[t._v("Name")]),t._v(" "),o("th",[t._v("Type")]),t._v(" "),o("th",[t._v("Default")]),t._v(" "),o("th",[t._v("Description")])])]),t._v(" "),o("tbody",[o("tr",[o("td",[o("code",[t._v("align")])]),t._v(" "),o("td",[o("code",[t._v("string")])]),t._v(" "),o("td",[o("code",[t._v("'center'")])]),t._v(" "),o("td",[t._v("The tick alignment along the axis. Can be "),o("code",[t._v("'start'")]),t._v(", "),o("code",[t._v("'center'")]),t._v(", "),o("code",[t._v("'end'")]),t._v(", or "),o("code",[t._v("'inner'")]),t._v(". "),o("code",[t._v("inner")]),t._v(" alignment means align "),o("code",[t._v("start")]),t._v(" for first tick and "),o("code",[t._v("end")]),t._v(" for the last tick of horizontal axis")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("crossAlign")])]),t._v(" "),o("td",[o("code",[t._v("string")])]),t._v(" "),o("td",[o("code",[t._v("'near'")])]),t._v(" "),o("td",[t._v("The tick alignment perpendicular to the axis. Can be "),o("code",[t._v("'near'")]),t._v(", "),o("code",[t._v("'center'")]),t._v(", or "),o("code",[t._v("'far'")]),t._v(". See "),o("RouterLink",{attrs:{to:"/axes/cartesian/#tick-alignment"}},[t._v("Tick Alignment")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("sampleSize")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td",[o("code",[t._v("ticks.length")])]),t._v(" "),o("td",[t._v("The number of ticks to examine when deciding how many labels will fit. Setting a smaller value will be faster, but may be less accurate when there is large variability in label length.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("autoSkip")])]),t._v(" "),o("td",[o("code",[t._v("boolean")])]),t._v(" "),o("td",[o("code",[t._v("true")])]),t._v(" "),o("td",[t._v("If true, automatically calculates how many labels can be shown and hides labels accordingly. Labels will be rotated up to "),o("code",[t._v("maxRotation")]),t._v(" before skipping any. Turn "),o("code",[t._v("autoSkip")]),t._v(" off to show all labels no matter what.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("autoSkipPadding")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td",[o("code",[t._v("3")])]),t._v(" "),o("td",[t._v("Padding between the ticks on the horizontal axis when "),o("code",[t._v("autoSkip")]),t._v(" is enabled.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("includeBounds")])]),t._v(" "),o("td",[o("code",[t._v("boolean")])]),t._v(" "),o("td",[o("code",[t._v("true")])]),t._v(" "),o("td",[t._v("Should the defined "),o("code",[t._v("min")]),t._v(" and "),o("code",[t._v("max")]),t._v(' values be presented as ticks even if they are not "nice".')])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("labelOffset")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td",[o("code",[t._v("0")])]),t._v(" "),o("td",[t._v("Distance in pixels to offset the label from the centre point of the tick (in the x-direction for the x-axis, and the y-direction for the y-axis). "),o("em",[t._v("Note: this can cause labels at the edges to be cropped by the edge of the canvas")])])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("maxRotation")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td",[o("code",[t._v("50")])]),t._v(" "),o("td",[t._v("Maximum rotation for tick labels when rotating to condense labels. Note: Rotation doesn't occur until necessary. "),o("em",[t._v("Note: Only applicable to horizontal scales.")])])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("minRotation")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td",[o("code",[t._v("0")])]),t._v(" "),o("td",[t._v("Minimum rotation for tick labels. "),o("em",[t._v("Note: Only applicable to horizontal scales.")])])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("mirror")])]),t._v(" "),o("td",[o("code",[t._v("boolean")])]),t._v(" "),o("td",[o("code",[t._v("false")])]),t._v(" "),o("td",[t._v("Flips tick labels around axis, displaying the labels inside the chart instead of outside. "),o("em",[t._v("Note: Only applicable to vertical scales.")])])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("padding")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td",[o("code",[t._v("0")])]),t._v(" "),o("td",[t._v("Padding between the tick label and the axis. When set on a vertical axis, this applies in the horizontal (X) direction. When set on a horizontal axis, this applies in the vertical (Y) direction.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("maxTicksLimit")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td",[o("code",[t._v("11")])]),t._v(" "),o("td",[t._v("Maximum number of ticks and gridlines to show.")])])])]),t._v(" "),o("h3",{attrs:{id:"common-tick-options-to-all-axes"}},[o("a",{staticClass:"header-anchor",attrs:{href:"#common-tick-options-to-all-axes"}},[t._v("#")]),t._v(" Common tick options to all axes")]),t._v(" "),o("p",[t._v("Namespace: "),o("code",[t._v("options.scales[scaleId].ticks")])]),t._v(" "),o("table",[o("thead",[o("tr",[o("th",[t._v("Name")]),t._v(" "),o("th",[t._v("Type")]),t._v(" "),o("th",{staticStyle:{"text-align":"center"}},[t._v("Scriptable")]),t._v(" "),o("th",[t._v("Default")]),t._v(" "),o("th",[t._v("Description")])])]),t._v(" "),o("tbody",[o("tr",[o("td",[o("code",[t._v("backdropColor")])]),t._v(" "),o("td",[o("RouterLink",{attrs:{to:"/general/colors.html"}},[o("code",[t._v("Color")])])],1),t._v(" "),o("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),o("td",[o("code",[t._v("'rgba(255, 255, 255, 0.75)'")])]),t._v(" "),o("td",[t._v("Color of label backdrops.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("backdropPadding")])]),t._v(" "),o("td",[o("RouterLink",{attrs:{to:"/general/padding.html"}},[o("code",[t._v("Padding")])])],1),t._v(" "),o("td",{staticStyle:{"text-align":"center"}}),t._v(" "),o("td",[o("code",[t._v("2")])]),t._v(" "),o("td",[t._v("Padding of label backdrop.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("callback")])]),t._v(" "),o("td",[o("code",[t._v("function")])]),t._v(" "),o("td",{staticStyle:{"text-align":"center"}}),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Returns the string representation of the tick value as it should be displayed on the chart. See "),o("RouterLink",{attrs:{to:"/axes/labelling.html#creating-custom-tick-formats"}},[t._v("callback")]),t._v(".")],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("display")])]),t._v(" "),o("td",[o("code",[t._v("boolean")])]),t._v(" "),o("td",{staticStyle:{"text-align":"center"}}),t._v(" "),o("td",[o("code",[t._v("true")])]),t._v(" "),o("td",[t._v("If true, show tick labels.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("color")])]),t._v(" "),o("td",[o("RouterLink",{attrs:{to:"/general/colors.html"}},[o("code",[t._v("Color")])])],1),t._v(" "),o("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),o("td",[o("code",[t._v("Chart.defaults.color")])]),t._v(" "),o("td",[t._v("Color of ticks.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("font")])]),t._v(" "),o("td",[o("code",[t._v("Font")])]),t._v(" "),o("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),o("td",[o("code",[t._v("Chart.defaults.font")])]),t._v(" "),o("td",[t._v("See "),o("RouterLink",{attrs:{to:"/general/fonts.html"}},[t._v("Fonts")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("major")])]),t._v(" "),o("td",[o("code",[t._v("object")])]),t._v(" "),o("td",{staticStyle:{"text-align":"center"}}),t._v(" "),o("td",[o("code",[t._v("{}")])]),t._v(" "),o("td",[o("RouterLink",{attrs:{to:"/axes/styling.html#major-tick-configuration"}},[t._v("Major ticks configuration")]),t._v(".")],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("padding")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td",{staticStyle:{"text-align":"center"}}),t._v(" "),o("td",[o("code",[t._v("3")])]),t._v(" "),o("td",[t._v("Sets the offset of the tick labels from the axis")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("showLabelBackdrop")])]),t._v(" "),o("td",[o("code",[t._v("boolean")])]),t._v(" "),o("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),o("td",[o("code",[t._v("true")]),t._v(" for radial scale, "),o("code",[t._v("false")]),t._v(" otherwise")]),t._v(" "),o("td",[t._v("If true, draw a background behind the tick labels.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("textStrokeColor")])]),t._v(" "),o("td",[o("RouterLink",{attrs:{to:"/general/colors.html"}},[o("code",[t._v("Color")])])],1),t._v(" "),o("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),o("td",[t._v("``")]),t._v(" "),o("td",[t._v("The color of the stroke around the text.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("textStrokeWidth")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),o("td",[o("code",[t._v("0")])]),t._v(" "),o("td",[t._v("Stroke width around the text.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("z")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td",{staticStyle:{"text-align":"center"}}),t._v(" "),o("td",[o("code",[t._v("0")])]),t._v(" "),o("td",[t._v("z-index of tick layer. Useful when ticks are drawn on chart area. Values <= 0 are drawn under datasets, > 0 on top.")])])])]),t._v(" "),o("h2",{attrs:{id:"internal-data-format"}},[o("a",{staticClass:"header-anchor",attrs:{href:"#internal-data-format"}},[t._v("#")]),t._v(" Internal data format")]),t._v(" "),o("p",[t._v("Internally, the logarithmic scale uses numeric data.")])])}),[],!1,null,null,null);e.default=v.exports}}]);