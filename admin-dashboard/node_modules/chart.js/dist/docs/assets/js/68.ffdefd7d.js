(window.webpackJsonp=window.webpackJsonp||[]).push([[68],{399:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-fillercontrollerdatasetoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-fillercontrollerdatasetoptions"}},[t._v("#")]),t._v(" Interface: FillerControllerDatasetOptions")]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"fill"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#fill"}},[t._v("#")]),t._v(" fill")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("fill")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#filltarget"}},[a("code",[t._v("FillTarget")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ComplexFillTarget.html"}},[a("code",[t._v("ComplexFillTarget")])])],1),t._v(" "),a("p",[t._v("Both line and radar charts support a fill option on the dataset object which can be used to create area between two datasets or a dataset and a boundary, i.e. the scale origin, start or end")]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2159",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2159"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);