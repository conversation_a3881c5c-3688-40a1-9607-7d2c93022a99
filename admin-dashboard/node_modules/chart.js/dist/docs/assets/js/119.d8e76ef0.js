(window.webpackJsonp=window.webpackJsonp||[]).push([[119],{449:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-tooltipitem-ttype"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-tooltipitem-ttype"}},[t._v("#")]),t._v(" Interface: TooltipItem<TType>")]),t._v(" "),a("h2",{attrs:{id:"type-parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"chart"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#chart"}},[t._v("#")]),t._v(" chart")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("chart")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("p",[t._v("The chart the tooltip is being shown on")]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2793",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2793"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"dataindex"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#dataindex"}},[t._v("#")]),t._v(" dataIndex")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("dataIndex")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Index of this data item in the dataset")]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2828",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2828"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"dataset"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#dataset"}},[t._v("#")]),t._v(" dataset")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("dataset")]),t._v(": "),a("code",[t._v("UnionToIntersection")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#chartdataset"}},[a("code",[t._v("ChartDataset")])]),t._v("<"),a("code",[t._v("TType")]),t._v(", "),a("code",[t._v("DistributiveArray")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v("["),a("code",[t._v("TType")]),t._v("]["),a("code",[t._v('"defaultDataPoint"')]),t._v("]>>>")],1),t._v(" "),a("p",[t._v("The dataset the item comes from")]),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2818",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2818"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"datasetindex"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#datasetindex"}},[t._v("#")]),t._v(" datasetIndex")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("datasetIndex")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Index of the dataset the item comes from")]),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2823",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2823"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"element"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#element"}},[t._v("#")]),t._v(" element")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("element")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">")],1),t._v(" "),a("p",[t._v("The chart element (point, arc, bar, etc.) for this tooltip item")]),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2833",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2833"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"formattedvalue"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#formattedvalue"}},[t._v("#")]),t._v(" formattedValue")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("formattedValue")]),t._v(": "),a("code",[t._v("string")])]),t._v(" "),a("p",[t._v("Formatted value for the tooltip")]),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2813",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2813"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"label"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#label"}},[t._v("#")]),t._v(" label")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("label")]),t._v(": "),a("code",[t._v("string")])]),t._v(" "),a("p",[t._v("Label for the tooltip")]),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2798",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2798"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"parsed"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parsed"}},[t._v("#")]),t._v(" parsed")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("parsed")]),t._v(": "),a("code",[t._v("UnionToIntersection")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#parseddatatype"}},[a("code",[t._v("ParsedDataType")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),a("p",[t._v("Parsed data values for the given "),a("code",[t._v("dataIndex")]),t._v(" and "),a("code",[t._v("datasetIndex")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2803",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2803"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"raw"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#raw"}},[t._v("#")]),t._v(" raw")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("raw")]),t._v(": "),a("code",[t._v("unknown")])]),t._v(" "),a("p",[t._v("Raw data values for the given "),a("code",[t._v("dataIndex")]),t._v(" and "),a("code",[t._v("datasetIndex")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2808",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2808"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);