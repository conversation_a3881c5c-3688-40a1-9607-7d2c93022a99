(window.webpackJsonp=window.webpackJsonp||[]).push([[27],{358:function(e,r,t){"use strict";t.r(r);var a=t(6),s=Object(a.a)({},(function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("ContentSlotsDistributor",{attrs:{"slot-key":e.$parent.slotKey}},[t("h1",{attrs:{id:"interface-arcoptions"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#interface-arcoptions"}},[e._v("#")]),e._v(" Interface: ArcOptions")]),e._v(" "),t("h2",{attrs:{id:"hierarchy"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[e._v("#")]),e._v(" Hierarchy")]),e._v(" "),t("ul",[t("li",[t("p",[t("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html"}},[t("code",[e._v("CommonElementOptions")])])],1),e._v(" "),t("p",[e._v("↳ "),t("strong",[t("code",[e._v("ArcOptions")])])])])]),e._v(" "),t("h2",{attrs:{id:"properties"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[e._v("#")]),e._v(" Properties")]),e._v(" "),t("h3",{attrs:{id:"backgroundcolor"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#backgroundcolor"}},[e._v("#")]),e._v(" backgroundColor")]),e._v(" "),t("p",[e._v("• "),t("strong",[e._v("backgroundColor")]),e._v(": "),t("RouterLink",{attrs:{to:"/api/#color"}},[t("code",[e._v("Color")])])],1),e._v(" "),t("h4",{attrs:{id:"inherited-from"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[e._v("#")]),e._v(" Inherited from")]),e._v(" "),t("p",[t("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html"}},[e._v("CommonElementOptions")]),e._v("."),t("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html#backgroundcolor"}},[e._v("backgroundColor")])],1),e._v(" "),t("h4",{attrs:{id:"defined-in"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1696",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1696"),t("OutboundLink")],1)]),e._v(" "),t("hr"),e._v(" "),t("h3",{attrs:{id:"borderalign"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#borderalign"}},[e._v("#")]),e._v(" borderAlign")]),e._v(" "),t("p",[e._v("• "),t("strong",[e._v("borderAlign")]),e._v(": "),t("code",[e._v('"center"')]),e._v(" | "),t("code",[e._v('"inner"')])]),e._v(" "),t("p",[e._v("Arc stroke alignment.")]),e._v(" "),t("h4",{attrs:{id:"defined-in-2"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1732",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1732"),t("OutboundLink")],1)]),e._v(" "),t("hr"),e._v(" "),t("h3",{attrs:{id:"bordercolor"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bordercolor"}},[e._v("#")]),e._v(" borderColor")]),e._v(" "),t("p",[e._v("• "),t("strong",[e._v("borderColor")]),e._v(": "),t("RouterLink",{attrs:{to:"/api/#color"}},[t("code",[e._v("Color")])])],1),e._v(" "),t("h4",{attrs:{id:"inherited-from-2"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[e._v("#")]),e._v(" Inherited from")]),e._v(" "),t("p",[t("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html"}},[e._v("CommonElementOptions")]),e._v("."),t("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html#bordercolor"}},[e._v("borderColor")])],1),e._v(" "),t("h4",{attrs:{id:"defined-in-3"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1695",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1695"),t("OutboundLink")],1)]),e._v(" "),t("hr"),e._v(" "),t("h3",{attrs:{id:"borderjoinstyle"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#borderjoinstyle"}},[e._v("#")]),e._v(" borderJoinStyle")]),e._v(" "),t("p",[e._v("• "),t("strong",[e._v("borderJoinStyle")]),e._v(": "),t("code",[e._v("CanvasLineJoin")])]),e._v(" "),t("p",[e._v("Line join style. See MDN. Default is 'round' when "),t("code",[e._v("borderAlign")]),e._v(" is 'inner', else 'bevel'.")]),e._v(" "),t("h4",{attrs:{id:"defined-in-4"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1737",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1737"),t("OutboundLink")],1)]),e._v(" "),t("hr"),e._v(" "),t("h3",{attrs:{id:"borderradius"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#borderradius"}},[e._v("#")]),e._v(" borderRadius")]),e._v(" "),t("p",[e._v("• "),t("strong",[e._v("borderRadius")]),e._v(": "),t("code",[e._v("number")]),e._v(" | "),t("RouterLink",{attrs:{to:"/api/interfaces/ArcBorderRadius.html"}},[t("code",[e._v("ArcBorderRadius")])])],1),e._v(" "),t("p",[e._v("Sets the border radius for arcs")]),e._v(" "),t("p",[t("strong",[t("code",[e._v("default")])]),e._v(" 0")]),e._v(" "),t("h4",{attrs:{id:"defined-in-5"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1743",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1743"),t("OutboundLink")],1)]),e._v(" "),t("hr"),e._v(" "),t("h3",{attrs:{id:"borderwidth"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#borderwidth"}},[e._v("#")]),e._v(" borderWidth")]),e._v(" "),t("p",[e._v("• "),t("strong",[e._v("borderWidth")]),e._v(": "),t("code",[e._v("number")])]),e._v(" "),t("h4",{attrs:{id:"inherited-from-3"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-3"}},[e._v("#")]),e._v(" Inherited from")]),e._v(" "),t("p",[t("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html"}},[e._v("CommonElementOptions")]),e._v("."),t("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html#borderwidth"}},[e._v("borderWidth")])],1),e._v(" "),t("h4",{attrs:{id:"defined-in-6"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1694",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1694"),t("OutboundLink")],1)]),e._v(" "),t("hr"),e._v(" "),t("h3",{attrs:{id:"circular"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#circular"}},[e._v("#")]),e._v(" circular")]),e._v(" "),t("p",[e._v("• "),t("strong",[e._v("circular")]),e._v(": "),t("code",[e._v("boolean")])]),e._v(" "),t("p",[e._v("If false, Arc will be flat.")]),e._v(" "),t("p",[t("strong",[t("code",[e._v("default")])]),e._v(" true")]),e._v(" "),t("h4",{attrs:{id:"defined-in-7"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1754",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1754"),t("OutboundLink")],1)]),e._v(" "),t("hr"),e._v(" "),t("h3",{attrs:{id:"offset"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#offset"}},[e._v("#")]),e._v(" offset")]),e._v(" "),t("p",[e._v("• "),t("strong",[e._v("offset")]),e._v(": "),t("code",[e._v("number")])]),e._v(" "),t("p",[e._v("Arc offset (in pixels).")]),e._v(" "),t("h4",{attrs:{id:"defined-in-8"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1748",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1748"),t("OutboundLink")],1)])])}),[],!1,null,null,null);r.default=s.exports}}]);