(window.webpackJsonp=window.webpackJsonp||[]).push([[19],{350:function(t,e,a){"use strict";a.r(e);var r=a(6),n=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"enumeration-decimationalgorithm"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#enumeration-decimationalgorithm"}},[t._v("#")]),t._v(" Enumeration: DecimationAlgorithm")]),t._v(" "),a("h2",{attrs:{id:"enumeration-members"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#enumeration-members"}},[t._v("#")]),t._v(" Enumeration members")]),t._v(" "),a("h3",{attrs:{id:"lttb"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#lttb"}},[t._v("#")]),t._v(" lttb")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("lttb")]),t._v(" = "),a("code",[t._v('"lttb"')])]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2113",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2113"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"minmax"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#minmax"}},[t._v("#")]),t._v(" minmax")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("minmax")]),t._v(" = "),a("code",[t._v('"min-max"')])]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2114",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2114"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=n.exports}}]);