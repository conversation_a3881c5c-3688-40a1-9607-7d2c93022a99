(window.webpackJsonp=window.webpackJsonp||[]).push([[45],{376:function(t,e,a){"use strict";a.r(e);var s=a(6),r=Object(s.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-chartdatacustomtypesperdataset-ttype-tdata-tlabel"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-chartdatacustomtypesperdataset-ttype-tdata-tlabel"}},[t._v("#")]),t._v(" Interface: ChartDataCustomTypesPerDataset<TType, TData, TLabel>")]),t._v(" "),a("h2",{attrs:{id:"type-parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TData")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#defaultdatapoint"}},[a("code",[t._v("DefaultDataPoint")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TLabel")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("unknown")])])])])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"datasets"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#datasets"}},[t._v("#")]),t._v(" datasets")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("datasets")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#chartdatasetcustomtypesperdataset"}},[a("code",[t._v("ChartDatasetCustomTypesPerDataset")])]),t._v("<"),a("code",[t._v("TType")]),t._v(", "),a("code",[t._v("TData")]),t._v(">[]")],1),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3693",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3693"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"labels"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#labels"}},[t._v("#")]),t._v(" labels")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("labels")]),t._v(": "),a("code",[t._v("TLabel")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3692",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3692"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=r.exports}}]);