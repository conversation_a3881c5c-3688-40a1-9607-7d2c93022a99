(window.webpackJsonp=window.webpackJsonp||[]).push([[112],{443:function(e,t,r){"use strict";r.r(t);var a=r(6),s=Object(a.a)({},(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("ContentSlotsDistributor",{attrs:{"slot-key":e.$parent.slotKey}},[r("h1",{attrs:{id:"interface-segment"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-segment"}},[e._v("#")]),e._v(" Interface: Segment")]),e._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[e._v("#")]),e._v(" Properties")]),e._v(" "),r("h3",{attrs:{id:"end"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#end"}},[e._v("#")]),e._v(" end")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("end")]),e._v(": "),r("code",[e._v("number")])]),e._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1707",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1707"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"loop"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#loop"}},[e._v("#")]),e._v(" loop")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("loop")]),e._v(": "),r("code",[e._v("boolean")])]),e._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1708",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1708"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"start"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#start"}},[e._v("#")]),e._v(" start")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("start")]),e._v(": "),r("code",[e._v("number")])]),e._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1706",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1706"),r("OutboundLink")],1)])])}),[],!1,null,null,null);t.default=s.exports}}]);