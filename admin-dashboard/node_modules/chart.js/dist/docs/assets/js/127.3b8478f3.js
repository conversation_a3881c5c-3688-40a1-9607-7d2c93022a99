(window.webpackJsonp=window.webpackJsonp||[]).push([[127],{456:function(t,e,o){"use strict";o.r(e);var v=o(6),d=Object(v.a)({},(function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[o("h3",{attrs:{id:"common-options-to-all-axes"}},[o("a",{staticClass:"header-anchor",attrs:{href:"#common-options-to-all-axes"}},[t._v("#")]),t._v(" Common options to all axes")]),t._v(" "),o("p",[t._v("Namespace: "),o("code",[t._v("options.scales[scaleId]")])]),t._v(" "),o("table",[o("thead",[o("tr",[o("th",[t._v("Name")]),t._v(" "),o("th",[t._v("Type")]),t._v(" "),o("th",[t._v("Default")]),t._v(" "),o("th",[t._v("Description")])])]),t._v(" "),o("tbody",[o("tr",[o("td",[o("code",[t._v("type")])]),t._v(" "),o("td",[o("code",[t._v("string")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Type of scale being employed. Custom scales can be created and registered with a string key. This allows changing the type of an axis for a chart.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("alignToPixels")])]),t._v(" "),o("td",[o("code",[t._v("boolean")])]),t._v(" "),o("td",[o("code",[t._v("false")])]),t._v(" "),o("td",[t._v("Align pixel values to device pixels.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("backgroundColor")])]),t._v(" "),o("td",[o("RouterLink",{attrs:{to:"/general/colors.html"}},[o("code",[t._v("Color")])])],1),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Background color of the scale area.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("display")])]),t._v(" "),o("td",[o("code",[t._v("boolean")]),t._v("|"),o("code",[t._v("string")])]),t._v(" "),o("td",[o("code",[t._v("true")])]),t._v(" "),o("td",[t._v("Controls the axis global visibility (visible when "),o("code",[t._v("true")]),t._v(", hidden when "),o("code",[t._v("false")]),t._v("). When "),o("code",[t._v("display: 'auto'")]),t._v(", the axis is visible only if at least one associated dataset is visible.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("grid")])]),t._v(" "),o("td",[o("code",[t._v("object")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Grid line configuration. "),o("RouterLink",{attrs:{to:"/axes/styling.html#grid-line-configuration"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("min")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("User defined minimum number for the scale, overrides minimum value from data. "),o("RouterLink",{attrs:{to:"/axes/#axis-range-settings"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("max")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("User defined maximum number for the scale, overrides maximum value from data. "),o("RouterLink",{attrs:{to:"/axes/#axis-range-settings"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("reverse")])]),t._v(" "),o("td",[o("code",[t._v("boolean")])]),t._v(" "),o("td",[o("code",[t._v("false")])]),t._v(" "),o("td",[t._v("Reverse the scale.")])]),t._v(" "),o("tr",[o("td",[o("code",[t._v("stacked")])]),t._v(" "),o("td",[o("code",[t._v("boolean")]),t._v("|"),o("code",[t._v("string")])]),t._v(" "),o("td",[o("code",[t._v("false")])]),t._v(" "),o("td",[t._v("Should the data be stacked. "),o("RouterLink",{attrs:{to:"/axes/#stacking"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("suggestedMax")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Adjustment used when calculating the maximum data value. "),o("RouterLink",{attrs:{to:"/axes/#axis-range-settings"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("suggestedMin")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Adjustment used when calculating the minimum data value. "),o("RouterLink",{attrs:{to:"/axes/#axis-range-settings"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("ticks")])]),t._v(" "),o("td",[o("code",[t._v("object")])]),t._v(" "),o("td"),t._v(" "),o("td",[t._v("Tick configuration. "),o("RouterLink",{attrs:{to:"/axes/#tick-configuration"}},[t._v("more...")])],1)]),t._v(" "),o("tr",[o("td",[o("code",[t._v("weight")])]),t._v(" "),o("td",[o("code",[t._v("number")])]),t._v(" "),o("td",[o("code",[t._v("0")])]),t._v(" "),o("td",[t._v("The weight used to sort the axis. Higher weights are further away from the chart area.")])])])])])}),[],!1,null,null,null);e.default=d.exports}}]);