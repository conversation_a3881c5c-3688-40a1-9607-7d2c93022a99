(window.webpackJsonp=window.webpackJsonp||[]).push([[92],{423:function(t,e,r){"use strict";r.r(e);var a=r(6),n=Object(a.a)({},(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[r("h1",{attrs:{id:"interface-pointoptions"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-pointoptions"}},[t._v("#")]),t._v(" Interface: PointOptions")]),t._v(" "),r("h2",{attrs:{id:"hierarchy"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),r("ul",[r("li",[r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html"}},[r("code",[t._v("CommonElementOptions")])])],1),t._v(" "),r("p",[t._v("↳ "),r("strong",[r("code",[t._v("PointOptions")])])])])]),t._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),r("h3",{attrs:{id:"backgroundcolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#backgroundcolor"}},[t._v("#")]),t._v(" backgroundColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("backgroundColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])])],1),t._v(" "),r("h4",{attrs:{id:"inherited-from"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html"}},[t._v("CommonElementOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html#backgroundcolor"}},[t._v("backgroundColor")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1696",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1696"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"bordercolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#bordercolor"}},[t._v("#")]),t._v(" borderColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])])],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html"}},[t._v("CommonElementOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html#bordercolor"}},[t._v("borderColor")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1695",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1695"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderwidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderwidth"}},[t._v("#")]),t._v(" borderWidth")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderWidth")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("h4",{attrs:{id:"inherited-from-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-3"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html"}},[t._v("CommonElementOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html#borderwidth"}},[t._v("borderWidth")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1694",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1694"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"drawactiveelementsontop"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#drawactiveelementsontop"}},[t._v("#")]),t._v(" drawActiveElementsOnTop")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("drawActiveElementsOnTop")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[t._v("Draw the active elements over the other elements of the dataset,")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" true")]),t._v(" "),r("h4",{attrs:{id:"defined-in-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1904",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1904"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hitradius"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hitradius"}},[t._v("#")]),t._v(" hitRadius")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hitRadius")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Extra radius added to point radius for hit detection.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 1")]),t._v(" "),r("h4",{attrs:{id:"defined-in-5"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1889",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1889"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointstyle"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointstyle"}},[t._v("#")]),t._v(" pointStyle")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointStyle")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#pointstyle"}},[r("code",[t._v("PointStyle")])])],1),t._v(" "),r("p",[t._v("Point style")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'circle;")]),t._v(" "),r("h4",{attrs:{id:"defined-in-6"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1894",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1894"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"radius"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#radius"}},[t._v("#")]),t._v(" radius")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("radius")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Point radius")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 3")]),t._v(" "),r("h4",{attrs:{id:"defined-in-7"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1884",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1884"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"rotation"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#rotation"}},[t._v("#")]),t._v(" rotation")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("rotation")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Point rotation (in degrees).")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),r("h4",{attrs:{id:"defined-in-8"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1899",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1899"),r("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=n.exports}}]);