(window.webpackJsonp=window.webpackJsonp||[]).push([[208],{538:function(t,e,a){"use strict";a.r(e);var o=a(6),s=Object(o.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"information"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#information"}},[t._v("#")]),t._v(" Information")]),t._v(" "),a("h2",{attrs:{id:"out-of-the-box-working-samples"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#out-of-the-box-working-samples"}},[t._v("#")]),t._v(" Out of the box working samples")]),t._v(" "),a("p",[t._v("These samples are made for demonstration purposes only. They won't work out of the box if you copy paste them into your own website. This is because of how the docs are getting built. Some boilerplate code gets hidden.\nFor a sample that can be copied and pasted and used directly you can check the "),a("RouterLink",{attrs:{to:"/getting-started/usage.html"}},[t._v("usage page")]),t._v(".")],1),t._v(" "),a("h2",{attrs:{id:"autogenerated-data"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#autogenerated-data"}},[t._v("#")]),t._v(" Autogenerated data")]),t._v(" "),a("p",[t._v("The data used in the samples is autogenerated using custom functions. These functions do not ship with the library, for more information about this you can check the "),a("RouterLink",{attrs:{to:"/samples/utils.html"}},[t._v("utils page")]),t._v(".")],1),t._v(" "),a("h2",{attrs:{id:"actions-block"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#actions-block"}},[t._v("#")]),t._v(" Actions block")]),t._v(" "),a("p",[t._v("The samples have an "),a("code",[t._v("actions")]),t._v(" code block. These actions are not part of chart.js. They are internally transformed to separate buttons together with onClick listeners by a plugin we use in the documentation. To implement such actions yourself you can make some buttons and add onClick event listeners to them. Then in these event listeners you can call your variable in which you made the chart and do the logic that the button is supposed to do.")])])}),[],!1,null,null,null);e.default=s.exports}}]);