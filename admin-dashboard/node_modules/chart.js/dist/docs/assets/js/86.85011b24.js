(window.webpackJsonp=window.webpackJsonp||[]).push([[86],{417:function(t,e,a){"use strict";a.r(e);var r=a(6),i=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-plugin-ttype-o"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-plugin-ttype-o"}},[t._v("#")]),t._v(" Interface: Plugin<TType, O>")]),t._v(" "),a("h2",{attrs:{id:"type-parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("AnyObject")])])])])]),t._v(" "),a("h2",{attrs:{id:"hierarchy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),a("ul",[a("li",[a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/ExtendedPlugin.html"}},[a("code",[t._v("ExtendedPlugin")])]),t._v("<"),a("code",[t._v("TType")]),t._v(", "),a("code",[t._v("O")]),t._v(">")],1),t._v(" "),a("p",[t._v("↳ "),a("strong",[a("code",[t._v("Plugin")])])]),t._v(" "),a("p",[t._v("↳↳ "),a("RouterLink",{attrs:{to:"/api/interfaces/Tooltip.html"}},[a("code",[t._v("Tooltip")])])],1)])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"id"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#id"}},[t._v("#")]),t._v(" id")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("id")]),t._v(": "),a("code",[t._v("string")])]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L808",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:808"),a("OutboundLink")],1)]),t._v(" "),a("h2",{attrs:{id:"methods"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),a("h3",{attrs:{id:"afterbuildticks"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterbuildticks"}},[t._v("#")]),t._v(" afterBuildTicks")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("afterBuildTicks")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called after scale has build its ticks. This hook is called separately for each scale in the chart.")]),t._v(" "),a("h4",{attrs:{id:"parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.scale")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The scale.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L967",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:967"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterdatalimits"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterdatalimits"}},[t._v("#")]),t._v(" afterDataLimits")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("afterDataLimits")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called after scale data limits are calculated. This hook is called separately for each scale in the chart.")]),t._v(" "),a("h4",{attrs:{id:"parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-2"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.scale")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The scale.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L951",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:951"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterdatasetdraw"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterdatasetdraw"}},[t._v("#")]),t._v(" afterDatasetDraw")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("afterDatasetDraw")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called after the "),a("code",[t._v("chart")]),t._v(" datasets at the given "),a("code",[t._v("args.index")]),t._v(" have been drawn\n(datasets are drawn in the reverse order). Note that this hook will not be called\nif the datasets drawing has been previously cancelled.")]),t._v(" "),a("h4",{attrs:{id:"parameters-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-3"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The dataset index.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.meta")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#chartmeta"}},[a("code",[t._v("ChartMeta")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The dataset metadata.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-3"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1049",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1049"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterdatasetupdate"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterdatasetupdate"}},[t._v("#")]),t._v(" afterDatasetUpdate")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("afterDatasetUpdate")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called after the "),a("code",[t._v("chart")]),t._v(" datasets at the given "),a("code",[t._v("args.index")]),t._v(" has been updated. Note\nthat this hook will not be called if the datasets update has been previously cancelled.")]),t._v(" "),a("h4",{attrs:{id:"parameters-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-4"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.cancelable")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("false")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The dataset index.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.meta")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#chartmeta"}},[a("code",[t._v("ChartMeta")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The dataset metadata.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.mode")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"resize"')]),t._v(" | "),a("code",[t._v('"reset"')]),t._v(" | "),a("code",[t._v('"none"')]),t._v(" | "),a("code",[t._v('"hide"')]),t._v(" | "),a("code",[t._v('"show"')]),t._v(" | "),a("code",[t._v('"normal"')]),t._v(" | "),a("code",[t._v('"active"')])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The update mode.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-4"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L926",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:926"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterdatasetsdraw"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterdatasetsdraw"}},[t._v("#")]),t._v(" afterDatasetsDraw")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("afterDatasetsDraw")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v(", "),a("code",[t._v("cancelable")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called after the "),a("code",[t._v("chart")]),t._v(" datasets have been drawn. Note that this hook\nwill not be called if the datasets drawing has been previously cancelled.")]),t._v(" "),a("h4",{attrs:{id:"parameters-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-5"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("EmptyObject")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("cancelable")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("false")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-5"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1026",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1026"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterdatasetsupdate"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterdatasetsupdate"}},[t._v("#")]),t._v(" afterDatasetsUpdate")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("afterDatasetsUpdate")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called after the "),a("code",[t._v("chart")]),t._v(" datasets have been updated. Note that this hook\nwill not be called if the datasets update has been previously cancelled.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("since")])]),t._v(" version 2.1.5")]),t._v(" "),a("h4",{attrs:{id:"parameters-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-6"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.cancelable")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("true")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.mode")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"resize"')]),t._v(" | "),a("code",[t._v('"reset"')]),t._v(" | "),a("code",[t._v('"none"')]),t._v(" | "),a("code",[t._v('"hide"')]),t._v(" | "),a("code",[t._v('"show"')]),t._v(" | "),a("code",[t._v('"normal"')]),t._v(" | "),a("code",[t._v('"active"')])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The update mode.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-6"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L903",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:903"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterdestroy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterdestroy"}},[t._v("#")]),t._v(" afterDestroy")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("afterDestroy")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Called after the chart has been destroyed.")]),t._v(" "),a("h4",{attrs:{id:"parameters-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-7"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("EmptyObject")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-7"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1102",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1102"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterdraw"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterdraw"}},[t._v("#")]),t._v(" afterDraw")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("afterDraw")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called after the "),a("code",[t._v("chart")]),t._v(" has been drawn. Note that this hook will not be called\nif the drawing has been previously cancelled.")]),t._v(" "),a("h4",{attrs:{id:"parameters-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-8"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("EmptyObject")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-8"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1009",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1009"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterevent"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterevent"}},[t._v("#")]),t._v(" afterEvent")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("afterEvent")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called after the "),a("code",[t._v("event")]),t._v(" has been consumed. Note that this hook\nwill not be called if the "),a("code",[t._v("event")]),t._v(" has been previously discarded.")]),t._v(" "),a("h4",{attrs:{id:"parameters-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-9"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.cancelable")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("false")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.changed?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.event")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ChartEvent.html"}},[a("code",[t._v("ChartEvent")])])],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The event object.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.inChartArea")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The event position is inside chartArea")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.replay")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("True if this event is replayed from "),a("code",[t._v("Chart.update")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-9"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1072",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1072"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterinit"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterinit"}},[t._v("#")]),t._v(" afterInit")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("afterInit")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called after "),a("code",[t._v("chart")]),t._v(" has been initialized and before the first update.")]),t._v(" "),a("h4",{attrs:{id:"parameters-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-10"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("EmptyObject")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-10"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L847",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:847"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterlayout"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterlayout"}},[t._v("#")]),t._v(" afterLayout")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("afterLayout")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called after the "),a("code",[t._v("chart")]),t._v(" has been laid out. Note that this hook will not\nbe called if the layout update has been previously cancelled.")]),t._v(" "),a("h4",{attrs:{id:"parameters-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-11"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("EmptyObject")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-11"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L975",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:975"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterrender"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterrender"}},[t._v("#")]),t._v(" afterRender")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("afterRender")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called after the "),a("code",[t._v("chart")]),t._v(" has been fully rendered (and animation completed). Note\nthat this hook will not be called if the rendering has been previously cancelled.")]),t._v(" "),a("h4",{attrs:{id:"parameters-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-12"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("EmptyObject")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-12"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L992",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:992"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"aftertooltipdraw"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#aftertooltipdraw"}},[t._v("#")]),t._v(" afterTooltipDraw")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("afterTooltipDraw")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called after drawing the "),a("code",[t._v("tooltip")]),t._v(". Note that this hook will not\nbe called if the tooltip drawing has been previously cancelled.")]),t._v(" "),a("h4",{attrs:{id:"parameters-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-13"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.tooltip")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/TooltipModel.html"}},[a("code",[t._v("TooltipModel")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The tooltip.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-13"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/ExtendedPlugin.html"}},[t._v("ExtendedPlugin")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/ExtendedPlugin.html#aftertooltipdraw"}},[t._v("afterTooltipDraw")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2601",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2601"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterupdate"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterupdate"}},[t._v("#")]),t._v(" afterUpdate")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("afterUpdate")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called after "),a("code",[t._v("chart")]),t._v(" has been updated and before rendering. Note that this\nhook will not be called if the chart update has been previously cancelled.")]),t._v(" "),a("h4",{attrs:{id:"parameters-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-14"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.mode")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"resize"')]),t._v(" | "),a("code",[t._v('"reset"')]),t._v(" | "),a("code",[t._v('"none"')]),t._v(" | "),a("code",[t._v('"hide"')]),t._v(" | "),a("code",[t._v('"show"')]),t._v(" | "),a("code",[t._v('"normal"')]),t._v(" | "),a("code",[t._v('"active"')])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The update mode")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-14"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-15"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L866",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:866"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforebuildticks"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforebuildticks"}},[t._v("#")]),t._v(" beforeBuildTicks")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeBuildTicks")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called before scale builds its ticks. This hook is called separately for each scale in the chart.")]),t._v(" "),a("h4",{attrs:{id:"parameters-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-15"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.scale")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The scale.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-15"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-16"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L959",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:959"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforedatalimits"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforedatalimits"}},[t._v("#")]),t._v(" beforeDataLimits")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeDataLimits")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called before scale data limits are calculated. This hook is called separately for each scale in the chart.")]),t._v(" "),a("h4",{attrs:{id:"parameters-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-16"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.scale")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The scale.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-16"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-17"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L943",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:943"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforedatasetdraw"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforedatasetdraw"}},[t._v("#")]),t._v(" beforeDatasetDraw")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeDatasetDraw")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called before drawing the "),a("code",[t._v("chart")]),t._v(" dataset at the given "),a("code",[t._v("args.index")]),t._v(" (datasets\nare drawn in the reverse order). If any plugin returns "),a("code",[t._v("false")]),t._v(", the datasets drawing\nis cancelled until another "),a("code",[t._v("render")]),t._v(" is triggered.")]),t._v(" "),a("h4",{attrs:{id:"parameters-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-17"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The dataset index.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.meta")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#chartmeta"}},[a("code",[t._v("ChartMeta")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The dataset metadata.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-17"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("code",[t._v("false")]),t._v(" to cancel the chart datasets drawing.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-18"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1038",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1038"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforedatasetupdate"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforedatasetupdate"}},[t._v("#")]),t._v(" beforeDatasetUpdate")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeDatasetUpdate")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called before updating the "),a("code",[t._v("chart")]),t._v(" dataset at the given "),a("code",[t._v("args.index")]),t._v(". If any plugin\nreturns "),a("code",[t._v("false")]),t._v(", the datasets update is cancelled until another "),a("code",[t._v("update")]),t._v(" is triggered.")]),t._v(" "),a("h4",{attrs:{id:"parameters-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-18"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.cancelable")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("true")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The dataset index.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.meta")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#chartmeta"}},[a("code",[t._v("ChartMeta")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The dataset metadata.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.mode")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"resize"')]),t._v(" | "),a("code",[t._v('"reset"')]),t._v(" | "),a("code",[t._v('"none"')]),t._v(" | "),a("code",[t._v('"hide"')]),t._v(" | "),a("code",[t._v('"show"')]),t._v(" | "),a("code",[t._v('"normal"')]),t._v(" | "),a("code",[t._v('"active"')])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The update mode.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-18"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("code",[t._v("false")]),t._v(" to cancel the chart datasets drawing.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-19"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-19"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L915",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:915"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforedatasetsdraw"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforedatasetsdraw"}},[t._v("#")]),t._v(" beforeDatasetsDraw")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeDatasetsDraw")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called before drawing the "),a("code",[t._v("chart")]),t._v(" datasets. If any plugin returns "),a("code",[t._v("false")]),t._v(",\nthe datasets drawing is cancelled until another "),a("code",[t._v("render")]),t._v(" is triggered.")]),t._v(" "),a("h4",{attrs:{id:"parameters-19"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-19"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.cancelable")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("true")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-19"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-19"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("code",[t._v("false")]),t._v(" to cancel the chart datasets drawing.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-20"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-20"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1018",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1018"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforedatasetsupdate"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforedatasetsupdate"}},[t._v("#")]),t._v(" beforeDatasetsUpdate")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeDatasetsUpdate")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called before updating the "),a("code",[t._v("chart")]),t._v(" datasets. If any plugin returns "),a("code",[t._v("false")]),t._v(",\nthe datasets update is cancelled until another "),a("code",[t._v("update")]),t._v(" is triggered.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("since")])]),t._v(" version 2.1.5")]),t._v(" "),a("h4",{attrs:{id:"parameters-20"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-20"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.mode")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"resize"')]),t._v(" | "),a("code",[t._v('"reset"')]),t._v(" | "),a("code",[t._v('"none"')]),t._v(" | "),a("code",[t._v('"hide"')]),t._v(" | "),a("code",[t._v('"show"')]),t._v(" | "),a("code",[t._v('"normal"')]),t._v(" | "),a("code",[t._v('"active"')])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The update mode.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-20"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-20"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("false to cancel the datasets update.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-21"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-21"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L893",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:893"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforedestroy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforedestroy"}},[t._v("#")]),t._v(" beforeDestroy")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeDestroy")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Called before the chart is being destroyed.")]),t._v(" "),a("h4",{attrs:{id:"parameters-21"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-21"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("EmptyObject")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-21"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-21"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-22"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-22"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1087",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1087"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforedraw"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforedraw"}},[t._v("#")]),t._v(" beforeDraw")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeDraw")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called before drawing "),a("code",[t._v("chart")]),t._v(" at every animation frame. If any plugin returns "),a("code",[t._v("false")]),t._v(",\nthe frame drawing is cancelled untilanother "),a("code",[t._v("render")]),t._v(" is triggered.")]),t._v(" "),a("h4",{attrs:{id:"parameters-22"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-22"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.cancelable")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("true")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-22"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-22"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("code",[t._v("false")]),t._v(" to cancel the chart drawing.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-23"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-23"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1001",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1001"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforeelementsupdate"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforeelementsupdate"}},[t._v("#")]),t._v(" beforeElementsUpdate")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeElementsUpdate")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called during the update process, before any chart elements have been created.\nThis can be used for data decimation by changing the data array inside a dataset.")]),t._v(" "),a("h4",{attrs:{id:"parameters-23"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-23"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("EmptyObject")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-23"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-23"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-24"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-24"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L874",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:874"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforeevent"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforeevent"}},[t._v("#")]),t._v(" beforeEvent")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeEvent")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called before processing the specified "),a("code",[t._v("event")]),t._v(". If any plugin returns "),a("code",[t._v("false")]),t._v(",\nthe event will be discarded.")]),t._v(" "),a("h4",{attrs:{id:"parameters-24"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-24"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.cancelable")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("true")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.event")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ChartEvent.html"}},[a("code",[t._v("ChartEvent")])])],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The event object.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.inChartArea")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The event position is inside chartArea")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.replay")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("True if this event is replayed from "),a("code",[t._v("Chart.update")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-24"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-24"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-25"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-25"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1060",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1060"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforeinit"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforeinit"}},[t._v("#")]),t._v(" beforeInit")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeInit")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called before initializing "),a("code",[t._v("chart")]),t._v(".")]),t._v(" "),a("h4",{attrs:{id:"parameters-25"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-25"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("EmptyObject")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-25"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-25"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-26"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-26"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L840",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:840"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforelayout"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforelayout"}},[t._v("#")]),t._v(" beforeLayout")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeLayout")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called before laying out "),a("code",[t._v("chart")]),t._v(". If any plugin returns "),a("code",[t._v("false")]),t._v(",\nthe layout update is cancelled until another "),a("code",[t._v("update")]),t._v(" is triggered.")]),t._v(" "),a("h4",{attrs:{id:"parameters-26"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-26"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.cancelable")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("true")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-26"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-26"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("code",[t._v("false")]),t._v(" to cancel the chart layout.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-27"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-27"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L935",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:935"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforerender"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforerender"}},[t._v("#")]),t._v(" beforeRender")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeRender")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called before rendering "),a("code",[t._v("chart")]),t._v(". If any plugin returns "),a("code",[t._v("false")]),t._v(",\nthe rendering is cancelled until another "),a("code",[t._v("render")]),t._v(" is triggered.")]),t._v(" "),a("h4",{attrs:{id:"parameters-27"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-27"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.cancelable")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("true")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-27"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-27"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("code",[t._v("false")]),t._v(" to cancel the chart rendering.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-28"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-28"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L984",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:984"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforetooltipdraw"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforetooltipdraw"}},[t._v("#")]),t._v(" beforeTooltipDraw")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeTooltipDraw")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called before drawing the "),a("code",[t._v("tooltip")]),t._v(". If any plugin returns "),a("code",[t._v("false")]),t._v(",\nthe tooltip drawing is cancelled until another "),a("code",[t._v("render")]),t._v(" is triggered.")]),t._v(" "),a("h4",{attrs:{id:"parameters-28"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-28"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.tooltip")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/TooltipModel.html"}},[a("code",[t._v("TooltipModel")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The tooltip.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-28"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-28"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("code",[t._v("false")]),t._v(" to cancel the chart tooltip drawing.")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/ExtendedPlugin.html"}},[t._v("ExtendedPlugin")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/ExtendedPlugin.html#beforetooltipdraw"}},[t._v("beforeTooltipDraw")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-29"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-29"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2592",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2592"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforeupdate"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforeupdate"}},[t._v("#")]),t._v(" beforeUpdate")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeUpdate")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called before updating "),a("code",[t._v("chart")]),t._v(". If any plugin returns "),a("code",[t._v("false")]),t._v(", the update\nis cancelled (and thus subsequent render(s)) until another "),a("code",[t._v("update")]),t._v(" is triggered.")]),t._v(" "),a("h4",{attrs:{id:"parameters-29"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-29"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.cancelable")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("true")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.mode")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"resize"')]),t._v(" | "),a("code",[t._v('"reset"')]),t._v(" | "),a("code",[t._v('"none"')]),t._v(" | "),a("code",[t._v('"hide"')]),t._v(" | "),a("code",[t._v('"show"')]),t._v(" | "),a("code",[t._v('"normal"')]),t._v(" | "),a("code",[t._v('"active"')])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The update mode")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-29"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-29"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("code",[t._v("false")]),t._v(" to cancel the chart update.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-30"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-30"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L857",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:857"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"destroy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#destroy"}},[t._v("#")]),t._v(" destroy")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("destroy")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Called after the chart has been destroyed.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("deprecated")])]),t._v(" since version 3.7.0 in favour of afterDestroy")]),t._v(" "),a("h4",{attrs:{id:"parameters-30"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-30"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("EmptyObject")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-30"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-30"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-31"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-31"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1095",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1095"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"install"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#install"}},[t._v("#")]),t._v(" install")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("install")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called when plugin is installed for this chart instance. This hook is also invoked for disabled plugins (options === false).")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("since")])]),t._v(" 3.0.0")]),t._v(" "),a("h4",{attrs:{id:"parameters-31"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-31"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("EmptyObject")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-31"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-31"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-32"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-32"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L817",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:817"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"reset"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#reset"}},[t._v("#")]),t._v(" reset")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("reset")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called during chart reset")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("since")])]),t._v(" version 3.0.0")]),t._v(" "),a("h4",{attrs:{id:"parameters-32"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-32"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("EmptyObject")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-32"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-32"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-33"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-33"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L882",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:882"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"resize"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#resize"}},[t._v("#")]),t._v(" resize")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("resize")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called after the chart as been resized.")]),t._v(" "),a("h4",{attrs:{id:"parameters-33"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-33"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.size")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The new canvas display size (eq. canvas.style width & height).")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.size.height")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.size.width")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-33"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-33"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-34"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-34"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1080",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1080"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"start"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#start"}},[t._v("#")]),t._v(" start")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("start")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called when a plugin is starting. This happens when chart is created or plugin is enabled.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("since")])]),t._v(" 3.0.0")]),t._v(" "),a("h4",{attrs:{id:"parameters-34"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-34"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("EmptyObject")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-34"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-34"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-35"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-35"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L825",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:825"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"stop"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#stop"}},[t._v("#")]),t._v(" stop")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("stop")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called when a plugin stopping. This happens when chart is destroyed or plugin is disabled.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("since")])]),t._v(" 3.0.0")]),t._v(" "),a("h4",{attrs:{id:"parameters-35"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-35"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("EmptyObject")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-35"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-35"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-36"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-36"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L833",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:833"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"uninstall"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#uninstall"}},[t._v("#")]),t._v(" uninstall")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("uninstall")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Called after chart is destroyed on all plugins that were installed for that chart. This hook is also invoked for disabled plugins (options === false).")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("since")])]),t._v(" 3.0.0")]),t._v(" "),a("h4",{attrs:{id:"parameters-36"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-36"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("EmptyObject")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-36"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-36"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-37"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-37"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1110",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1110"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=i.exports}}]);