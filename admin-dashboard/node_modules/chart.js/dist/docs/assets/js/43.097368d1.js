(window.webpackJsonp=window.webpackJsonp||[]).push([[43],{374:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-chartconfigurationcustomtypesperdataset-ttype-tdata-tlabel"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-chartconfigurationcustomtypesperdataset-ttype-tdata-tlabel"}},[t._v("#")]),t._v(" Interface: ChartConfigurationCustomTypesPerDataset<TType, TData, TLabel>")]),t._v(" "),a("h2",{attrs:{id:"type-parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TData")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#defaultdatapoint"}},[a("code",[t._v("DefaultDataPoint")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TLabel")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("unknown")])])])])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"data"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#data"}},[t._v("#")]),t._v(" data")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("data")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartDataCustomTypesPerDataset.html"}},[a("code",[t._v("ChartDataCustomTypesPerDataset")])]),t._v("<"),a("code",[t._v("TType")]),t._v(", "),a("code",[t._v("TData")]),t._v(", "),a("code",[t._v("TLabel")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3712",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3712"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"options"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#options"}},[t._v("#")]),t._v(" options")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("options")]),t._v(": "),a("code",[t._v("DeepPartial")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreChartOptions.html"}},[a("code",[t._v("CoreChartOptions")])]),t._v("<"),a("code",[t._v("TType")]),t._v("> & "),a("RouterLink",{attrs:{to:"/api/#elementchartoptions"}},[a("code",[t._v("ElementChartOptions")])]),t._v("<"),a("code",[t._v("TType")]),t._v("> & "),a("RouterLink",{attrs:{to:"/api/interfaces/PluginChartOptions.html"}},[a("code",[t._v("PluginChartOptions")])]),t._v("<"),a("code",[t._v("TType")]),t._v("> & "),a("RouterLink",{attrs:{to:"/api/#datasetchartoptions"}},[a("code",[t._v("DatasetChartOptions")])]),t._v("<"),a("code",[t._v("TType")]),t._v("> & "),a("RouterLink",{attrs:{to:"/api/#scalechartoptions"}},[a("code",[t._v("ScaleChartOptions")])]),t._v("<"),a("code",[t._v("TType")]),t._v("> & "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v("["),a("code",[t._v("TType")]),t._v("]["),a("code",[t._v('"chartOptions"')]),t._v("]>")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3713",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3713"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"plugins"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#plugins"}},[t._v("#")]),t._v(" plugins")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("plugins")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/Plugin.html"}},[a("code",[t._v("Plugin")])]),t._v("<"),a("code",[t._v("TType")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">[]")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3714",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3714"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);