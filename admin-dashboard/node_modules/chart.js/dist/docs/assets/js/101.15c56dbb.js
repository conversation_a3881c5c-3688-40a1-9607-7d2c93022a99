(window.webpackJsonp=window.webpackJsonp||[]).push([[101],{432:function(t,e,a){"use strict";a.r(e);var r=a(6),i=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-radialscaletyperegistry"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-radialscaletyperegistry"}},[t._v("#")]),t._v(" Interface: RadialScaleTypeRegistry")]),t._v(" "),a("h2",{attrs:{id:"hierarchy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),a("ul",[a("li",[a("p",[a("strong",[a("code",[t._v("RadialScaleTypeRegistry")])])]),t._v(" "),a("p",[t._v("↳ "),a("RouterLink",{attrs:{to:"/api/interfaces/ScaleTypeRegistry.html"}},[a("code",[t._v("ScaleTypeRegistry")])])],1)])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"radiallinear"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#radiallinear"}},[t._v("#")]),t._v(" radialLinear")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("radialLinear")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#radiallinearscaleoptions"}},[a("code",[t._v("RadialLinearScaleOptions")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3503",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3503"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=i.exports}}]);