(window.webpackJsonp=window.webpackJsonp||[]).push([[61],{392:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-doughnutcontroller"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-doughnutcontroller"}},[t._v("#")]),t._v(" Interface: DoughnutController")]),t._v(" "),a("h2",{attrs:{id:"hierarchy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),a("ul",[a("li",[a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[a("code",[t._v("DatasetController")])])],1),t._v(" "),a("p",[t._v("↳ "),a("strong",[a("code",[t._v("DoughnutController")])])])])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"cachedmeta"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#cachedmeta"}},[t._v("#")]),t._v(" _cachedMeta")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("_cachedMeta")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#chartmeta"}},[a("code",[t._v("ChartMeta")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"inherited-from"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#_cachedmeta"}},[t._v("_cachedMeta")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L583",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:583"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"chart"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#chart"}},[t._v("#")]),t._v(" chart")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("chart")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"inherited-from-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#chart"}},[t._v("chart")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L581",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:581"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"enableoptionsharing"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#enableoptionsharing"}},[t._v("#")]),t._v(" enableOptionSharing")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("enableOptionSharing")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-3"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#enableoptionsharing"}},[t._v("enableOptionSharing")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L584",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:584"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"index"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#index"}},[t._v("#")]),t._v(" index")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("index")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-4"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#index"}},[t._v("index")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L582",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:582"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"innerradius"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#innerradius"}},[t._v("#")]),t._v(" innerRadius")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("innerRadius")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L334",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:334"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"offsetx"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#offsetx"}},[t._v("#")]),t._v(" offsetX")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("offsetX")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L336",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:336"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"offsety"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#offsety"}},[t._v("#")]),t._v(" offsetY")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("offsetY")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L337",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:337"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"outerradius"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#outerradius"}},[t._v("#")]),t._v(" outerRadius")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("outerRadius")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L335",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:335"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"supportsdecimation"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#supportsdecimation"}},[t._v("#")]),t._v(" supportsDecimation")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("supportsDecimation")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-5"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#supportsdecimation"}},[t._v("supportsDecimation")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L588",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:588"),a("OutboundLink")],1)]),t._v(" "),a("h2",{attrs:{id:"methods"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),a("h3",{attrs:{id:"addelements"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#addelements"}},[t._v("#")]),t._v(" addElements")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("addElements")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-6"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#addelements"}},[t._v("addElements")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L604",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:604"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"applystack"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#applystack"}},[t._v("#")]),t._v(" applyStack")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Protected")]),t._v(" "),a("strong",[t._v("applyStack")]),t._v("("),a("code",[t._v("scale")]),t._v(", "),a("code",[t._v("parsed")]),t._v("): "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("scale")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("parsed")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("unknown")]),t._v("[]")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-7"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#applystack"}},[t._v("applyStack")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L640",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:640"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"buildorupdateelements"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#buildorupdateelements"}},[t._v("#")]),t._v(" buildOrUpdateElements")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("buildOrUpdateElements")]),t._v("("),a("code",[t._v("resetNewElements?")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-2"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("resetNewElements?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-3"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-8"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#buildorupdateelements"}},[t._v("buildOrUpdateElements")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L605",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:605"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"calculatecircumference"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#calculatecircumference"}},[t._v("#")]),t._v(" calculateCircumference")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("calculateCircumference")]),t._v("("),a("code",[t._v("value")]),t._v("): "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"parameters-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-3"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("value")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-4"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L340",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:340"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"calculatetotal"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#calculatetotal"}},[t._v("#")]),t._v(" calculateTotal")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("calculateTotal")]),t._v("(): "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"returns-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-5"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L339",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:339"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"configure"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#configure"}},[t._v("#")]),t._v(" configure")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("configure")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-6"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-9"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#configure"}},[t._v("configure")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-15"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L602",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:602"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"draw"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#draw"}},[t._v("#")]),t._v(" draw")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("draw")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-7"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-10"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#draw"}},[t._v("draw")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-16"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L597",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:597"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getallparsedvalues"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getallparsedvalues"}},[t._v("#")]),t._v(" getAllParsedValues")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getAllParsedValues")]),t._v("("),a("code",[t._v("scale")]),t._v("): "),a("code",[t._v("number")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"parameters-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-4"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("scale")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-8"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-11"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#getallparsedvalues"}},[t._v("getAllParsedValues")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-17"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L591",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:591"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getdataset"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getdataset"}},[t._v("#")]),t._v(" getDataset")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getDataset")]),t._v("(): "),a("RouterLink",{attrs:{to:"/api/#chartdataset"}},[a("code",[t._v("ChartDataset")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[]>")],1),t._v(" "),a("h4",{attrs:{id:"returns-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-9"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/#chartdataset"}},[a("code",[t._v("ChartDataset")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[]>")],1),t._v(" "),a("h4",{attrs:{id:"inherited-from-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-12"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#getdataset"}},[t._v("getDataset")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-18"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L599",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:599"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getlabelandvalue"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getlabelandvalue"}},[t._v("#")]),t._v(" getLabelAndValue")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Protected")]),t._v(" "),a("strong",[t._v("getLabelAndValue")]),t._v("("),a("code",[t._v("index")]),t._v("): "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"parameters-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-5"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-10"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("Object")])]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("label")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("string")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("value")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("string")])])])])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-13"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#getlabelandvalue"}},[t._v("getLabelAndValue")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-19"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-19"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L592",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:592"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getmaxoverflow"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getmaxoverflow"}},[t._v("#")]),t._v(" getMaxOverflow")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Protected")]),t._v(" "),a("strong",[t._v("getMaxOverflow")]),t._v("(): "),a("code",[t._v("number")]),t._v(" | "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"returns-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-11"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")]),t._v(" | "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-14"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#getmaxoverflow"}},[t._v("getMaxOverflow")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-20"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-20"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L596",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:596"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getmeta"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getmeta"}},[t._v("#")]),t._v(" getMeta")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getMeta")]),t._v("(): "),a("RouterLink",{attrs:{to:"/api/#chartmeta"}},[a("code",[t._v("ChartMeta")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"returns-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-12"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/#chartmeta"}},[a("code",[t._v("ChartMeta")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"inherited-from-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-15"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#getmeta"}},[t._v("getMeta")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-21"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-21"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L600",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:600"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getminmax"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getminmax"}},[t._v("#")]),t._v(" getMinMax")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Protected")]),t._v(" "),a("strong",[t._v("getMinMax")]),t._v("("),a("code",[t._v("scale")]),t._v(", "),a("code",[t._v("canStack?")]),t._v("): "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"parameters-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-6"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("scale")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("canStack?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-13"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("Object")])]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("max")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("min")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-16"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#getminmax"}},[t._v("getMinMax")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-22"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-22"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L647",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:647"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getparsed"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getparsed"}},[t._v("#")]),t._v(" getParsed")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Protected")]),t._v(" "),a("strong",[t._v("getParsed")]),t._v("("),a("code",[t._v("index")]),t._v("): "),a("code",[t._v("number")]),t._v(" | "),a("code",[t._v("BarParsedData")]),t._v(" | "),a("code",[t._v("CartesianParsedData")]),t._v(" | "),a("code",[t._v("BubbleParsedData")]),t._v(" | "),a("code",[t._v("RadialParsedData")])]),t._v(" "),a("h4",{attrs:{id:"parameters-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-7"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-14"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")]),t._v(" | "),a("code",[t._v("BarParsedData")]),t._v(" | "),a("code",[t._v("CartesianParsedData")]),t._v(" | "),a("code",[t._v("BubbleParsedData")]),t._v(" | "),a("code",[t._v("RadialParsedData")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-17"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#getparsed"}},[t._v("getParsed")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-23"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-23"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L639",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:639"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getscaleforid"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getscaleforid"}},[t._v("#")]),t._v(" getScaleForId")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getScaleForId")]),t._v("("),a("code",[t._v("scaleID")]),t._v("): "),a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"parameters-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-8"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("scaleID")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("string")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-15"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"inherited-from-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-18"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#getscaleforid"}},[t._v("getScaleForId")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-24"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-24"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L601",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:601"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getsharedoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getsharedoptions"}},[t._v("#")]),t._v(" getSharedOptions")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Protected")]),t._v(" "),a("strong",[t._v("getSharedOptions")]),t._v("("),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("AnyObject")])]),t._v(" "),a("p",[t._v("Utility for checking if the options are shared and should be animated separately.")]),t._v(" "),a("h4",{attrs:{id:"parameters-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-9"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("AnyObject")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-16"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("AnyObject")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-19"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-19"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#getsharedoptions"}},[t._v("getSharedOptions")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-25"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-25"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L614",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:614"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getstyle"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getstyle"}},[t._v("#")]),t._v(" getStyle")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getStyle")]),t._v("("),a("code",[t._v("index")]),t._v(", "),a("code",[t._v("active")]),t._v("): "),a("code",[t._v("AnyObject")])]),t._v(" "),a("h4",{attrs:{id:"parameters-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-10"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("active")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-17"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("AnyObject")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-20"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-20"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#getstyle"}},[t._v("getStyle")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-26"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-26"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L607",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:607"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"includeoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#includeoptions"}},[t._v("#")]),t._v(" includeOptions")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Protected")]),t._v(" "),a("strong",[t._v("includeOptions")]),t._v("("),a("code",[t._v("mode")]),t._v(", "),a("code",[t._v("sharedOptions")]),t._v("): "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("Utility for determining if "),a("code",[t._v("options")]),t._v(" should be included in the updated properties")]),t._v(" "),a("h4",{attrs:{id:"parameters-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-11"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("mode")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"resize"')]),t._v(" | "),a("code",[t._v('"reset"')]),t._v(" | "),a("code",[t._v('"none"')]),t._v(" | "),a("code",[t._v('"hide"')]),t._v(" | "),a("code",[t._v('"show"')]),t._v(" | "),a("code",[t._v('"normal"')]),t._v(" | "),a("code",[t._v('"active"')])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("sharedOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("AnyObject")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-18"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-21"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-21"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#includeoptions"}},[t._v("includeOptions")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-27"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-27"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L619",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:619"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"initialize"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#initialize"}},[t._v("#")]),t._v(" initialize")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("initialize")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-19"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-19"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-22"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-22"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#initialize"}},[t._v("initialize")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-28"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-28"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L603",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:603"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"linkscales"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#linkscales"}},[t._v("#")]),t._v(" linkScales")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("linkScales")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-20"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-20"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-23"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-23"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#linkscales"}},[t._v("linkScales")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-29"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-29"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L590",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:590"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"parse"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parse"}},[t._v("#")]),t._v(" parse")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("parse")]),t._v("("),a("code",[t._v("start")]),t._v(", "),a("code",[t._v("count")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-12"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("start")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("count")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-21"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-21"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-24"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-24"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#parse"}},[t._v("parse")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-30"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-30"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L635",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:635"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"parsearraydata"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parsearraydata"}},[t._v("#")]),t._v(" parseArrayData")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Protected")]),t._v(" "),a("strong",[t._v("parseArrayData")]),t._v("("),a("code",[t._v("meta")]),t._v(", "),a("code",[t._v("data")]),t._v(", "),a("code",[t._v("start")]),t._v(", "),a("code",[t._v("count")]),t._v("): "),a("code",[t._v("AnyObject")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"parameters-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-13"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("meta")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#chartmeta"}},[a("code",[t._v("ChartMeta")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(">")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("data")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("AnyObject")]),t._v("[]")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("start")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("count")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-22"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-22"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("AnyObject")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-25"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-25"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#parsearraydata"}},[t._v("parseArrayData")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-31"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-31"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L637",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:637"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"parseobjectdata"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parseobjectdata"}},[t._v("#")]),t._v(" parseObjectData")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Protected")]),t._v(" "),a("strong",[t._v("parseObjectData")]),t._v("("),a("code",[t._v("meta")]),t._v(", "),a("code",[t._v("data")]),t._v(", "),a("code",[t._v("start")]),t._v(", "),a("code",[t._v("count")]),t._v("): "),a("code",[t._v("AnyObject")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"parameters-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-14"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("meta")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#chartmeta"}},[a("code",[t._v("ChartMeta")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(">")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("data")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("AnyObject")]),t._v("[]")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("start")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("count")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-23"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-23"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("AnyObject")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-26"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-26"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#parseobjectdata"}},[t._v("parseObjectData")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-32"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-32"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L638",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:638"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"parseprimitivedata"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parseprimitivedata"}},[t._v("#")]),t._v(" parsePrimitiveData")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Protected")]),t._v(" "),a("strong",[t._v("parsePrimitiveData")]),t._v("("),a("code",[t._v("meta")]),t._v(", "),a("code",[t._v("data")]),t._v(", "),a("code",[t._v("start")]),t._v(", "),a("code",[t._v("count")]),t._v("): "),a("code",[t._v("AnyObject")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"parameters-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-15"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("meta")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#chartmeta"}},[a("code",[t._v("ChartMeta")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(">")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("data")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("AnyObject")]),t._v("[]")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("start")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("count")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-24"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-24"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("AnyObject")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-27"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-27"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#parseprimitivedata"}},[t._v("parsePrimitiveData")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-33"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-33"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L636",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:636"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"removehoverstyle"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#removehoverstyle"}},[t._v("#")]),t._v(" removeHoverStyle")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("removeHoverStyle")]),t._v("("),a("code",[t._v("element")]),t._v(", "),a("code",[t._v("datasetIndex")]),t._v(", "),a("code",[t._v("index")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-16"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("element")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("datasetIndex")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-25"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-25"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-28"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-28"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#removehoverstyle"}},[t._v("removeHoverStyle")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-34"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-34"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L632",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:632"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"reset"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#reset"}},[t._v("#")]),t._v(" reset")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("reset")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-26"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-26"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-29"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-29"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#reset"}},[t._v("reset")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-35"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-35"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L598",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:598"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"resolvedataelementoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#resolvedataelementoptions"}},[t._v("#")]),t._v(" resolveDataElementOptions")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Protected")]),t._v(" "),a("strong",[t._v("resolveDataElementOptions")]),t._v("("),a("code",[t._v("index")]),t._v(", "),a("code",[t._v("mode")]),t._v("): "),a("code",[t._v("AnyObject")])]),t._v(" "),a("h4",{attrs:{id:"parameters-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-17"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("mode")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"resize"')]),t._v(" | "),a("code",[t._v('"reset"')]),t._v(" | "),a("code",[t._v('"none"')]),t._v(" | "),a("code",[t._v('"hide"')]),t._v(" | "),a("code",[t._v('"show"')]),t._v(" | "),a("code",[t._v('"normal"')]),t._v(" | "),a("code",[t._v('"active"')])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-27"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-27"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("AnyObject")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-30"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-30"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#resolvedataelementoptions"}},[t._v("resolveDataElementOptions")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-36"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-36"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L609",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:609"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"resolvedatasetelementoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#resolvedatasetelementoptions"}},[t._v("#")]),t._v(" resolveDatasetElementOptions")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Protected")]),t._v(" "),a("strong",[t._v("resolveDatasetElementOptions")]),t._v("("),a("code",[t._v("mode")]),t._v("): "),a("code",[t._v("AnyObject")])]),t._v(" "),a("h4",{attrs:{id:"parameters-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-18"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("mode")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"resize"')]),t._v(" | "),a("code",[t._v('"reset"')]),t._v(" | "),a("code",[t._v('"none"')]),t._v(" | "),a("code",[t._v('"hide"')]),t._v(" | "),a("code",[t._v('"show"')]),t._v(" | "),a("code",[t._v('"normal"')]),t._v(" | "),a("code",[t._v('"active"')])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-28"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-28"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("AnyObject")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-31"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-31"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#resolvedatasetelementoptions"}},[t._v("resolveDatasetElementOptions")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-37"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-37"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L608",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:608"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"sethoverstyle"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#sethoverstyle"}},[t._v("#")]),t._v(" setHoverStyle")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("setHoverStyle")]),t._v("("),a("code",[t._v("element")]),t._v(", "),a("code",[t._v("datasetIndex")]),t._v(", "),a("code",[t._v("index")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters-19"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-19"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("element")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("datasetIndex")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-29"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-29"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-32"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-32"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#sethoverstyle"}},[t._v("setHoverStyle")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-38"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-38"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L633",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:633"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"update"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#update"}},[t._v("#")]),t._v(" update")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("update")]),t._v("("),a("code",[t._v("mode")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters-20"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-20"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("mode")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"resize"')]),t._v(" | "),a("code",[t._v('"reset"')]),t._v(" | "),a("code",[t._v('"none"')]),t._v(" | "),a("code",[t._v('"hide"')]),t._v(" | "),a("code",[t._v('"show"')]),t._v(" | "),a("code",[t._v('"normal"')]),t._v(" | "),a("code",[t._v('"active"')])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-30"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-30"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-33"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-33"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#update"}},[t._v("update")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-39"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-39"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L594",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:594"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"updateelement"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#updateelement"}},[t._v("#")]),t._v(" updateElement")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Protected")]),t._v(" "),a("strong",[t._v("updateElement")]),t._v("("),a("code",[t._v("element")]),t._v(", "),a("code",[t._v("index")]),t._v(", "),a("code",[t._v("properties")]),t._v(", "),a("code",[t._v("mode")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Utility for updating an element with new properties, using animations when appropriate.")]),t._v(" "),a("h4",{attrs:{id:"parameters-21"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-21"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("element")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("properties")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("AnyObject")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("mode")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"resize"')]),t._v(" | "),a("code",[t._v('"reset"')]),t._v(" | "),a("code",[t._v('"none"')]),t._v(" | "),a("code",[t._v('"hide"')]),t._v(" | "),a("code",[t._v('"show"')]),t._v(" | "),a("code",[t._v('"normal"')]),t._v(" | "),a("code",[t._v('"active"')])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-31"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-31"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-34"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-34"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#updateelement"}},[t._v("updateElement")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-40"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-40"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L625",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:625"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"updateelements"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#updateelements"}},[t._v("#")]),t._v(" updateElements")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("updateElements")]),t._v("("),a("code",[t._v("elements")]),t._v(", "),a("code",[t._v("start")]),t._v(", "),a("code",[t._v("count")]),t._v(", "),a("code",[t._v("mode")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters-22"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-22"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("elements")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">[]")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("start")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("count")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("mode")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"resize"')]),t._v(" | "),a("code",[t._v('"reset"')]),t._v(" | "),a("code",[t._v('"none"')]),t._v(" | "),a("code",[t._v('"hide"')]),t._v(" | "),a("code",[t._v('"show"')]),t._v(" | "),a("code",[t._v('"normal"')]),t._v(" | "),a("code",[t._v('"active"')])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-32"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-32"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-35"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-35"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#updateelements"}},[t._v("updateElements")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-41"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-41"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L593",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:593"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"updateindex"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#updateindex"}},[t._v("#")]),t._v(" updateIndex")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("updateIndex")]),t._v("("),a("code",[t._v("datasetIndex")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters-23"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-23"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("datasetIndex")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-33"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-33"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-36"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-36"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#updateindex"}},[t._v("updateIndex")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-42"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-42"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L595",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:595"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"updaterangefromparsed"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#updaterangefromparsed"}},[t._v("#")]),t._v(" updateRangeFromParsed")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Protected")]),t._v(" "),a("strong",[t._v("updateRangeFromParsed")]),t._v("("),a("code",[t._v("range")]),t._v(", "),a("code",[t._v("scale")]),t._v(", "),a("code",[t._v("parsed")]),t._v(", "),a("code",[t._v("stack")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters-24"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-24"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("range")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("range.max")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("range.min")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("scale")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("parsed")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("unknown")]),t._v("[]")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("stack")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-34"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-34"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-37"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-37"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#updaterangefromparsed"}},[t._v("updateRangeFromParsed")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-43"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-43"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L641",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:641"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"updatesharedoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#updatesharedoptions"}},[t._v("#")]),t._v(" updateSharedOptions")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Protected")]),t._v(" "),a("strong",[t._v("updateSharedOptions")]),t._v("("),a("code",[t._v("sharedOptions")]),t._v(", "),a("code",[t._v("mode")]),t._v(", "),a("code",[t._v("newOptions")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Utility to animate the shared options, that are potentially affecting multiple elements.")]),t._v(" "),a("h4",{attrs:{id:"parameters-25"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-25"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("sharedOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("AnyObject")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("mode")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"resize"')]),t._v(" | "),a("code",[t._v('"reset"')]),t._v(" | "),a("code",[t._v('"none"')]),t._v(" | "),a("code",[t._v('"hide"')]),t._v(" | "),a("code",[t._v('"show"')]),t._v(" | "),a("code",[t._v('"normal"')]),t._v(" | "),a("code",[t._v('"active"')])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("newOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("AnyObject")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-35"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-35"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-38"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-38"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html#updatesharedoptions"}},[t._v("updateSharedOptions")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-44"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-44"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L631",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:631"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);