(window.webpackJsonp=window.webpackJsonp||[]).push([[85],{416:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-parsingoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-parsingoptions"}},[t._v("#")]),t._v(" Interface: ParsingOptions")]),t._v(" "),a("h2",{attrs:{id:"hierarchy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),a("ul",[a("li",[a("p",[a("strong",[a("code",[t._v("ParsingOptions")])])]),t._v(" "),a("p",[t._v("↳ "),a("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html"}},[a("code",[t._v("ControllerDatasetOptions")])])],1),t._v(" "),a("p",[t._v("↳ "),a("RouterLink",{attrs:{to:"/api/interfaces/CoreChartOptions.html"}},[a("code",[t._v("CoreChartOptions")])])],1)])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"normalized"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#normalized"}},[t._v("#")]),t._v(" normalized")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("normalized")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("Chart.js is fastest if you provide data with indices that are unique, sorted, and consistent across datasets and provide the normalized: true option to let Chart.js know that you have done so.")]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L58",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:58"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"parsing"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parsing"}},[t._v("#")]),t._v(" parsing")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("parsing")]),t._v(": "),a("code",[t._v("false")]),t._v(" | { [key: string]: "),a("code",[t._v("string")]),t._v(";  }")]),t._v(" "),a("p",[t._v("How to parse the dataset. The parsing can be disabled by specifying parsing: false at chart options or dataset. If parsing is disabled, data must be sorted and in the formats the associated chart type and scales use internally.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L49",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:49"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);