(window.webpackJsonp=window.webpackJsonp||[]).push([[70],{401:function(e,t,a){"use strict";a.r(t);var r=a(6),s=Object(r.a)({},(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ContentSlotsDistributor",{attrs:{"slot-key":e.$parent.slotKey}},[a("h1",{attrs:{id:"interface-fontspec"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-fontspec"}},[e._v("#")]),e._v(" Interface: FontSpec")]),e._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[e._v("#")]),e._v(" Properties")]),e._v(" "),a("h3",{attrs:{id:"family"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#family"}},[e._v("#")]),e._v(" family")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("family")]),e._v(": "),a("code",[e._v("string")])]),e._v(" "),a("p",[e._v("Default font family for all text, follows CSS font-family options.")]),e._v(" "),a("p",[a("strong",[a("code",[e._v("default")])]),e._v(" \"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\"")]),e._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1659",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1659"),a("OutboundLink")],1)]),e._v(" "),a("hr"),e._v(" "),a("h3",{attrs:{id:"lineheight"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#lineheight"}},[e._v("#")]),e._v(" lineHeight")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("lineHeight")]),e._v(": "),a("code",[e._v("string")]),e._v(" | "),a("code",[e._v("number")])]),e._v(" "),a("p",[e._v("Height of an individual line of text (see MDN).")]),e._v(" "),a("p",[a("strong",[a("code",[e._v("default")])]),e._v(" 1.2")]),e._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1678",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1678"),a("OutboundLink")],1)]),e._v(" "),a("hr"),e._v(" "),a("h3",{attrs:{id:"size"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#size"}},[e._v("#")]),e._v(" size")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("size")]),e._v(": "),a("code",[e._v("number")])]),e._v(" "),a("p",[e._v("Default font size (in px) for text. Does not apply to radialLinear scale point labels.")]),e._v(" "),a("p",[a("strong",[a("code",[e._v("default")])]),e._v(" 12")]),e._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1664",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1664"),a("OutboundLink")],1)]),e._v(" "),a("hr"),e._v(" "),a("h3",{attrs:{id:"style"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#style"}},[e._v("#")]),e._v(" style")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("style")]),e._v(": "),a("code",[e._v('"normal"')]),e._v(" | "),a("code",[e._v('"italic"')]),e._v(" | "),a("code",[e._v('"oblique"')]),e._v(" | "),a("code",[e._v('"initial"')]),e._v(" | "),a("code",[e._v('"inherit"')])]),e._v(" "),a("p",[e._v("Default font style. Does not apply to tooltip title or footer. Does not apply to chart title. Follows CSS font-style options (i.e. normal, italic, oblique, initial, inherit)")]),e._v(" "),a("p",[a("strong",[a("code",[e._v("default")])]),e._v(" 'normal'")]),e._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1669",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1669"),a("OutboundLink")],1)]),e._v(" "),a("hr"),e._v(" "),a("h3",{attrs:{id:"weight"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#weight"}},[e._v("#")]),e._v(" weight")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("weight")]),e._v(": "),a("code",[e._v("string")])]),e._v(" "),a("p",[e._v("Default font weight (boldness). (see MDN).")]),e._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1673",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1673"),a("OutboundLink")],1)])])}),[],!1,null,null,null);t.default=s.exports}}]);