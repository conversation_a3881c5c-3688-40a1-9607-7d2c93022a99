(window.webpackJsonp=window.webpackJsonp||[]).push([[14],{345:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"class-basicplatform"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#class-basicplatform"}},[t._v("#")]),t._v(" Class: BasicPlatform")]),t._v(" "),a("h2",{attrs:{id:"hierarchy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),a("ul",[a("li",[a("p",[a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html"}},[a("code",[t._v("BasePlatform")])])],1),t._v(" "),a("p",[t._v("↳ "),a("strong",[a("code",[t._v("BasicPlatform")])])])])]),t._v(" "),a("h2",{attrs:{id:"constructors"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#constructors"}},[t._v("#")]),t._v(" Constructors")]),t._v(" "),a("h3",{attrs:{id:"constructor"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#constructor"}},[t._v("#")]),t._v(" constructor")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("new BasicPlatform")]),t._v("()")]),t._v(" "),a("h4",{attrs:{id:"inherited-from"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html"}},[t._v("BasePlatform")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html#constructor"}},[t._v("constructor")])],1),t._v(" "),a("h2",{attrs:{id:"methods"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),a("h3",{attrs:{id:"acquirecontext"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#acquirecontext"}},[t._v("#")]),t._v(" acquireContext")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("acquireContext")]),t._v("("),a("code",[t._v("canvas")]),t._v(", "),a("code",[t._v("options?")]),t._v("): "),a("code",[t._v("CanvasRenderingContext2D")])]),t._v(" "),a("p",[t._v("Called at chart construction time, returns a context2d instance implementing\nthe "),a("a",{attrs:{href:"https://www.w3.org/TR/2dcontext/",target:"_blank",rel:"noopener noreferrer"}},[t._v("W3C Canvas 2D Context API standard"),a("OutboundLink")],1),t._v(".")]),t._v(" "),a("h4",{attrs:{id:"parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("canvas")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("HTMLCanvasElement")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The canvas from which to acquire context (platform specific)")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("CanvasRenderingContext2DSettings")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart options")])])])]),t._v(" "),a("h4",{attrs:{id:"returns"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("CanvasRenderingContext2D")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html"}},[t._v("BasePlatform")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html#acquirecontext"}},[t._v("acquireContext")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2057",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2057"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"addeventlistener"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#addeventlistener"}},[t._v("#")]),t._v(" addEventListener")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("addEventListener")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("type")]),t._v(", "),a("code",[t._v("listener")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Registers the specified listener on the given chart.")]),t._v(" "),a("h4",{attrs:{id:"parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-2"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Chart from which to listen for event")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("type")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("string")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The ("),a("RouterLink",{attrs:{to:"/api/interfaces/ChartEvent.html"}},[t._v("ChartEvent")]),t._v(") type to listen for")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("listener")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("("),a("code",[t._v("e")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartEvent.html"}},[a("code",[t._v("ChartEvent")])]),t._v(") => "),a("code",[t._v("void")])],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Receives a notification (an object that implements the "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartEvent.html"}},[t._v("ChartEvent")]),t._v(" interface) when an event of the specified type occurs.")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-3"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html"}},[t._v("BasePlatform")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html#addeventlistener"}},[t._v("addEventListener")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2075",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2075"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getdevicepixelratio"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getdevicepixelratio"}},[t._v("#")]),t._v(" getDevicePixelRatio")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getDevicePixelRatio")]),t._v("(): "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"returns-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-3"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("the current devicePixelRatio of the device this platform is connected to.")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-4"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html"}},[t._v("BasePlatform")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html#getdevicepixelratio"}},[t._v("getDevicePixelRatio")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2086",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2086"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getmaximumsize"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getmaximumsize"}},[t._v("#")]),t._v(" getMaximumSize")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getMaximumSize")]),t._v("("),a("code",[t._v("canvas")]),t._v(", "),a("code",[t._v("width?")]),t._v(", "),a("code",[t._v("height?")]),t._v(", "),a("code",[t._v("aspectRatio?")]),t._v("): "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"parameters-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-3"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("canvas")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("HTMLCanvasElement")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The canvas for which to calculate the maximum size")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("width?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("height?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("aspectRatio?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("-")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-4"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("Object")])]),t._v(" "),a("p",[t._v("the maximum size available.")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("height")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("width")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-5"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html"}},[t._v("BasePlatform")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html#getmaximumsize"}},[t._v("getMaximumSize")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2094",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2094"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"isattached"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#isattached"}},[t._v("#")]),t._v(" isAttached")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("isAttached")]),t._v("("),a("code",[t._v("canvas")]),t._v("): "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"parameters-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-4"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("canvas")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("HTMLCanvasElement")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-5"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("true if the canvas is attached to the platform, false if not.")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-6"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html"}},[t._v("BasePlatform")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html#isattached"}},[t._v("isAttached")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2099",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2099"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"releasecontext"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#releasecontext"}},[t._v("#")]),t._v(" releaseContext")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("releaseContext")]),t._v("("),a("code",[t._v("context")]),t._v("): "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("Called at chart destruction time, releases any resources associated to the context\npreviously returned by the acquireContext() method.")]),t._v(" "),a("h4",{attrs:{id:"parameters-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-5"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("context")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("CanvasRenderingContext2D")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The context2d instance")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-6"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("true if the method succeeded, else false")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-7"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html"}},[t._v("BasePlatform")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html#releasecontext"}},[t._v("releaseContext")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2067",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2067"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"removeeventlistener"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#removeeventlistener"}},[t._v("#")]),t._v(" removeEventListener")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("removeEventListener")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("type")]),t._v(", "),a("code",[t._v("listener")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Removes the specified listener previously registered with addEventListener.")]),t._v(" "),a("h4",{attrs:{id:"parameters-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-6"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Chart from which to remove the listener")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("type")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("string")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The ("),a("RouterLink",{attrs:{to:"/api/interfaces/ChartEvent.html"}},[t._v("ChartEvent")]),t._v(") type to remove")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("listener")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("("),a("code",[t._v("e")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartEvent.html"}},[a("code",[t._v("ChartEvent")])]),t._v(") => "),a("code",[t._v("void")])],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The listener function to remove from the event target.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-7"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-8"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html"}},[t._v("BasePlatform")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html#removeeventlistener"}},[t._v("removeEventListener")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2082",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2082"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"updateconfig"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#updateconfig"}},[t._v("#")]),t._v(" updateConfig")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("updateConfig")]),t._v("("),a("code",[t._v("config")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Updates config with platform specific requirements")]),t._v(" "),a("h4",{attrs:{id:"parameters-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-7"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("config")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ChartConfiguration.html"}},[a("code",[t._v("ChartConfiguration")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v("> | "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartConfigurationCustomTypesPerDataset.html"}},[a("code",[t._v("ChartConfigurationCustomTypesPerDataset")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-8"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-9"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html"}},[t._v("BasePlatform")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html#updateconfig"}},[t._v("updateConfig")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2104",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2104"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);