(window.webpackJsonp=window.webpackJsonp||[]).push([[64],{395:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-doughnutmetaextensions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-doughnutmetaextensions"}},[t._v("#")]),t._v(" Interface: DoughnutMetaExtensions")]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"total"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#total"}},[t._v("#")]),t._v(" total")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("total")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L349",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:349"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);