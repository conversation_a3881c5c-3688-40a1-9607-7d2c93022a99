(window.webpackJsonp=window.webpackJsonp||[]).push([[38],{369:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-cartesianscaleoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-cartesianscaleoptions"}},[t._v("#")]),t._v(" Interface: CartesianScaleOptions")]),t._v(" "),a("h2",{attrs:{id:"hierarchy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),a("ul",[a("li",[a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])])],1),t._v(" "),a("p",[t._v("↳ "),a("strong",[a("code",[t._v("CartesianScaleOptions")])])])])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"aligntopixels"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#aligntopixels"}},[t._v("#")]),t._v(" alignToPixels")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("alignToPixels")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("Align pixel values to device pixels")]),t._v(" "),a("h4",{attrs:{id:"inherited-from"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#aligntopixels"}},[t._v("alignToPixels")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1156",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1156"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"axis"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#axis"}},[t._v("#")]),t._v(" axis")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("axis")]),t._v(": "),a("code",[t._v('"x"')]),t._v(" | "),a("code",[t._v('"y"')])]),t._v(" "),a("p",[t._v("Which type of axis this is. Possible values are: 'x', 'y'. If not set, this is inferred from the first character of the ID which should be 'x' or 'y'.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3089",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3089"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"bounds"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#bounds"}},[t._v("#")]),t._v(" bounds")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("bounds")]),t._v(": "),a("code",[t._v('"data"')]),t._v(" | "),a("code",[t._v('"ticks"')])]),t._v(" "),a("p",[t._v("Scale boundary strategy (bypassed by min/max time options)")]),t._v(" "),a("ul",[a("li",[a("code",[t._v("data")]),t._v(": make sure data are fully visible, ticks outside are removed")]),t._v(" "),a("li",[a("code",[t._v("ticks")]),t._v(": make sure ticks are fully visible, data outside are truncated")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("since")])]),t._v(" 2.7.0")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" 'ticks'")]),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3068",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3068"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"display"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#display"}},[t._v("#")]),t._v(" display")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("display")]),t._v(": "),a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v('"auto"')])]),t._v(" "),a("p",[t._v("Controls the axis global visibility (visible when true, hidden when false). When display: 'auto', the axis is visible only if at least one associated dataset is visible.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" true")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#display"}},[t._v("display")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1152",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1152"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"grid"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#grid"}},[t._v("#")]),t._v(" grid")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("grid")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/GridLineOptions.html"}},[a("code",[t._v("GridLineOptions")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3107",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3107"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"max"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#max"}},[t._v("#")]),t._v(" max")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("max")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("User defined maximum value for the scale, overrides maximum value from data.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3099",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3099"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"min"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#min"}},[t._v("#")]),t._v(" min")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("min")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("User defined minimum value for the scale, overrides minimum value from data.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3094",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3094"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"offset"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#offset"}},[t._v("#")]),t._v(" offset")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("offset")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("If true, extra space is added to the both edges and the axis is scaled to fit into the chart area. This is set to true for a bar chart by default.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" false")]),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3105",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3105"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"position"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#position"}},[t._v("#")]),t._v(" position")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("position")]),t._v(": "),a("code",[t._v('"left"')]),t._v(" | "),a("code",[t._v('"right"')]),t._v(" | "),a("code",[t._v('"bottom"')]),t._v(" | "),a("code",[t._v('"top"')]),t._v(" | "),a("code",[t._v('"center"')]),t._v(" | { [scale: string]: "),a("code",[t._v("number")]),t._v(";  }")]),t._v(" "),a("p",[t._v("Position of the axis.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3073",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3073"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"reverse"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#reverse"}},[t._v("#")]),t._v(" reverse")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("reverse")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("Reverse the scale.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" false")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-3"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#reverse"}},[t._v("reverse")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1161",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1161"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"stack"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#stack"}},[t._v("#")]),t._v(" stack")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("stack")]),t._v(": "),a("code",[t._v("string")])]),t._v(" "),a("p",[t._v("Stack group. Axes at the same "),a("code",[t._v("position")]),t._v(" with same "),a("code",[t._v("stack")]),t._v(" are stacked.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3078",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3078"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"stackweight"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#stackweight"}},[t._v("#")]),t._v(" stackWeight")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("stackWeight")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Weight of the scale in stack group. Used to determine the amount of allocated space for the scale within the group.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" 1")]),t._v(" "),a("h4",{attrs:{id:"defined-in-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3084",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3084"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"stacked"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#stacked"}},[t._v("#")]),t._v(" stacked")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("stacked")]),t._v(": "),a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v('"single"')])]),t._v(" "),a("p",[t._v("If true, data will be comprised between datasets of data")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" false")]),t._v(" "),a("h4",{attrs:{id:"defined-in-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3136",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3136"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"ticks"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#ticks"}},[t._v("#")]),t._v(" ticks")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("ticks")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#cartesiantickoptions"}},[a("code",[t._v("CartesianTickOptions")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3138",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3138"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"title"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#title"}},[t._v("#")]),t._v(" title")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("title")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("p",[t._v("Options for the scale title.")]),t._v(" "),a("h4",{attrs:{id:"type-declaration"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("align")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#align"}},[a("code",[t._v("Align")])])],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Alignment of the axis title.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("color")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[t._v("Color")])])],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Color of the axis label.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("display")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("If true, displays the axis title.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("font")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#scriptableandscriptableoptions"}},[a("code",[t._v("ScriptableAndScriptableOptions")])]),t._v("<"),a("code",[t._v("Partial")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/FontSpec.html"}},[a("code",[t._v("FontSpec")])]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableCartesianScaleContext.html"}},[a("code",[t._v("ScriptableCartesianScaleContext")])]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Information about the axis title font.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("padding")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")]),t._v(" | { "),a("code",[t._v("bottom")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("top")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("y")]),t._v(": "),a("code",[t._v("number")]),t._v("  }")]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Padding to apply around scale labels.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("text")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v('The text for the title, e.g. "# of People" or "Response Choices".')])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-15"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3110",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3110"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"weight"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#weight"}},[t._v("#")]),t._v(" weight")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("weight")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("The weight used to sort the axis. Higher weights are further away from the chart area.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" true")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-4"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#weight"}},[t._v("weight")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-16"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1166",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1166"),a("OutboundLink")],1)]),t._v(" "),a("h2",{attrs:{id:"methods"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),a("h3",{attrs:{id:"afterbuildticks"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterbuildticks"}},[t._v("#")]),t._v(" afterBuildTicks")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterBuildTicks")]),t._v("("),a("code",[t._v("axis")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Callback that runs after ticks are created. Useful for filtering ticks.")]),t._v(" "),a("h4",{attrs:{id:"parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("axis")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-5"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#afterbuildticks"}},[t._v("afterBuildTicks")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-17"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1194",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1194"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"aftercalculatelabelrotation"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#aftercalculatelabelrotation"}},[t._v("#")]),t._v(" afterCalculateLabelRotation")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterCalculateLabelRotation")]),t._v("("),a("code",[t._v("axis")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Callback that runs after tick rotation is determined.")]),t._v(" "),a("h4",{attrs:{id:"parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-2"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("axis")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-6"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#aftercalculatelabelrotation"}},[t._v("afterCalculateLabelRotation")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-18"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1210",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1210"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterdatalimits"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterdatalimits"}},[t._v("#")]),t._v(" afterDataLimits")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterDataLimits")]),t._v("("),a("code",[t._v("axis")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Callback that runs after data limits are determined.")]),t._v(" "),a("h4",{attrs:{id:"parameters-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-3"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("axis")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-3"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-7"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#afterdatalimits"}},[t._v("afterDataLimits")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-19"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-19"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1186",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1186"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterfit"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterfit"}},[t._v("#")]),t._v(" afterFit")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterFit")]),t._v("("),a("code",[t._v("axis")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Callback that runs after the scale fits to the canvas.")]),t._v(" "),a("h4",{attrs:{id:"parameters-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-4"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("axis")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-4"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-8"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#afterfit"}},[t._v("afterFit")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-20"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-20"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1218",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1218"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"aftersetdimensions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#aftersetdimensions"}},[t._v("#")]),t._v(" afterSetDimensions")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterSetDimensions")]),t._v("("),a("code",[t._v("axis")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Callback that runs after dimensions are set.")]),t._v(" "),a("h4",{attrs:{id:"parameters-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-5"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("axis")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-5"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-9"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#aftersetdimensions"}},[t._v("afterSetDimensions")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-21"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-21"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1178",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1178"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterticktolabelconversion"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterticktolabelconversion"}},[t._v("#")]),t._v(" afterTickToLabelConversion")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterTickToLabelConversion")]),t._v("("),a("code",[t._v("axis")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Callback that runs after ticks are converted into strings.")]),t._v(" "),a("h4",{attrs:{id:"parameters-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-6"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("axis")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-6"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-10"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#afterticktolabelconversion"}},[t._v("afterTickToLabelConversion")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-22"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-22"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1202",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1202"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterupdate"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterupdate"}},[t._v("#")]),t._v(" afterUpdate")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterUpdate")]),t._v("("),a("code",[t._v("axis")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Callback that runs at the end of the update process.")]),t._v(" "),a("h4",{attrs:{id:"parameters-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-7"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("axis")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-7"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-11"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#afterupdate"}},[t._v("afterUpdate")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-23"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-23"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1222",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1222"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforebuildticks"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforebuildticks"}},[t._v("#")]),t._v(" beforeBuildTicks")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeBuildTicks")]),t._v("("),a("code",[t._v("axis")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Callback that runs before ticks are created.")]),t._v(" "),a("h4",{attrs:{id:"parameters-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-8"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("axis")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-8"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-12"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#beforebuildticks"}},[t._v("beforeBuildTicks")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-24"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-24"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1190",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1190"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforecalculatelabelrotation"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforecalculatelabelrotation"}},[t._v("#")]),t._v(" beforeCalculateLabelRotation")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeCalculateLabelRotation")]),t._v("("),a("code",[t._v("axis")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Callback that runs before tick rotation is determined.")]),t._v(" "),a("h4",{attrs:{id:"parameters-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-9"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("axis")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-9"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-13"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#beforecalculatelabelrotation"}},[t._v("beforeCalculateLabelRotation")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-25"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-25"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1206",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1206"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforedatalimits"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforedatalimits"}},[t._v("#")]),t._v(" beforeDataLimits")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeDataLimits")]),t._v("("),a("code",[t._v("axis")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Callback that runs before data limits are determined.")]),t._v(" "),a("h4",{attrs:{id:"parameters-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-10"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("axis")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-10"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-14"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#beforedatalimits"}},[t._v("beforeDataLimits")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-26"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-26"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1182",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1182"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforefit"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforefit"}},[t._v("#")]),t._v(" beforeFit")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeFit")]),t._v("("),a("code",[t._v("axis")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Callback that runs before the scale fits to the canvas.")]),t._v(" "),a("h4",{attrs:{id:"parameters-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-11"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("axis")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-11"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-15"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#beforefit"}},[t._v("beforeFit")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-27"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-27"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1214",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1214"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforesetdimensions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforesetdimensions"}},[t._v("#")]),t._v(" beforeSetDimensions")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeSetDimensions")]),t._v("("),a("code",[t._v("axis")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Callback that runs before dimensions are set.")]),t._v(" "),a("h4",{attrs:{id:"parameters-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-12"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("axis")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-12"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-16"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#beforesetdimensions"}},[t._v("beforeSetDimensions")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-28"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-28"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1174",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1174"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforeticktolabelconversion"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforeticktolabelconversion"}},[t._v("#")]),t._v(" beforeTickToLabelConversion")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeTickToLabelConversion")]),t._v("("),a("code",[t._v("axis")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Callback that runs before ticks are converted into strings.")]),t._v(" "),a("h4",{attrs:{id:"parameters-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-13"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("axis")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-13"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-17"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#beforeticktolabelconversion"}},[t._v("beforeTickToLabelConversion")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-29"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-29"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1198",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1198"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforeupdate"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforeupdate"}},[t._v("#")]),t._v(" beforeUpdate")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeUpdate")]),t._v("("),a("code",[t._v("axis")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Callback called before the update process starts.")]),t._v(" "),a("h4",{attrs:{id:"parameters-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-14"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("axis")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-14"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-18"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html#beforeupdate"}},[t._v("beforeUpdate")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-30"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-30"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1170",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1170"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);