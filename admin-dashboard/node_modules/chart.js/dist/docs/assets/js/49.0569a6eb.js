(window.webpackJsonp=window.webpackJsonp||[]).push([[49],{380:function(t,e,a){"use strict";a.r(e);var r=a(6),i=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-charttyperegistry"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-charttyperegistry"}},[t._v("#")]),t._v(" Interface: ChartTypeRegistry")]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"bar"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#bar"}},[t._v("#")]),t._v(" bar")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("bar")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chartOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/BarControllerChartOptions.html"}},[a("code",[t._v("BarControllerChartOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("datasetOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/BarControllerDatasetOptions.html"}},[a("code",[t._v("BarControllerDatasetOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("defaultDataPoint")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("metaExtensions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("parsedDataType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("BarParsedData")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("scales")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html"}},[a("code",[t._v("CartesianScaleTypeRegistry")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3549",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3549"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"bubble"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#bubble"}},[t._v("#")]),t._v(" bubble")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("bubble")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-2"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chartOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("unknown")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("datasetOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/BubbleControllerDatasetOptions.html"}},[a("code",[t._v("BubbleControllerDatasetOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("defaultDataPoint")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("metaExtensions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("parsedDataType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("BubbleParsedData")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("scales")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html"}},[a("code",[t._v("CartesianScaleTypeRegistry")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3573",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3573"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"doughnut"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#doughnut"}},[t._v("#")]),t._v(" doughnut")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("doughnut")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-3"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chartOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerChartOptions.html"}},[a("code",[t._v("DoughnutControllerChartOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("datasetOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[a("code",[t._v("DoughnutControllerDatasetOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("defaultDataPoint")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("metaExtensions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutMetaExtensions.html"}},[a("code",[t._v("DoughnutMetaExtensions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("parsedDataType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("scales")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html"}},[a("code",[t._v("CartesianScaleTypeRegistry")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3589",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3589"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"line"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#line"}},[t._v("#")]),t._v(" line")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("line")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-4"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chartOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/LineControllerChartOptions.html"}},[a("code",[t._v("LineControllerChartOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("datasetOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/LineControllerDatasetOptions.html"}},[a("code",[t._v("LineControllerDatasetOptions")])]),t._v(" & "),a("RouterLink",{attrs:{to:"/api/interfaces/FillerControllerDatasetOptions.html"}},[a("code",[t._v("FillerControllerDatasetOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("defaultDataPoint")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("metaExtensions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("parsedDataType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("CartesianParsedData")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("scales")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html"}},[a("code",[t._v("CartesianScaleTypeRegistry")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3557",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3557"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"pie"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#pie"}},[t._v("#")]),t._v(" pie")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("pie")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-5"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chartOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerChartOptions.html"}},[a("code",[t._v("DoughnutControllerChartOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("datasetOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[a("code",[t._v("DoughnutControllerDatasetOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("defaultDataPoint")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("metaExtensions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutMetaExtensions.html"}},[a("code",[t._v("DoughnutMetaExtensions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("parsedDataType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("scales")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html"}},[a("code",[t._v("CartesianScaleTypeRegistry")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3581",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3581"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"polararea"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#polararea"}},[t._v("#")]),t._v(" polarArea")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("polarArea")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-6"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chartOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/PolarAreaControllerChartOptions.html"}},[a("code",[t._v("PolarAreaControllerChartOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("datasetOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/PolarAreaControllerDatasetOptions.html"}},[a("code",[t._v("PolarAreaControllerDatasetOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("defaultDataPoint")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("metaExtensions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("parsedDataType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("RadialParsedData")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("scales")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"radialLinear"')])])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3597",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3597"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"radar"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#radar"}},[t._v("#")]),t._v(" radar")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("radar")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-7"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chartOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/LineControllerChartOptions.html"}},[a("code",[t._v("LineControllerChartOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("datasetOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/RadarControllerDatasetOptions.html"}},[a("code",[t._v("RadarControllerDatasetOptions")])]),t._v(" & "),a("RouterLink",{attrs:{to:"/api/interfaces/FillerControllerDatasetOptions.html"}},[a("code",[t._v("FillerControllerDatasetOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("defaultDataPoint")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("metaExtensions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("parsedDataType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("RadialParsedData")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("scales")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"radialLinear"')])])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3605",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3605"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"scatter"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#scatter"}},[t._v("#")]),t._v(" scatter")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("scatter")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-8"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chartOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/LineControllerChartOptions.html"}},[a("code",[t._v("LineControllerChartOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("datasetOptions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/LineControllerDatasetOptions.html"}},[a("code",[t._v("LineControllerDatasetOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("defaultDataPoint")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("metaExtensions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("parsedDataType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("CartesianParsedData")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("scales")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html"}},[a("code",[t._v("CartesianScaleTypeRegistry")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3565",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3565"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=i.exports}}]);