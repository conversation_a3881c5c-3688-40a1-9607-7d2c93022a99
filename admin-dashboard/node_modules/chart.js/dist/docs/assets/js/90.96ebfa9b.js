(window.webpackJsonp=window.webpackJsonp||[]).push([[90],{421:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-pointelement-t-o"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-pointelement-t-o"}},[t._v("#")]),t._v(" Interface: PointElement<T, O>")]),t._v(" "),a("h2",{attrs:{id:"type-parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("T")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/interfaces/PointProps.html"}},[a("code",[t._v("PointProps")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/interfaces/PointProps.html"}},[a("code",[t._v("PointProps")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/interfaces/PointOptions.html"}},[a("code",[t._v("PointOptions")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/interfaces/PointOptions.html"}},[a("code",[t._v("PointOptions")])])],1)])])]),t._v(" "),a("h2",{attrs:{id:"hierarchy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),a("ul",[a("li",[a("p",[a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("T")]),t._v(", "),a("code",[t._v("O")]),t._v(">")],1)]),t._v(" "),a("li",[a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/VisualElement.html"}},[a("code",[t._v("VisualElement")])])],1),t._v(" "),a("p",[t._v("↳ "),a("strong",[a("code",[t._v("PointElement")])])])])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"active"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#active"}},[t._v("#")]),t._v(" active")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("active")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[t._v("Element.active")]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L7",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:7"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"options"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#options"}},[t._v("#")]),t._v(" options")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("options")]),t._v(": "),a("code",[t._v("O")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[t._v("Element.options")]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L8",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:8"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"parsed"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parsed"}},[t._v("#")]),t._v(" parsed")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("parsed")]),t._v(": "),a("code",[t._v("CartesianParsedData")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1969",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1969"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"skip"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#skip"}},[t._v("#")]),t._v(" skip")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("skip")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1968",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1968"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"x"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#x"}},[t._v("#")]),t._v(" x")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("x")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-3"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[t._v("Element.x")]),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L5",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:5"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"y"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#y"}},[t._v("#")]),t._v(" y")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("y")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-4"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[t._v("Element.y")]),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L6",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:6"),a("OutboundLink")],1)]),t._v(" "),a("h2",{attrs:{id:"methods"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),a("h3",{attrs:{id:"draw"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#draw"}},[t._v("#")]),t._v(" draw")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("draw")]),t._v("("),a("code",[t._v("ctx")]),t._v(", "),a("code",[t._v("area?")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("ctx")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("CanvasRenderingContext2D")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("area?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-5"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/VisualElement.html"}},[t._v("VisualElement")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/VisualElement.html#draw"}},[t._v("draw")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1685",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1685"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getcenterpoint"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getcenterpoint"}},[t._v("#")]),t._v(" getCenterPoint")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getCenterPoint")]),t._v("("),a("code",[t._v("useFinalPosition?")]),t._v("): "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-2"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("useFinalPosition?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("Object")])]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("x")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("y")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-6"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/VisualElement.html"}},[t._v("VisualElement")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/VisualElement.html#getcenterpoint"}},[t._v("getCenterPoint")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1689",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1689"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getprops"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getprops"}},[t._v("#")]),t._v(" getProps")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getProps")]),t._v("<"),a("code",[t._v("P")]),t._v(">("),a("code",[t._v("props")]),t._v(", "),a("code",[t._v("final?")]),t._v("): "),a("code",[t._v("Pick")]),t._v("<"),a("code",[t._v("T")]),t._v(", "),a("code",[t._v("P")]),t._v("["),a("code",[t._v("number")]),t._v("]>")]),t._v(" "),a("h4",{attrs:{id:"type-parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-2"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("P")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends keyof "),a("code",[t._v("T")]),t._v("[]")])])])]),t._v(" "),a("h4",{attrs:{id:"parameters-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-3"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("props")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("P")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("final?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-3"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("Pick")]),t._v("<"),a("code",[t._v("T")]),t._v(", "),a("code",[t._v("P")]),t._v("["),a("code",[t._v("number")]),t._v("]>")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-7"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[t._v("Element.getProps")]),t._v(" "),a("h4",{attrs:{id:"defined-in-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L12",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:12"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getrange"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getrange"}},[t._v("#")]),t._v(" getRange")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("getRange")]),t._v("("),a("code",[t._v("axis")]),t._v("): "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"parameters-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-4"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("axis")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v('"x"')]),t._v(" | "),a("code",[t._v('"y"')])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-4"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-8"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/VisualElement.html"}},[t._v("VisualElement")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/VisualElement.html#getrange"}},[t._v("getRange")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1690",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1690"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"hasvalue"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hasvalue"}},[t._v("#")]),t._v(" hasValue")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("hasValue")]),t._v("(): "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"returns-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-5"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-9"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[t._v("Element.hasValue")]),t._v(" "),a("h4",{attrs:{id:"defined-in-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L11",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:11"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"inrange"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inrange"}},[t._v("#")]),t._v(" inRange")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("inRange")]),t._v("("),a("code",[t._v("mouseX")]),t._v(", "),a("code",[t._v("mouseY")]),t._v(", "),a("code",[t._v("useFinalPosition?")]),t._v("): "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"parameters-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-5"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("mouseX")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("mouseY")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("useFinalPosition?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-6"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-10"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/VisualElement.html"}},[t._v("VisualElement")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/VisualElement.html#inrange"}},[t._v("inRange")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1686",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1686"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"inxrange"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inxrange"}},[t._v("#")]),t._v(" inXRange")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("inXRange")]),t._v("("),a("code",[t._v("mouseX")]),t._v(", "),a("code",[t._v("useFinalPosition?")]),t._v("): "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"parameters-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-6"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("mouseX")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("useFinalPosition?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-7"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-11"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/VisualElement.html"}},[t._v("VisualElement")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/VisualElement.html#inxrange"}},[t._v("inXRange")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1687",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1687"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"inyrange"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inyrange"}},[t._v("#")]),t._v(" inYRange")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("inYRange")]),t._v("("),a("code",[t._v("mouseY")]),t._v(", "),a("code",[t._v("useFinalPosition?")]),t._v("): "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"parameters-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-7"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("mouseY")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("useFinalPosition?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-8"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-12"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/VisualElement.html"}},[t._v("VisualElement")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/VisualElement.html#inyrange"}},[t._v("inYRange")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1688",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1688"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"tooltipposition"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#tooltipposition"}},[t._v("#")]),t._v(" tooltipPosition")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("tooltipPosition")]),t._v("("),a("code",[t._v("useFinalPosition?")]),t._v("): "),a("RouterLink",{attrs:{to:"/api/interfaces/Point.html"}},[a("code",[t._v("Point")])])],1),t._v(" "),a("h4",{attrs:{id:"parameters-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-8"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("useFinalPosition?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-9"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/Point.html"}},[a("code",[t._v("Point")])])],1),t._v(" "),a("h4",{attrs:{id:"inherited-from-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-13"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[t._v("Element.tooltipPosition")]),t._v(" "),a("h4",{attrs:{id:"defined-in-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-15"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L10",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:10"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);