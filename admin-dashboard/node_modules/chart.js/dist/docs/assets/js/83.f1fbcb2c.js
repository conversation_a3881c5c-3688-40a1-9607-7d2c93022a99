(window.webpackJsonp=window.webpackJsonp||[]).push([[83],{414:function(t,e,r){"use strict";r.r(e);var a=r(6),n=Object(a.a)({},(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[r("h1",{attrs:{id:"interface-lineoptions"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-lineoptions"}},[t._v("#")]),t._v(" Interface: LineOptions")]),t._v(" "),r("h2",{attrs:{id:"hierarchy"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),r("ul",[r("li",[r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html"}},[r("code",[t._v("CommonElementOptions")])])],1),t._v(" "),r("p",[t._v("↳ "),r("strong",[r("code",[t._v("LineOptions")])])])])]),t._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),r("h3",{attrs:{id:"backgroundcolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#backgroundcolor"}},[t._v("#")]),t._v(" backgroundColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("backgroundColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])])],1),t._v(" "),r("h4",{attrs:{id:"inherited-from"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html"}},[t._v("CommonElementOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html#backgroundcolor"}},[t._v("backgroundColor")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1696",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1696"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"bordercapstyle"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#bordercapstyle"}},[t._v("#")]),t._v(" borderCapStyle")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderCapStyle")]),t._v(": "),r("code",[t._v("CanvasLineCap")])]),t._v(" "),r("p",[t._v("Line cap style. See MDN.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'butt'")]),t._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1779",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1779"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"bordercolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#bordercolor"}},[t._v("#")]),t._v(" borderColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])])],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html"}},[t._v("CommonElementOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html#bordercolor"}},[t._v("borderColor")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1695",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1695"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderdash"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderdash"}},[t._v("#")]),t._v(" borderDash")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderDash")]),t._v(": "),r("code",[t._v("number")]),t._v("[]")]),t._v(" "),r("p",[t._v("Line dash. See MDN.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" []")]),t._v(" "),r("h4",{attrs:{id:"defined-in-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1784",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1784"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderdashoffset"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderdashoffset"}},[t._v("#")]),t._v(" borderDashOffset")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderDashOffset")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Line dash offset. See MDN.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 0.0")]),t._v(" "),r("h4",{attrs:{id:"defined-in-5"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1789",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1789"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderjoinstyle"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderjoinstyle"}},[t._v("#")]),t._v(" borderJoinStyle")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderJoinStyle")]),t._v(": "),r("code",[t._v("CanvasLineJoin")])]),t._v(" "),r("p",[t._v("Line join style. See MDN.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'miter'")]),t._v(" "),r("h4",{attrs:{id:"defined-in-6"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1794",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1794"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderwidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderwidth"}},[t._v("#")]),t._v(" borderWidth")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderWidth")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("h4",{attrs:{id:"inherited-from-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-3"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html"}},[t._v("CommonElementOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html#borderwidth"}},[t._v("borderWidth")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-7"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1694",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1694"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"capbezierpoints"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#capbezierpoints"}},[t._v("#")]),t._v(" capBezierPoints")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("capBezierPoints")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[t._v("true to keep Bézier control inside the chart, false for no restriction.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" true")]),t._v(" "),r("h4",{attrs:{id:"defined-in-8"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1799",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1799"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"cubicinterpolationmode"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#cubicinterpolationmode"}},[t._v("#")]),t._v(" cubicInterpolationMode")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("cubicInterpolationMode")]),t._v(": "),r("code",[t._v('"default"')]),t._v(" | "),r("code",[t._v('"monotone"')])]),t._v(" "),r("p",[t._v("Interpolation mode to apply.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'default'")]),t._v(" "),r("h4",{attrs:{id:"defined-in-9"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1804",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1804"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"fill"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#fill"}},[t._v("#")]),t._v(" fill")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("fill")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#filltarget"}},[r("code",[t._v("FillTarget")])]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/ComplexFillTarget.html"}},[r("code",[t._v("ComplexFillTarget")])])],1),t._v(" "),r("p",[t._v("Both line and radar charts support a fill option on the dataset object which can be used to create area between two datasets or a dataset and a boundary, i.e. the scale origin, start or end")]),t._v(" "),r("h4",{attrs:{id:"defined-in-10"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1818",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1818"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"segment"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#segment"}},[t._v("#")]),t._v(" segment")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("segment")]),t._v(": "),r("code",[t._v("Object")])]),t._v(" "),r("h4",{attrs:{id:"type-declaration"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),r("table",[r("thead",[r("tr",[r("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),r("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),r("tbody",[r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("backgroundColor")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableLineSegmentContext.html"}},[r("code",[t._v("ScriptableLineSegmentContext")])]),t._v(">")],1)]),t._v(" "),r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("borderCapStyle")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("CanvasLineCap")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableLineSegmentContext.html"}},[r("code",[t._v("ScriptableLineSegmentContext")])]),t._v(">")],1)]),t._v(" "),r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("borderColor")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableLineSegmentContext.html"}},[r("code",[t._v("ScriptableLineSegmentContext")])]),t._v(">")],1)]),t._v(" "),r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("borderDash")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v("[], "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableLineSegmentContext.html"}},[r("code",[t._v("ScriptableLineSegmentContext")])]),t._v(">")],1)]),t._v(" "),r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("borderDashOffset")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableLineSegmentContext.html"}},[r("code",[t._v("ScriptableLineSegmentContext")])]),t._v(">")],1)]),t._v(" "),r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("borderJoinStyle")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("CanvasLineJoin")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableLineSegmentContext.html"}},[r("code",[t._v("ScriptableLineSegmentContext")])]),t._v(">")],1)]),t._v(" "),r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("borderWidth")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableLineSegmentContext.html"}},[r("code",[t._v("ScriptableLineSegmentContext")])]),t._v(">")],1)])])]),t._v(" "),r("h4",{attrs:{id:"defined-in-11"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1824",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1824"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"spangaps"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#spangaps"}},[t._v("#")]),t._v(" spanGaps")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("spanGaps")]),t._v(": "),r("code",[t._v("number")]),t._v(" | "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[t._v("If true, lines will be drawn between points with no or null data. If false, points with NaN data will create a break in the line. Can also be a number specifying the maximum gap length to span. The unit of the value depends on the scale used.")]),t._v(" "),r("h4",{attrs:{id:"defined-in-12"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1822",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1822"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"stepped"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#stepped"}},[t._v("#")]),t._v(" stepped")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("stepped")]),t._v(": "),r("code",[t._v("boolean")]),t._v(" | "),r("code",[t._v('"middle"')]),t._v(" | "),r("code",[t._v('"before"')]),t._v(" | "),r("code",[t._v('"after"')])]),t._v(" "),r("p",[t._v("true to show the line as a stepped line (tension will be ignored).")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" false")]),t._v(" "),r("h4",{attrs:{id:"defined-in-13"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1814",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1814"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"tension"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#tension"}},[t._v("#")]),t._v(" tension")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("tension")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Bézier curve tension (0 for no Bézier curves).")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),r("h4",{attrs:{id:"defined-in-14"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1809",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1809"),r("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=n.exports}}]);