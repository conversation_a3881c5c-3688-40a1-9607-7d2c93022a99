(window.webpackJsonp=window.webpackJsonp||[]).push([[65],{396:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-element-t-o"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-element-t-o"}},[t._v("#")]),t._v(" Interface: Element<T, O>")]),t._v(" "),a("h2",{attrs:{id:"type-parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("T")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("AnyObject")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("AnyObject")])])])])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"active"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#active"}},[t._v("#")]),t._v(" active")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("active")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L7",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:7"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"options"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#options"}},[t._v("#")]),t._v(" options")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("options")]),t._v(": "),a("code",[t._v("O")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L8",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:8"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"x"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#x"}},[t._v("#")]),t._v(" x")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("x")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L5",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:5"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"y"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#y"}},[t._v("#")]),t._v(" y")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("y")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L6",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:6"),a("OutboundLink")],1)]),t._v(" "),a("h2",{attrs:{id:"methods"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),a("h3",{attrs:{id:"getprops"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getprops"}},[t._v("#")]),t._v(" getProps")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getProps")]),t._v("<"),a("code",[t._v("P")]),t._v(">("),a("code",[t._v("props")]),t._v(", "),a("code",[t._v("final?")]),t._v("): "),a("code",[t._v("Pick")]),t._v("<"),a("code",[t._v("T")]),t._v(", "),a("code",[t._v("P")]),t._v("["),a("code",[t._v("number")]),t._v("]>")]),t._v(" "),a("h4",{attrs:{id:"type-parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-2"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("P")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends keyof "),a("code",[t._v("T")]),t._v("[]")])])])]),t._v(" "),a("h4",{attrs:{id:"parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("props")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("P")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("final?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("Pick")]),t._v("<"),a("code",[t._v("T")]),t._v(", "),a("code",[t._v("P")]),t._v("["),a("code",[t._v("number")]),t._v("]>")]),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L12",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:12"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"hasvalue"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hasvalue"}},[t._v("#")]),t._v(" hasValue")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("hasValue")]),t._v("(): "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"returns-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L11",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:11"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"tooltipposition"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#tooltipposition"}},[t._v("#")]),t._v(" tooltipPosition")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("tooltipPosition")]),t._v("("),a("code",[t._v("useFinalPosition?")]),t._v("): "),a("RouterLink",{attrs:{to:"/api/interfaces/Point.html"}},[a("code",[t._v("Point")])])],1),t._v(" "),a("h4",{attrs:{id:"parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-2"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("useFinalPosition?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-3"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/Point.html"}},[a("code",[t._v("Point")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L10",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:10"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);