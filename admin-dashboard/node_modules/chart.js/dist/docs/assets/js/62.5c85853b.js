(window.webpackJsonp=window.webpackJsonp||[]).push([[62],{393:function(t,e,r){"use strict";r.r(e);var a=r(6),n=Object(a.a)({},(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[r("h1",{attrs:{id:"interface-doughnutcontrollerchartoptions"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-doughnutcontrollerchartoptions"}},[t._v("#")]),t._v(" Interface: DoughnutControllerChartOptions")]),t._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),r("h3",{attrs:{id:"animation"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#animation"}},[t._v("#")]),t._v(" animation")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("animation")]),t._v(": "),r("code",[t._v("false")]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/DoughnutAnimationOptions.html"}},[r("code",[t._v("DoughnutAnimationOptions")])])],1),t._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L328",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:328"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"circumference"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#circumference"}},[t._v("#")]),t._v(" circumference")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("circumference")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Sweep to allow arcs to cover.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 360")]),t._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L296",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:296"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"cutout"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#cutout"}},[t._v("#")]),t._v(" cutout")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("cutout")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("string")]),t._v(" | "),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("The portion of the chart that is cut out of the middle. ('50%' - for doughnut, 0 - for pie)\nString ending with '%' means percentage, number means pixels.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 50")]),t._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L303",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:303"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"offset"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#offset"}},[t._v("#")]),t._v(" offset")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("offset")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Arc offset (in pixels).")]),t._v(" "),r("h4",{attrs:{id:"defined-in-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L308",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:308"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"radius"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#radius"}},[t._v("#")]),t._v(" radius")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("radius")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("string")]),t._v(" | "),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"doughnut"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("The outer radius of the chart. String ending with '%' means percentage of maximum radius, number means pixels.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" '100%'")]),t._v(" "),r("h4",{attrs:{id:"defined-in-5"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L314",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:314"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"rotation"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#rotation"}},[t._v("#")]),t._v(" rotation")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("rotation")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Starting angle to draw arcs from.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),r("h4",{attrs:{id:"defined-in-6"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L320",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:320"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"spacing"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#spacing"}},[t._v("#")]),t._v(" spacing")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("spacing")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Spacing between the arcs")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),r("h4",{attrs:{id:"defined-in-7"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L326",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:326"),r("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=n.exports}}]);