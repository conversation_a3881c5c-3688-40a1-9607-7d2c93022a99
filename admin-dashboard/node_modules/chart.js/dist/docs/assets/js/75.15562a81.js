(window.webpackJsonp=window.webpackJsonp||[]).push([[75],{406:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-layoutitem"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-layoutitem"}},[t._v("#")]),t._v(" Interface: LayoutItem")]),t._v(" "),a("h2",{attrs:{id:"hierarchy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),a("ul",[a("li",[a("p",[a("strong",[a("code",[t._v("LayoutItem")])])]),t._v(" "),a("p",[t._v("↳ "),a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])])],1),t._v(" "),a("p",[t._v("↳ "),a("RouterLink",{attrs:{to:"/api/interfaces/LegendElement.html"}},[a("code",[t._v("LegendElement")])])],1)])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"bottom"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#bottom"}},[t._v("#")]),t._v(" bottom")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("bottom")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Bottom edge of the item. Set by layout system and cannot be used in update")]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L41",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:41"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"fullsize"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#fullsize"}},[t._v("#")]),t._v(" fullSize")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("fullSize")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("if true, and the item is horizontal, then push vertical boxes down")]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L17",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:17"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"height"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#height"}},[t._v("#")]),t._v(" height")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("height")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Height of item. Must be valid after update()")]),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L25",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:25"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"left"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#left"}},[t._v("#")]),t._v(" left")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("left")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Left edge of the item. Set by layout system and cannot be used in update")]),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L29",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:29"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"position"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#position"}},[t._v("#")]),t._v(" position")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("position")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#layoutposition"}},[a("code",[t._v("LayoutPosition")])])],1),t._v(" "),a("p",[t._v("The position of the item in the chart layout. Possible values are")]),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L9",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:9"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"right"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#right"}},[t._v("#")]),t._v(" right")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("right")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Right edge of the item. Set by layout system and cannot be used in update")]),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L37",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:37"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"top"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#top"}},[t._v("#")]),t._v(" top")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("top")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Top edge of the item. Set by layout system and cannot be used in update")]),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L33",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:33"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"weight"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#weight"}},[t._v("#")]),t._v(" weight")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("weight")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("The weight used to sort the item. Higher weights are further away from the chart area")]),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L13",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:13"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"width"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#width"}},[t._v("#")]),t._v(" width")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("width")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Width of item. Must be valid after update()")]),t._v(" "),a("h4",{attrs:{id:"defined-in-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L21",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:21"),a("OutboundLink")],1)]),t._v(" "),a("h2",{attrs:{id:"methods"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),a("h3",{attrs:{id:"beforelayout"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforelayout"}},[t._v("#")]),t._v(" beforeLayout")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeLayout")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Called before the layout process starts")]),t._v(" "),a("h4",{attrs:{id:"returns"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L46",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:46"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"draw"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#draw"}},[t._v("#")]),t._v(" draw")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("draw")]),t._v("("),a("code",[t._v("chartArea")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Draws the element")]),t._v(" "),a("h4",{attrs:{id:"parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chartArea")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L50",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:50"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getpadding"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getpadding"}},[t._v("#")]),t._v(" getPadding")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("getPadding")]),t._v("(): "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1),t._v(" "),a("p",[t._v("Returns an object with padding on the edges")]),t._v(" "),a("h4",{attrs:{id:"returns-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-3"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L54",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:54"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"ishorizontal"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#ishorizontal"}},[t._v("#")]),t._v(" isHorizontal")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("isHorizontal")]),t._v("(): "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("returns true if the layout item is horizontal (ie. top or bottom)")]),t._v(" "),a("h4",{attrs:{id:"returns-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-4"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L58",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:58"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"update"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#update"}},[t._v("#")]),t._v(" update")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("update")]),t._v("("),a("code",[t._v("width")]),t._v(", "),a("code",[t._v("height")]),t._v(", "),a("code",[t._v("margins?")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Takes two parameters: width and height.")]),t._v(" "),a("h4",{attrs:{id:"parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-2"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("width")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("height")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("margins?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-5"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L64",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:64"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);