(window.webpackJsonp=window.webpackJsonp||[]).push([[169],{498:function(e,r,t){"use strict";t.r(r);var s=t(6),a=Object(s.a)({},(function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("ContentSlotsDistributor",{attrs:{"slot-key":e.$parent.slotKey}},[t("h1",{attrs:{id:"developers"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#developers"}},[e._v("#")]),e._v(" Developers")]),e._v(" "),t("p",[e._v("Developer features allow extending and enhancing Chart.js in many different ways.")]),e._v(" "),t("h2",{attrs:{id:"latest-resources"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#latest-resources"}},[e._v("#")]),e._v(" Latest resources")]),e._v(" "),t("p",[e._v("Latest documentation and samples, including unreleased features, are available at:")]),e._v(" "),t("ul",[t("li",[t("a",{attrs:{href:"https://www.chartjs.org/docs/master/",target:"_blank",rel:"noopener noreferrer"}},[e._v("https://www.chartjs.org/docs/master/"),t("OutboundLink")],1)]),e._v(" "),t("li",[t("a",{attrs:{href:"https://www.chartjs.org/samples/master/",target:"_blank",rel:"noopener noreferrer"}},[e._v("https://www.chartjs.org/samples/master/"),t("OutboundLink")],1)])]),e._v(" "),t("h2",{attrs:{id:"development-releases"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#development-releases"}},[e._v("#")]),e._v(" Development releases")]),e._v(" "),t("p",[e._v("Latest builds are available for testing at:")]),e._v(" "),t("ul",[t("li",[t("a",{attrs:{href:"https://www.chartjs.org/dist/master/chart.js",target:"_blank",rel:"noopener noreferrer"}},[e._v("https://www.chartjs.org/dist/master/chart.js"),t("OutboundLink")],1)]),e._v(" "),t("li",[t("a",{attrs:{href:"https://www.chartjs.org/dist/master/chart.min.js",target:"_blank",rel:"noopener noreferrer"}},[e._v("https://www.chartjs.org/dist/master/chart.min.js"),t("OutboundLink")],1)])]),e._v(" "),t("p",[t("strong",[e._v("WARNING: Development builds MUST not be used for production purposes or as replacement for CDN.")])]),e._v(" "),t("h2",{attrs:{id:"browser-support"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#browser-support"}},[e._v("#")]),e._v(" Browser support")]),e._v(" "),t("p",[e._v("All modern and up-to-date browsers are supported, including, but not limited to:")]),e._v(" "),t("p",[e._v("Chrome\nEdge\nFirefox\nSafari")]),e._v(" "),t("p",[e._v("As of version 3, we have dropped Internet Explorer 11 support.")]),e._v(" "),t("p",[e._v("Browser support for the canvas element is available in all modern & major mobile browsers. "),t("a",{attrs:{href:"https://caniuse.com/#feat=canvas",target:"_blank",rel:"noopener noreferrer"}},[e._v("CanIUse"),t("OutboundLink")],1)]),e._v(" "),t("p",[e._v("Run "),t("code",[e._v("npx browserslist")]),e._v(" at the root of the "),t("a",{attrs:{href:"https://github.com/chartjs/Chart.js",target:"_blank",rel:"noopener noreferrer"}},[e._v("codebase"),t("OutboundLink")],1),e._v(" to get a list of supported browsers.")]),e._v(" "),t("p",[e._v("Thanks to "),t("a",{attrs:{href:"https://browserstack.com",target:"_blank",rel:"noopener noreferrer"}},[e._v("BrowserStack"),t("OutboundLink")],1),e._v(" for allowing our team to test on thousands of browsers.")]),e._v(" "),t("h2",{attrs:{id:"previous-versions"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#previous-versions"}},[e._v("#")]),e._v(" Previous versions")]),e._v(" "),t("p",[e._v("To migrate from version 2 to version 3, please see "),t("a",{attrs:{href:"../getting-started/v3-migration"}},[e._v("the v3 migration guide")]),e._v(".")]),e._v(" "),t("p",[e._v("Version 3 has a largely different API than earlier versions.")]),e._v(" "),t("p",[e._v("Most earlier version options have current equivalents or are the same.")]),e._v(" "),t("p",[e._v("Please note - documentation for previous versions is available online or in the GitHub repo.")]),e._v(" "),t("ul",[t("li",[t("a",{attrs:{href:"https://www.chartjs.org/docs/2.9.4/",target:"_blank",rel:"noopener noreferrer"}},[e._v("2.9.4 Documentation"),t("OutboundLink")],1)]),e._v(" "),t("li",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/tree/v1.1.1/docs",target:"_blank",rel:"noopener noreferrer"}},[e._v("1.x Documentation"),t("OutboundLink")],1)])])])}),[],!1,null,null,null);r.default=a.exports}}]);