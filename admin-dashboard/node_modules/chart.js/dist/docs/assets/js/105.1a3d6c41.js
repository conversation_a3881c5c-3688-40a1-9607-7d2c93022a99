(window.webpackJsonp=window.webpackJsonp||[]).push([[105],{436:function(e,t,a){"use strict";a.r(t);var r=a(6),s=Object(r.a)({},(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ContentSlotsDistributor",{attrs:{"slot-key":e.$parent.slotKey}},[a("h1",{attrs:{id:"interface-scriptablecartesianscalecontext"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-scriptablecartesianscalecontext"}},[e._v("#")]),e._v(" Interface: ScriptableCartesianScaleContext")]),e._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[e._v("#")]),e._v(" Properties")]),e._v(" "),a("h3",{attrs:{id:"scale"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#scale"}},[e._v("#")]),e._v(" scale")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("scale")]),e._v(": keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html"}},[a("code",[e._v("CartesianScaleTypeRegistry")])])],1),e._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3051",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:3051"),a("OutboundLink")],1)]),e._v(" "),a("hr"),e._v(" "),a("h3",{attrs:{id:"type"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type"}},[e._v("#")]),e._v(" type")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("type")]),e._v(": "),a("code",[e._v("string")])]),e._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3052",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:3052"),a("OutboundLink")],1)])])}),[],!1,null,null,null);t.default=s.exports}}]);