(window.webpackJsonp=window.webpackJsonp||[]).push([[51],{382:function(r,e,t){"use strict";t.r(e);var o=t(6),a=Object(o.a)({},(function(){var r=this,e=r.$createElement,t=r._self._c||e;return t("ContentSlotsDistributor",{attrs:{"slot-key":r.$parent.slotKey}},[t("h1",{attrs:{id:"interface-commonhoveroptions"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#interface-commonhoveroptions"}},[r._v("#")]),r._v(" Interface: CommonHoverOptions")]),r._v(" "),t("h2",{attrs:{id:"hierarchy"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[r._v("#")]),r._v(" Hierarchy")]),r._v(" "),t("ul",[t("li",[t("p",[t("strong",[t("code",[r._v("CommonHoverOptions")])])]),r._v(" "),t("p",[r._v("↳ "),t("RouterLink",{attrs:{to:"/api/interfaces/ArcHoverOptions.html"}},[t("code",[r._v("ArcHoverOptions")])])],1),r._v(" "),t("p",[r._v("↳ "),t("RouterLink",{attrs:{to:"/api/interfaces/LineHoverOptions.html"}},[t("code",[r._v("LineHoverOptions")])])],1),r._v(" "),t("p",[r._v("↳ "),t("RouterLink",{attrs:{to:"/api/interfaces/PointHoverOptions.html"}},[t("code",[r._v("PointHoverOptions")])])],1),r._v(" "),t("p",[r._v("↳ "),t("RouterLink",{attrs:{to:"/api/interfaces/BarHoverOptions.html"}},[t("code",[r._v("BarHoverOptions")])])],1)])]),r._v(" "),t("h2",{attrs:{id:"properties"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[r._v("#")]),r._v(" Properties")]),r._v(" "),t("h3",{attrs:{id:"hoverbackgroundcolor"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#hoverbackgroundcolor"}},[r._v("#")]),r._v(" hoverBackgroundColor")]),r._v(" "),t("p",[r._v("• "),t("strong",[r._v("hoverBackgroundColor")]),r._v(": "),t("RouterLink",{attrs:{to:"/api/#color"}},[t("code",[r._v("Color")])])],1),r._v(" "),t("h4",{attrs:{id:"defined-in"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[r._v("#")]),r._v(" Defined in")]),r._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1702",target:"_blank",rel:"noopener noreferrer"}},[r._v("index.esm.d.ts:1702"),t("OutboundLink")],1)]),r._v(" "),t("hr"),r._v(" "),t("h3",{attrs:{id:"hoverbordercolor"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#hoverbordercolor"}},[r._v("#")]),r._v(" hoverBorderColor")]),r._v(" "),t("p",[r._v("• "),t("strong",[r._v("hoverBorderColor")]),r._v(": "),t("RouterLink",{attrs:{to:"/api/#color"}},[t("code",[r._v("Color")])])],1),r._v(" "),t("h4",{attrs:{id:"defined-in-2"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[r._v("#")]),r._v(" Defined in")]),r._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1701",target:"_blank",rel:"noopener noreferrer"}},[r._v("index.esm.d.ts:1701"),t("OutboundLink")],1)]),r._v(" "),t("hr"),r._v(" "),t("h3",{attrs:{id:"hoverborderwidth"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#hoverborderwidth"}},[r._v("#")]),r._v(" hoverBorderWidth")]),r._v(" "),t("p",[r._v("• "),t("strong",[r._v("hoverBorderWidth")]),r._v(": "),t("code",[r._v("number")])]),r._v(" "),t("h4",{attrs:{id:"defined-in-3"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[r._v("#")]),r._v(" Defined in")]),r._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1700",target:"_blank",rel:"noopener noreferrer"}},[r._v("index.esm.d.ts:1700"),t("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=a.exports}}]);