(window.webpackJsonp=window.webpackJsonp||[]).push([[60],{391:function(t,e,a){"use strict";a.r(e);var n=a(6),r=Object(n.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-doughnutanimationoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-doughnutanimationoptions"}},[t._v("#")]),t._v(" Interface: DoughnutAnimationOptions")]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"animaterotate"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#animaterotate"}},[t._v("#")]),t._v(" animateRotate")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("animateRotate")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("If true, the chart will animate in with a rotation animation. This property is in the options.animation object.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" true")]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L282",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:282"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"animatescale"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#animatescale"}},[t._v("#")]),t._v(" animateScale")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("animateScale")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("If true, will animate scaling the chart from the center outwards.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" false")]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L288",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:288"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=r.exports}}]);