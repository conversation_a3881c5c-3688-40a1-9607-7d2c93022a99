(window.webpackJsonp=window.webpackJsonp||[]).push([[97],{428:function(t,e,a){"use strict";a.r(e);var r=a(6),n=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-polarareacontrollerchartoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-polarareacontrollerchartoptions"}},[t._v("#")]),t._v(" Interface: PolarAreaControllerChartOptions")]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"animation"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#animation"}},[t._v("#")]),t._v(" animation")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("animation")]),t._v(": "),a("code",[t._v("false")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutAnimationOptions.html"}},[a("code",[t._v("DoughnutAnimationOptions")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L382",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:382"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"startangle"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#startangle"}},[t._v("#")]),t._v(" startAngle")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("startAngle")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Starting angle to draw arcs for the first item in a dataset. In degrees, 0 is at top.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L380",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:380"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=n.exports}}]);