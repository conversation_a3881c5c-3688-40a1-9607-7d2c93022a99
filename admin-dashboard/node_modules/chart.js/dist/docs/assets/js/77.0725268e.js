(window.webpackJsonp=window.webpackJsonp||[]).push([[77],{408:function(e,t,r){"use strict";r.r(t);var a=r(6),n=Object(a.a)({},(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("ContentSlotsDistributor",{attrs:{"slot-key":e.$parent.slotKey}},[r("h1",{attrs:{id:"interface-legenditem"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-legenditem"}},[e._v("#")]),e._v(" Interface: LegendItem")]),e._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[e._v("#")]),e._v(" Properties")]),e._v(" "),r("h3",{attrs:{id:"borderradius"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderradius"}},[e._v("#")]),e._v(" borderRadius")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("borderRadius")]),e._v(": "),r("code",[e._v("number")]),e._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/BorderRadius.html"}},[r("code",[e._v("BorderRadius")])])],1),e._v(" "),r("p",[e._v("Border radius of the legend box")]),e._v(" "),r("p",[r("strong",[r("code",[e._v("since")])]),e._v(" 3.1.0")]),e._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2174",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2174"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"datasetindex"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#datasetindex"}},[e._v("#")]),e._v(" datasetIndex")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("datasetIndex")]),e._v(": "),r("code",[e._v("number")])]),e._v(" "),r("p",[e._v("Index of the associated dataset")]),e._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2179",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2179"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"fillstyle"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#fillstyle"}},[e._v("#")]),e._v(" fillStyle")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("fillStyle")]),e._v(": "),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[e._v("Color")])])],1),e._v(" "),r("p",[e._v("Fill style of the legend box")]),e._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2189",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2189"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"fontcolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#fontcolor"}},[e._v("#")]),e._v(" fontColor")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("fontColor")]),e._v(": "),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[e._v("Color")])])],1),e._v(" "),r("p",[e._v("Font color for the text\nDefaults to LegendOptions.labels.color")]),e._v(" "),r("h4",{attrs:{id:"defined-in-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2195",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2195"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"hidden"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hidden"}},[e._v("#")]),e._v(" hidden")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("hidden")]),e._v(": "),r("code",[e._v("boolean")])]),e._v(" "),r("p",[e._v("If true, this item represents a hidden dataset. Label will be rendered with a strike-through effect")]),e._v(" "),r("h4",{attrs:{id:"defined-in-5"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2200",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2200"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"index"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#index"}},[e._v("#")]),e._v(" index")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("index")]),e._v(": "),r("code",[e._v("number")])]),e._v(" "),r("p",[e._v("Index the associated label in the labels array")]),e._v(" "),r("h4",{attrs:{id:"defined-in-6"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2184",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2184"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"linecap"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#linecap"}},[e._v("#")]),e._v(" lineCap")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("lineCap")]),e._v(": "),r("code",[e._v("CanvasLineCap")])]),e._v(" "),r("p",[e._v("For box border.")]),e._v(" "),r("p",[r("strong",[r("code",[e._v("see")])]),e._v(" https://developer.mozilla.org/en/docs/Web/API/CanvasRenderingContext2D/lineCap")]),e._v(" "),r("h4",{attrs:{id:"defined-in-7"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2206",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2206"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"linedash"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#linedash"}},[e._v("#")]),e._v(" lineDash")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("lineDash")]),e._v(": "),r("code",[e._v("number")]),e._v("[]")]),e._v(" "),r("p",[e._v("For box border.")]),e._v(" "),r("p",[r("strong",[r("code",[e._v("see")])]),e._v(" https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/setLineDash")]),e._v(" "),r("h4",{attrs:{id:"defined-in-8"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2212",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2212"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"linedashoffset"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#linedashoffset"}},[e._v("#")]),e._v(" lineDashOffset")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("lineDashOffset")]),e._v(": "),r("code",[e._v("number")])]),e._v(" "),r("p",[e._v("For box border.")]),e._v(" "),r("p",[r("strong",[r("code",[e._v("see")])]),e._v(" https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineDashOffset")]),e._v(" "),r("h4",{attrs:{id:"defined-in-9"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2218",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2218"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"linejoin"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#linejoin"}},[e._v("#")]),e._v(" lineJoin")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("lineJoin")]),e._v(": "),r("code",[e._v("CanvasLineJoin")])]),e._v(" "),r("p",[e._v("For box border.")]),e._v(" "),r("p",[r("strong",[r("code",[e._v("see")])]),e._v(" https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineJoin")]),e._v(" "),r("h4",{attrs:{id:"defined-in-10"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2224",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2224"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"linewidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#linewidth"}},[e._v("#")]),e._v(" lineWidth")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("lineWidth")]),e._v(": "),r("code",[e._v("number")])]),e._v(" "),r("p",[e._v("Width of box border")]),e._v(" "),r("h4",{attrs:{id:"defined-in-11"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2229",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2229"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"pointstyle"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointstyle"}},[e._v("#")]),e._v(" pointStyle")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("pointStyle")]),e._v(": "),r("RouterLink",{attrs:{to:"/api/#pointstyle"}},[r("code",[e._v("PointStyle")])])],1),e._v(" "),r("p",[e._v("Point style of the legend box (only used if usePointStyle is true)")]),e._v(" "),r("h4",{attrs:{id:"defined-in-12"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2239",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2239"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"rotation"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#rotation"}},[e._v("#")]),e._v(" rotation")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("rotation")]),e._v(": "),r("code",[e._v("number")])]),e._v(" "),r("p",[e._v("Rotation of the point in degrees (only used if usePointStyle is true)")]),e._v(" "),r("h4",{attrs:{id:"defined-in-13"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2244",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2244"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"strokestyle"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#strokestyle"}},[e._v("#")]),e._v(" strokeStyle")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("strokeStyle")]),e._v(": "),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[e._v("Color")])])],1),e._v(" "),r("p",[e._v("Stroke style of the legend box")]),e._v(" "),r("h4",{attrs:{id:"defined-in-14"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2234",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2234"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"text"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#text"}},[e._v("#")]),e._v(" text")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("text")]),e._v(": "),r("code",[e._v("string")])]),e._v(" "),r("p",[e._v("Label that will be displayed")]),e._v(" "),r("h4",{attrs:{id:"defined-in-15"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-15"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2168",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2168"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"textalign"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#textalign"}},[e._v("#")]),e._v(" textAlign")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("textAlign")]),e._v(": "),r("RouterLink",{attrs:{to:"/api/#textalign"}},[r("code",[e._v("TextAlign")])])],1),e._v(" "),r("p",[e._v("Text alignment")]),e._v(" "),r("h4",{attrs:{id:"defined-in-16"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-16"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2249",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2249"),r("OutboundLink")],1)])])}),[],!1,null,null,null);t.default=n.exports}}]);