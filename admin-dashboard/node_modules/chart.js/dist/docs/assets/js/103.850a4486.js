(window.webpackJsonp=window.webpackJsonp||[]).push([[103],{434:function(t,e,a){"use strict";a.r(e);var r=a(6),i=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-scaletyperegistry"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-scaletyperegistry"}},[t._v("#")]),t._v(" Interface: ScaleTypeRegistry")]),t._v(" "),a("h2",{attrs:{id:"hierarchy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),a("ul",[a("li",[a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html"}},[a("code",[t._v("CartesianScaleTypeRegistry")])])],1)]),t._v(" "),a("li",[a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/RadialScaleTypeRegistry.html"}},[a("code",[t._v("RadialScaleTypeRegistry")])])],1),t._v(" "),a("p",[t._v("↳ "),a("strong",[a("code",[t._v("ScaleTypeRegistry")])])])])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"category"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#category"}},[t._v("#")]),t._v(" category")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("category")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#categoryscaleoptions"}},[a("code",[t._v("CategoryScaleOptions")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"inherited-from"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html"}},[t._v("CartesianScaleTypeRegistry")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html#category"}},[t._v("category")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3491",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3491"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"linear"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#linear"}},[t._v("#")]),t._v(" linear")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("linear")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-2"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#linearscaleoptions"}},[a("code",[t._v("LinearScaleOptions")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html"}},[t._v("CartesianScaleTypeRegistry")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html#linear"}},[t._v("linear")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3485",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3485"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"logarithmic"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#logarithmic"}},[t._v("#")]),t._v(" logarithmic")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("logarithmic")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-3"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#logarithmicscaleoptions"}},[a("code",[t._v("LogarithmicScaleOptions")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-3"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html"}},[t._v("CartesianScaleTypeRegistry")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html#logarithmic"}},[t._v("logarithmic")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3488",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3488"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"radiallinear"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#radiallinear"}},[t._v("#")]),t._v(" radialLinear")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("radialLinear")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-4"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#radiallinearscaleoptions"}},[a("code",[t._v("RadialLinearScaleOptions")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-4"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/RadialScaleTypeRegistry.html"}},[t._v("RadialScaleTypeRegistry")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/RadialScaleTypeRegistry.html#radiallinear"}},[t._v("radialLinear")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3503",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3503"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"time"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#time"}},[t._v("#")]),t._v(" time")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("time")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-5"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#timescaleoptions"}},[a("code",[t._v("TimeScaleOptions")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-5"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html"}},[t._v("CartesianScaleTypeRegistry")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html#time"}},[t._v("time")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3494",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3494"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"timeseries"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#timeseries"}},[t._v("#")]),t._v(" timeseries")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("timeseries")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-6"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#timescaleoptions"}},[a("code",[t._v("TimeScaleOptions")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-6"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html"}},[t._v("CartesianScaleTypeRegistry")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html#timeseries"}},[t._v("timeseries")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3497",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3497"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=i.exports}}]);