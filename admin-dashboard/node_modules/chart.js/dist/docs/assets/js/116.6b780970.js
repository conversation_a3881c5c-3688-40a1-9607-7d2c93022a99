(window.webpackJsonp=window.webpackJsonp||[]).push([[116],{447:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-titleoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-titleoptions"}},[t._v("#")]),t._v(" Interface: TitleOptions")]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"align"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#align"}},[t._v("#")]),t._v(" align")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("align")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#align"}},[a("code",[t._v("Align")])])],1),t._v(" "),a("p",[t._v("Alignment of the title.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" 'center'")]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2410",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2410"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"color"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#color"}},[t._v("#")]),t._v(" color")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("color")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[t._v("Color")])])],1),t._v(" "),a("p",[t._v("Color of text")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("see")])]),t._v(" Defaults.color")]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2425",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2425"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"display"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#display"}},[t._v("#")]),t._v(" display")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("display")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("Is the title shown?")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" false")]),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2415",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2415"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"font"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#font"}},[t._v("#")]),t._v(" font")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("font")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptableandscriptableoptions"}},[a("code",[t._v("ScriptableAndScriptableOptions")])]),t._v("<"),a("code",[t._v("Partial")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/FontSpec.html"}},[a("code",[t._v("FontSpec")])]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableChartContext.html"}},[a("code",[t._v("ScriptableChartContext")])]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2426",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2426"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"fullsize"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#fullsize"}},[t._v("#")]),t._v(" fullSize")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("fullSize")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("Marks that this box should take the full width/height of the canvas (moving other boxes). If set to "),a("code",[t._v("false")]),t._v(", places the box above/beside the\nchart area")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" true")]),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2433",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2433"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"padding"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#padding"}},[t._v("#")]),t._v(" padding")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("padding")]),t._v(": "),a("code",[t._v("number")]),t._v(" | { "),a("code",[t._v("bottom")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("top")]),t._v(": "),a("code",[t._v("number")]),t._v("  }")]),t._v(" "),a("p",[t._v("Adds padding above and below the title text if a single number is specified. It is also possible to change top and bottom padding separately.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2437",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2437"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"position"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#position"}},[t._v("#")]),t._v(" position")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("position")]),t._v(": "),a("code",[t._v('"left"')]),t._v(" | "),a("code",[t._v('"right"')]),t._v(" | "),a("code",[t._v('"bottom"')]),t._v(" | "),a("code",[t._v('"top"')])]),t._v(" "),a("p",[t._v("Position of title")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" 'top'")]),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2420",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2420"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"text"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#text"}},[t._v("#")]),t._v(" text")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("text")]),t._v(": "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("p",[t._v("Title text to display. If specified as an array, text is rendered on multiple lines.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2441",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2441"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);