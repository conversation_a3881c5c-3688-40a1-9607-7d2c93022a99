(window.webpackJsonp=window.webpackJsonp||[]).push([[74],{405:function(e,t,r){"use strict";r.r(t);var a=r(6),s=Object(a.a)({},(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("ContentSlotsDistributor",{attrs:{"slot-key":e.$parent.slotKey}},[r("h1",{attrs:{id:"interface-interactionoptions"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-interactionoptions"}},[e._v("#")]),e._v(" Interface: InteractionOptions")]),e._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[e._v("#")]),e._v(" Properties")]),e._v(" "),r("h3",{attrs:{id:"axis"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#axis"}},[e._v("#")]),e._v(" axis")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("axis")]),e._v(": "),r("code",[e._v("string")])]),e._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L703",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:703"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"includeinvisible"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#includeinvisible"}},[e._v("#")]),e._v(" includeInvisible")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("includeInvisible")]),e._v(": "),r("code",[e._v("boolean")])]),e._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L705",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:705"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"intersect"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#intersect"}},[e._v("#")]),e._v(" intersect")]),e._v(" "),r("p",[e._v("• "),r("code",[e._v("Optional")]),e._v(" "),r("strong",[e._v("intersect")]),e._v(": "),r("code",[e._v("boolean")])]),e._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L704",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:704"),r("OutboundLink")],1)])])}),[],!1,null,null,null);t.default=s.exports}}]);