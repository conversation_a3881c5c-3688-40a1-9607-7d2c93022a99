(window.webpackJsonp=window.webpackJsonp||[]).push([[99],{430:function(t,e,r){"use strict";r.r(e);var a=r(6),i=Object(a.a)({},(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[r("h1",{attrs:{id:"interface-radarcontrollerdatasetoptions"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-radarcontrollerdatasetoptions"}},[t._v("#")]),t._v(" Interface: RadarControllerDatasetOptions")]),t._v(" "),r("h2",{attrs:{id:"hierarchy"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),r("ul",[r("li",[r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html"}},[r("code",[t._v("ControllerDatasetOptions")])])],1)]),t._v(" "),r("li",[r("p",[r("RouterLink",{attrs:{to:"/api/#scriptableandarrayoptions"}},[r("code",[t._v("ScriptableAndArrayOptions")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/interfaces/PointOptions.html"}},[r("code",[t._v("PointOptions")])]),t._v(" & "),r("RouterLink",{attrs:{to:"/api/interfaces/PointHoverOptions.html"}},[r("code",[t._v("PointHoverOptions")])]),t._v(" & "),r("RouterLink",{attrs:{to:"/api/interfaces/PointPrefixedOptions.html"}},[r("code",[t._v("PointPrefixedOptions")])]),t._v(" & "),r("RouterLink",{attrs:{to:"/api/interfaces/PointPrefixedHoverOptions.html"}},[r("code",[t._v("PointPrefixedHoverOptions")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1)]),t._v(" "),r("li",[r("p",[r("RouterLink",{attrs:{to:"/api/#scriptableandarrayoptions"}},[r("code",[t._v("ScriptableAndArrayOptions")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/interfaces/LineOptions.html"}},[r("code",[t._v("LineOptions")])]),t._v(" & "),r("RouterLink",{attrs:{to:"/api/interfaces/LineHoverOptions.html"}},[r("code",[t._v("LineHoverOptions")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1)]),t._v(" "),r("li",[r("p",[r("RouterLink",{attrs:{to:"/api/#animationoptions"}},[r("code",[t._v("AnimationOptions")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">")],1),t._v(" "),r("p",[t._v("↳ "),r("strong",[r("code",[t._v("RadarControllerDatasetOptions")])])])])]),t._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),r("h3",{attrs:{id:"animation"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#animation"}},[t._v("#")]),t._v(" animation")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("animation")]),t._v(": "),r("code",[t._v("false")]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/#animationspec"}},[r("code",[t._v("AnimationSpec")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v("> & { "),r("code",[t._v("onComplete?")]),t._v(": ("),r("code",[t._v("event")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/interfaces/AnimationEvent.html"}},[r("code",[t._v("AnimationEvent")])]),t._v(") => "),r("code",[t._v("void")]),t._v(" ; "),r("code",[t._v("onProgress?")]),t._v(": ("),r("code",[t._v("event")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/interfaces/AnimationEvent.html"}},[r("code",[t._v("AnimationEvent")])]),t._v(") => "),r("code",[t._v("void")]),t._v("  }")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("AnimationOptions.animation")]),t._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1640",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1640"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"animations"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#animations"}},[t._v("#")]),t._v(" animations")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("animations")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#animationsspec"}},[r("code",[t._v("AnimationsSpec")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("AnimationOptions.animations")]),t._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1650",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1650"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"backgroundcolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#backgroundcolor"}},[t._v("#")]),t._v(" backgroundColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("backgroundColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-3"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.backgroundColor")]),t._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1696",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1696"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"bordercapstyle"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#bordercapstyle"}},[t._v("#")]),t._v(" borderCapStyle")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderCapStyle")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("CanvasLineCap")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Line cap style. See MDN.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'butt'")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-4"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.borderCapStyle")]),t._v(" "),r("h4",{attrs:{id:"defined-in-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1779",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1779"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"bordercolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#bordercolor"}},[t._v("#")]),t._v(" borderColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-5"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-5"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.borderColor")]),t._v(" "),r("h4",{attrs:{id:"defined-in-5"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1695",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1695"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderdash"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderdash"}},[t._v("#")]),t._v(" borderDash")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderDash")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v("[], "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Line dash. See MDN.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" []")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-6"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-6"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.borderDash")]),t._v(" "),r("h4",{attrs:{id:"defined-in-6"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1784",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1784"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderdashoffset"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderdashoffset"}},[t._v("#")]),t._v(" borderDashOffset")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderDashOffset")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Line dash offset. See MDN.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 0.0")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-7"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-7"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.borderDashOffset")]),t._v(" "),r("h4",{attrs:{id:"defined-in-7"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1789",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1789"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderjoinstyle"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderjoinstyle"}},[t._v("#")]),t._v(" borderJoinStyle")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderJoinStyle")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("CanvasLineJoin")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Line join style. See MDN.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'miter'")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-8"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-8"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.borderJoinStyle")]),t._v(" "),r("h4",{attrs:{id:"defined-in-8"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1794",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1794"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderwidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderwidth"}},[t._v("#")]),t._v(" borderWidth")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderWidth")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-9"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-9"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.borderWidth")]),t._v(" "),r("h4",{attrs:{id:"defined-in-9"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1694",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1694"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"capbezierpoints"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#capbezierpoints"}},[t._v("#")]),t._v(" capBezierPoints")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("capBezierPoints")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("boolean")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("true to keep Bézier control inside the chart, false for no restriction.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" true")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-10"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-10"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.capBezierPoints")]),t._v(" "),r("h4",{attrs:{id:"defined-in-10"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1799",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1799"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"clip"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#clip"}},[t._v("#")]),t._v(" clip")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("clip")]),t._v(": "),r("code",[t._v("number")]),t._v(" | "),r("code",[t._v("false")]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[r("code",[t._v("ChartArea")])])],1),t._v(" "),r("p",[t._v("How to clip relative to chartArea. Positive value allows overflow, negative value clips that many pixels inside chartArea. 0 = clip at chartArea. Clipping can also be configured per side: clip: {left: 5, top: false, right: -2, bottom: 0}")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-11"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-11"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html"}},[t._v("ControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html#clip"}},[t._v("clip")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-11"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L70",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:70"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"cubicinterpolationmode"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#cubicinterpolationmode"}},[t._v("#")]),t._v(" cubicInterpolationMode")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("cubicInterpolationMode")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v('"default"')]),t._v(" | "),r("code",[t._v('"monotone"')]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Interpolation mode to apply.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'default'")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-12"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-12"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.cubicInterpolationMode")]),t._v(" "),r("h4",{attrs:{id:"defined-in-12"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1804",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1804"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"drawactiveelementsontop"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#drawactiveelementsontop"}},[t._v("#")]),t._v(" drawActiveElementsOnTop")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("drawActiveElementsOnTop")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("boolean")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Draw the active elements over the other elements of the dataset,")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" true")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-13"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-13"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.drawActiveElementsOnTop")]),t._v(" "),r("h4",{attrs:{id:"defined-in-13"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1904",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1904"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"fill"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#fill"}},[t._v("#")]),t._v(" fill")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("fill")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#filltarget"}},[r("code",[t._v("FillTarget")])]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/ComplexFillTarget.html"}},[r("code",[t._v("ComplexFillTarget")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Both line and radar charts support a fill option on the dataset object which can be used to create area between two datasets or a dataset and a boundary, i.e. the scale origin, start or end")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-14"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-14"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.fill")]),t._v(" "),r("h4",{attrs:{id:"defined-in-14"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1818",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1818"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hidden"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hidden"}},[t._v("#")]),t._v(" hidden")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hidden")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[t._v("Configures the visibility state of the dataset. Set it to true, to hide the dataset from the chart.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" false")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-15"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-15"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html"}},[t._v("ControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html#hidden"}},[t._v("hidden")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-15"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-15"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L88",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:88"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hitradius"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hitradius"}},[t._v("#")]),t._v(" hitRadius")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hitRadius")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Extra radius added to point radius for hit detection.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 1")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-16"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-16"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.hitRadius")]),t._v(" "),r("h4",{attrs:{id:"defined-in-16"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-16"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1889",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1889"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hoverbackgroundcolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hoverbackgroundcolor"}},[t._v("#")]),t._v(" hoverBackgroundColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hoverBackgroundColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-17"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-17"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.hoverBackgroundColor")]),t._v(" "),r("h4",{attrs:{id:"defined-in-17"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-17"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1702",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1702"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hoverbordercapstyle"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hoverbordercapstyle"}},[t._v("#")]),t._v(" hoverBorderCapStyle")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hoverBorderCapStyle")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("CanvasLineCap")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-18"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-18"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.hoverBorderCapStyle")]),t._v(" "),r("h4",{attrs:{id:"defined-in-18"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-18"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1836",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1836"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hoverbordercolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hoverbordercolor"}},[t._v("#")]),t._v(" hoverBorderColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hoverBorderColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-19"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-19"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.hoverBorderColor")]),t._v(" "),r("h4",{attrs:{id:"defined-in-19"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-19"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1701",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1701"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hoverborderdash"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hoverborderdash"}},[t._v("#")]),t._v(" hoverBorderDash")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hoverBorderDash")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v("[], "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-20"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-20"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.hoverBorderDash")]),t._v(" "),r("h4",{attrs:{id:"defined-in-20"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-20"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1837",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1837"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hoverborderdashoffset"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hoverborderdashoffset"}},[t._v("#")]),t._v(" hoverBorderDashOffset")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hoverBorderDashOffset")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-21"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-21"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.hoverBorderDashOffset")]),t._v(" "),r("h4",{attrs:{id:"defined-in-21"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-21"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1838",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1838"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hoverborderjoinstyle"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hoverborderjoinstyle"}},[t._v("#")]),t._v(" hoverBorderJoinStyle")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hoverBorderJoinStyle")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("CanvasLineJoin")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-22"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-22"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.hoverBorderJoinStyle")]),t._v(" "),r("h4",{attrs:{id:"defined-in-22"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-22"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1839",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1839"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hoverborderwidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hoverborderwidth"}},[t._v("#")]),t._v(" hoverBorderWidth")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hoverBorderWidth")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-23"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-23"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.hoverBorderWidth")]),t._v(" "),r("h4",{attrs:{id:"defined-in-23"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-23"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1700",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1700"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"hoverradius"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hoverradius"}},[t._v("#")]),t._v(" hoverRadius")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("hoverRadius")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Point radius when hovered.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 4")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-24"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-24"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.hoverRadius")]),t._v(" "),r("h4",{attrs:{id:"defined-in-24"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-24"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1912",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1912"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"indexaxis"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#indexaxis"}},[t._v("#")]),t._v(" indexAxis")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("indexAxis")]),t._v(": "),r("code",[t._v('"x"')]),t._v(" | "),r("code",[t._v('"y"')])]),t._v(" "),r("p",[t._v("The base axis of the chart. 'x' for vertical charts and 'y' for horizontal charts.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'x'")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-25"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-25"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html"}},[t._v("ControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html#indexaxis"}},[t._v("indexAxis")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-25"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-25"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L66",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:66"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"label"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#label"}},[t._v("#")]),t._v(" label")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("label")]),t._v(": "),r("code",[t._v("string")])]),t._v(" "),r("p",[t._v("The label for the dataset which appears in the legend and tooltips.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-26"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-26"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html"}},[t._v("ControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html#label"}},[t._v("label")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-26"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-26"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L74",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:74"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"normalized"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#normalized"}},[t._v("#")]),t._v(" normalized")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("normalized")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[t._v("Chart.js is fastest if you provide data with indices that are unique, sorted, and consistent across datasets and provide the normalized: true option to let Chart.js know that you have done so.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-27"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-27"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html"}},[t._v("ControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html#normalized"}},[t._v("normalized")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-27"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-27"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L58",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:58"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"order"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#order"}},[t._v("#")]),t._v(" order")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("order")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("The drawing order of dataset. Also affects order for stacking, tooltip and legend.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-28"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-28"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html"}},[t._v("ControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html#order"}},[t._v("order")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-28"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-28"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L78",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:78"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"parsing"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#parsing"}},[t._v("#")]),t._v(" parsing")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("parsing")]),t._v(": "),r("code",[t._v("false")]),t._v(" | { [key: string]: "),r("code",[t._v("string")]),t._v(";  }")]),t._v(" "),r("p",[t._v("How to parse the dataset. The parsing can be disabled by specifying parsing: false at chart options or dataset. If parsing is disabled, data must be sorted and in the formats the associated chart type and scales use internally.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-29"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-29"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html"}},[t._v("ControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html#parsing"}},[t._v("parsing")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-29"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-29"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L49",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:49"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointbackgroundcolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointbackgroundcolor"}},[t._v("#")]),t._v(" pointBackgroundColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointBackgroundColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("The fill color for points.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-30"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-30"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.pointBackgroundColor")]),t._v(" "),r("h4",{attrs:{id:"defined-in-30"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-30"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1919",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1919"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointbordercolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointbordercolor"}},[t._v("#")]),t._v(" pointBorderColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointBorderColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("The border color for points.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-31"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-31"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.pointBorderColor")]),t._v(" "),r("h4",{attrs:{id:"defined-in-31"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-31"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1923",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1923"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointborderwidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointborderwidth"}},[t._v("#")]),t._v(" pointBorderWidth")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointBorderWidth")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("The width of the point border in pixels.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-32"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-32"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.pointBorderWidth")]),t._v(" "),r("h4",{attrs:{id:"defined-in-32"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-32"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1927",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1927"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointhitradius"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointhitradius"}},[t._v("#")]),t._v(" pointHitRadius")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointHitRadius")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("The pixel size of the non-displayed point that reacts to mouse events.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-33"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-33"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.pointHitRadius")]),t._v(" "),r("h4",{attrs:{id:"defined-in-33"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-33"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1931",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1931"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointhoverbackgroundcolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointhoverbackgroundcolor"}},[t._v("#")]),t._v(" pointHoverBackgroundColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointHoverBackgroundColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Point background color when hovered.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-34"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-34"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.pointHoverBackgroundColor")]),t._v(" "),r("h4",{attrs:{id:"defined-in-34"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-34"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1950",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1950"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointhoverbordercolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointhoverbordercolor"}},[t._v("#")]),t._v(" pointHoverBorderColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointHoverBorderColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Point border color when hovered.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-35"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-35"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.pointHoverBorderColor")]),t._v(" "),r("h4",{attrs:{id:"defined-in-35"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-35"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1954",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1954"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointhoverborderwidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointhoverborderwidth"}},[t._v("#")]),t._v(" pointHoverBorderWidth")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointHoverBorderWidth")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Border width of point when hovered.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-36"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-36"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.pointHoverBorderWidth")]),t._v(" "),r("h4",{attrs:{id:"defined-in-36"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-36"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1958",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1958"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointhoverradius"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointhoverradius"}},[t._v("#")]),t._v(" pointHoverRadius")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointHoverRadius")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("The radius of the point when hovered.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-37"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-37"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.pointHoverRadius")]),t._v(" "),r("h4",{attrs:{id:"defined-in-37"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-37"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1962",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1962"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointradius"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointradius"}},[t._v("#")]),t._v(" pointRadius")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointRadius")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("The radius of the point shape. If set to 0, the point is not rendered.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-38"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-38"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.pointRadius")]),t._v(" "),r("h4",{attrs:{id:"defined-in-38"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-38"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1935",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1935"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointrotation"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointrotation"}},[t._v("#")]),t._v(" pointRotation")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointRotation")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("The rotation of the point in degrees.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-39"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-39"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.pointRotation")]),t._v(" "),r("h4",{attrs:{id:"defined-in-39"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-39"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1939",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1939"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointstyle"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointstyle"}},[t._v("#")]),t._v(" pointStyle")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointStyle")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#pointstyle"}},[r("code",[t._v("PointStyle")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Point style")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'circle;")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-40"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-40"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.pointStyle")]),t._v(" "),r("h4",{attrs:{id:"defined-in-40"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-40"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1894",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1894"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"radius"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#radius"}},[t._v("#")]),t._v(" radius")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("radius")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Point radius")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 3")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-41"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-41"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.radius")]),t._v(" "),r("h4",{attrs:{id:"defined-in-41"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-41"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1884",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1884"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"rotation"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#rotation"}},[t._v("#")]),t._v(" rotation")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("rotation")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Point rotation (in degrees).")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-42"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-42"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.rotation")]),t._v(" "),r("h4",{attrs:{id:"defined-in-42"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-42"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1899",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1899"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"segment"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#segment"}},[t._v("#")]),t._v(" segment")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("segment")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<{ "),r("code",[t._v("backgroundColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableLineSegmentContext.html"}},[r("code",[t._v("ScriptableLineSegmentContext")])]),t._v("> ; "),r("code",[t._v("borderCapStyle")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("CanvasLineCap")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableLineSegmentContext.html"}},[r("code",[t._v("ScriptableLineSegmentContext")])]),t._v("> ; "),r("code",[t._v("borderColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableLineSegmentContext.html"}},[r("code",[t._v("ScriptableLineSegmentContext")])]),t._v("> ; "),r("code",[t._v("borderDash")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v("[], "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableLineSegmentContext.html"}},[r("code",[t._v("ScriptableLineSegmentContext")])]),t._v("> ; "),r("code",[t._v("borderDashOffset")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableLineSegmentContext.html"}},[r("code",[t._v("ScriptableLineSegmentContext")])]),t._v("> ; "),r("code",[t._v("borderJoinStyle")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("CanvasLineJoin")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableLineSegmentContext.html"}},[r("code",[t._v("ScriptableLineSegmentContext")])]),t._v("> ; "),r("code",[t._v("borderWidth")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableLineSegmentContext.html"}},[r("code",[t._v("ScriptableLineSegmentContext")])]),t._v(">  }, "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-43"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-43"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.segment")]),t._v(" "),r("h4",{attrs:{id:"defined-in-43"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-43"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1824",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1824"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"showline"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#showline"}},[t._v("#")]),t._v(" showLine")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("showLine")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[t._v("If false, the line is not drawn for this dataset.")]),t._v(" "),r("h4",{attrs:{id:"defined-in-44"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-44"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L415",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:415"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"spangaps"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#spangaps"}},[t._v("#")]),t._v(" spanGaps")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("spanGaps")]),t._v(": "),r("code",[t._v("number")]),t._v(" | "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[t._v("If true, lines will be drawn between points with no or null data. If false, points with NaN data will create a break in the line. Can also be a number specifying the maximum gap length to span. The unit of the value depends on the scale used.")]),t._v(" "),r("h4",{attrs:{id:"overrides"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#overrides"}},[t._v("#")]),t._v(" Overrides")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.spanGaps")]),t._v(" "),r("h4",{attrs:{id:"defined-in-45"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-45"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L410",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:410"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"stack"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#stack"}},[t._v("#")]),t._v(" stack")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("stack")]),t._v(": "),r("code",[t._v("string")])]),t._v(" "),r("p",[t._v("The ID of the group to which this dataset belongs to (when stacked, each group will be a separate stack).")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-44"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-44"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html"}},[t._v("ControllerDatasetOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html#stack"}},[t._v("stack")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-46"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-46"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L83",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:83"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"stepped"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#stepped"}},[t._v("#")]),t._v(" stepped")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("stepped")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("boolean")]),t._v(" | "),r("code",[t._v('"middle"')]),t._v(" | "),r("code",[t._v('"before"')]),t._v(" | "),r("code",[t._v('"after"')]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("true to show the line as a stepped line (tension will be ignored).")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" false")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-45"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-45"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.stepped")]),t._v(" "),r("h4",{attrs:{id:"defined-in-47"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-47"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1814",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1814"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"tension"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#tension"}},[t._v("#")]),t._v(" tension")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("tension")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">>")],1),t._v(" "),r("p",[t._v("Bézier curve tension (0 for no Bézier curves).")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-46"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-46"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("ScriptableAndArrayOptions.tension")]),t._v(" "),r("h4",{attrs:{id:"defined-in-48"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-48"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1809",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1809"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"transitions"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#transitions"}},[t._v("#")]),t._v(" transitions")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("transitions")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#transitionsspec"}},[r("code",[t._v("TransitionsSpec")])]),t._v("<"),r("code",[t._v('"radar"')]),t._v(">")],1),t._v(" "),r("h4",{attrs:{id:"inherited-from-47"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-47"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[t._v("AnimationOptions.transitions")]),t._v(" "),r("h4",{attrs:{id:"defined-in-49"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-49"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1651",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1651"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"xaxisid"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#xaxisid"}},[t._v("#")]),t._v(" xAxisID")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("xAxisID")]),t._v(": "),r("code",[t._v("string")])]),t._v(" "),r("p",[t._v("The ID of the x axis to plot this dataset on.")]),t._v(" "),r("h4",{attrs:{id:"defined-in-50"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-50"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L401",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:401"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"yaxisid"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#yaxisid"}},[t._v("#")]),t._v(" yAxisID")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("yAxisID")]),t._v(": "),r("code",[t._v("string")])]),t._v(" "),r("p",[t._v("The ID of the y axis to plot this dataset on.")]),t._v(" "),r("h4",{attrs:{id:"defined-in-51"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-51"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L405",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:405"),r("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=i.exports}}]);