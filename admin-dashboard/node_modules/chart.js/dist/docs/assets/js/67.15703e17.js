(window.webpackJsonp=window.webpackJsonp||[]).push([[67],{398:function(t,e,a){"use strict";a.r(e);var r=a(6),i=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-extendedplugin-ttype-o-model"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-extendedplugin-ttype-o-model"}},[t._v("#")]),t._v(" Interface: ExtendedPlugin<TType, O, Model>")]),t._v(" "),a("h2",{attrs:{id:"type-parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("AnyObject")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Model")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/TooltipModel.html"}},[a("code",[t._v("TooltipModel")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)])])]),t._v(" "),a("h2",{attrs:{id:"hierarchy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),a("ul",[a("li",[a("p",[a("strong",[a("code",[t._v("ExtendedPlugin")])])]),t._v(" "),a("p",[t._v("↳ "),a("RouterLink",{attrs:{to:"/api/interfaces/Plugin.html"}},[a("code",[t._v("Plugin")])])],1)])]),t._v(" "),a("h2",{attrs:{id:"methods"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),a("h3",{attrs:{id:"aftertooltipdraw"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#aftertooltipdraw"}},[t._v("#")]),t._v(" afterTooltipDraw")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("afterTooltipDraw")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called after drawing the "),a("code",[t._v("tooltip")]),t._v(". Note that this hook will not\nbe called if the tooltip drawing has been previously cancelled.")]),t._v(" "),a("h4",{attrs:{id:"parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.tooltip")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Model")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The tooltip.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2601",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2601"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforetooltipdraw"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforetooltipdraw"}},[t._v("#")]),t._v(" beforeTooltipDraw")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeTooltipDraw")]),t._v("("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("args")]),t._v(", "),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("strong",[a("code",[t._v("desc")])]),t._v(" Called before drawing the "),a("code",[t._v("tooltip")]),t._v(". If any plugin returns "),a("code",[t._v("false")]),t._v(",\nthe tooltip drawing is cancelled until another "),a("code",[t._v("render")]),t._v(" is triggered.")]),t._v(" "),a("h4",{attrs:{id:"parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-2"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The chart instance.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The call arguments.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("args.tooltip")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Model")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The tooltip.")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The plugin options.")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("void")])]),t._v(" "),a("p",[a("code",[t._v("false")]),t._v(" to cancel the chart tooltip drawing.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2592",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2592"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=i.exports}}]);