(window.webpackJsonp=window.webpackJsonp||[]).push([[124],{453:function(t,e,r){"use strict";r.r(e);var a=r(6),s=Object(a.a)({},(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[r("h1",{attrs:{id:"interface-tooltippositionermap"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-tooltippositionermap"}},[t._v("#")]),t._v(" Interface: TooltipPositionerMap")]),t._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),r("h3",{attrs:{id:"average"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#average"}},[t._v("#")]),t._v(" average")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("average")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#tooltippositionerfunction"}},[r("code",[t._v("TooltipPositionerFunction")])]),t._v("<keyof "),r("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[r("code",[t._v("ChartTypeRegistry")])]),t._v(">")],1),t._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2542",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2542"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"nearest"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#nearest"}},[t._v("#")]),t._v(" nearest")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("nearest")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#tooltippositionerfunction"}},[r("code",[t._v("TooltipPositionerFunction")])]),t._v("<keyof "),r("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[r("code",[t._v("ChartTypeRegistry")])]),t._v(">")],1),t._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2543",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2543"),r("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);