(window.webpackJsonp=window.webpackJsonp||[]).push([[120],{575:function(e,r,t){"use strict";t.r(r);var a=t(6),s=Object(a.a)({},(function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("ContentSlotsDistributor",{attrs:{"slot-key":e.$parent.slotKey}},[t("h1",{attrs:{id:"interface-tooltiplabelstyle"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#interface-tooltiplabelstyle"}},[e._v("#")]),e._v(" Interface: TooltipLabelStyle")]),e._v(" "),t("h2",{attrs:{id:"properties"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[e._v("#")]),e._v(" Properties")]),e._v(" "),t("h3",{attrs:{id:"backgroundcolor"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#backgroundcolor"}},[e._v("#")]),e._v(" backgroundColor")]),e._v(" "),t("p",[e._v("• "),t("strong",[e._v("backgroundColor")]),e._v(": "),t("RouterLink",{attrs:{to:"/api/#color"}},[t("code",[e._v("Color")])])],1),e._v(" "),t("h4",{attrs:{id:"defined-in"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2448",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2448"),t("OutboundLink")],1)]),e._v(" "),t("hr"),e._v(" "),t("h3",{attrs:{id:"bordercolor"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bordercolor"}},[e._v("#")]),e._v(" borderColor")]),e._v(" "),t("p",[e._v("• "),t("strong",[e._v("borderColor")]),e._v(": "),t("RouterLink",{attrs:{to:"/api/#color"}},[t("code",[e._v("Color")])])],1),e._v(" "),t("h4",{attrs:{id:"defined-in-2"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2447",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2447"),t("OutboundLink")],1)]),e._v(" "),t("hr"),e._v(" "),t("h3",{attrs:{id:"borderdash"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#borderdash"}},[e._v("#")]),e._v(" borderDash")]),e._v(" "),t("p",[e._v("• "),t("code",[e._v("Optional")]),e._v(" "),t("strong",[e._v("borderDash")]),e._v(": ["),t("code",[e._v("number")]),e._v(", "),t("code",[e._v("number")]),e._v("]")]),e._v(" "),t("p",[e._v("Border dash")]),e._v(" "),t("p",[t("strong",[t("code",[e._v("since")])]),e._v(" 3.1.0")]),e._v(" "),t("h4",{attrs:{id:"defined-in-3"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2460",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2460"),t("OutboundLink")],1)]),e._v(" "),t("hr"),e._v(" "),t("h3",{attrs:{id:"borderdashoffset"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#borderdashoffset"}},[e._v("#")]),e._v(" borderDashOffset")]),e._v(" "),t("p",[e._v("• "),t("code",[e._v("Optional")]),e._v(" "),t("strong",[e._v("borderDashOffset")]),e._v(": "),t("code",[e._v("number")])]),e._v(" "),t("p",[e._v("Border dash offset")]),e._v(" "),t("p",[t("strong",[t("code",[e._v("since")])]),e._v(" 3.1.0")]),e._v(" "),t("h4",{attrs:{id:"defined-in-4"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2466",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2466"),t("OutboundLink")],1)]),e._v(" "),t("hr"),e._v(" "),t("h3",{attrs:{id:"borderradius"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#borderradius"}},[e._v("#")]),e._v(" borderRadius")]),e._v(" "),t("p",[e._v("• "),t("code",[e._v("Optional")]),e._v(" "),t("strong",[e._v("borderRadius")]),e._v(": "),t("code",[e._v("number")]),e._v(" | "),t("RouterLink",{attrs:{to:"/api/interfaces/BorderRadius.html"}},[t("code",[e._v("BorderRadius")])])],1),e._v(" "),t("p",[e._v("borderRadius")]),e._v(" "),t("p",[t("strong",[t("code",[e._v("since")])]),e._v(" 3.1.0")]),e._v(" "),t("h4",{attrs:{id:"defined-in-5"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2472",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2472"),t("OutboundLink")],1)]),e._v(" "),t("hr"),e._v(" "),t("h3",{attrs:{id:"borderwidth"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#borderwidth"}},[e._v("#")]),e._v(" borderWidth")]),e._v(" "),t("p",[e._v("• "),t("code",[e._v("Optional")]),e._v(" "),t("strong",[e._v("borderWidth")]),e._v(": "),t("code",[e._v("number")])]),e._v(" "),t("p",[e._v("Width of border line")]),e._v(" "),t("p",[t("strong",[t("code",[e._v("since")])]),e._v(" 3.1.0")]),e._v(" "),t("h4",{attrs:{id:"defined-in-6"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),t("p",[t("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2454",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2454"),t("OutboundLink")],1)])])}),[],!1,null,null,null);r.default=s.exports}}]);