(window.webpackJsonp=window.webpackJsonp||[]).push([[50],{381:function(t,r,e){"use strict";e.r(r);var o=e(6),a=Object(o.a)({},(function(){var t=this,r=t.$createElement,e=t._self._c||r;return e("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[e("h1",{attrs:{id:"interface-commonelementoptions"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#interface-commonelementoptions"}},[t._v("#")]),t._v(" Interface: CommonElementOptions")]),t._v(" "),e("h2",{attrs:{id:"hierarchy"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),e("ul",[e("li",[e("p",[e("strong",[e("code",[t._v("CommonElementOptions")])])]),t._v(" "),e("p",[t._v("↳ "),e("RouterLink",{attrs:{to:"/api/interfaces/ArcOptions.html"}},[e("code",[t._v("ArcOptions")])])],1),t._v(" "),e("p",[t._v("↳ "),e("RouterLink",{attrs:{to:"/api/interfaces/LineOptions.html"}},[e("code",[t._v("LineOptions")])])],1),t._v(" "),e("p",[t._v("↳ "),e("RouterLink",{attrs:{to:"/api/interfaces/PointOptions.html"}},[e("code",[t._v("PointOptions")])])],1)])]),t._v(" "),e("h2",{attrs:{id:"properties"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),e("h3",{attrs:{id:"backgroundcolor"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#backgroundcolor"}},[t._v("#")]),t._v(" backgroundColor")]),t._v(" "),e("p",[t._v("• "),e("strong",[t._v("backgroundColor")]),t._v(": "),e("RouterLink",{attrs:{to:"/api/#color"}},[e("code",[t._v("Color")])])],1),t._v(" "),e("h4",{attrs:{id:"defined-in"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),e("p",[e("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1696",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1696"),e("OutboundLink")],1)]),t._v(" "),e("hr"),t._v(" "),e("h3",{attrs:{id:"bordercolor"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#bordercolor"}},[t._v("#")]),t._v(" borderColor")]),t._v(" "),e("p",[t._v("• "),e("strong",[t._v("borderColor")]),t._v(": "),e("RouterLink",{attrs:{to:"/api/#color"}},[e("code",[t._v("Color")])])],1),t._v(" "),e("h4",{attrs:{id:"defined-in-2"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),e("p",[e("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1695",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1695"),e("OutboundLink")],1)]),t._v(" "),e("hr"),t._v(" "),e("h3",{attrs:{id:"borderwidth"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#borderwidth"}},[t._v("#")]),t._v(" borderWidth")]),t._v(" "),e("p",[t._v("• "),e("strong",[t._v("borderWidth")]),t._v(": "),e("code",[t._v("number")])]),t._v(" "),e("h4",{attrs:{id:"defined-in-3"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),e("p",[e("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1694",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1694"),e("OutboundLink")],1)])])}),[],!1,null,null,null);r.default=a.exports}}]);