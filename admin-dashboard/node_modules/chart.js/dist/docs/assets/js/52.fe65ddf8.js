(window.webpackJsonp=window.webpackJsonp||[]).push([[52],{383:function(e,t,a){"use strict";a.r(t);var r=a(6),s=Object(r.a)({},(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ContentSlotsDistributor",{attrs:{"slot-key":e.$parent.slotKey}},[a("h1",{attrs:{id:"interface-complexfilltarget"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-complexfilltarget"}},[e._v("#")]),e._v(" Interface: ComplexFillTarget")]),e._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[e._v("#")]),e._v(" Properties")]),e._v(" "),a("h3",{attrs:{id:"above"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#above"}},[e._v("#")]),e._v(" above")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("above")]),e._v(": "),a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[e._v("Color")])])],1),e._v(" "),a("p",[e._v("If no color is set, the default color will be the background color of the chart.")]),e._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2148",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2148"),a("OutboundLink")],1)]),e._v(" "),a("hr"),e._v(" "),a("h3",{attrs:{id:"below"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#below"}},[e._v("#")]),e._v(" below")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("below")]),e._v(": "),a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[e._v("Color")])])],1),e._v(" "),a("p",[e._v("Same as the above.")]),e._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2152",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2152"),a("OutboundLink")],1)]),e._v(" "),a("hr"),e._v(" "),a("h3",{attrs:{id:"target"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#target"}},[e._v("#")]),e._v(" target")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("target")]),e._v(": "),a("RouterLink",{attrs:{to:"/api/#filltarget"}},[a("code",[e._v("FillTarget")])])],1),e._v(" "),a("p",[e._v("The accepted values are the same as the filling mode values, so you may use absolute and relative dataset indexes and/or boundaries.")]),e._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2144",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2144"),a("OutboundLink")],1)])])}),[],!1,null,null,null);t.default=s.exports}}]);