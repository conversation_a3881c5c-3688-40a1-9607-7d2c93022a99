(window.webpackJsonp=window.webpackJsonp||[]).push([[33],{364:function(e,t,r){"use strict";r.r(t);var a=r(6),s=Object(a.a)({},(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("ContentSlotsDistributor",{attrs:{"slot-key":e.$parent.slotKey}},[r("h1",{attrs:{id:"interface-baroptions"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-baroptions"}},[e._v("#")]),e._v(" Interface: BarOptions")]),e._v(" "),r("h2",{attrs:{id:"hierarchy"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[e._v("#")]),e._v(" Hierarchy")]),e._v(" "),r("ul",[r("li",[r("p",[r("code",[e._v("Omit")]),e._v("<"),r("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html"}},[r("code",[e._v("CommonElementOptions")])]),e._v(", "),r("code",[e._v('"borderWidth"')]),e._v(">")],1),e._v(" "),r("p",[e._v("↳ "),r("strong",[r("code",[e._v("BarOptions")])])])])]),e._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[e._v("#")]),e._v(" Properties")]),e._v(" "),r("h3",{attrs:{id:"backgroundcolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#backgroundcolor"}},[e._v("#")]),e._v(" backgroundColor")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("backgroundColor")]),e._v(": "),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[e._v("Color")])])],1),e._v(" "),r("h4",{attrs:{id:"inherited-from"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[e._v("#")]),e._v(" Inherited from")]),e._v(" "),r("p",[e._v("Omit.backgroundColor")]),e._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1696",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1696"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"base"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#base"}},[e._v("#")]),e._v(" base")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("base")]),e._v(": "),r("code",[e._v("number")])]),e._v(" "),r("p",[e._v("The base value for the bar in data units along the value axis.")]),e._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1990",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1990"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"bordercolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#bordercolor"}},[e._v("#")]),e._v(" borderColor")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("borderColor")]),e._v(": "),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[e._v("Color")])])],1),e._v(" "),r("h4",{attrs:{id:"inherited-from-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[e._v("#")]),e._v(" Inherited from")]),e._v(" "),r("p",[e._v("Omit.borderColor")]),e._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1695",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1695"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"borderradius"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderradius"}},[e._v("#")]),e._v(" borderRadius")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("borderRadius")]),e._v(": "),r("code",[e._v("number")]),e._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/BorderRadius.html"}},[r("code",[e._v("BorderRadius")])])],1),e._v(" "),r("p",[e._v("Border radius")]),e._v(" "),r("p",[r("strong",[r("code",[e._v("default")])]),e._v(" 0")]),e._v(" "),r("h4",{attrs:{id:"defined-in-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2002",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2002"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"borderskipped"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderskipped"}},[e._v("#")]),e._v(" borderSkipped")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("borderSkipped")]),e._v(": "),r("code",[e._v("boolean")]),e._v(" | "),r("code",[e._v('"start"')]),e._v(" | "),r("code",[e._v('"end"')]),e._v(" | "),r("code",[e._v('"left"')]),e._v(" | "),r("code",[e._v('"right"')]),e._v(" | "),r("code",[e._v('"bottom"')]),e._v(" | "),r("code",[e._v('"top"')]),e._v(" | "),r("code",[e._v('"middle"')])]),e._v(" "),r("p",[e._v("Skipped (excluded) border: 'start', 'end', 'left',  'right', 'bottom', 'top', 'middle', false (none) or true (all).")]),e._v(" "),r("p",[r("strong",[r("code",[e._v("default")])]),e._v(" 'start'")]),e._v(" "),r("h4",{attrs:{id:"defined-in-5"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1996",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1996"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"borderwidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderwidth"}},[e._v("#")]),e._v(" borderWidth")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("borderWidth")]),e._v(": "),r("code",[e._v("number")]),e._v(" | { "),r("code",[e._v("bottom?")]),e._v(": "),r("code",[e._v("number")]),e._v(" ; "),r("code",[e._v("left?")]),e._v(": "),r("code",[e._v("number")]),e._v(" ; "),r("code",[e._v("right?")]),e._v(": "),r("code",[e._v("number")]),e._v(" ; "),r("code",[e._v("top?")]),e._v(": "),r("code",[e._v("number")]),e._v("  }")]),e._v(" "),r("p",[e._v("Width of the border, number for all sides, object to specify width for each side specifically")]),e._v(" "),r("p",[r("strong",[r("code",[e._v("default")])]),e._v(" 0")]),e._v(" "),r("h4",{attrs:{id:"defined-in-6"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2015",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2015"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"inflateamount"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inflateamount"}},[e._v("#")]),e._v(" inflateAmount")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("inflateAmount")]),e._v(": "),r("code",[e._v("number")]),e._v(" | "),r("code",[e._v('"auto"')])]),e._v(" "),r("p",[e._v("Amount to inflate the rectangle(s). This can be used to hide artifacts between bars.\nUnit is pixels. 'auto' translates to 0.33 pixels when barPercentage * categoryPercentage is 1, else 0.")]),e._v(" "),r("p",[r("strong",[r("code",[e._v("default")])]),e._v(" 'auto'")]),e._v(" "),r("h4",{attrs:{id:"defined-in-7"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2009",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:2009"),r("OutboundLink")],1)])])}),[],!1,null,null,null);t.default=s.exports}}]);