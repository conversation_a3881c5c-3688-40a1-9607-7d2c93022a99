(window.webpackJsonp=window.webpackJsonp||[]).push([[175],{504:function(t,e,s){"use strict";s.r(e);var a=s(6),n=Object(a.a)({},(function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[s("h1",{attrs:{id:"fonts"}},[s("a",{staticClass:"header-anchor",attrs:{href:"#fonts"}},[t._v("#")]),t._v(" Fonts")]),t._v(" "),s("p",[t._v("There are special global settings that can change all of the fonts on the chart. These options are in "),s("code",[t._v("Chart.defaults.font")]),t._v(". The global font settings only apply when more specific options are not included in the config.")]),t._v(" "),s("p",[t._v("For example, in this chart the text will have a font size of 16px except for the labels in the legend.")]),t._v(" "),s("div",{staticClass:"language-javascript extra-class"},[s("pre",{pre:!0,attrs:{class:"language-javascript"}},[s("code",[t._v("Chart"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(".")]),t._v("defaults"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(".")]),t._v("font"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(".")]),t._v("size "),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("=")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token number"}},[t._v("16")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token keyword"}},[t._v("let")]),t._v(" chart "),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v("=")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token keyword"}},[t._v("new")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token class-name"}},[t._v("Chart")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("(")]),t._v("ctx"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(",")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n    "),s("span",{pre:!0,attrs:{class:"token literal-property property"}},[t._v("type")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v(":")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token string"}},[t._v("'line'")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(",")]),t._v("\n    "),s("span",{pre:!0,attrs:{class:"token literal-property property"}},[t._v("data")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v(":")]),t._v(" data"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(",")]),t._v("\n    "),s("span",{pre:!0,attrs:{class:"token literal-property property"}},[t._v("options")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v(":")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n        "),s("span",{pre:!0,attrs:{class:"token literal-property property"}},[t._v("plugins")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v(":")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n            "),s("span",{pre:!0,attrs:{class:"token literal-property property"}},[t._v("legend")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v(":")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n                "),s("span",{pre:!0,attrs:{class:"token literal-property property"}},[t._v("labels")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v(":")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n                    "),s("span",{pre:!0,attrs:{class:"token comment"}},[t._v("// This more specific font property overrides the global property")]),t._v("\n                    "),s("span",{pre:!0,attrs:{class:"token literal-property property"}},[t._v("font")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v(":")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("{")]),t._v("\n                        "),s("span",{pre:!0,attrs:{class:"token literal-property property"}},[t._v("size")]),s("span",{pre:!0,attrs:{class:"token operator"}},[t._v(":")]),t._v(" "),s("span",{pre:!0,attrs:{class:"token number"}},[t._v("14")]),t._v("\n                    "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n                "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n            "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n        "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n    "),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),t._v("\n"),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v("}")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(")")]),s("span",{pre:!0,attrs:{class:"token punctuation"}},[t._v(";")]),t._v("\n")])])]),s("table",[s("thead",[s("tr",[s("th",[t._v("Name")]),t._v(" "),s("th",[t._v("Type")]),t._v(" "),s("th",[t._v("Default")]),t._v(" "),s("th",[t._v("Description")])])]),t._v(" "),s("tbody",[s("tr",[s("td",[s("code",[t._v("family")])]),t._v(" "),s("td",[s("code",[t._v("string")])]),t._v(" "),s("td",[s("code",[t._v("\"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif\"")])]),t._v(" "),s("td",[t._v("Default font family for all text, follows CSS font-family options.")])]),t._v(" "),s("tr",[s("td",[s("code",[t._v("size")])]),t._v(" "),s("td",[s("code",[t._v("number")])]),t._v(" "),s("td",[s("code",[t._v("12")])]),t._v(" "),s("td",[t._v("Default font size (in px) for text. Does not apply to radialLinear scale point labels.")])]),t._v(" "),s("tr",[s("td",[s("code",[t._v("style")])]),t._v(" "),s("td",[s("code",[t._v("string")])]),t._v(" "),s("td",[s("code",[t._v("'normal'")])]),t._v(" "),s("td",[t._v("Default font style. Does not apply to tooltip title or footer. Does not apply to chart title. Follows CSS font-style options (i.e. normal, italic, oblique, initial, inherit).")])]),t._v(" "),s("tr",[s("td",[s("code",[t._v("weight")])]),t._v(" "),s("td",[s("code",[t._v("string")])]),t._v(" "),s("td",[s("code",[t._v("undefined")])]),t._v(" "),s("td",[t._v("Default font weight (boldness). (see "),s("a",{attrs:{href:"https://developer.mozilla.org/en-US/docs/Web/CSS/font-weight",target:"_blank",rel:"noopener noreferrer"}},[t._v("MDN"),s("OutboundLink")],1),t._v(").")])]),t._v(" "),s("tr",[s("td",[s("code",[t._v("lineHeight")])]),t._v(" "),s("td",[s("code",[t._v("number")]),t._v("|"),s("code",[t._v("string")])]),t._v(" "),s("td",[s("code",[t._v("1.2")])]),t._v(" "),s("td",[t._v("Height of an individual line of text (see "),s("a",{attrs:{href:"https://developer.mozilla.org/en-US/docs/Web/CSS/line-height",target:"_blank",rel:"noopener noreferrer"}},[t._v("MDN"),s("OutboundLink")],1),t._v(").")])])])]),t._v(" "),s("h2",{attrs:{id:"missing-fonts"}},[s("a",{staticClass:"header-anchor",attrs:{href:"#missing-fonts"}},[t._v("#")]),t._v(" Missing Fonts")]),t._v(" "),s("p",[t._v("If a font is specified for a chart that does exist on the system, the browser will not apply the font when it is set. If you notice odd fonts appearing in your charts, check that the font you are applying exists on your system. See "),s("a",{attrs:{href:"https://github.com/chartjs/Chart.js/issues/3318",target:"_blank",rel:"noopener noreferrer"}},[t._v("issue 3318"),s("OutboundLink")],1),t._v(" for more details.")]),t._v(" "),s("h2",{attrs:{id:"loading-fonts"}},[s("a",{staticClass:"header-anchor",attrs:{href:"#loading-fonts"}},[t._v("#")]),t._v(" Loading Fonts")]),t._v(" "),s("p",[t._v("If a font is not cached and needs to be loaded, charts that use the font will need to be updated once the font is loaded. This can be accomplished using the "),s("a",{attrs:{href:"https://developer.mozilla.org/en-US/docs/Web/API/CSS_Font_Loading_API",target:"_blank",rel:"noopener noreferrer"}},[t._v("Font Loading APIs"),s("OutboundLink")],1),t._v(". See "),s("a",{attrs:{href:"https://github.com/chartjs/Chart.js/issues/8020",target:"_blank",rel:"noopener noreferrer"}},[t._v("issue 8020"),s("OutboundLink")],1),t._v(" for more details.")])])}),[],!1,null,null,null);e.default=n.exports}}]);