(window.webpackJsonp=window.webpackJsonp||[]).push([[106],{437:function(t,e,r){"use strict";r.r(e);var a=r(6),s=Object(a.a)({},(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[r("h1",{attrs:{id:"interface-scriptablechartcontext"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-scriptablechartcontext"}},[t._v("#")]),t._v(" Interface: ScriptableChartContext")]),t._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),r("h3",{attrs:{id:"chart"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#chart"}},[t._v("#")]),t._v(" chart")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("chart")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[r("code",[t._v("Chart")])]),t._v("<keyof "),r("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[r("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),r("code",[t._v("number")]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[r("code",[t._v("ScatterDataPoint")])]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[r("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),r("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3056",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3056"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"type"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#type"}},[t._v("#")]),t._v(" type")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("type")]),t._v(": "),r("code",[t._v("string")])]),t._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3057",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3057"),r("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);