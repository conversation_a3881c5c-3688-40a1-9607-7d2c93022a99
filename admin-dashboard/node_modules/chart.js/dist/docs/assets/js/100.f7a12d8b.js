(window.webpackJsonp=window.webpackJsonp||[]).push([[100],{431:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-radiallinearscale-o"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-radiallinearscale-o"}},[t._v("#")]),t._v(" Interface: RadialLinearScale<O>")]),t._v(" "),a("h2",{attrs:{id:"type-parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#radiallinearscaleoptions"}},[a("code",[t._v("RadialLinearScaleOptions")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#radiallinearscaleoptions"}},[a("code",[t._v("RadialLinearScaleOptions")])])],1)])])]),t._v(" "),a("h2",{attrs:{id:"hierarchy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),a("ul",[a("li",[a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("code",[t._v("O")]),t._v(">")],1),t._v(" "),a("p",[t._v("↳ "),a("strong",[a("code",[t._v("RadialLinearScale")])])])])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"active"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#active"}},[t._v("#")]),t._v(" active")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("active")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#active"}},[t._v("active")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L7",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:7"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"axis"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#axis"}},[t._v("#")]),t._v(" axis")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("axis")]),t._v(": "),a("code",[t._v("string")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#axis"}},[t._v("axis")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1239",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1239"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"bottom"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#bottom"}},[t._v("#")]),t._v(" bottom")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("bottom")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Bottom edge of the item. Set by layout system and cannot be used in update")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-3"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#bottom"}},[t._v("bottom")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L41",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:41"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"chart"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#chart"}},[t._v("#")]),t._v(" chart")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("chart")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"inherited-from-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-4"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#chart"}},[t._v("chart")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1229",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1229"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"ctx"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#ctx"}},[t._v("#")]),t._v(" ctx")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("ctx")]),t._v(": "),a("code",[t._v("CanvasRenderingContext2D")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-5"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#ctx"}},[t._v("ctx")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1228",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1228"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"fullsize"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#fullsize"}},[t._v("#")]),t._v(" fullSize")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("fullSize")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("if true, and the item is horizontal, then push vertical boxes down")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-6"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#fullsize"}},[t._v("fullSize")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L17",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:17"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"height"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#height"}},[t._v("#")]),t._v(" height")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("height")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Height of item. Must be valid after update()")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-7"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#height"}},[t._v("height")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L25",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:25"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"id"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#id"}},[t._v("#")]),t._v(" id")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("id")]),t._v(": "),a("code",[t._v("string")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-8"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#id"}},[t._v("id")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1226",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1226"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"labelrotation"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#labelrotation"}},[t._v("#")]),t._v(" labelRotation")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("labelRotation")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-9"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#labelrotation"}},[t._v("labelRotation")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1240",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1240"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"left"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#left"}},[t._v("#")]),t._v(" left")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("left")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Left edge of the item. Set by layout system and cannot be used in update")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-10"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#left"}},[t._v("left")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L29",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:29"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"max"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#max"}},[t._v("#")]),t._v(" max")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("max")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-11"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#max"}},[t._v("max")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1242",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1242"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"maxheight"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#maxheight"}},[t._v("#")]),t._v(" maxHeight")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("maxHeight")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-12"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#maxheight"}},[t._v("maxHeight")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1232",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1232"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"maxwidth"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#maxwidth"}},[t._v("#")]),t._v(" maxWidth")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("maxWidth")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-13"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#maxwidth"}},[t._v("maxWidth")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1231",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1231"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"min"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#min"}},[t._v("#")]),t._v(" min")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("min")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-14"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#min"}},[t._v("min")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1241",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1241"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"options"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#options"}},[t._v("#")]),t._v(" options")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("options")]),t._v(": "),a("code",[t._v("O")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-15"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#options"}},[t._v("options")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-15"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L8",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:8"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"paddingbottom"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#paddingbottom"}},[t._v("#")]),t._v(" paddingBottom")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("paddingBottom")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-16"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#paddingbottom"}},[t._v("paddingBottom")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-16"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1235",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1235"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"paddingleft"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#paddingleft"}},[t._v("#")]),t._v(" paddingLeft")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("paddingLeft")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-17"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#paddingleft"}},[t._v("paddingLeft")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-17"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1236",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1236"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"paddingright"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#paddingright"}},[t._v("#")]),t._v(" paddingRight")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("paddingRight")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-18"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#paddingright"}},[t._v("paddingRight")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-18"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1237",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1237"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"paddingtop"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#paddingtop"}},[t._v("#")]),t._v(" paddingTop")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("paddingTop")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-19"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-19"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#paddingtop"}},[t._v("paddingTop")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-19"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-19"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1234",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1234"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"position"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#position"}},[t._v("#")]),t._v(" position")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("position")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#layoutposition"}},[a("code",[t._v("LayoutPosition")])])],1),t._v(" "),a("p",[t._v("The position of the item in the chart layout. Possible values are")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-20"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-20"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#position"}},[t._v("position")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-20"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-20"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L9",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:9"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"right"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#right"}},[t._v("#")]),t._v(" right")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("right")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Right edge of the item. Set by layout system and cannot be used in update")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-21"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-21"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#right"}},[t._v("right")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-21"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-21"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L37",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:37"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"ticks"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#ticks"}},[t._v("#")]),t._v(" ticks")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("ticks")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/Tick.html"}},[a("code",[t._v("Tick")])]),t._v("[]")],1),t._v(" "),a("h4",{attrs:{id:"inherited-from-22"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-22"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#ticks"}},[t._v("ticks")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-22"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-22"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1243",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1243"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"top"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#top"}},[t._v("#")]),t._v(" top")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("top")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Top edge of the item. Set by layout system and cannot be used in update")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-23"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-23"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#top"}},[t._v("top")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-23"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-23"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L33",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:33"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"type"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type"}},[t._v("#")]),t._v(" type")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("type")]),t._v(": "),a("code",[t._v("string")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-24"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-24"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#type"}},[t._v("type")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-24"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-24"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1227",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1227"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"weight"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#weight"}},[t._v("#")]),t._v(" weight")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("weight")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("The weight used to sort the item. Higher weights are further away from the chart area")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-25"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-25"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#weight"}},[t._v("weight")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-25"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-25"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L13",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:13"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"width"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#width"}},[t._v("#")]),t._v(" width")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("width")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Width of item. Must be valid after update()")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-26"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-26"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#width"}},[t._v("width")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-26"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-26"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L21",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:21"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"x"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#x"}},[t._v("#")]),t._v(" x")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("x")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-27"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-27"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#x"}},[t._v("x")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-27"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-27"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L5",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:5"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"y"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#y"}},[t._v("#")]),t._v(" y")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("y")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-28"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-28"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#y"}},[t._v("y")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-28"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-28"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L6",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:6"),a("OutboundLink")],1)]),t._v(" "),a("h2",{attrs:{id:"methods"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),a("h3",{attrs:{id:"afterbuildticks"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterbuildticks"}},[t._v("#")]),t._v(" afterBuildTicks")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterBuildTicks")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-29"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-29"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#afterbuildticks"}},[t._v("afterBuildTicks")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-29"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-29"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1323",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1323"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"aftercalculatelabelrotation"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#aftercalculatelabelrotation"}},[t._v("#")]),t._v(" afterCalculateLabelRotation")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterCalculateLabelRotation")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-30"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-30"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#aftercalculatelabelrotation"}},[t._v("afterCalculateLabelRotation")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-30"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-30"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1329",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1329"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterdatalimits"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterdatalimits"}},[t._v("#")]),t._v(" afterDataLimits")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterDataLimits")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-3"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-31"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-31"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#afterdatalimits"}},[t._v("afterDataLimits")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-31"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-31"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1320",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1320"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterfit"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterfit"}},[t._v("#")]),t._v(" afterFit")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterFit")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-4"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-32"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-32"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#afterfit"}},[t._v("afterFit")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-32"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-32"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1332",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1332"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"aftersetdimensions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#aftersetdimensions"}},[t._v("#")]),t._v(" afterSetDimensions")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterSetDimensions")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-5"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-33"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-33"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#aftersetdimensions"}},[t._v("afterSetDimensions")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-33"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-33"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1317",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1317"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterticktolabelconversion"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterticktolabelconversion"}},[t._v("#")]),t._v(" afterTickToLabelConversion")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterTickToLabelConversion")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-6"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-34"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-34"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#afterticktolabelconversion"}},[t._v("afterTickToLabelConversion")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-34"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-34"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1326",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1326"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterupdate"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterupdate"}},[t._v("#")]),t._v(" afterUpdate")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterUpdate")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-7"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-35"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-35"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#afterupdate"}},[t._v("afterUpdate")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-35"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-35"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1314",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1314"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforebuildticks"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforebuildticks"}},[t._v("#")]),t._v(" beforeBuildTicks")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeBuildTicks")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-8"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-36"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-36"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#beforebuildticks"}},[t._v("beforeBuildTicks")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-36"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-36"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1321",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1321"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforecalculatelabelrotation"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforecalculatelabelrotation"}},[t._v("#")]),t._v(" beforeCalculateLabelRotation")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeCalculateLabelRotation")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-9"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-37"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-37"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#beforecalculatelabelrotation"}},[t._v("beforeCalculateLabelRotation")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-37"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-37"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1327",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1327"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforedatalimits"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforedatalimits"}},[t._v("#")]),t._v(" beforeDataLimits")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeDataLimits")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-10"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-38"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-38"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#beforedatalimits"}},[t._v("beforeDataLimits")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-38"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-38"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1318",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1318"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforefit"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforefit"}},[t._v("#")]),t._v(" beforeFit")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeFit")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-11"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-39"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-39"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#beforefit"}},[t._v("beforeFit")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-39"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-39"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1330",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1330"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforelayout"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforelayout"}},[t._v("#")]),t._v(" beforeLayout")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("beforeLayout")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Called before the layout process starts")]),t._v(" "),a("h4",{attrs:{id:"returns-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-12"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-40"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-40"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#beforelayout"}},[t._v("beforeLayout")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-40"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-40"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L46",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:46"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforesetdimensions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforesetdimensions"}},[t._v("#")]),t._v(" beforeSetDimensions")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeSetDimensions")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-13"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-41"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-41"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#beforesetdimensions"}},[t._v("beforeSetDimensions")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-41"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-41"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1315",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1315"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforeticktolabelconversion"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforeticktolabelconversion"}},[t._v("#")]),t._v(" beforeTickToLabelConversion")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeTickToLabelConversion")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-14"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-42"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-42"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#beforeticktolabelconversion"}},[t._v("beforeTickToLabelConversion")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-42"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-42"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1324",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1324"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforeupdate"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforeupdate"}},[t._v("#")]),t._v(" beforeUpdate")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeUpdate")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-15"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-43"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-43"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#beforeupdate"}},[t._v("beforeUpdate")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-43"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-43"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1312",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1312"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"buildticks"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#buildticks"}},[t._v("#")]),t._v(" buildTicks")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("buildTicks")]),t._v("(): "),a("RouterLink",{attrs:{to:"/api/interfaces/Tick.html"}},[a("code",[t._v("Tick")])]),t._v("[]")],1),t._v(" "),a("h4",{attrs:{id:"returns-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-16"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/Tick.html"}},[a("code",[t._v("Tick")])]),t._v("[]")],1),t._v(" "),a("h4",{attrs:{id:"inherited-from-44"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-44"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#buildticks"}},[t._v("buildTicks")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-44"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-44"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1322",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1322"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"calculatelabelrotation"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#calculatelabelrotation"}},[t._v("#")]),t._v(" calculateLabelRotation")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("calculateLabelRotation")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-17"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-45"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-45"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#calculatelabelrotation"}},[t._v("calculateLabelRotation")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-45"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-45"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1328",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1328"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"configure"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#configure"}},[t._v("#")]),t._v(" configure")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("configure")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-18"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-46"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-46"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#configure"}},[t._v("configure")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-46"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-46"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1313",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1313"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"determinedatalimits"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#determinedatalimits"}},[t._v("#")]),t._v(" determineDataLimits")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("determineDataLimits")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-19"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-19"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-47"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-47"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#determinedatalimits"}},[t._v("determineDataLimits")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-47"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-47"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1319",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1319"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"draw"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#draw"}},[t._v("#")]),t._v(" draw")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("draw")]),t._v("("),a("code",[t._v("chartArea")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Draws the element")]),t._v(" "),a("h4",{attrs:{id:"parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chartArea")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-20"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-20"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-48"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-48"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#draw"}},[t._v("draw")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-48"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-48"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L50",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:50"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"drawgrid"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#drawgrid"}},[t._v("#")]),t._v(" drawGrid")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("drawGrid")]),t._v("("),a("code",[t._v("chartArea")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-2"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chartArea")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-21"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-21"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-49"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-49"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#drawgrid"}},[t._v("drawGrid")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-49"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-49"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1248",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1248"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"drawlabels"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#drawlabels"}},[t._v("#")]),t._v(" drawLabels")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("drawLabels")]),t._v("("),a("code",[t._v("chartArea")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-3"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chartArea")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-22"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-22"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-50"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-50"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#drawlabels"}},[t._v("drawLabels")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-50"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-50"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1247",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1247"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"drawtitle"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#drawtitle"}},[t._v("#")]),t._v(" drawTitle")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("drawTitle")]),t._v("("),a("code",[t._v("chartArea")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-4"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chartArea")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-23"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-23"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-51"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-51"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#drawtitle"}},[t._v("drawTitle")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-51"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-51"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1246",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1246"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"fit"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#fit"}},[t._v("#")]),t._v(" fit")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("fit")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-24"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-24"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-52"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-52"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#fit"}},[t._v("fit")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-52"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-52"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1331",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1331"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"generateticklabels"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#generateticklabels"}},[t._v("#")]),t._v(" generateTickLabels")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("generateTickLabels")]),t._v("("),a("code",[t._v("ticks")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-5"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("ticks")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/Tick.html"}},[a("code",[t._v("Tick")])]),t._v("[]")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-25"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-25"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-53"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-53"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#generateticklabels"}},[t._v("generateTickLabels")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-53"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-53"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1325",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1325"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getbasepixel"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getbasepixel"}},[t._v("#")]),t._v(" getBasePixel")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getBasePixel")]),t._v("(): "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Returns the pixel for the minimum chart value\nThe coordinate (0, 0) is at the upper-left corner of the canvas")]),t._v(" "),a("h4",{attrs:{id:"returns-26"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-26"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-54"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-54"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getbasepixel"}},[t._v("getBasePixel")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-54"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-54"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1304",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1304"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getbaseposition"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getbaseposition"}},[t._v("#")]),t._v(" getBasePosition")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getBasePosition")]),t._v("("),a("code",[t._v("index")]),t._v("): "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"parameters-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-6"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-27"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-27"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("Object")])]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("angle")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("x")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("y")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-55"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-55"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3477",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3477"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getbasevalue"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getbasevalue"}},[t._v("#")]),t._v(" getBaseValue")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getBaseValue")]),t._v("(): "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"returns-28"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-28"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-55"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-55"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getbasevalue"}},[t._v("getBaseValue")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-56"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-56"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1298",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1298"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getdecimalforpixel"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getdecimalforpixel"}},[t._v("#")]),t._v(" getDecimalForPixel")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getDecimalForPixel")]),t._v("("),a("code",[t._v("pixel")]),t._v("): "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"parameters-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-7"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("pixel")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-29"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-29"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-56"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-56"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getdecimalforpixel"}},[t._v("getDecimalForPixel")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-57"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-57"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1254",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1254"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getdistancefromcenterforvalue"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getdistancefromcenterforvalue"}},[t._v("#")]),t._v(" getDistanceFromCenterForValue")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getDistanceFromCenterForValue")]),t._v("("),a("code",[t._v("value")]),t._v("): "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"parameters-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-8"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("value")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-30"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-30"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-58"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-58"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3472",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3472"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getindexangle"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getindexangle"}},[t._v("#")]),t._v(" getIndexAngle")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getIndexAngle")]),t._v("("),a("code",[t._v("index")]),t._v("): "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"parameters-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-9"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-31"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-31"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-59"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-59"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3471",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3471"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getlabelforvalue"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getlabelforvalue"}},[t._v("#")]),t._v(" getLabelForValue")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getLabelForValue")]),t._v("("),a("code",[t._v("value")]),t._v("): "),a("code",[t._v("string")])]),t._v(" "),a("p",[t._v("Used to get the label to display in the tooltip for the given value")]),t._v(" "),a("h4",{attrs:{id:"parameters-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-10"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("value")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-32"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-32"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("string")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-57"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-57"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getlabelforvalue"}},[t._v("getLabelForValue")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-60"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-60"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1274",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1274"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getlabels"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getlabels"}},[t._v("#")]),t._v(" getLabels")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getLabels")]),t._v("(): "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"returns-33"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-33"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-58"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-58"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getlabels"}},[t._v("getLabels")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-61"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-61"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1311",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1311"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getlinewidthforvalue"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getlinewidthforvalue"}},[t._v("#")]),t._v(" getLineWidthForValue")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getLineWidthForValue")]),t._v("("),a("code",[t._v("value")]),t._v("): "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Returns the grid line width at given value")]),t._v(" "),a("h4",{attrs:{id:"parameters-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-11"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("value")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-34"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-34"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-59"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-59"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getlinewidthforvalue"}},[t._v("getLineWidthForValue")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-62"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-62"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1279",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1279"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getmatchingvisiblemetas"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getmatchingvisiblemetas"}},[t._v("#")]),t._v(" getMatchingVisibleMetas")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getMatchingVisibleMetas")]),t._v("("),a("code",[t._v("type?")]),t._v("): "),a("RouterLink",{attrs:{to:"/api/#chartmeta"}},[a("code",[t._v("ChartMeta")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(">[]")],1),t._v(" "),a("h4",{attrs:{id:"parameters-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-12"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("type?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("string")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-35"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-35"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/#chartmeta"}},[a("code",[t._v("ChartMeta")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">, keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(">[]")],1),t._v(" "),a("h4",{attrs:{id:"inherited-from-60"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-60"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getmatchingvisiblemetas"}},[t._v("getMatchingVisibleMetas")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-63"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-63"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1244",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1244"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getminmax"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getminmax"}},[t._v("#")]),t._v(" getMinMax")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getMinMax")]),t._v("("),a("code",[t._v("canStack")]),t._v("): "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"parameters-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-13"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("canStack")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-36"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-36"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("Object")])]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("max")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("min")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-61"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-61"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getminmax"}},[t._v("getMinMax")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-64"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-64"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1309",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1309"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getpadding"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getpadding"}},[t._v("#")]),t._v(" getPadding")]),t._v(" "),a("p",[t._v("▸ "),a("code",[t._v("Optional")]),t._v(" "),a("strong",[t._v("getPadding")]),t._v("(): "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1),t._v(" "),a("p",[t._v("Returns an object with padding on the edges")]),t._v(" "),a("h4",{attrs:{id:"returns-37"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-37"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1),t._v(" "),a("h4",{attrs:{id:"inherited-from-62"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-62"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getpadding"}},[t._v("getPadding")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-65"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-65"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L54",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:54"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getpixelfordecimal"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getpixelfordecimal"}},[t._v("#")]),t._v(" getPixelForDecimal")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getPixelForDecimal")]),t._v("("),a("code",[t._v("decimal")]),t._v("): "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Utility for getting the pixel location of a percentage of scale\nThe coordinate (0, 0) is at the upper-left corner of the canvas")]),t._v(" "),a("h4",{attrs:{id:"parameters-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-14"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("decimal")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-38"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-38"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-63"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-63"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getpixelfordecimal"}},[t._v("getPixelForDecimal")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-66"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-66"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1261",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1261"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getpixelfortick"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getpixelfortick"}},[t._v("#")]),t._v(" getPixelForTick")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getPixelForTick")]),t._v("("),a("code",[t._v("index")]),t._v("): "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Returns the location of the tick at the given index\nThe coordinate (0, 0) is at the upper-left corner of the canvas")]),t._v(" "),a("h4",{attrs:{id:"parameters-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-15"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-39"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-39"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-64"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-64"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getpixelfortick"}},[t._v("getPixelForTick")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-67"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-67"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1268",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1268"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getpixelforvalue"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getpixelforvalue"}},[t._v("#")]),t._v(" getPixelForValue")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getPixelForValue")]),t._v("("),a("code",[t._v("value")]),t._v(", "),a("code",[t._v("index?")]),t._v("): "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Returns the location of the given data point. Value can either be an index or a numerical value\nThe coordinate (0, 0) is at the upper-left corner of the canvas")]),t._v(" "),a("h4",{attrs:{id:"parameters-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-16"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("value")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-40"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-40"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-65"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-65"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getpixelforvalue"}},[t._v("getPixelForValue")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-68"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-68"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1288",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1288"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getpointlabelposition"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getpointlabelposition"}},[t._v("#")]),t._v(" getPointLabelPosition")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getPointLabelPosition")]),t._v("("),a("code",[t._v("index")]),t._v("): "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1),t._v(" "),a("h4",{attrs:{id:"parameters-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-17"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-41"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-41"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-69"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-69"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3476",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3476"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getpointposition"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getpointposition"}},[t._v("#")]),t._v(" getPointPosition")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getPointPosition")]),t._v("("),a("code",[t._v("index")]),t._v(", "),a("code",[t._v("distanceFromCenter")]),t._v("): "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"parameters-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-18"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("distanceFromCenter")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-42"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-42"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("Object")])]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("angle")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("x")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("y")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-70"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-70"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3474",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3474"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getpointpositionforvalue"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getpointpositionforvalue"}},[t._v("#")]),t._v(" getPointPositionForValue")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getPointPositionForValue")]),t._v("("),a("code",[t._v("index")]),t._v(", "),a("code",[t._v("value")]),t._v("): "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"parameters-19"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-19"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("value")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-43"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-43"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("Object")])]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("angle")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("x")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("y")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-71"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-71"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3475",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3475"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getprops"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getprops"}},[t._v("#")]),t._v(" getProps")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getProps")]),t._v("<"),a("code",[t._v("P")]),t._v(">("),a("code",[t._v("props")]),t._v(", "),a("code",[t._v("final?")]),t._v("): "),a("code",[t._v("Pick")]),t._v("<"),a("code",[t._v("unknown")]),t._v(", "),a("code",[t._v("P")]),t._v("["),a("code",[t._v("number")]),t._v("]>")]),t._v(" "),a("h4",{attrs:{id:"type-parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-2"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("P")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("code",[t._v("never")]),t._v("[]")])])])]),t._v(" "),a("h4",{attrs:{id:"parameters-20"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-20"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("props")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("P")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("final?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-44"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-44"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("Pick")]),t._v("<"),a("code",[t._v("unknown")]),t._v(", "),a("code",[t._v("P")]),t._v("["),a("code",[t._v("number")]),t._v("]>")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-66"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-66"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getprops"}},[t._v("getProps")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-72"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-72"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L12",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:12"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getticks"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getticks"}},[t._v("#")]),t._v(" getTicks")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getTicks")]),t._v("(): "),a("RouterLink",{attrs:{to:"/api/interfaces/Tick.html"}},[a("code",[t._v("Tick")])]),t._v("[]")],1),t._v(" "),a("h4",{attrs:{id:"returns-45"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-45"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/Tick.html"}},[a("code",[t._v("Tick")])]),t._v("[]")],1),t._v(" "),a("h4",{attrs:{id:"inherited-from-67"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-67"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getticks"}},[t._v("getTicks")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-73"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-73"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1310",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1310"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getuserbounds"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getuserbounds"}},[t._v("#")]),t._v(" getUserBounds")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getUserBounds")]),t._v("(): "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"returns-46"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-46"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("Object")])]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("max")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("maxDefined")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("min")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("minDefined")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-68"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-68"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getuserbounds"}},[t._v("getUserBounds")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-74"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-74"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1308",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1308"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getvaluefordistancefromcenter"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getvaluefordistancefromcenter"}},[t._v("#")]),t._v(" getValueForDistanceFromCenter")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getValueForDistanceFromCenter")]),t._v("("),a("code",[t._v("distance")]),t._v("): "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"parameters-21"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-21"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("distance")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-47"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-47"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-75"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-75"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3473",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3473"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getvalueforpixel"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getvalueforpixel"}},[t._v("#")]),t._v(" getValueForPixel")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getValueForPixel")]),t._v("("),a("code",[t._v("pixel")]),t._v("): "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Used to get the data value from a given pixel. This is the inverse of getPixelForValue\nThe coordinate (0, 0) is at the upper-left corner of the canvas")]),t._v(" "),a("h4",{attrs:{id:"parameters-22"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-22"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("pixel")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-48"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-48"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-69"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-69"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#getvalueforpixel"}},[t._v("getValueForPixel")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-76"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-76"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1296",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1296"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"hasvalue"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hasvalue"}},[t._v("#")]),t._v(" hasValue")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("hasValue")]),t._v("(): "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"returns-49"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-49"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-70"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-70"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#hasvalue"}},[t._v("hasValue")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-77"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-77"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L11",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:11"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"init"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#init"}},[t._v("#")]),t._v(" init")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("init")]),t._v("("),a("code",[t._v("options")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters-23"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-23"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-50"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-50"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-71"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-71"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#init"}},[t._v("init")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-78"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-78"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1306",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1306"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"isfullsize"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#isfullsize"}},[t._v("#")]),t._v(" isFullSize")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("isFullSize")]),t._v("(): "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"returns-51"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-51"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-72"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-72"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#isfullsize"}},[t._v("isFullSize")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-79"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-79"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1334",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1334"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"ishorizontal"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#ishorizontal"}},[t._v("#")]),t._v(" isHorizontal")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("isHorizontal")]),t._v("(): "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("returns true if the layout item is horizontal (ie. top or bottom)")]),t._v(" "),a("h4",{attrs:{id:"returns-52"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-52"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-73"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-73"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#ishorizontal"}},[t._v("isHorizontal")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-80"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-80"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L58",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:58"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"parse"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parse"}},[t._v("#")]),t._v(" parse")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("parse")]),t._v("("),a("code",[t._v("raw")]),t._v(", "),a("code",[t._v("index")]),t._v("): "),a("code",[t._v("unknown")])]),t._v(" "),a("h4",{attrs:{id:"parameters-24"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-24"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("raw")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("unknown")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-53"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-53"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("unknown")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-74"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-74"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#parse"}},[t._v("parse")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-81"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-81"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1307",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1307"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"setcenterpoint"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#setcenterpoint"}},[t._v("#")]),t._v(" setCenterPoint")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("setCenterPoint")]),t._v("("),a("code",[t._v("leftMovement")]),t._v(", "),a("code",[t._v("rightMovement")]),t._v(", "),a("code",[t._v("topMovement")]),t._v(", "),a("code",[t._v("bottomMovement")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters-25"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-25"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("leftMovement")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("rightMovement")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("topMovement")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("bottomMovement")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-54"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-54"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-82"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-82"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3470",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3470"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"setdimensions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#setdimensions"}},[t._v("#")]),t._v(" setDimensions")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("setDimensions")]),t._v("(): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"returns-55"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-55"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-75"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-75"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#setdimensions"}},[t._v("setDimensions")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-83"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-83"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1316",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1316"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"tooltipposition"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#tooltipposition"}},[t._v("#")]),t._v(" tooltipPosition")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("tooltipPosition")]),t._v("("),a("code",[t._v("useFinalPosition?")]),t._v("): "),a("RouterLink",{attrs:{to:"/api/interfaces/Point.html"}},[a("code",[t._v("Point")])])],1),t._v(" "),a("h4",{attrs:{id:"parameters-26"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-26"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("useFinalPosition?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-56"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-56"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/Point.html"}},[a("code",[t._v("Point")])])],1),t._v(" "),a("h4",{attrs:{id:"inherited-from-76"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-76"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#tooltipposition"}},[t._v("tooltipPosition")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-84"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-84"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L10",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:10"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"update"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#update"}},[t._v("#")]),t._v(" update")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("update")]),t._v("("),a("code",[t._v("width")]),t._v(", "),a("code",[t._v("height")]),t._v(", "),a("code",[t._v("margins?")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("p",[t._v("Takes two parameters: width and height.")]),t._v(" "),a("h4",{attrs:{id:"parameters-27"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-27"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("width")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("height")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("margins?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-57"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-57"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-77"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-77"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")]),t._v("."),a("RouterLink",{attrs:{to:"/api/classes/Scale.html#update"}},[t._v("update")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-85"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-85"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L64",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:64"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);