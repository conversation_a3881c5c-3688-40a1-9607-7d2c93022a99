(window.webpackJsonp=window.webpackJsonp||[]).push([[114],{445:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-tickoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-tickoptions"}},[t._v("#")]),t._v(" Interface: TickOptions")]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"backdropcolor"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#backdropcolor"}},[t._v("#")]),t._v(" backdropColor")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("backdropColor")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[t._v("Color")])]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[a("code",[t._v("ScriptableScaleContext")])]),t._v(">")],1),t._v(" "),a("p",[t._v("Color of label backdrops.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" 'rgba(255, 255, 255, 0.75)'")]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2923",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2923"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"backdroppadding"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#backdroppadding"}},[t._v("#")]),t._v(" backdropPadding")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("backdropPadding")]),t._v(": "),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1),t._v(" "),a("p",[t._v("Padding of tick backdrop.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" 2")]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2928",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2928"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"color"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#color"}},[t._v("#")]),t._v(" color")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("color")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[a("code",[t._v("ScriptableAndArray")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[t._v("Color")])]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[a("code",[t._v("ScriptableScaleContext")])]),t._v(">")],1),t._v(" "),a("p",[t._v("Color of tick")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("see")])]),t._v(" Defaults.color")]),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2943",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2943"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"display"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#display"}},[t._v("#")]),t._v(" display")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("display")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("If true, show tick labels.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" true")]),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2938",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2938"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"font"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#font"}},[t._v("#")]),t._v(" font")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("font")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptableandscriptableoptions"}},[a("code",[t._v("ScriptableAndScriptableOptions")])]),t._v("<"),a("code",[t._v("Partial")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/FontSpec.html"}},[a("code",[t._v("FontSpec")])]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[a("code",[t._v("ScriptableScaleContext")])]),t._v(">")],1),t._v(" "),a("p",[t._v("see Fonts")]),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2947",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2947"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"major"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#major"}},[t._v("#")]),t._v(" major")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("major")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("enabled")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("If true, major ticks are generated. A major tick will affect autoskipping and major will be defined on ticks in the scriptable options context.  "),a("strong",[a("code",[t._v("default")])]),t._v(" false")])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2973",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2973"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"padding"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#padding"}},[t._v("#")]),t._v(" padding")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("padding")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("Sets the offset of the tick labels from the axis")]),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2951",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2951"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"showlabelbackdrop"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#showlabelbackdrop"}},[t._v("#")]),t._v(" showLabelBackdrop")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("showLabelBackdrop")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("code",[t._v("boolean")]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[a("code",[t._v("ScriptableScaleContext")])]),t._v(">")],1),t._v(" "),a("p",[t._v("If true, draw a background behind the tick labels.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" false")]),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2956",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2956"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"textstrokecolor"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#textstrokecolor"}},[t._v("#")]),t._v(" textStrokeColor")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("textStrokeColor")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[t._v("Color")])]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[a("code",[t._v("ScriptableScaleContext")])]),t._v(">")],1),t._v(" "),a("p",[t._v("The color of the stroke around the text.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" undefined")]),t._v(" "),a("h4",{attrs:{id:"defined-in-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2961",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2961"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"textstrokewidth"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#textstrokewidth"}},[t._v("#")]),t._v(" textStrokeWidth")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("textStrokeWidth")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("code",[t._v("number")]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[a("code",[t._v("ScriptableScaleContext")])]),t._v(">")],1),t._v(" "),a("p",[t._v("Stroke width around the text.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),a("h4",{attrs:{id:"defined-in-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2966",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2966"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"z"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#z"}},[t._v("#")]),t._v(" z")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("z")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("z-index of tick layer. Useful when ticks are drawn on chart area. Values <= 0 are drawn under datasets, > 0 on top.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),a("h4",{attrs:{id:"defined-in-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2971",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2971"),a("OutboundLink")],1)]),t._v(" "),a("h2",{attrs:{id:"methods"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),a("h3",{attrs:{id:"callback"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#callback"}},[t._v("#")]),t._v(" callback")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("callback")]),t._v("("),a("code",[t._v("tickValue")]),t._v(", "),a("code",[t._v("index")]),t._v(", "),a("code",[t._v("ticks")]),t._v("): "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("number")]),t._v(" | "),a("code",[t._v("string")]),t._v("[] | "),a("code",[t._v("number")]),t._v("[]")]),t._v(" "),a("p",[t._v("Returns the string representation of the tick value as it should be displayed on the chart. See callback.")]),t._v(" "),a("h4",{attrs:{id:"parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("tickValue")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("index")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("ticks")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/Tick.html"}},[a("code",[t._v("Tick")])]),t._v("[]")],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("number")]),t._v(" | "),a("code",[t._v("string")]),t._v("[] | "),a("code",[t._v("number")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2933",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2933"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);