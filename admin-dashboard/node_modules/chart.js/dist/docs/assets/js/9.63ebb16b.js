(window.webpackJsonp=window.webpackJsonp||[]).push([[9],{341:function(t,e,a){"use strict";a.r(e);var r=a(6),i=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"chart-js-v3-9-1"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#chart-js-v3-9-1"}},[t._v("#")]),t._v(" Chart.js - v3.9.1")]),t._v(" "),a("h2",{attrs:{id:"enumerations"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#enumerations"}},[t._v("#")]),t._v(" Enumerations")]),t._v(" "),a("ul",[a("li",[a("RouterLink",{attrs:{to:"/api/enums/DecimationAlgorithm.html"}},[t._v("DecimationAlgorithm")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/enums/UpdateModeEnum.html"}},[t._v("UpdateModeEnum")])],1)]),t._v(" "),a("h2",{attrs:{id:"classes"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#classes"}},[t._v("#")]),t._v(" Classes")]),t._v(" "),a("ul",[a("li",[a("RouterLink",{attrs:{to:"/api/classes/Animation.html"}},[t._v("Animation")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/classes/Animations.html"}},[t._v("Animations")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/classes/Animator.html"}},[t._v("Animator")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/classes/BasePlatform.html"}},[t._v("BasePlatform")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/classes/BasicPlatform.html"}},[t._v("BasicPlatform")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[t._v("Chart")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[t._v("DatasetController")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/classes/DomPlatform.html"}},[t._v("DomPlatform")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[t._v("Scale")])],1)]),t._v(" "),a("h2",{attrs:{id:"interfaces"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interfaces"}},[t._v("#")]),t._v(" Interfaces")]),t._v(" "),a("ul",[a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ActiveDataPoint.html"}},[t._v("ActiveDataPoint")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ActiveElement.html"}},[t._v("ActiveElement")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/AnimationEvent.html"}},[t._v("AnimationEvent")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ArcBorderRadius.html"}},[t._v("ArcBorderRadius")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ArcElement.html"}},[t._v("ArcElement")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ArcHoverOptions.html"}},[t._v("ArcHoverOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ArcOptions.html"}},[t._v("ArcOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ArcProps.html"}},[t._v("ArcProps")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/BarControllerChartOptions.html"}},[t._v("BarControllerChartOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/BarControllerDatasetOptions.html"}},[t._v("BarControllerDatasetOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/BarElement.html"}},[t._v("BarElement")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/BarHoverOptions.html"}},[t._v("BarHoverOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/BarOptions.html"}},[t._v("BarOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/BarProps.html"}},[t._v("BarProps")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/BorderRadius.html"}},[t._v("BorderRadius")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/BubbleControllerDatasetOptions.html"}},[t._v("BubbleControllerDatasetOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[t._v("BubbleDataPoint")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleOptions.html"}},[t._v("CartesianScaleOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleTypeRegistry.html"}},[t._v("CartesianScaleTypeRegistry")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[t._v("ChartArea")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[t._v("ChartComponent")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ChartConfiguration.html"}},[t._v("ChartConfiguration")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ChartConfigurationCustomTypesPerDataset.html"}},[t._v("ChartConfigurationCustomTypesPerDataset")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ChartData.html"}},[t._v("ChartData")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ChartDataCustomTypesPerDataset.html"}},[t._v("ChartDataCustomTypesPerDataset")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ChartDatasetProperties.html"}},[t._v("ChartDatasetProperties")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ChartDatasetPropertiesCustomTypesPerDataset.html"}},[t._v("ChartDatasetPropertiesCustomTypesPerDataset")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ChartEvent.html"}},[t._v("ChartEvent")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[t._v("ChartTypeRegistry")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/CommonElementOptions.html"}},[t._v("CommonElementOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/CommonHoverOptions.html"}},[t._v("CommonHoverOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ComplexFillTarget.html"}},[t._v("ComplexFillTarget")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ControllerDatasetOptions.html"}},[t._v("ControllerDatasetOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreChartOptions.html"}},[t._v("CoreChartOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreInteractionOptions.html"}},[t._v("CoreInteractionOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[t._v("CoreScaleOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/DatasetControllerChartComponent.html"}},[t._v("DatasetControllerChartComponent")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/DateAdapter.html"}},[t._v("DateAdapter")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/Defaults.html"}},[t._v("Defaults")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutAnimationOptions.html"}},[t._v("DoughnutAnimationOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutController.html"}},[t._v("DoughnutController")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerChartOptions.html"}},[t._v("DoughnutControllerChartOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[t._v("DoughnutControllerDatasetOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutMetaExtensions.html"}},[t._v("DoughnutMetaExtensions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/Element.html"}},[t._v("Element")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ElementOptionsByType.html"}},[t._v("ElementOptionsByType")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ExtendedPlugin.html"}},[t._v("ExtendedPlugin")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/FillerControllerDatasetOptions.html"}},[t._v("FillerControllerDatasetOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/FillerOptions.html"}},[t._v("FillerOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/FontSpec.html"}},[t._v("FontSpec")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/GridLineOptions.html"}},[t._v("GridLineOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/InteractionItem.html"}},[t._v("InteractionItem")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/InteractionModeMap.html"}},[t._v("InteractionModeMap")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/InteractionOptions.html"}},[t._v("InteractionOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/LayoutItem.html"}},[t._v("LayoutItem")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/LegendElement.html"}},[t._v("LegendElement")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/LegendItem.html"}},[t._v("LegendItem")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/LegendOptions.html"}},[t._v("LegendOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/LineControllerChartOptions.html"}},[t._v("LineControllerChartOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/LineControllerDatasetOptions.html"}},[t._v("LineControllerDatasetOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/LineElement.html"}},[t._v("LineElement")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/LineHoverOptions.html"}},[t._v("LineHoverOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/LineOptions.html"}},[t._v("LineOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/LineProps.html"}},[t._v("LineProps")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ParsingOptions.html"}},[t._v("ParsingOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/Plugin.html"}},[t._v("Plugin")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/PluginChartOptions.html"}},[t._v("PluginChartOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/PluginOptionsByType.html"}},[t._v("PluginOptionsByType")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/Point.html"}},[t._v("Point")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/PointElement.html"}},[t._v("PointElement")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/PointHoverOptions.html"}},[t._v("PointHoverOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/PointOptions.html"}},[t._v("PointOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/PointPrefixedHoverOptions.html"}},[t._v("PointPrefixedHoverOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/PointPrefixedOptions.html"}},[t._v("PointPrefixedOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/PointProps.html"}},[t._v("PointProps")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/PolarAreaController.html"}},[t._v("PolarAreaController")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/PolarAreaControllerChartOptions.html"}},[t._v("PolarAreaControllerChartOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/PolarAreaControllerDatasetOptions.html"}},[t._v("PolarAreaControllerDatasetOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/RadarControllerDatasetOptions.html"}},[t._v("RadarControllerDatasetOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/RadialLinearScale.html"}},[t._v("RadialLinearScale")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/RadialScaleTypeRegistry.html"}},[t._v("RadialScaleTypeRegistry")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/Registry.html"}},[t._v("Registry")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ScaleTypeRegistry.html"}},[t._v("ScaleTypeRegistry")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[t._v("ScatterDataPoint")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableCartesianScaleContext.html"}},[t._v("ScriptableCartesianScaleContext")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableChartContext.html"}},[t._v("ScriptableChartContext")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[t._v("ScriptableContext")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableLineSegmentContext.html"}},[t._v("ScriptableLineSegmentContext")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[t._v("ScriptableScaleContext")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScalePointLabelContext.html"}},[t._v("ScriptableScalePointLabelContext")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[t._v("ScriptableTooltipContext")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/Segment.html"}},[t._v("Segment")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/Tick.html"}},[t._v("Tick")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/TickOptions.html"}},[t._v("TickOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/TimeScale.html"}},[t._v("TimeScale")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/TitleOptions.html"}},[t._v("TitleOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/Tooltip.html"}},[t._v("Tooltip")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/TooltipCallbacks.html"}},[t._v("TooltipCallbacks")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/TooltipItem.html"}},[t._v("TooltipItem")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/TooltipLabelStyle.html"}},[t._v("TooltipLabelStyle")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/TooltipModel.html"}},[t._v("TooltipModel")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/TooltipOptions.html"}},[t._v("TooltipOptions")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/TooltipPosition.html"}},[t._v("TooltipPosition")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/TooltipPositionerMap.html"}},[t._v("TooltipPositionerMap")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/TypedRegistry.html"}},[t._v("TypedRegistry")])],1),t._v(" "),a("li",[a("RouterLink",{attrs:{to:"/api/interfaces/VisualElement.html"}},[t._v("VisualElement")])],1)]),t._v(" "),a("h2",{attrs:{id:"type-aliases"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-aliases"}},[t._v("#")]),t._v(" Type aliases")]),t._v(" "),a("h3",{attrs:{id:"align"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#align"}},[t._v("#")]),t._v(" Align")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("Align")]),t._v(": "),a("code",[t._v('"start"')]),t._v(" | "),a("code",[t._v('"center"')]),t._v(" | "),a("code",[t._v('"end"')])]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1682",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1682"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"animationoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#animationoptions"}},[t._v("#")]),t._v(" AnimationOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("AnimationOptions")]),t._v("<"),a("code",[t._v("TType")]),t._v(">: "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"type-declaration"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("animation")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("false")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/#animationspec"}},[a("code",[t._v("AnimationSpec")])]),t._v("<"),a("code",[t._v("TType")]),t._v("> & { "),a("code",[t._v("onComplete?")]),t._v(": ("),a("code",[t._v("event")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/AnimationEvent.html"}},[a("code",[t._v("AnimationEvent")])]),t._v(") => "),a("code",[t._v("void")]),t._v(" ; "),a("code",[t._v("onProgress?")]),t._v(": ("),a("code",[t._v("event")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/AnimationEvent.html"}},[a("code",[t._v("AnimationEvent")])]),t._v(") => "),a("code",[t._v("void")]),t._v("  }")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("animations")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#animationsspec"}},[a("code",[t._v("AnimationsSpec")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("transitions")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#transitionsspec"}},[a("code",[t._v("TransitionsSpec")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1639",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1639"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"animationspec"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#animationspec"}},[t._v("#")]),t._v(" AnimationSpec")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("AnimationSpec")]),t._v("<"),a("code",[t._v("TType")]),t._v(">: "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-2"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-2"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Description")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("delay?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("code",[t._v("number")]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[a("code",[t._v("ScriptableContext")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Delay before starting the animations.  "),a("strong",[a("code",[t._v("default")])]),t._v(" 0")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("duration?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("code",[t._v("number")]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[a("code",[t._v("ScriptableContext")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("The number of milliseconds an animation takes.  "),a("strong",[a("code",[t._v("default")])]),t._v(" 1000")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("easing?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#easingfunction"}},[a("code",[t._v("EasingFunction")])]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[a("code",[t._v("ScriptableContext")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("Easing function to use  "),a("strong",[a("code",[t._v("default")])]),t._v(" 'easeOutQuart'")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("loop?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("code",[t._v("boolean")]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[a("code",[t._v("ScriptableContext")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("If set to true, the animations loop endlessly.  "),a("strong",[a("code",[t._v("default")])]),t._v(" false")])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1583",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1583"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"animationsspec"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#animationsspec"}},[t._v("#")]),t._v(" AnimationsSpec")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("AnimationsSpec")]),t._v("<"),a("code",[t._v("TType")]),t._v(">: "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-parameters-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-3"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"index-signature"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#index-signature"}},[t._v("#")]),t._v(" Index signature")]),t._v(" "),a("p",[t._v("▪ [name: "),a("code",[t._v("string")]),t._v("]: "),a("code",[t._v("false")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/#animationspec"}},[a("code",[t._v("AnimationSpec")])]),t._v("<"),a("code",[t._v("TType")]),t._v("> & { "),a("code",[t._v("from")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[t._v("Color")])]),t._v(" | "),a("code",[t._v("number")]),t._v(" | "),a("code",[t._v("boolean")]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[a("code",[t._v("ScriptableContext")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">> ; "),a("code",[t._v("properties")]),t._v(": "),a("code",[t._v("string")]),t._v("[] ; "),a("code",[t._v("to")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[t._v("Color")])]),t._v(" | "),a("code",[t._v("number")]),t._v(" | "),a("code",[t._v("boolean")]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[a("code",[t._v("ScriptableContext")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">> ; "),a("code",[t._v("type")]),t._v(": "),a("code",[t._v('"color"')]),t._v(" | "),a("code",[t._v('"number"')]),t._v(" | "),a("code",[t._v('"boolean"')]),t._v(" ; "),a("code",[t._v("fn")]),t._v(": <T>("),a("code",[t._v("from")]),t._v(": "),a("code",[t._v("T")]),t._v(", "),a("code",[t._v("to")]),t._v(": "),a("code",[t._v("T")]),t._v(", "),a("code",[t._v("factor")]),t._v(": "),a("code",[t._v("number")]),t._v(") => "),a("code",[t._v("T")]),t._v("  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1608",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1608"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"barcontroller"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#barcontroller"}},[t._v("#")]),t._v(" BarController")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("BarController")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[a("code",[t._v("DatasetController")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L145",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:145"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"bubblecontroller"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#bubblecontroller"}},[t._v("#")]),t._v(" BubbleController")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("BubbleController")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[a("code",[t._v("DatasetController")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L173",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:173"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"cartesiantickoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#cartesiantickoptions"}},[t._v("#")]),t._v(" CartesianTickOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("CartesianTickOptions")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/TickOptions.html"}},[a("code",[t._v("TickOptions")])]),t._v(" & { "),a("code",[t._v("align")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#align"}},[a("code",[t._v("Align")])]),t._v(" | "),a("code",[t._v('"inner"')]),t._v(" ; "),a("code",[t._v("autoSkip")]),t._v(": "),a("code",[t._v("boolean")]),t._v(" ; "),a("code",[t._v("autoSkipPadding")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("crossAlign")]),t._v(": "),a("code",[t._v('"near"')]),t._v(" | "),a("code",[t._v('"center"')]),t._v(" | "),a("code",[t._v('"far"')]),t._v(" ; "),a("code",[t._v("includeBounds")]),t._v(": "),a("code",[t._v("boolean")]),t._v(" ; "),a("code",[t._v("labelOffset")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("maxRotation")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("maxTicksLimit")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("minRotation")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("mirror")]),t._v(": "),a("code",[t._v("boolean")]),t._v(" ; "),a("code",[t._v("padding")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("sampleSize")]),t._v(": "),a("code",[t._v("number")]),t._v("  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2982",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2982"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"categoryscale"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#categoryscale"}},[t._v("#")]),t._v(" CategoryScale")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("CategoryScale")]),t._v("<"),a("code",[t._v("O")]),t._v(">: "),a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("code",[t._v("O")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"type-parameters-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-4"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#categoryscaleoptions"}},[a("code",[t._v("CategoryScaleOptions")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#categoryscaleoptions"}},[a("code",[t._v("CategoryScaleOptions")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3147",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3147"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"categoryscaleoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#categoryscaleoptions"}},[t._v("#")]),t._v(" CategoryScaleOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("CategoryScaleOptions")]),t._v(": "),a("code",[t._v("Omit")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleOptions.html"}},[a("code",[t._v("CartesianScaleOptions")])]),t._v(", "),a("code",[t._v('"min"')]),t._v(" | "),a("code",[t._v('"max"')]),t._v("> & { "),a("code",[t._v("labels")]),t._v(": "),a("code",[t._v("string")]),t._v("[] | "),a("code",[t._v("string")]),t._v("[][] ; "),a("code",[t._v("max")]),t._v(": "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("min")]),t._v(": "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("number")]),t._v("  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3141",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3141"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"chartcomponentlike"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#chartcomponentlike"}},[t._v("#")]),t._v(" ChartComponentLike")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ChartComponentLike")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v("[] | { [key: string]: "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(";  } | "),a("RouterLink",{attrs:{to:"/api/interfaces/Plugin.html"}},[a("code",[t._v("Plugin")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/Plugin.html"}},[a("code",[t._v("Plugin")])]),t._v("[]")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1113",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1113"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"chartdataset"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#chartdataset"}},[t._v("#")]),t._v(" ChartDataset")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ChartDataset")]),t._v("<"),a("code",[t._v("TType")]),t._v(", "),a("code",[t._v("TData")]),t._v(">: "),a("code",[t._v("DeepPartial")]),t._v('<{ [key in ChartType]: Object & ChartTypeRegistry[key]["datasetOptions"] }['),a("code",[t._v("TType")]),t._v("]> & "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartDatasetProperties.html"}},[a("code",[t._v("ChartDatasetProperties")])]),t._v("<"),a("code",[t._v("TType")]),t._v(", "),a("code",[t._v("TData")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"type-parameters-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-5"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TData")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#defaultdatapoint"}},[a("code",[t._v("DefaultDataPoint")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3659",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3659"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"chartdatasetcustomtypesperdataset"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#chartdatasetcustomtypesperdataset"}},[t._v("#")]),t._v(" ChartDatasetCustomTypesPerDataset")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ChartDatasetCustomTypesPerDataset")]),t._v("<"),a("code",[t._v("TType")]),t._v(", "),a("code",[t._v("TData")]),t._v(">: "),a("code",[t._v("DeepPartial")]),t._v('<{ [key in ChartType]: Object & ChartTypeRegistry[key]["datasetOptions"] }['),a("code",[t._v("TType")]),t._v("]> & "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartDatasetPropertiesCustomTypesPerDataset.html"}},[a("code",[t._v("ChartDatasetPropertiesCustomTypesPerDataset")])]),t._v("<"),a("code",[t._v("TType")]),t._v(", "),a("code",[t._v("TData")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"type-parameters-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-6"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TData")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#defaultdatapoint"}},[a("code",[t._v("DefaultDataPoint")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3666",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3666"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"chartitem"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#chartitem"}},[t._v("#")]),t._v(" ChartItem")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ChartItem")]),t._v(": "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("CanvasRenderingContext2D")]),t._v(" | "),a("code",[t._v("HTMLCanvasElement")]),t._v(" | { "),a("code",[t._v("canvas")]),t._v(": "),a("code",[t._v("HTMLCanvasElement")]),t._v("  } | "),a("code",[t._v("ArrayLike")]),t._v("<"),a("code",[t._v("CanvasRenderingContext2D")]),t._v(" | "),a("code",[t._v("HTMLCanvasElement")]),t._v(">")]),t._v(" "),a("h4",{attrs:{id:"defined-in-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L554",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:554"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"chartmeta"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#chartmeta"}},[t._v("#")]),t._v(" ChartMeta")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ChartMeta")]),t._v("<"),a("code",[t._v("TElement")]),t._v(", "),a("code",[t._v("TDatasetElement")]),t._v(", "),a("code",[t._v("TType")]),t._v(">: "),a("code",[t._v("DeepPartial")]),t._v('<{ [key in ChartType]: ChartTypeRegistry[key]["metaExtensions"] }['),a("code",[t._v("TType")]),t._v("]> & "),a("code",[t._v("ChartMetaCommon")]),t._v("<"),a("code",[t._v("TElement")]),t._v(", "),a("code",[t._v("TDatasetElement")]),t._v(">")]),t._v(" "),a("h4",{attrs:{id:"type-parameters-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-7"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TElement")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TDatasetElement")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L460",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:460"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"chartoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#chartoptions"}},[t._v("#")]),t._v(" ChartOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ChartOptions")]),t._v("<"),a("code",[t._v("TType")]),t._v(">: "),a("code",[t._v("DeepPartial")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CoreChartOptions.html"}},[a("code",[t._v("CoreChartOptions")])]),t._v("<"),a("code",[t._v("TType")]),t._v("> & "),a("RouterLink",{attrs:{to:"/api/#elementchartoptions"}},[a("code",[t._v("ElementChartOptions")])]),t._v("<"),a("code",[t._v("TType")]),t._v("> & "),a("RouterLink",{attrs:{to:"/api/interfaces/PluginChartOptions.html"}},[a("code",[t._v("PluginChartOptions")])]),t._v("<"),a("code",[t._v("TType")]),t._v("> & "),a("RouterLink",{attrs:{to:"/api/#datasetchartoptions"}},[a("code",[t._v("DatasetChartOptions")])]),t._v("<"),a("code",[t._v("TType")]),t._v("> & "),a("RouterLink",{attrs:{to:"/api/#scalechartoptions"}},[a("code",[t._v("ScaleChartOptions")])]),t._v("<"),a("code",[t._v("TType")]),t._v("> & "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v("["),a("code",[t._v("TType")]),t._v("]["),a("code",[t._v('"chartOptions"')]),t._v("]>")],1),t._v(" "),a("h4",{attrs:{id:"type-parameters-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-8"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-15"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3636",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3636"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"charttype"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#charttype"}},[t._v("#")]),t._v(" ChartType")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ChartType")]),t._v(": keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-16"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3615",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3615"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"color"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#color"}},[t._v("#")]),t._v(" Color")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("Color")]),t._v(": "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("CanvasGradient")]),t._v(" | "),a("code",[t._v("CanvasPattern")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-17"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/color.d.ts#L1",target:"_blank",rel:"noopener noreferrer"}},[t._v("color.d.ts:1"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"datasetchartoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#datasetchartoptions"}},[t._v("#")]),t._v(" DatasetChartOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("DatasetChartOptions")]),t._v("<"),a("code",[t._v("TType")]),t._v(">: { [key in TType]: Object }")]),t._v(" "),a("h4",{attrs:{id:"type-parameters-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-9"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-18"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3624",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3624"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"decimationoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#decimationoptions"}},[t._v("#")]),t._v(" DecimationOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("DecimationOptions")]),t._v(": "),a("code",[t._v("LttbDecimationOptions")]),t._v(" | "),a("code",[t._v("MinMaxDecimationOptions")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-19"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-19"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2130",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2130"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"defaultdatapoint"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defaultdatapoint"}},[t._v("#")]),t._v(" DefaultDataPoint")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("DefaultDataPoint")]),t._v("<"),a("code",[t._v("TType")]),t._v(">: "),a("code",[t._v("DistributiveArray")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v("["),a("code",[t._v("TType")]),t._v("]["),a("code",[t._v('"defaultDataPoint"')]),t._v("]>")],1),t._v(" "),a("h4",{attrs:{id:"type-parameters-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-10"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-20"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-20"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3645",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3645"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"doughnutdatapoint"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#doughnutdatapoint"}},[t._v("#")]),t._v(" DoughnutDataPoint")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("DoughnutDataPoint")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-21"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-21"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L331",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:331"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"easingfunction"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#easingfunction"}},[t._v("#")]),t._v(" EasingFunction")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("EasingFunction")]),t._v(": "),a("code",[t._v('"linear"')]),t._v(" | "),a("code",[t._v('"easeInQuad"')]),t._v(" | "),a("code",[t._v('"easeOutQuad"')]),t._v(" | "),a("code",[t._v('"easeInOutQuad"')]),t._v(" | "),a("code",[t._v('"easeInCubic"')]),t._v(" | "),a("code",[t._v('"easeOutCubic"')]),t._v(" | "),a("code",[t._v('"easeInOutCubic"')]),t._v(" | "),a("code",[t._v('"easeInQuart"')]),t._v(" | "),a("code",[t._v('"easeOutQuart"')]),t._v(" | "),a("code",[t._v('"easeInOutQuart"')]),t._v(" | "),a("code",[t._v('"easeInQuint"')]),t._v(" | "),a("code",[t._v('"easeOutQuint"')]),t._v(" | "),a("code",[t._v('"easeInOutQuint"')]),t._v(" | "),a("code",[t._v('"easeInSine"')]),t._v(" | "),a("code",[t._v('"easeOutSine"')]),t._v(" | "),a("code",[t._v('"easeInOutSine"')]),t._v(" | "),a("code",[t._v('"easeInExpo"')]),t._v(" | "),a("code",[t._v('"easeOutExpo"')]),t._v(" | "),a("code",[t._v('"easeInOutExpo"')]),t._v(" | "),a("code",[t._v('"easeInCirc"')]),t._v(" | "),a("code",[t._v('"easeOutCirc"')]),t._v(" | "),a("code",[t._v('"easeInOutCirc"')]),t._v(" | "),a("code",[t._v('"easeInElastic"')]),t._v(" | "),a("code",[t._v('"easeOutElastic"')]),t._v(" | "),a("code",[t._v('"easeInOutElastic"')]),t._v(" | "),a("code",[t._v('"easeInBack"')]),t._v(" | "),a("code",[t._v('"easeOutBack"')]),t._v(" | "),a("code",[t._v('"easeInOutBack"')]),t._v(" | "),a("code",[t._v('"easeInBounce"')]),t._v(" | "),a("code",[t._v('"easeOutBounce"')]),t._v(" | "),a("code",[t._v('"easeInOutBounce"')])]),t._v(" "),a("h4",{attrs:{id:"defined-in-22"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-22"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1550",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1550"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"elementchartoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#elementchartoptions"}},[t._v("#")]),t._v(" ElementChartOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ElementChartOptions")]),t._v("<"),a("code",[t._v("TType")]),t._v(">: "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-parameters-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-11"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-3"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("elements")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ElementOptionsByType.html"}},[a("code",[t._v("ElementOptionsByType")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-23"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-23"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2046",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2046"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"filltarget"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#filltarget"}},[t._v("#")]),t._v(" FillTarget")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("FillTarget")]),t._v(": "),a("code",[t._v("number")]),t._v(" | "),a("code",[t._v("string")]),t._v(" | { "),a("code",[t._v("value")]),t._v(": "),a("code",[t._v("number")]),t._v("  } | "),a("code",[t._v('"start"')]),t._v(" | "),a("code",[t._v('"end"')]),t._v(" | "),a("code",[t._v('"origin"')]),t._v(" | "),a("code",[t._v('"stack"')]),t._v(" | "),a("code",[t._v('"shape"')]),t._v(" | "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-24"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-24"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2138",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2138"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"interactionaxis"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interactionaxis"}},[t._v("#")]),t._v(" InteractionAxis")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("InteractionAxis")]),t._v(": "),a("code",[t._v('"x"')]),t._v(" | "),a("code",[t._v('"y"')]),t._v(" | "),a("code",[t._v('"xy"')]),t._v(" | "),a("code",[t._v('"r"')])]),t._v(" "),a("h4",{attrs:{id:"defined-in-25"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-25"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1422",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1422"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"interactionmode"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interactionmode"}},[t._v("#")]),t._v(" InteractionMode")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("InteractionMode")]),t._v(": keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/InteractionModeMap.html"}},[a("code",[t._v("InteractionModeMap")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-26"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-26"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L752",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:752"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"interactionmodefunction"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interactionmodefunction"}},[t._v("#")]),t._v(" InteractionModeFunction")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("InteractionModeFunction")]),t._v(": ("),a("code",[t._v("chart")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v(", "),a("code",[t._v("e")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartEvent.html"}},[a("code",[t._v("ChartEvent")])]),t._v(", "),a("code",[t._v("options")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/InteractionOptions.html"}},[a("code",[t._v("InteractionOptions")])]),t._v(", "),a("code",[t._v("useFinalPosition?")]),t._v(": "),a("code",[t._v("boolean")]),t._v(") => "),a("RouterLink",{attrs:{to:"/api/interfaces/InteractionItem.html"}},[a("code",[t._v("InteractionItem")])]),t._v("[]")],1),t._v(" "),a("h4",{attrs:{id:"type-declaration-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-4"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("p",[t._v("▸ ("),a("code",[t._v("chart")]),t._v(", "),a("code",[t._v("e")]),t._v(", "),a("code",[t._v("options")]),t._v(", "),a("code",[t._v("useFinalPosition?")]),t._v("): "),a("RouterLink",{attrs:{to:"/api/interfaces/InteractionItem.html"}},[a("code",[t._v("InteractionItem")])]),t._v("[]")],1),t._v(" "),a("h5",{attrs:{id:"parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("chart")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("e")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ChartEvent.html"}},[a("code",[t._v("ChartEvent")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("options")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/InteractionOptions.html"}},[a("code",[t._v("InteractionOptions")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("useFinalPosition?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h5",{attrs:{id:"returns"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/InteractionItem.html"}},[a("code",[t._v("InteractionItem")])]),t._v("[]")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-27"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-27"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L714",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:714"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"layoutposition"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#layoutposition"}},[t._v("#")]),t._v(" LayoutPosition")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("LayoutPosition")]),t._v(": "),a("code",[t._v('"left"')]),t._v(" | "),a("code",[t._v('"top"')]),t._v(" | "),a("code",[t._v('"right"')]),t._v(" | "),a("code",[t._v('"bottom"')]),t._v(" | "),a("code",[t._v('"center"')]),t._v(" | "),a("code",[t._v('"chartArea"')]),t._v(" | { [scaleId: string]: "),a("code",[t._v("number")]),t._v(";  }")]),t._v(" "),a("h4",{attrs:{id:"defined-in-28"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-28"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L3",target:"_blank",rel:"noopener noreferrer"}},[t._v("layout.d.ts:3"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"linecontroller"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#linecontroller"}},[t._v("#")]),t._v(" LineController")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("LineController")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[a("code",[t._v("DatasetController")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-29"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-29"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L219",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:219"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"linearscale"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#linearscale"}},[t._v("#")]),t._v(" LinearScale")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("LinearScale")]),t._v("<"),a("code",[t._v("O")]),t._v(">: "),a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("code",[t._v("O")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"type-parameters-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-12"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#linearscaleoptions"}},[a("code",[t._v("LinearScaleOptions")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#linearscaleoptions"}},[a("code",[t._v("LinearScaleOptions")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-30"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-30"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3196",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3196"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"linearscaleoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#linearscaleoptions"}},[t._v("#")]),t._v(" LinearScaleOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("LinearScaleOptions")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleOptions.html"}},[a("code",[t._v("CartesianScaleOptions")])]),t._v(" & { "),a("code",[t._v("beginAtZero")]),t._v(": "),a("code",[t._v("boolean")]),t._v(" ; "),a("code",[t._v("grace?")]),t._v(": "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("suggestedMax?")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("suggestedMin?")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("ticks")]),t._v(": { "),a("code",[t._v("count")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("format")]),t._v(": "),a("code",[t._v("Intl.NumberFormatOptions")]),t._v(" ; "),a("code",[t._v("precision")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("stepSize")]),t._v(": "),a("code",[t._v("number")]),t._v("  }  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-31"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-31"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3153",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3153"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"logarithmicscale"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#logarithmicscale"}},[t._v("#")]),t._v(" LogarithmicScale")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("LogarithmicScale")]),t._v("<"),a("code",[t._v("O")]),t._v(">: "),a("RouterLink",{attrs:{to:"/api/classes/Scale.html"}},[a("code",[t._v("Scale")])]),t._v("<"),a("code",[t._v("O")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"type-parameters-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-13"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#logarithmicscaleoptions"}},[a("code",[t._v("LogarithmicScaleOptions")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#logarithmicscaleoptions"}},[a("code",[t._v("LogarithmicScaleOptions")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-32"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-32"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3220",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3220"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"logarithmicscaleoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#logarithmicscaleoptions"}},[t._v("#")]),t._v(" LogarithmicScaleOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("LogarithmicScaleOptions")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleOptions.html"}},[a("code",[t._v("CartesianScaleOptions")])]),t._v(" & { "),a("code",[t._v("suggestedMax?")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("suggestedMin?")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("ticks")]),t._v(": { "),a("code",[t._v("format")]),t._v(": "),a("code",[t._v("Intl.NumberFormatOptions")]),t._v("  }  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-33"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-33"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3202",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3202"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"overrides"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#overrides"}},[t._v("#")]),t._v(" Overrides")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("Overrides")]),t._v(': { [key in ChartType]: CoreChartOptions<key> & ElementChartOptions<key> & PluginChartOptions<key> & DatasetChartOptions<ChartType> & ScaleChartOptions<key> & ChartTypeRegistry[key]["chartOptions"] }')]),t._v(" "),a("h4",{attrs:{id:"defined-in-34"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-34"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L691",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:691"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"parseddatatype"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parseddatatype"}},[t._v("#")]),t._v(" ParsedDataType")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ParsedDataType")]),t._v("<"),a("code",[t._v("TType")]),t._v(">: "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v("["),a("code",[t._v("TType")]),t._v("]["),a("code",[t._v('"parsedDataType"')]),t._v("]")],1),t._v(" "),a("h4",{attrs:{id:"type-parameters-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-14"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-35"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-35"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3647",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3647"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"pieanimationoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#pieanimationoptions"}},[t._v("#")]),t._v(" PieAnimationOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("PieAnimationOptions")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutAnimationOptions.html"}},[a("code",[t._v("DoughnutAnimationOptions")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-36"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-36"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L354",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:354"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"piecontroller"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#piecontroller"}},[t._v("#")]),t._v(" PieController")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("PieController")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#doughnutcontroller"}},[a("code",[t._v("DoughnutController")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-37"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-37"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L359",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:359"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"piecontrollerchartoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#piecontrollerchartoptions"}},[t._v("#")]),t._v(" PieControllerChartOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("PieControllerChartOptions")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerChartOptions.html"}},[a("code",[t._v("DoughnutControllerChartOptions")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-38"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-38"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L353",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:353"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"piecontrollerdatasetoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#piecontrollerdatasetoptions"}},[t._v("#")]),t._v(" PieControllerDatasetOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("PieControllerDatasetOptions")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[a("code",[t._v("DoughnutControllerDatasetOptions")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-39"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-39"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L352",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:352"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"piedatapoint"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#piedatapoint"}},[t._v("#")]),t._v(" PieDataPoint")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("PieDataPoint")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#doughnutdatapoint"}},[a("code",[t._v("DoughnutDataPoint")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-40"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-40"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L356",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:356"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"piemetaextensions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#piemetaextensions"}},[t._v("#")]),t._v(" PieMetaExtensions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("PieMetaExtensions")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutMetaExtensions.html"}},[a("code",[t._v("DoughnutMetaExtensions")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-41"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-41"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L357",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:357"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"pointstyle"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#pointstyle"}},[t._v("#")]),t._v(" PointStyle")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("PointStyle")]),t._v(": "),a("code",[t._v('"circle"')]),t._v(" | "),a("code",[t._v('"cross"')]),t._v(" | "),a("code",[t._v('"crossRot"')]),t._v(" | "),a("code",[t._v('"dash"')]),t._v(" | "),a("code",[t._v('"line"')]),t._v(" | "),a("code",[t._v('"rect"')]),t._v(" | "),a("code",[t._v('"rectRounded"')]),t._v(" | "),a("code",[t._v('"rectRot"')]),t._v(" | "),a("code",[t._v('"star"')]),t._v(" | "),a("code",[t._v('"triangle"')]),t._v(" | "),a("code",[t._v("HTMLImageElement")]),t._v(" | "),a("code",[t._v("HTMLCanvasElement")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-42"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-42"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1865",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1865"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"polarareaanimationoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#polarareaanimationoptions"}},[t._v("#")]),t._v(" PolarAreaAnimationOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("PolarAreaAnimationOptions")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutAnimationOptions.html"}},[a("code",[t._v("DoughnutAnimationOptions")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-43"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-43"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L373",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:373"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"radarcontroller"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#radarcontroller"}},[t._v("#")]),t._v(" RadarController")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("RadarController")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/DatasetController.html"}},[a("code",[t._v("DatasetController")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-44"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-44"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L420",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:420"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"radarcontrollerchartoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#radarcontrollerchartoptions"}},[t._v("#")]),t._v(" RadarControllerChartOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("RadarControllerChartOptions")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/LineControllerChartOptions.html"}},[a("code",[t._v("LineControllerChartOptions")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-45"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-45"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L418",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:418"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"radiallinearscaleoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#radiallinearscaleoptions"}},[t._v("#")]),t._v(" RadialLinearScaleOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("RadialLinearScaleOptions")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/CoreScaleOptions.html"}},[a("code",[t._v("CoreScaleOptions")])]),t._v(" & { "),a("code",[t._v("angleLines")]),t._v(": { "),a("code",[t._v("borderDash")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("code",[t._v("number")]),t._v("[], "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[a("code",[t._v("ScriptableScaleContext")])]),t._v("> ; "),a("code",[t._v("borderDashOffset")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("code",[t._v("number")]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[a("code",[t._v("ScriptableScaleContext")])]),t._v("> ; "),a("code",[t._v("color")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[t._v("Color")])]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[a("code",[t._v("ScriptableScaleContext")])]),t._v("> ; "),a("code",[t._v("display")]),t._v(": "),a("code",[t._v("boolean")]),t._v(" ; "),a("code",[t._v("lineWidth")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("code",[t._v("number")]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[a("code",[t._v("ScriptableScaleContext")])]),t._v(">  } ; "),a("code",[t._v("animate")]),t._v(": "),a("code",[t._v("boolean")]),t._v(" ; "),a("code",[t._v("beginAtZero")]),t._v(": "),a("code",[t._v("boolean")]),t._v(" ; "),a("code",[t._v("grid")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/GridLineOptions.html"}},[a("code",[t._v("GridLineOptions")])]),t._v(" ; "),a("code",[t._v("max")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("min")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("pointLabels")]),t._v(": { "),a("code",[t._v("backdropColor")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[t._v("Color")])]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScalePointLabelContext.html"}},[a("code",[t._v("ScriptableScalePointLabelContext")])]),t._v("> ; "),a("code",[t._v("backdropPadding")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScalePointLabelContext.html"}},[a("code",[t._v("ScriptableScalePointLabelContext")])]),t._v("> ; "),a("code",[t._v("borderRadius")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BorderRadius.html"}},[a("code",[t._v("BorderRadius")])]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScalePointLabelContext.html"}},[a("code",[t._v("ScriptableScalePointLabelContext")])]),t._v("> ; "),a("code",[t._v("centerPointLabels")]),t._v(": "),a("code",[t._v("boolean")]),t._v(" ; "),a("code",[t._v("color")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[t._v("Color")])]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScalePointLabelContext.html"}},[a("code",[t._v("ScriptableScalePointLabelContext")])]),t._v("> ; "),a("code",[t._v("display")]),t._v(": "),a("code",[t._v("boolean")]),t._v(" ; "),a("code",[t._v("font")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptableandscriptableoptions"}},[a("code",[t._v("ScriptableAndScriptableOptions")])]),t._v("<"),a("code",[t._v("Partial")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/FontSpec.html"}},[a("code",[t._v("FontSpec")])]),t._v(">, "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScalePointLabelContext.html"}},[a("code",[t._v("ScriptableScalePointLabelContext")])]),t._v("> ; "),a("code",[t._v("padding")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("code",[t._v("number")]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScalePointLabelContext.html"}},[a("code",[t._v("ScriptableScalePointLabelContext")])]),t._v("> ; "),a("code",[t._v("callback")]),t._v(": ("),a("code",[t._v("label")]),t._v(": "),a("code",[t._v("string")]),t._v(", "),a("code",[t._v("index")]),t._v(": "),a("code",[t._v("number")]),t._v(") => "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("number")]),t._v(" | "),a("code",[t._v("string")]),t._v("[] | "),a("code",[t._v("number")]),t._v("[]  } ; "),a("code",[t._v("startAngle")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("suggestedMax")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("suggestedMin")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("ticks")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#radialtickoptions"}},[a("code",[t._v("RadialTickOptions")])]),t._v("  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-46"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-46"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3356",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3356"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"radialtickoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#radialtickoptions"}},[t._v("#")]),t._v(" RadialTickOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("RadialTickOptions")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/TickOptions.html"}},[a("code",[t._v("TickOptions")])]),t._v(" & { "),a("code",[t._v("count")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("format")]),t._v(": "),a("code",[t._v("Intl.NumberFormatOptions")]),t._v(" ; "),a("code",[t._v("maxTicksLimit")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("precision")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("stepSize")]),t._v(": "),a("code",[t._v("number")]),t._v("  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-47"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-47"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3328",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3328"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"scalechartoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#scalechartoptions"}},[t._v("#")]),t._v(" ScaleChartOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ScaleChartOptions")]),t._v("<"),a("code",[t._v("TType")]),t._v(">: "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-parameters-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-15"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-5"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("scales")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-48"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-48"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3630",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3630"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"scaleoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#scaleoptions"}},[t._v("#")]),t._v(" ScaleOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ScaleOptions")]),t._v("<"),a("code",[t._v("TScale")]),t._v(">: "),a("code",[t._v("DeepPartial")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#scaleoptionsbytype"}},[a("code",[t._v("ScaleOptionsByType")])]),t._v("<"),a("code",[t._v("TScale")]),t._v(">>")],1),t._v(" "),a("h4",{attrs:{id:"type-parameters-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-16"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TScale")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#scaletype"}},[a("code",[t._v("ScaleType")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#scaletype"}},[a("code",[t._v("ScaleType")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-49"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-49"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3622",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3622"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"scaleoptionsbytype"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#scaleoptionsbytype"}},[t._v("#")]),t._v(" ScaleOptionsByType")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ScaleOptionsByType")]),t._v("<"),a("code",[t._v("TScale")]),t._v('>: { [key in ScaleType]: Object & ScaleTypeRegistry[key]["options"] }['),a("code",[t._v("TScale")]),t._v("]")]),t._v(" "),a("h4",{attrs:{id:"type-parameters-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-17"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TScale")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#scaletype"}},[a("code",[t._v("ScaleType")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#scaletype"}},[a("code",[t._v("ScaleType")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-50"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-50"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3617",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3617"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"scaletype"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#scaletype"}},[t._v("#")]),t._v(" ScaleType")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ScaleType")]),t._v(": keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ScaleTypeRegistry.html"}},[a("code",[t._v("ScaleTypeRegistry")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-51"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-51"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3511",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3511"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"scattercontroller"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#scattercontroller"}},[t._v("#")]),t._v(" ScatterController")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ScatterController")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#linecontroller"}},[a("code",[t._v("LineController")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-52"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-52"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L234",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:234"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"scattercontrollerchartoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#scattercontrollerchartoptions"}},[t._v("#")]),t._v(" ScatterControllerChartOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ScatterControllerChartOptions")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/LineControllerChartOptions.html"}},[a("code",[t._v("LineControllerChartOptions")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-53"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-53"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L232",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:232"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"scattercontrollerdatasetoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#scattercontrollerdatasetoptions"}},[t._v("#")]),t._v(" ScatterControllerDatasetOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ScatterControllerDatasetOptions")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/LineControllerDatasetOptions.html"}},[a("code",[t._v("LineControllerDatasetOptions")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-54"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-54"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L225",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:225"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"scriptable"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#scriptable"}},[t._v("#")]),t._v(" Scriptable")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("Scriptable")]),t._v("<"),a("code",[t._v("T")]),t._v(", "),a("code",[t._v("TContext")]),t._v(">: "),a("code",[t._v("T")]),t._v(" | ("),a("code",[t._v("ctx")]),t._v(": "),a("code",[t._v("TContext")]),t._v(", "),a("code",[t._v("options")]),t._v(": "),a("code",[t._v("AnyObject")]),t._v(") => "),a("code",[t._v("T")]),t._v(" | "),a("code",[t._v("undefined")])]),t._v(" "),a("h4",{attrs:{id:"type-parameters-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-18"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("T")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TContext")])])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-55"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-55"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L39",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:39"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"scriptableandarray"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#scriptableandarray"}},[t._v("#")]),t._v(" ScriptableAndArray")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ScriptableAndArray")]),t._v("<"),a("code",[t._v("T")]),t._v(", "),a("code",[t._v("TContext")]),t._v(">: readonly "),a("code",[t._v("T")]),t._v("[] | "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("code",[t._v("T")]),t._v(", "),a("code",[t._v("TContext")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"type-parameters-19"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-19"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("T")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TContext")])])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-56"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-56"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L42",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:42"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"scriptableandarrayoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#scriptableandarrayoptions"}},[t._v("#")]),t._v(" ScriptableAndArrayOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ScriptableAndArrayOptions")]),t._v("<"),a("code",[t._v("T")]),t._v(", "),a("code",[t._v("TContext")]),t._v(">: { [P in keyof T]: ScriptableAndArray<T[P], TContext> }")]),t._v(" "),a("h4",{attrs:{id:"type-parameters-20"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-20"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("T")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TContext")])])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-57"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-57"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L43",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:43"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"scriptableandscriptableoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#scriptableandscriptableoptions"}},[t._v("#")]),t._v(" ScriptableAndScriptableOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ScriptableAndScriptableOptions")]),t._v("<"),a("code",[t._v("T")]),t._v(", "),a("code",[t._v("TContext")]),t._v(">: "),a("RouterLink",{attrs:{to:"/api/#scriptable"}},[a("code",[t._v("Scriptable")])]),t._v("<"),a("code",[t._v("T")]),t._v(", "),a("code",[t._v("TContext")]),t._v("> | "),a("RouterLink",{attrs:{to:"/api/#scriptableoptions"}},[a("code",[t._v("ScriptableOptions")])]),t._v("<"),a("code",[t._v("T")]),t._v(", "),a("code",[t._v("TContext")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"type-parameters-21"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-21"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("T")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TContext")])])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-58"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-58"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L41",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:41"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"scriptableoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#scriptableoptions"}},[t._v("#")]),t._v(" ScriptableOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("ScriptableOptions")]),t._v("<"),a("code",[t._v("T")]),t._v(", "),a("code",[t._v("TContext")]),t._v(">: { [P in keyof T]: Scriptable<T[P], TContext> }")]),t._v(" "),a("h4",{attrs:{id:"type-parameters-22"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-22"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("T")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TContext")])])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-59"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-59"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L40",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:40"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"textalign"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#textalign"}},[t._v("#")]),t._v(" TextAlign")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("TextAlign")]),t._v(": "),a("code",[t._v('"left"')]),t._v(" | "),a("code",[t._v('"center"')]),t._v(" | "),a("code",[t._v('"right"')])]),t._v(" "),a("h4",{attrs:{id:"defined-in-60"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-60"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1681",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1681"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"timescaleoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#timescaleoptions"}},[t._v("#")]),t._v(" TimeScaleOptions")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("TimeScaleOptions")]),t._v(": "),a("code",[t._v("Omit")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/CartesianScaleOptions.html"}},[a("code",[t._v("CartesianScaleOptions")])]),t._v(", "),a("code",[t._v('"min"')]),t._v(" | "),a("code",[t._v('"max"')]),t._v("> & { "),a("code",[t._v("adapters")]),t._v(": { "),a("code",[t._v("date")]),t._v(": "),a("code",[t._v("unknown")]),t._v("  } ; "),a("code",[t._v("bounds")]),t._v(": "),a("code",[t._v('"ticks"')]),t._v(" | "),a("code",[t._v('"data"')]),t._v(" ; "),a("code",[t._v("max")]),t._v(": "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("min")]),t._v(": "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("offsetAfterAutoskip")]),t._v(": "),a("code",[t._v("boolean")]),t._v(" ; "),a("code",[t._v("suggestedMax")]),t._v(": "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("suggestedMin")]),t._v(": "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("ticks")]),t._v(": { "),a("code",[t._v("source")]),t._v(": "),a("code",[t._v('"labels"')]),t._v(" | "),a("code",[t._v('"auto"')]),t._v(" | "),a("code",[t._v('"data"')]),t._v("  } ; "),a("code",[t._v("time")]),t._v(": { "),a("code",[t._v("displayFormats")]),t._v(": { [key: string]: "),a("code",[t._v("string")]),t._v(";  } ; "),a("code",[t._v("isoWeekday")]),t._v(": "),a("code",[t._v("boolean")]),t._v(" | "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("minUnit")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#timeunit"}},[a("code",[t._v("TimeUnit")])]),t._v(" ; "),a("code",[t._v("parser")]),t._v(": "),a("code",[t._v("string")]),t._v(" | ("),a("code",[t._v("v")]),t._v(": "),a("code",[t._v("unknown")]),t._v(") => "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("round")]),t._v(": "),a("code",[t._v("false")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/#timeunit"}},[a("code",[t._v("TimeUnit")])]),t._v(" ; "),a("code",[t._v("stepSize")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("tooltipFormat")]),t._v(": "),a("code",[t._v("string")]),t._v(" ; "),a("code",[t._v("unit")]),t._v(": "),a("code",[t._v("false")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/#timeunit"}},[a("code",[t._v("TimeUnit")])]),t._v("  }  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-61"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-61"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3226",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3226"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"timeseriesscale"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#timeseriesscale"}},[t._v("#")]),t._v(" TimeSeriesScale")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("TimeSeriesScale")]),t._v("<"),a("code",[t._v("O")]),t._v(">: "),a("RouterLink",{attrs:{to:"/api/#timescale"}},[a("code",[t._v("TimeScale")])]),t._v("<"),a("code",[t._v("O")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"type-parameters-23"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-23"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("O")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#timescaleoptions"}},[a("code",[t._v("TimeScaleOptions")])]),t._v(" = "),a("RouterLink",{attrs:{to:"/api/#timescaleoptions"}},[a("code",[t._v("TimeScaleOptions")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-62"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-62"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3322",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3322"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"timeunit"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#timeunit"}},[t._v("#")]),t._v(" TimeUnit")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("TimeUnit")]),t._v(": "),a("code",[t._v('"millisecond"')]),t._v(" | "),a("code",[t._v('"second"')]),t._v(" | "),a("code",[t._v('"minute"')]),t._v(" | "),a("code",[t._v('"hour"')]),t._v(" | "),a("code",[t._v('"day"')]),t._v(" | "),a("code",[t._v('"week"')]),t._v(" | "),a("code",[t._v('"month"')]),t._v(" | "),a("code",[t._v('"quarter"')]),t._v(" | "),a("code",[t._v('"year"')])]),t._v(" "),a("h4",{attrs:{id:"defined-in-63"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-63"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/adapters.d.ts#L3",target:"_blank",rel:"noopener noreferrer"}},[t._v("adapters.d.ts:3"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"tooltippositioner"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#tooltippositioner"}},[t._v("#")]),t._v(" TooltipPositioner")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("TooltipPositioner")]),t._v(": keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/TooltipPositionerMap.html"}},[a("code",[t._v("TooltipPositionerMap")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-64"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-64"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2546",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2546"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"tooltippositionerfunction"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#tooltippositionerfunction"}},[t._v("#")]),t._v(" TooltipPositionerFunction")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("TooltipPositionerFunction")]),t._v("<"),a("code",[t._v("TType")]),t._v(">: ("),a("code",[t._v("this")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/TooltipModel.html"}},[a("code",[t._v("TooltipModel")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">, "),a("code",[t._v("items")]),t._v(": readonly "),a("RouterLink",{attrs:{to:"/api/interfaces/ActiveElement.html"}},[a("code",[t._v("ActiveElement")])]),t._v("[], "),a("code",[t._v("eventPosition")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/Point.html"}},[a("code",[t._v("Point")])]),t._v(") => "),a("RouterLink",{attrs:{to:"/api/interfaces/TooltipPosition.html"}},[a("code",[t._v("TooltipPosition")])]),t._v(" | "),a("code",[t._v("false")])],1),t._v(" "),a("h4",{attrs:{id:"type-parameters-24"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-24"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-6"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("p",[t._v("▸ ("),a("code",[t._v("this")]),t._v(", "),a("code",[t._v("items")]),t._v(", "),a("code",[t._v("eventPosition")]),t._v("): "),a("RouterLink",{attrs:{to:"/api/interfaces/TooltipPosition.html"}},[a("code",[t._v("TooltipPosition")])]),t._v(" | "),a("code",[t._v("false")])],1),t._v(" "),a("h5",{attrs:{id:"parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-2"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("this")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/TooltipModel.html"}},[a("code",[t._v("TooltipModel")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("items")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("readonly "),a("RouterLink",{attrs:{to:"/api/interfaces/ActiveElement.html"}},[a("code",[t._v("ActiveElement")])]),t._v("[]")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("eventPosition")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/Point.html"}},[a("code",[t._v("Point")])])],1)])])]),t._v(" "),a("h5",{attrs:{id:"returns-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/TooltipPosition.html"}},[a("code",[t._v("TooltipPosition")])]),t._v(" | "),a("code",[t._v("false")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-65"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-65"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2535",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2535"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"tooltipxalignment"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#tooltipxalignment"}},[t._v("#")]),t._v(" TooltipXAlignment")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("TooltipXAlignment")]),t._v(": "),a("code",[t._v('"left"')]),t._v(" | "),a("code",[t._v('"center"')]),t._v(" | "),a("code",[t._v('"right"')])]),t._v(" "),a("h4",{attrs:{id:"defined-in-66"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-66"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2444",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2444"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"tooltipyalignment"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#tooltipyalignment"}},[t._v("#")]),t._v(" TooltipYAlignment")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("TooltipYAlignment")]),t._v(": "),a("code",[t._v('"top"')]),t._v(" | "),a("code",[t._v('"center"')]),t._v(" | "),a("code",[t._v('"bottom"')])]),t._v(" "),a("h4",{attrs:{id:"defined-in-67"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-67"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2445",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2445"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"transitionspec"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#transitionspec"}},[t._v("#")]),t._v(" TransitionSpec")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("TransitionSpec")]),t._v("<"),a("code",[t._v("TType")]),t._v(">: "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-parameters-25"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-25"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-7"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("animation")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#animationspec"}},[a("code",[t._v("AnimationSpec")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("animations")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#animationsspec"}},[a("code",[t._v("AnimationsSpec")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-68"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-68"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1630",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1630"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"transitionsspec"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#transitionsspec"}},[t._v("#")]),t._v(" TransitionsSpec")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("TransitionsSpec")]),t._v("<"),a("code",[t._v("TType")]),t._v(">: "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-parameters-26"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-26"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"index-signature-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#index-signature-2"}},[t._v("#")]),t._v(" Index signature")]),t._v(" "),a("p",[t._v("▪ [mode: "),a("code",[t._v("string")]),t._v("]: "),a("RouterLink",{attrs:{to:"/api/#transitionspec"}},[a("code",[t._v("TransitionSpec")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-69"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-69"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1635",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1635"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"updatemode"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#updatemode"}},[t._v("#")]),t._v(" UpdateMode")]),t._v(" "),a("p",[t._v("Ƭ "),a("strong",[t._v("UpdateMode")]),t._v(": keyof typeof "),a("RouterLink",{attrs:{to:"/api/enums/UpdateModeEnum.html"}},[a("code",[t._v("UpdateModeEnum")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-70"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-70"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L571",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:571"),a("OutboundLink")],1)]),t._v(" "),a("h2",{attrs:{id:"variables"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#variables"}},[t._v("#")]),t._v(" Variables")]),t._v(" "),a("h3",{attrs:{id:"arcelement"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#arcelement"}},[t._v("#")]),t._v(" ArcElement")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("ArcElement")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#arcelement"}},[a("code",[t._v("ArcElement")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/ArcProps.html"}},[a("code",[t._v("ArcProps")])]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/ArcOptions.html"}},[a("code",[t._v("ArcOptions")])]),t._v(">  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-71"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-71"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1765",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1765"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"barcontroller-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#barcontroller-2"}},[t._v("#")]),t._v(" BarController")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("BarController")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#barcontroller"}},[a("code",[t._v("BarController")])]),t._v("  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-72"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-72"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L146",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:146"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"barelement"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#barelement"}},[t._v("#")]),t._v(" BarElement")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("BarElement")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#barelement"}},[a("code",[t._v("BarElement")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/BarProps.html"}},[a("code",[t._v("BarProps")])]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/BarOptions.html"}},[a("code",[t._v("BarOptions")])]),t._v(">  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-73"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-73"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2034",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2034"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"bubblecontroller-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#bubblecontroller-2"}},[t._v("#")]),t._v(" BubbleController")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("BubbleController")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#bubblecontroller"}},[a("code",[t._v("BubbleController")])]),t._v("  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-74"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-74"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L174",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:174"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"categoryscale-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#categoryscale-2"}},[t._v("#")]),t._v(" CategoryScale")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("CategoryScale")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#categoryscale"}},[a("code",[t._v("CategoryScale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#categoryscaleoptions"}},[a("code",[t._v("CategoryScaleOptions")])]),t._v(">  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-75"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-75"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3148",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3148"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"decimation"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#decimation"}},[t._v("#")]),t._v(" Decimation")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("Decimation")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/Plugin.html"}},[a("code",[t._v("Plugin")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-76"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-76"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2110",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2110"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"doughnutcontroller"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#doughnutcontroller"}},[t._v("#")]),t._v(" DoughnutController")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("DoughnutController")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#doughnutcontroller"}},[a("code",[t._v("DoughnutController")])]),t._v("  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-77"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-77"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L343",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:343"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"element"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#element"}},[t._v("#")]),t._v(" Element")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("Element")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-8"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("prototype")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v(">")],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-78"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-78"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L14",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:14"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"filler"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#filler"}},[t._v("#")]),t._v(" Filler")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("Filler")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/Plugin.html"}},[a("code",[t._v("Plugin")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-79"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-79"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2132",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2132"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"interaction"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interaction"}},[t._v("#")]),t._v(" Interaction")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("Interaction")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-9"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("modes")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/InteractionModeMap.html"}},[a("code",[t._v("InteractionModeMap")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("evaluateInteractionItems")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("("),a("code",[t._v("chart")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">, "),a("code",[t._v("axis")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#interactionaxis"}},[a("code",[t._v("InteractionAxis")])]),t._v(", "),a("code",[t._v("position")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/Point.html"}},[a("code",[t._v("Point")])]),t._v(", "),a("code",[t._v("handler")]),t._v(": ("),a("code",[t._v("element")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("AnyObject")]),t._v("> & "),a("RouterLink",{attrs:{to:"/api/interfaces/VisualElement.html"}},[a("code",[t._v("VisualElement")])]),t._v(", "),a("code",[t._v("datasetIndex")]),t._v(": "),a("code",[t._v("number")]),t._v(", "),a("code",[t._v("index")]),t._v(": "),a("code",[t._v("number")]),t._v(") => "),a("code",[t._v("void")]),t._v(", "),a("code",[t._v("intersect?")]),t._v(": "),a("code",[t._v("boolean")]),t._v(") => "),a("RouterLink",{attrs:{to:"/api/interfaces/InteractionItem.html"}},[a("code",[t._v("InteractionItem")])]),t._v("[]")],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-80"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-80"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L754",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:754"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"legend"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#legend"}},[t._v("#")]),t._v(" Legend")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("Legend")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/Plugin.html"}},[a("code",[t._v("Plugin")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-81"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-81"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2162",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2162"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"linecontroller-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#linecontroller-2"}},[t._v("#")]),t._v(" LineController")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("LineController")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#linecontroller"}},[a("code",[t._v("LineController")])]),t._v("  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-82"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-82"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L220",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:220"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"lineelement"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#lineelement"}},[t._v("#")]),t._v(" LineElement")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("LineElement")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#lineelement"}},[a("code",[t._v("LineElement")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/LineProps.html"}},[a("code",[t._v("LineProps")])]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/LineOptions.html"}},[a("code",[t._v("LineOptions")])]),t._v(">  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-83"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-83"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1855",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1855"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"linearscale-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#linearscale-2"}},[t._v("#")]),t._v(" LinearScale")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("LinearScale")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#linearscale"}},[a("code",[t._v("LinearScale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#linearscaleoptions"}},[a("code",[t._v("LinearScaleOptions")])]),t._v(">  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-84"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-84"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3197",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3197"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"logarithmicscale-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#logarithmicscale-2"}},[t._v("#")]),t._v(" LogarithmicScale")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("LogarithmicScale")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#logarithmicscale"}},[a("code",[t._v("LogarithmicScale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#logarithmicscaleoptions"}},[a("code",[t._v("LogarithmicScaleOptions")])]),t._v(">  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-85"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-85"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3221",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3221"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"piecontroller-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#piecontroller-2"}},[t._v("#")]),t._v(" PieController")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("PieController")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#doughnutcontroller"}},[a("code",[t._v("DoughnutController")])]),t._v("  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-86"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-86"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L360",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:360"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"pointelement"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#pointelement"}},[t._v("#")]),t._v(" PointElement")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("PointElement")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#pointelement"}},[a("code",[t._v("PointElement")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/PointProps.html"}},[a("code",[t._v("PointProps")])]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/PointOptions.html"}},[a("code",[t._v("PointOptions")])]),t._v(">  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-87"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-87"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1972",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1972"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"polarareacontroller"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#polarareacontroller"}},[t._v("#")]),t._v(" PolarAreaController")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("PolarAreaController")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#polarareacontroller"}},[a("code",[t._v("PolarAreaController")])]),t._v("  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-88"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-88"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L388",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:388"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"radarcontroller-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#radarcontroller-2"}},[t._v("#")]),t._v(" RadarController")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("RadarController")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#radarcontroller"}},[a("code",[t._v("RadarController")])]),t._v("  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-89"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-89"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L421",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:421"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"radiallinearscale"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#radiallinearscale"}},[t._v("#")]),t._v(" RadialLinearScale")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("RadialLinearScale")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#radiallinearscale"}},[a("code",[t._v("RadialLinearScale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#radiallinearscaleoptions"}},[a("code",[t._v("RadialLinearScaleOptions")])]),t._v(">  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-90"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-90"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3479",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3479"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"scattercontroller-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#scattercontroller-2"}},[t._v("#")]),t._v(" ScatterController")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("ScatterController")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#linecontroller"}},[a("code",[t._v("LineController")])]),t._v("  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-91"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-91"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L235",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:235"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"subtitle"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#subtitle"}},[t._v("#")]),t._v(" SubTitle")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("SubTitle")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/Plugin.html"}},[a("code",[t._v("Plugin")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-92"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-92"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2402",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2402"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"ticks"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#ticks"}},[t._v("#")]),t._v(" Ticks")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("Ticks")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-10"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("formatters")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Object")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("formatters.logarithmic")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("[object Object]")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("formatters.numeric")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("[object Object]")])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("formatters.values")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("[object Object]")])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-93"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-93"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1356",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1356"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"timescale"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#timescale"}},[t._v("#")]),t._v(" TimeScale")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("TimeScale")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#timescale"}},[a("code",[t._v("TimeScale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#timescaleoptions"}},[a("code",[t._v("TimeScaleOptions")])]),t._v(">  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-94"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-94"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3317",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3317"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"timeseriesscale-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#timeseriesscale-2"}},[t._v("#")]),t._v(" TimeSeriesScale")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("TimeSeriesScale")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartComponent.html"}},[a("code",[t._v("ChartComponent")])]),t._v(" & { "),a("code",[t._v("prototype")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#timeseriesscale"}},[a("code",[t._v("TimeSeriesScale")])]),t._v("<"),a("RouterLink",{attrs:{to:"/api/#timescaleoptions"}},[a("code",[t._v("TimeScaleOptions")])]),t._v(">  }")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-95"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-95"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3323",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:3323"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"title"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#title"}},[t._v("#")]),t._v(" Title")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("Title")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/Plugin.html"}},[a("code",[t._v("Plugin")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-96"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-96"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2403",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2403"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"tooltip"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#tooltip"}},[t._v("#")]),t._v(" Tooltip")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("Tooltip")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#tooltip"}},[a("code",[t._v("Tooltip")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-97"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-97"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2552",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2552"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"adapters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#adapters"}},[t._v("#")]),t._v(" _adapters")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("_adapters")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-11"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("_date")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/DateAdapter.html"}},[a("code",[t._v("DateAdapter")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-98"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-98"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/adapters.d.ts#L68",target:"_blank",rel:"noopener noreferrer"}},[t._v("adapters.d.ts:68"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"defaults"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defaults"}},[t._v("#")]),t._v(" defaults")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("defaults")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/Defaults.html"}},[a("code",[t._v("Defaults")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-99"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-99"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L701",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:701"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"layouts"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#layouts"}},[t._v("#")]),t._v(" layouts")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("layouts")]),t._v(": "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"type-declaration-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-declaration-12"}},[t._v("#")]),t._v(" Type declaration")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("addBox")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("("),a("code",[t._v("chart")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">, "),a("code",[t._v("item")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/LayoutItem.html"}},[a("code",[t._v("LayoutItem")])]),t._v(") => "),a("code",[t._v("void")])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("configure")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("("),a("code",[t._v("chart")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">, "),a("code",[t._v("item")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/LayoutItem.html"}},[a("code",[t._v("LayoutItem")])]),t._v(", "),a("code",[t._v("options")]),t._v(": { "),a("code",[t._v("fullSize?")]),t._v(": "),a("code",[t._v("number")]),t._v(" ; "),a("code",[t._v("position?")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#layoutposition"}},[a("code",[t._v("LayoutPosition")])]),t._v(" ; "),a("code",[t._v("weight?")]),t._v(": "),a("code",[t._v("number")]),t._v("  }) => "),a("code",[t._v("void")])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("removeBox")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("("),a("code",[t._v("chart")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">, "),a("code",[t._v("layoutItem")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/LayoutItem.html"}},[a("code",[t._v("LayoutItem")])]),t._v(") => "),a("code",[t._v("void")])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("update")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("("),a("code",[t._v("chart")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">, "),a("code",[t._v("width")]),t._v(": "),a("code",[t._v("number")]),t._v(", "),a("code",[t._v("height")]),t._v(": "),a("code",[t._v("number")]),t._v(") => "),a("code",[t._v("void")])],1)])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-100"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-100"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L769",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:769"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"registerables"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#registerables"}},[t._v("#")]),t._v(" registerables")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("registerables")]),t._v(": readonly "),a("RouterLink",{attrs:{to:"/api/#chartcomponentlike"}},[a("code",[t._v("ChartComponentLike")])]),t._v("[]")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-101"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-101"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L552",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:552"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"registry"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#registry"}},[t._v("#")]),t._v(" registry")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("registry")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/Registry.html"}},[a("code",[t._v("Registry")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-102"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-102"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1139",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1139"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=i.exports}}]);