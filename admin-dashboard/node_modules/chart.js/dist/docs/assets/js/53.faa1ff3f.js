(window.webpackJsonp=window.webpackJsonp||[]).push([[53],{384:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-controllerdatasetoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-controllerdatasetoptions"}},[t._v("#")]),t._v(" Interface: ControllerDatasetOptions")]),t._v(" "),a("h2",{attrs:{id:"hierarchy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),a("ul",[a("li",[a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/ParsingOptions.html"}},[a("code",[t._v("ParsingOptions")])])],1),t._v(" "),a("p",[t._v("↳ "),a("strong",[a("code",[t._v("ControllerDatasetOptions")])])]),t._v(" "),a("p",[t._v("↳↳ "),a("RouterLink",{attrs:{to:"/api/interfaces/BarControllerDatasetOptions.html"}},[a("code",[t._v("BarControllerDatasetOptions")])])],1),t._v(" "),a("p",[t._v("↳↳ "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleControllerDatasetOptions.html"}},[a("code",[t._v("BubbleControllerDatasetOptions")])])],1),t._v(" "),a("p",[t._v("↳↳ "),a("RouterLink",{attrs:{to:"/api/interfaces/LineControllerDatasetOptions.html"}},[a("code",[t._v("LineControllerDatasetOptions")])])],1),t._v(" "),a("p",[t._v("↳↳ "),a("RouterLink",{attrs:{to:"/api/interfaces/DoughnutControllerDatasetOptions.html"}},[a("code",[t._v("DoughnutControllerDatasetOptions")])])],1),t._v(" "),a("p",[t._v("↳↳ "),a("RouterLink",{attrs:{to:"/api/interfaces/RadarControllerDatasetOptions.html"}},[a("code",[t._v("RadarControllerDatasetOptions")])])],1)])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"clip"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#clip"}},[t._v("#")]),t._v(" clip")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("clip")]),t._v(": "),a("code",[t._v("number")]),t._v(" | "),a("code",[t._v("false")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[a("code",[t._v("ChartArea")])])],1),t._v(" "),a("p",[t._v("How to clip relative to chartArea. Positive value allows overflow, negative value clips that many pixels inside chartArea. 0 = clip at chartArea. Clipping can also be configured per side: clip: {left: 5, top: false, right: -2, bottom: 0}")]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L70",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:70"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"hidden"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hidden"}},[t._v("#")]),t._v(" hidden")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("hidden")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("Configures the visibility state of the dataset. Set it to true, to hide the dataset from the chart.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" false")]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L88",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:88"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"indexaxis"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#indexaxis"}},[t._v("#")]),t._v(" indexAxis")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("indexAxis")]),t._v(": "),a("code",[t._v('"x"')]),t._v(" | "),a("code",[t._v('"y"')])]),t._v(" "),a("p",[t._v("The base axis of the chart. 'x' for vertical charts and 'y' for horizontal charts.")]),t._v(" "),a("p",[a("strong",[a("code",[t._v("default")])]),t._v(" 'x'")]),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L66",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:66"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"label"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#label"}},[t._v("#")]),t._v(" label")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("label")]),t._v(": "),a("code",[t._v("string")])]),t._v(" "),a("p",[t._v("The label for the dataset which appears in the legend and tooltips.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L74",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:74"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"normalized"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#normalized"}},[t._v("#")]),t._v(" normalized")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("normalized")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("p",[t._v("Chart.js is fastest if you provide data with indices that are unique, sorted, and consistent across datasets and provide the normalized: true option to let Chart.js know that you have done so.")]),t._v(" "),a("h4",{attrs:{id:"inherited-from"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/ParsingOptions.html"}},[t._v("ParsingOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/ParsingOptions.html#normalized"}},[t._v("normalized")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L58",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:58"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"order"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#order"}},[t._v("#")]),t._v(" order")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("order")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("p",[t._v("The drawing order of dataset. Also affects order for stacking, tooltip and legend.")]),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L78",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:78"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"parsing"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parsing"}},[t._v("#")]),t._v(" parsing")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("parsing")]),t._v(": "),a("code",[t._v("false")]),t._v(" | { [key: string]: "),a("code",[t._v("string")]),t._v(";  }")]),t._v(" "),a("p",[t._v("How to parse the dataset. The parsing can be disabled by specifying parsing: false at chart options or dataset. If parsing is disabled, data must be sorted and in the formats the associated chart type and scales use internally.")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/ParsingOptions.html"}},[t._v("ParsingOptions")]),t._v("."),a("RouterLink",{attrs:{to:"/api/interfaces/ParsingOptions.html#parsing"}},[t._v("parsing")])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L49",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:49"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"stack"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#stack"}},[t._v("#")]),t._v(" stack")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("stack")]),t._v(": "),a("code",[t._v("string")])]),t._v(" "),a("p",[t._v("The ID of the group to which this dataset belongs to (when stacked, each group will be a separate stack).")]),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L83",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:83"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);