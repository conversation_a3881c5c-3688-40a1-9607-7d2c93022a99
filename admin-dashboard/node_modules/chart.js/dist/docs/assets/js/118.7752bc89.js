(window.webpackJsonp=window.webpackJsonp||[]).push([[118],{591:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-tooltipcallbacks-ttype-model-item"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-tooltipcallbacks-ttype-model-item"}},[t._v("#")]),t._v(" Interface: TooltipCallbacks<TType, Model, Item>")]),t._v(" "),a("h2",{attrs:{id:"type-parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Model")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/TooltipModel.html"}},[a("code",[t._v("TooltipModel")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Item")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/TooltipItem.html"}},[a("code",[t._v("TooltipItem")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1)])])]),t._v(" "),a("h2",{attrs:{id:"methods"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),a("h3",{attrs:{id:"afterbody"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterbody"}},[t._v("#")]),t._v(" afterBody")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterBody")]),t._v("("),a("code",[t._v("tooltipItems")]),t._v("): "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("tooltipItems")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Item")]),t._v("[]")])])])]),t._v(" "),a("h4",{attrs:{id:"returns"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2564",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2564"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterfooter"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterfooter"}},[t._v("#")]),t._v(" afterFooter")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterFooter")]),t._v("("),a("code",[t._v("tooltipItems")]),t._v("): "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-2"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("tooltipItems")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Item")]),t._v("[]")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2576",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2576"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterlabel"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterlabel"}},[t._v("#")]),t._v(" afterLabel")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterLabel")]),t._v("("),a("code",[t._v("tooltipItem")]),t._v("): "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"parameters-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-3"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("tooltipItem")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Item")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-3"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2568",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2568"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"aftertitle"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#aftertitle"}},[t._v("#")]),t._v(" afterTitle")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("afterTitle")]),t._v("("),a("code",[t._v("tooltipItems")]),t._v("): "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"parameters-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-4"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("tooltipItems")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Item")]),t._v("[]")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-4"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2561",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2561"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforebody"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforebody"}},[t._v("#")]),t._v(" beforeBody")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeBody")]),t._v("("),a("code",[t._v("tooltipItems")]),t._v("): "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"parameters-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-5"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("tooltipItems")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Item")]),t._v("[]")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-5"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2563",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2563"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforefooter"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforefooter"}},[t._v("#")]),t._v(" beforeFooter")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeFooter")]),t._v("("),a("code",[t._v("tooltipItems")]),t._v("): "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"parameters-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-6"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("tooltipItems")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Item")]),t._v("[]")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-6"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2574",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2574"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforelabel"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforelabel"}},[t._v("#")]),t._v(" beforeLabel")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeLabel")]),t._v("("),a("code",[t._v("tooltipItem")]),t._v("): "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"parameters-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-7"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("tooltipItem")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Item")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-7"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2566",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2566"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforetitle"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforetitle"}},[t._v("#")]),t._v(" beforeTitle")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("beforeTitle")]),t._v("("),a("code",[t._v("tooltipItems")]),t._v("): "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"parameters-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-8"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("tooltipItems")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Item")]),t._v("[]")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-8"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2559",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2559"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"footer"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#footer"}},[t._v("#")]),t._v(" footer")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("footer")]),t._v("("),a("code",[t._v("tooltipItems")]),t._v("): "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"parameters-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-9"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("tooltipItems")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Item")]),t._v("[]")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-9"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2575",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2575"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"label"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#label"}},[t._v("#")]),t._v(" label")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("label")]),t._v("("),a("code",[t._v("tooltipItem")]),t._v("): "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"parameters-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-10"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("tooltipItem")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Item")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-10"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2567",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2567"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"labelcolor"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#labelcolor"}},[t._v("#")]),t._v(" labelColor")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("labelColor")]),t._v("("),a("code",[t._v("tooltipItem")]),t._v("): "),a("RouterLink",{attrs:{to:"/api/interfaces/TooltipLabelStyle.html"}},[a("code",[t._v("TooltipLabelStyle")])])],1),t._v(" "),a("h4",{attrs:{id:"parameters-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-11"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("tooltipItem")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Item")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-11"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/TooltipLabelStyle.html"}},[a("code",[t._v("TooltipLabelStyle")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2570",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2570"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"labelpointstyle"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#labelpointstyle"}},[t._v("#")]),t._v(" labelPointStyle")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("labelPointStyle")]),t._v("("),a("code",[t._v("tooltipItem")]),t._v("): "),a("code",[t._v("Object")])]),t._v(" "),a("h4",{attrs:{id:"parameters-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-12"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("tooltipItem")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Item")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-12"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("Object")])]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("pointStyle")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/#pointstyle"}},[a("code",[t._v("PointStyle")])])],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("rotation")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("number")])])])])]),t._v(" "),a("h4",{attrs:{id:"defined-in-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2572",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2572"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"labeltextcolor"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#labeltextcolor"}},[t._v("#")]),t._v(" labelTextColor")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("labelTextColor")]),t._v("("),a("code",[t._v("tooltipItem")]),t._v("): "),a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[t._v("Color")])])],1),t._v(" "),a("h4",{attrs:{id:"parameters-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-13"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("tooltipItem")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Item")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-13"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[t._v("Color")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2571",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2571"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"title"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#title"}},[t._v("#")]),t._v(" title")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("title")]),t._v("("),a("code",[t._v("tooltipItems")]),t._v("): "),a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"parameters-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-14"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("tooltipItems")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("Item")]),t._v("[]")])])])]),t._v(" "),a("h4",{attrs:{id:"returns-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-14"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("string")]),t._v(" | "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2560",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2560"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);