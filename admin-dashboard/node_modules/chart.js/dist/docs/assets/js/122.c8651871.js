(window.webpackJsonp=window.webpackJsonp||[]).push([[122],{450:function(t,e,r){"use strict";r.r(e);var a=r(6),o=Object(a.a)({},(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[r("h1",{attrs:{id:"interface-tooltipoptions-ttype"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-tooltipoptions-ttype"}},[t._v("#")]),t._v(" Interface: TooltipOptions<TType>")]),t._v(" "),r("h2",{attrs:{id:"type-parameters"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),r("table",[r("thead",[r("tr",[r("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),r("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),r("tbody",[r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("TType")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),r("RouterLink",{attrs:{to:"/api/#charttype"}},[r("code",[t._v("ChartType")])]),t._v(" = "),r("RouterLink",{attrs:{to:"/api/#charttype"}},[r("code",[t._v("ChartType")])])],1)])])]),t._v(" "),r("h2",{attrs:{id:"hierarchy"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),r("ul",[r("li",[r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/CoreInteractionOptions.html"}},[r("code",[t._v("CoreInteractionOptions")])])],1),t._v(" "),r("p",[t._v("↳ "),r("strong",[r("code",[t._v("TooltipOptions")])])])])]),t._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),r("h3",{attrs:{id:"animation"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#animation"}},[t._v("#")]),t._v(" animation")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("animation")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#animationspec"}},[r("code",[t._v("AnimationSpec")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">")],1),t._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2784",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2784"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"animations"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#animations"}},[t._v("#")]),t._v(" animations")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("animations")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#animationsspec"}},[r("code",[t._v("AnimationsSpec")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">")],1),t._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2785",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2785"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"axis"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#axis"}},[t._v("#")]),t._v(" axis")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("axis")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#interactionaxis"}},[r("code",[t._v("InteractionAxis")])])],1),t._v(" "),r("p",[t._v("Defines which directions are used in calculating distances. Defaults to 'x' for 'index' mode and 'xy' in dataset and 'nearest' modes.")]),t._v(" "),r("h4",{attrs:{id:"inherited-from"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/CoreInteractionOptions.html"}},[t._v("CoreInteractionOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/CoreInteractionOptions.html#axis"}},[t._v("axis")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1439",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1439"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"backgroundcolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#backgroundcolor"}},[t._v("#")]),t._v(" backgroundColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("backgroundColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Background color of the tooltip.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'rgba(0, 0, 0, 0.8)'")]),t._v(" "),r("h4",{attrs:{id:"defined-in-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2642",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2642"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"bodyalign"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#bodyalign"}},[t._v("#")]),t._v(" bodyAlign")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("bodyAlign")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#textalign"}},[r("code",[t._v("TextAlign")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Horizontal alignment of the body text lines.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'left'")]),t._v(" "),r("h4",{attrs:{id:"defined-in-5"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2692",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2692"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"bodycolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#bodycolor"}},[t._v("#")]),t._v(" bodyColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("bodyColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Color of body")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" '#fff'")]),t._v(" "),r("h4",{attrs:{id:"defined-in-6"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2682",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2682"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"bodyfont"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#bodyfont"}},[t._v("#")]),t._v(" bodyFont")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("bodyFont")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandscriptableoptions"}},[r("code",[t._v("ScriptableAndScriptableOptions")])]),t._v("<"),r("code",[t._v("Partial")]),t._v("<"),r("RouterLink",{attrs:{to:"/api/interfaces/FontSpec.html"}},[r("code",[t._v("FontSpec")])]),t._v(">, "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("See Fonts.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" {}")]),t._v(" "),r("h4",{attrs:{id:"defined-in-7"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2687",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2687"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"bodyspacing"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#bodyspacing"}},[t._v("#")]),t._v(" bodySpacing")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("bodySpacing")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Spacing to add to top and bottom of each tooltip item.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 2")]),t._v(" "),r("h4",{attrs:{id:"defined-in-8"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2677",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2677"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"bordercolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#bordercolor"}},[t._v("#")]),t._v(" borderColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Color of the border.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'rgba(0, 0, 0, 0)'")]),t._v(" "),r("h4",{attrs:{id:"defined-in-9"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2767",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2767"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderwidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderwidth"}},[t._v("#")]),t._v(" borderWidth")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderWidth")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Size of the border.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),r("h4",{attrs:{id:"defined-in-10"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2772",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2772"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"boxheight"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#boxheight"}},[t._v("#")]),t._v(" boxHeight")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("boxHeight")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Height of the color box if displayColors is true.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" bodyFont.size")]),t._v(" "),r("h4",{attrs:{id:"defined-in-11"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2757",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2757"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"boxpadding"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#boxpadding"}},[t._v("#")]),t._v(" boxPadding")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("boxPadding")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Padding between the color box and the text.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 1")]),t._v(" "),r("h4",{attrs:{id:"defined-in-12"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2647",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2647"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"boxwidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#boxwidth"}},[t._v("#")]),t._v(" boxWidth")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("boxWidth")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Width of the color box if displayColors is true.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" bodyFont.size")]),t._v(" "),r("h4",{attrs:{id:"defined-in-13"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2752",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2752"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"callbacks"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#callbacks"}},[t._v("#")]),t._v(" callbacks")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("callbacks")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/interfaces/TooltipCallbacks.html"}},[r("code",[t._v("TooltipCallbacks")])]),t._v("<"),r("code",[t._v("TType")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/TooltipModel.html"}},[r("code",[t._v("TooltipModel")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">, "),r("RouterLink",{attrs:{to:"/api/interfaces/TooltipItem.html"}},[r("code",[t._v("TooltipItem")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"defined-in-14"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2786",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2786"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"caretpadding"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#caretpadding"}},[t._v("#")]),t._v(" caretPadding")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("caretPadding")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Extra distance to move the end of the tooltip arrow away from the tooltip point.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 2")]),t._v(" "),r("h4",{attrs:{id:"defined-in-15"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-15"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2727",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2727"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"caretsize"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#caretsize"}},[t._v("#")]),t._v(" caretSize")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("caretSize")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Size, in px, of the tooltip arrow.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 5")]),t._v(" "),r("h4",{attrs:{id:"defined-in-16"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-16"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2732",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2732"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"cornerradius"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#cornerradius"}},[t._v("#")]),t._v(" cornerRadius")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("cornerRadius")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/BorderRadius.html"}},[r("code",[t._v("BorderRadius")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Radius of tooltip corner curves.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 6")]),t._v(" "),r("h4",{attrs:{id:"defined-in-17"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-17"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2737",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2737"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"displaycolors"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#displaycolors"}},[t._v("#")]),t._v(" displayColors")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("displayColors")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("boolean")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("If true, color boxes are shown in the tooltip.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" true")]),t._v(" "),r("h4",{attrs:{id:"defined-in-18"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-18"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2747",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2747"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"enabled"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#enabled"}},[t._v("#")]),t._v(" enabled")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("enabled")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("boolean")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Are on-canvas tooltips enabled?")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" true")]),t._v(" "),r("h4",{attrs:{id:"defined-in-19"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-19"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2615",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2615"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"footeralign"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#footeralign"}},[t._v("#")]),t._v(" footerAlign")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("footerAlign")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#textalign"}},[r("code",[t._v("TextAlign")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Horizontal alignment of the footer text lines.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'left'")]),t._v(" "),r("h4",{attrs:{id:"defined-in-20"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-20"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2717",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2717"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"footercolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#footercolor"}},[t._v("#")]),t._v(" footerColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("footerColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Color of footer")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" '#fff'")]),t._v(" "),r("h4",{attrs:{id:"defined-in-21"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-21"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2707",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2707"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"footerfont"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#footerfont"}},[t._v("#")]),t._v(" footerFont")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("footerFont")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandscriptableoptions"}},[r("code",[t._v("ScriptableAndScriptableOptions")])]),t._v("<"),r("code",[t._v("Partial")]),t._v("<"),r("RouterLink",{attrs:{to:"/api/interfaces/FontSpec.html"}},[r("code",[t._v("FontSpec")])]),t._v(">, "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("See Fonts")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" {weight: 'bold'}")]),t._v(" "),r("h4",{attrs:{id:"defined-in-22"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-22"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2712",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2712"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"footermargintop"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#footermargintop"}},[t._v("#")]),t._v(" footerMarginTop")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("footerMarginTop")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Margin to add before drawing the footer.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 6")]),t._v(" "),r("h4",{attrs:{id:"defined-in-23"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-23"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2702",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2702"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"footerspacing"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#footerspacing"}},[t._v("#")]),t._v(" footerSpacing")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("footerSpacing")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Spacing to add to top and bottom of each footer line.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 2")]),t._v(" "),r("h4",{attrs:{id:"defined-in-24"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-24"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2697",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2697"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"includeinvisible"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#includeinvisible"}},[t._v("#")]),t._v(" includeInvisible")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("includeInvisible")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[t._v("if true, the invisible points that are outside of the chart area will also be included when evaluating interactions.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" false")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/CoreInteractionOptions.html"}},[t._v("CoreInteractionOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/CoreInteractionOptions.html#includeinvisible"}},[t._v("includeInvisible")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-25"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-25"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1445",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1445"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"intersect"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#intersect"}},[t._v("#")]),t._v(" intersect")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("intersect")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[t._v("if true, the hover mode only applies when the mouse position intersects an item on the chart.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" true")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-3"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/CoreInteractionOptions.html"}},[t._v("CoreInteractionOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/CoreInteractionOptions.html#intersect"}},[t._v("intersect")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-26"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-26"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1434",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1434"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"mode"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#mode"}},[t._v("#")]),t._v(" mode")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("mode")]),t._v(": keyof "),r("RouterLink",{attrs:{to:"/api/interfaces/InteractionModeMap.html"}},[r("code",[t._v("InteractionModeMap")])])],1),t._v(" "),r("p",[t._v("Sets which elements appear in the tooltip. See Interaction Modes for details.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'nearest'")]),t._v(" "),r("h4",{attrs:{id:"inherited-from-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-4"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),r("p",[r("RouterLink",{attrs:{to:"/api/interfaces/CoreInteractionOptions.html"}},[t._v("CoreInteractionOptions")]),t._v("."),r("RouterLink",{attrs:{to:"/api/interfaces/CoreInteractionOptions.html#mode"}},[t._v("mode")])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-27"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-27"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1429",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1429"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"multikeybackground"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#multikeybackground"}},[t._v("#")]),t._v(" multiKeyBackground")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("multiKeyBackground")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Color to draw behind the colored boxes when multiple items are in the tooltip.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" '#fff'")]),t._v(" "),r("h4",{attrs:{id:"defined-in-28"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-28"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2742",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2742"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"padding"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#padding"}},[t._v("#")]),t._v(" padding")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("padding")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/ChartArea.html"}},[r("code",[t._v("ChartArea")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Padding to add to the tooltip")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 6")]),t._v(" "),r("h4",{attrs:{id:"defined-in-29"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-29"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2722",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2722"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"position"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#position"}},[t._v("#")]),t._v(" position")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("position")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<keyof "),r("RouterLink",{attrs:{to:"/api/interfaces/TooltipPositionerMap.html"}},[r("code",[t._v("TooltipPositionerMap")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("The mode for positioning the tooltip")]),t._v(" "),r("h4",{attrs:{id:"defined-in-30"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-30"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2623",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2623"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"rtl"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#rtl"}},[t._v("#")]),t._v(" rtl")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("rtl")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("boolean")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("true for rendering the legends from right to left.")]),t._v(" "),r("h4",{attrs:{id:"defined-in-31"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-31"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2776",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2776"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"textdirection"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#textdirection"}},[t._v("#")]),t._v(" textDirection")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("textDirection")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("string")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("This will force the text direction 'rtl' or 'ltr on the canvas for rendering the tooltips, regardless of the css specified on the canvas")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" canvas's default")]),t._v(" "),r("h4",{attrs:{id:"defined-in-32"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-32"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2782",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2782"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"titlealign"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#titlealign"}},[t._v("#")]),t._v(" titleAlign")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("titleAlign")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#textalign"}},[r("code",[t._v("TextAlign")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Horizontal alignment of the title text lines.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'left'")]),t._v(" "),r("h4",{attrs:{id:"defined-in-33"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-33"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2672",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2672"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"titlecolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#titlecolor"}},[t._v("#")]),t._v(" titleColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("titleColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Color of title")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" '#fff'")]),t._v(" "),r("h4",{attrs:{id:"defined-in-34"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-34"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2652",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2652"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"titlefont"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#titlefont"}},[t._v("#")]),t._v(" titleFont")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("titleFont")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandscriptableoptions"}},[r("code",[t._v("ScriptableAndScriptableOptions")])]),t._v("<"),r("code",[t._v("Partial")]),t._v("<"),r("RouterLink",{attrs:{to:"/api/interfaces/FontSpec.html"}},[r("code",[t._v("FontSpec")])]),t._v(">, "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("See Fonts")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" {weight: 'bold'}")]),t._v(" "),r("h4",{attrs:{id:"defined-in-35"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-35"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2657",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2657"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"titlemarginbottom"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#titlemarginbottom"}},[t._v("#")]),t._v(" titleMarginBottom")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("titleMarginBottom")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Margin to add on bottom of title section.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 6")]),t._v(" "),r("h4",{attrs:{id:"defined-in-36"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-36"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2667",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2667"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"titlespacing"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#titlespacing"}},[t._v("#")]),t._v(" titleSpacing")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("titleSpacing")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Spacing to add to top and bottom of each title line.")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 2")]),t._v(" "),r("h4",{attrs:{id:"defined-in-37"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-37"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2662",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2662"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"usepointstyle"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#usepointstyle"}},[t._v("#")]),t._v(" usePointStyle")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("usePointStyle")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("boolean")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Use the corresponding point style (from dataset options) instead of color boxes, ex: star, triangle etc. (size is based on the minimum value between boxWidth and boxHeight)")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" false")]),t._v(" "),r("h4",{attrs:{id:"defined-in-38"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-38"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2762",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2762"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"xalign"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#xalign"}},[t._v("#")]),t._v(" xAlign")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("xAlign")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#tooltipxalignment"}},[r("code",[t._v("TooltipXAlignment")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("p",[t._v("Override the tooltip alignment calculations")]),t._v(" "),r("h4",{attrs:{id:"defined-in-39"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-39"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2628",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2628"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"yalign"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#yalign"}},[t._v("#")]),t._v(" yAlign")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("yAlign")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#tooltipyalignment"}},[r("code",[t._v("TooltipYAlignment")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableTooltipContext.html"}},[r("code",[t._v("ScriptableTooltipContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"defined-in-40"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-40"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2629",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2629"),r("OutboundLink")],1)]),t._v(" "),r("h2",{attrs:{id:"methods"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),r("h3",{attrs:{id:"external"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#external"}},[t._v("#")]),t._v(" external")]),t._v(" "),r("p",[t._v("▸ "),r("strong",[t._v("external")]),t._v("("),r("code",[t._v("args")]),t._v("): "),r("code",[t._v("void")])]),t._v(" "),r("p",[t._v("See external tooltip section.")]),t._v(" "),r("h4",{attrs:{id:"parameters"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),r("table",[r("thead",[r("tr",[r("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),r("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),r("tbody",[r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("args")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("Object")])])]),t._v(" "),r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("args.chart")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[r("code",[t._v("Chart")])]),t._v("<keyof "),r("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[r("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),r("code",[t._v("number")]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[r("code",[t._v("ScatterDataPoint")])]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[r("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),r("code",[t._v("unknown")]),t._v(">")],1)]),t._v(" "),r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("args.tooltip")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("RouterLink",{attrs:{to:"/api/interfaces/TooltipModel.html"}},[r("code",[t._v("TooltipModel")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">")],1)])])]),t._v(" "),r("h4",{attrs:{id:"returns"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),r("p",[r("code",[t._v("void")])]),t._v(" "),r("h4",{attrs:{id:"defined-in-41"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-41"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2619",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2619"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"filter"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#filter"}},[t._v("#")]),t._v(" filter")]),t._v(" "),r("p",[t._v("▸ "),r("strong",[t._v("filter")]),t._v("("),r("code",[t._v("e")]),t._v(", "),r("code",[t._v("index")]),t._v(", "),r("code",[t._v("array")]),t._v(", "),r("code",[t._v("data")]),t._v("): "),r("code",[t._v("boolean")])]),t._v(" "),r("h4",{attrs:{id:"parameters-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#parameters-2"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),r("table",[r("thead",[r("tr",[r("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),r("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),r("tbody",[r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("e")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("RouterLink",{attrs:{to:"/api/interfaces/TooltipItem.html"}},[r("code",[t._v("TooltipItem")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">")],1)]),t._v(" "),r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("index")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("number")])])]),t._v(" "),r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("array")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("RouterLink",{attrs:{to:"/api/interfaces/TooltipItem.html"}},[r("code",[t._v("TooltipItem")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">[]")],1)]),t._v(" "),r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("data")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("RouterLink",{attrs:{to:"/api/interfaces/ChartData.html"}},[r("code",[t._v("ChartData")])]),t._v("<keyof "),r("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[r("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),r("code",[t._v("number")]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[r("code",[t._v("ScatterDataPoint")])]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[r("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),r("code",[t._v("unknown")]),t._v(">")],1)])])]),t._v(" "),r("h4",{attrs:{id:"returns-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),r("p",[r("code",[t._v("boolean")])]),t._v(" "),r("h4",{attrs:{id:"defined-in-42"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-42"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2636",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2636"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"itemsort"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#itemsort"}},[t._v("#")]),t._v(" itemSort")]),t._v(" "),r("p",[t._v("▸ "),r("strong",[t._v("itemSort")]),t._v("("),r("code",[t._v("a")]),t._v(", "),r("code",[t._v("b")]),t._v(", "),r("code",[t._v("data")]),t._v("): "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("Sort tooltip items.")]),t._v(" "),r("h4",{attrs:{id:"parameters-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#parameters-3"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),r("table",[r("thead",[r("tr",[r("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),r("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),r("tbody",[r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("a")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("RouterLink",{attrs:{to:"/api/interfaces/TooltipItem.html"}},[r("code",[t._v("TooltipItem")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">")],1)]),t._v(" "),r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("b")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("RouterLink",{attrs:{to:"/api/interfaces/TooltipItem.html"}},[r("code",[t._v("TooltipItem")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">")],1)]),t._v(" "),r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("data")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[r("RouterLink",{attrs:{to:"/api/interfaces/ChartData.html"}},[r("code",[t._v("ChartData")])]),t._v("<keyof "),r("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[r("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),r("code",[t._v("number")]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[r("code",[t._v("ScatterDataPoint")])]),t._v(" | "),r("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[r("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),r("code",[t._v("unknown")]),t._v(">")],1)])])]),t._v(" "),r("h4",{attrs:{id:"returns-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#returns-3"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),r("p",[r("code",[t._v("number")])]),t._v(" "),r("h4",{attrs:{id:"defined-in-43"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-43"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2634",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2634"),r("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=o.exports}}]);