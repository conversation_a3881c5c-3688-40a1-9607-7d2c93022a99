(window.webpackJsonp=window.webpackJsonp||[]).push([[94],{425:function(t,e,r){"use strict";r.r(e);var a=r(6),n=Object(a.a)({},(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[r("h1",{attrs:{id:"interface-pointprefixedoptions"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-pointprefixedoptions"}},[t._v("#")]),t._v(" Interface: PointPrefixedOptions")]),t._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),r("h3",{attrs:{id:"pointbackgroundcolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointbackgroundcolor"}},[t._v("#")]),t._v(" pointBackgroundColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointBackgroundColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])])],1),t._v(" "),r("p",[t._v("The fill color for points.")]),t._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1919",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1919"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointbordercolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointbordercolor"}},[t._v("#")]),t._v(" pointBorderColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointBorderColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])])],1),t._v(" "),r("p",[t._v("The border color for points.")]),t._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1923",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1923"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointborderwidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointborderwidth"}},[t._v("#")]),t._v(" pointBorderWidth")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointBorderWidth")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("The width of the point border in pixels.")]),t._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1927",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1927"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointhitradius"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointhitradius"}},[t._v("#")]),t._v(" pointHitRadius")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointHitRadius")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("The pixel size of the non-displayed point that reacts to mouse events.")]),t._v(" "),r("h4",{attrs:{id:"defined-in-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1931",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1931"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointradius"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointradius"}},[t._v("#")]),t._v(" pointRadius")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointRadius")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("The radius of the point shape. If set to 0, the point is not rendered.")]),t._v(" "),r("h4",{attrs:{id:"defined-in-5"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1935",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1935"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointrotation"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointrotation"}},[t._v("#")]),t._v(" pointRotation")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointRotation")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[t._v("The rotation of the point in degrees.")]),t._v(" "),r("h4",{attrs:{id:"defined-in-6"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1939",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1939"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"pointstyle"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#pointstyle"}},[t._v("#")]),t._v(" pointStyle")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("pointStyle")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#pointstyle"}},[r("code",[t._v("PointStyle")])])],1),t._v(" "),r("p",[t._v("Style of the point.")]),t._v(" "),r("h4",{attrs:{id:"defined-in-7"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1943",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1943"),r("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=n.exports}}]);