(window.webpackJsonp=window.webpackJsonp||[]).push([[141],{470:function(t,e,d){"use strict";d.r(e);var a=d(6),r=Object(a.a)({},(function(){var t=this,e=t.$createElement,d=t._self._c||e;return d("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[d("h1",{attrs:{id:"styling"}},[d("a",{staticClass:"header-anchor",attrs:{href:"#styling"}},[t._v("#")]),t._v(" Styling")]),t._v(" "),d("p",[t._v("There are a number of options to allow styling an axis. There are settings to control "),d("a",{attrs:{href:"#grid-line-configuration"}},[t._v("grid lines")]),t._v(" and "),d("a",{attrs:{href:"#tick-configuration"}},[t._v("ticks")]),t._v(".")]),t._v(" "),d("h2",{attrs:{id:"grid-line-configuration"}},[d("a",{staticClass:"header-anchor",attrs:{href:"#grid-line-configuration"}},[t._v("#")]),t._v(" Grid Line Configuration")]),t._v(" "),d("p",[t._v("Namespace: "),d("code",[t._v("options.scales[scaleId].grid")]),t._v(", it defines options for the grid lines that run perpendicular to the axis.")]),t._v(" "),d("table",[d("thead",[d("tr",[d("th",[t._v("Name")]),t._v(" "),d("th",[t._v("Type")]),t._v(" "),d("th",{staticStyle:{"text-align":"center"}},[t._v("Scriptable")]),t._v(" "),d("th",{staticStyle:{"text-align":"center"}},[t._v("Indexable")]),t._v(" "),d("th",[t._v("Default")]),t._v(" "),d("th",[t._v("Description")])])]),t._v(" "),d("tbody",[d("tr",[d("td",[d("code",[t._v("borderColor")])]),t._v(" "),d("td",[d("RouterLink",{attrs:{to:"/general/colors.html"}},[d("code",[t._v("Color")])])],1),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("Chart.defaults.borderColor")])]),t._v(" "),d("td",[t._v("The color of the border line.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("borderWidth")])]),t._v(" "),d("td",[d("code",[t._v("number")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("1")])]),t._v(" "),d("td",[t._v("The width of the border line.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("borderDash")])]),t._v(" "),d("td",[d("code",[t._v("number[]")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("[]")])]),t._v(" "),d("td",[t._v("Length and spacing of dashes on grid lines. See "),d("a",{attrs:{href:"https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/setLineDash",target:"_blank",rel:"noopener noreferrer"}},[t._v("MDN"),d("OutboundLink")],1),t._v(".")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("borderDashOffset")])]),t._v(" "),d("td",[d("code",[t._v("number")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("0.0")])]),t._v(" "),d("td",[t._v("Offset for line dashes. See "),d("a",{attrs:{href:"https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineDashOffset",target:"_blank",rel:"noopener noreferrer"}},[t._v("MDN"),d("OutboundLink")],1),t._v(".")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("circular")])]),t._v(" "),d("td",[d("code",[t._v("boolean")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("false")])]),t._v(" "),d("td",[t._v("If true, gridlines are circular (on radar and polar area charts only).")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("color")])]),t._v(" "),d("td",[d("RouterLink",{attrs:{to:"/general/colors.html"}},[d("code",[t._v("Color")])])],1),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td",[d("code",[t._v("Chart.defaults.borderColor")])]),t._v(" "),d("td",[t._v("The color of the grid lines. If specified as an array, the first color applies to the first grid line, the second to the second grid line, and so on.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("display")])]),t._v(" "),d("td",[d("code",[t._v("boolean")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("true")])]),t._v(" "),d("td",[t._v("If false, do not display grid lines for this axis.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("drawBorder")])]),t._v(" "),d("td",[d("code",[t._v("boolean")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("true")])]),t._v(" "),d("td",[t._v("If true, draw a border at the edge between the axis and the chart area.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("drawOnChartArea")])]),t._v(" "),d("td",[d("code",[t._v("boolean")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("true")])]),t._v(" "),d("td",[t._v("If true, draw lines on the chart area inside the axis lines. This is useful when there are multiple axes and you need to control which grid lines are drawn.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("drawTicks")])]),t._v(" "),d("td",[d("code",[t._v("boolean")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("true")])]),t._v(" "),d("td",[t._v("If true, draw lines beside the ticks in the axis area beside the chart.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("lineWidth")])]),t._v(" "),d("td",[d("code",[t._v("number")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td",[d("code",[t._v("1")])]),t._v(" "),d("td",[t._v("Stroke width of grid lines.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("offset")])]),t._v(" "),d("td",[d("code",[t._v("boolean")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("false")])]),t._v(" "),d("td",[t._v("If true, grid lines will be shifted to be between labels. This is set to "),d("code",[t._v("true")]),t._v(" for a bar chart by default.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("tickBorderDash")])]),t._v(" "),d("td",[d("code",[t._v("number[]")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td"),t._v(" "),d("td",[t._v("Length and spacing of the tick mark line. If not set, defaults to the grid line "),d("code",[t._v("borderDash")]),t._v(" value.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("tickBorderDashOffset")])]),t._v(" "),d("td",[d("code",[t._v("number")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td"),t._v(" "),d("td",[t._v("Offset for the line dash of the tick mark. If unset, defaults to the grid line "),d("code",[t._v("borderDashOffset")]),t._v(" value")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("tickColor")])]),t._v(" "),d("td",[d("RouterLink",{attrs:{to:"/general/colors.html"}},[d("code",[t._v("Color")])])],1),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td"),t._v(" "),d("td",[t._v("Color of the tick line. If unset, defaults to the grid line color.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("tickLength")])]),t._v(" "),d("td",[d("code",[t._v("number")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("8")])]),t._v(" "),d("td",[t._v("Length in pixels that the grid lines will draw into the axis area.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("tickWidth")])]),t._v(" "),d("td",[d("code",[t._v("number")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td"),t._v(" "),d("td",[t._v("Width of the tick mark in pixels. If unset, defaults to the grid line width.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("z")])]),t._v(" "),d("td",[d("code",[t._v("number")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("0")])]),t._v(" "),d("td",[t._v("z-index of gridline layer. Values <= 0 are drawn under datasets, > 0 on top.")])])])]),t._v(" "),d("p",[t._v("The scriptable context is described in "),d("RouterLink",{attrs:{to:"/general/options.html#tick"}},[t._v("Options")]),t._v(" section.")],1),t._v(" "),d("h2",{attrs:{id:"tick-configuration"}},[d("a",{staticClass:"header-anchor",attrs:{href:"#tick-configuration"}},[t._v("#")]),t._v(" Tick Configuration")]),t._v(" "),d("h3",{attrs:{id:"common-tick-options-to-all-axes"}},[d("a",{staticClass:"header-anchor",attrs:{href:"#common-tick-options-to-all-axes"}},[t._v("#")]),t._v(" Common tick options to all axes")]),t._v(" "),d("p",[t._v("Namespace: "),d("code",[t._v("options.scales[scaleId].ticks")])]),t._v(" "),d("table",[d("thead",[d("tr",[d("th",[t._v("Name")]),t._v(" "),d("th",[t._v("Type")]),t._v(" "),d("th",{staticStyle:{"text-align":"center"}},[t._v("Scriptable")]),t._v(" "),d("th",[t._v("Default")]),t._v(" "),d("th",[t._v("Description")])])]),t._v(" "),d("tbody",[d("tr",[d("td",[d("code",[t._v("backdropColor")])]),t._v(" "),d("td",[d("RouterLink",{attrs:{to:"/general/colors.html"}},[d("code",[t._v("Color")])])],1),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td",[d("code",[t._v("'rgba(255, 255, 255, 0.75)'")])]),t._v(" "),d("td",[t._v("Color of label backdrops.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("backdropPadding")])]),t._v(" "),d("td",[d("RouterLink",{attrs:{to:"/general/padding.html"}},[d("code",[t._v("Padding")])])],1),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("2")])]),t._v(" "),d("td",[t._v("Padding of label backdrop.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("callback")])]),t._v(" "),d("td",[d("code",[t._v("function")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td"),t._v(" "),d("td",[t._v("Returns the string representation of the tick value as it should be displayed on the chart. See "),d("RouterLink",{attrs:{to:"/axes/labelling.html#creating-custom-tick-formats"}},[t._v("callback")]),t._v(".")],1)]),t._v(" "),d("tr",[d("td",[d("code",[t._v("display")])]),t._v(" "),d("td",[d("code",[t._v("boolean")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("true")])]),t._v(" "),d("td",[t._v("If true, show tick labels.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("color")])]),t._v(" "),d("td",[d("RouterLink",{attrs:{to:"/general/colors.html"}},[d("code",[t._v("Color")])])],1),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td",[d("code",[t._v("Chart.defaults.color")])]),t._v(" "),d("td",[t._v("Color of ticks.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("font")])]),t._v(" "),d("td",[d("code",[t._v("Font")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td",[d("code",[t._v("Chart.defaults.font")])]),t._v(" "),d("td",[t._v("See "),d("RouterLink",{attrs:{to:"/general/fonts.html"}},[t._v("Fonts")])],1)]),t._v(" "),d("tr",[d("td",[d("code",[t._v("major")])]),t._v(" "),d("td",[d("code",[t._v("object")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("{}")])]),t._v(" "),d("td",[d("RouterLink",{attrs:{to:"/axes/styling.html#major-tick-configuration"}},[t._v("Major ticks configuration")]),t._v(".")],1)]),t._v(" "),d("tr",[d("td",[d("code",[t._v("padding")])]),t._v(" "),d("td",[d("code",[t._v("number")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("3")])]),t._v(" "),d("td",[t._v("Sets the offset of the tick labels from the axis")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("showLabelBackdrop")])]),t._v(" "),d("td",[d("code",[t._v("boolean")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td",[d("code",[t._v("true")]),t._v(" for radial scale, "),d("code",[t._v("false")]),t._v(" otherwise")]),t._v(" "),d("td",[t._v("If true, draw a background behind the tick labels.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("textStrokeColor")])]),t._v(" "),d("td",[d("RouterLink",{attrs:{to:"/general/colors.html"}},[d("code",[t._v("Color")])])],1),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td",[t._v("``")]),t._v(" "),d("td",[t._v("The color of the stroke around the text.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("textStrokeWidth")])]),t._v(" "),d("td",[d("code",[t._v("number")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}},[t._v("Yes")]),t._v(" "),d("td",[d("code",[t._v("0")])]),t._v(" "),d("td",[t._v("Stroke width around the text.")])]),t._v(" "),d("tr",[d("td",[d("code",[t._v("z")])]),t._v(" "),d("td",[d("code",[t._v("number")])]),t._v(" "),d("td",{staticStyle:{"text-align":"center"}}),t._v(" "),d("td",[d("code",[t._v("0")])]),t._v(" "),d("td",[t._v("z-index of tick layer. Useful when ticks are drawn on chart area. Values <= 0 are drawn under datasets, > 0 on top.")])])])]),t._v(" "),d("p",[t._v("The scriptable context is described in "),d("RouterLink",{attrs:{to:"/general/options.html#tick"}},[t._v("Options")]),t._v(" section.")],1),t._v(" "),d("h2",{attrs:{id:"major-tick-configuration"}},[d("a",{staticClass:"header-anchor",attrs:{href:"#major-tick-configuration"}},[t._v("#")]),t._v(" Major Tick Configuration")]),t._v(" "),d("p",[t._v("Namespace: "),d("code",[t._v("options.scales[scaleId].ticks.major")]),t._v(", it defines options for the major tick marks that are generated by the axis.")]),t._v(" "),d("table",[d("thead",[d("tr",[d("th",[t._v("Name")]),t._v(" "),d("th",[t._v("Type")]),t._v(" "),d("th",[t._v("Default")]),t._v(" "),d("th",[t._v("Description")])])]),t._v(" "),d("tbody",[d("tr",[d("td",[d("code",[t._v("enabled")])]),t._v(" "),d("td",[d("code",[t._v("boolean")])]),t._v(" "),d("td",[d("code",[t._v("false")])]),t._v(" "),d("td",[t._v("If true, major ticks are generated. A major tick will affect autoskipping and "),d("code",[t._v("major")]),t._v(" will be defined on ticks in the scriptable options context.")])])])])])}),[],!1,null,null,null);e.default=r.exports}}]);