(window.webpackJsonp=window.webpackJsonp||[]).push([[73],{404:function(t,e,n){"use strict";n.r(e);var r=n(6),a=Object(r.a)({},(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[n("h1",{attrs:{id:"interface-interactionmodemap"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#interface-interactionmodemap"}},[t._v("#")]),t._v(" Interface: InteractionModeMap")]),t._v(" "),n("h2",{attrs:{id:"properties"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),n("h3",{attrs:{id:"dataset"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#dataset"}},[t._v("#")]),t._v(" dataset")]),t._v(" "),n("p",[t._v("• "),n("strong",[t._v("dataset")]),t._v(": "),n("RouterLink",{attrs:{to:"/api/#interactionmodefunction"}},[n("code",[t._v("InteractionModeFunction")])])],1),t._v(" "),n("p",[t._v("Returns items in the same dataset. If the options.intersect parameter is true, we only return items if we intersect something\nIf the options.intersect is false, we find the nearest item and return the items in that dataset")]),t._v(" "),n("h4",{attrs:{id:"defined-in"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),n("p",[n("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L732",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:732"),n("OutboundLink")],1)]),t._v(" "),n("hr"),t._v(" "),n("h3",{attrs:{id:"index"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#index"}},[t._v("#")]),t._v(" index")]),t._v(" "),n("p",[t._v("• "),n("strong",[t._v("index")]),t._v(": "),n("RouterLink",{attrs:{to:"/api/#interactionmodefunction"}},[n("code",[t._v("InteractionModeFunction")])])],1),t._v(" "),n("p",[t._v("Returns items at the same index. If the options.intersect parameter is true, we only return items if we intersect something\nIf the options.intersect mode is false, we find the nearest item and return the items at the same index as that item")]),t._v(" "),n("h4",{attrs:{id:"defined-in-2"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),n("p",[n("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L726",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:726"),n("OutboundLink")],1)]),t._v(" "),n("hr"),t._v(" "),n("h3",{attrs:{id:"nearest"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#nearest"}},[t._v("#")]),t._v(" nearest")]),t._v(" "),n("p",[t._v("• "),n("strong",[t._v("nearest")]),t._v(": "),n("RouterLink",{attrs:{to:"/api/#interactionmodefunction"}},[n("code",[t._v("InteractionModeFunction")])])],1),t._v(" "),n("p",[t._v("nearest mode returns the element closest to the point")]),t._v(" "),n("h4",{attrs:{id:"defined-in-3"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),n("p",[n("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L741",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:741"),n("OutboundLink")],1)]),t._v(" "),n("hr"),t._v(" "),n("h3",{attrs:{id:"point"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#point"}},[t._v("#")]),t._v(" point")]),t._v(" "),n("p",[t._v("• "),n("strong",[t._v("point")]),t._v(": "),n("RouterLink",{attrs:{to:"/api/#interactionmodefunction"}},[n("code",[t._v("InteractionModeFunction")])])],1),t._v(" "),n("p",[t._v("Point mode returns all elements that hit test based on the event position\nof the event")]),t._v(" "),n("h4",{attrs:{id:"defined-in-4"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),n("p",[n("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L737",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:737"),n("OutboundLink")],1)]),t._v(" "),n("hr"),t._v(" "),n("h3",{attrs:{id:"x"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#x"}},[t._v("#")]),t._v(" x")]),t._v(" "),n("p",[t._v("• "),n("strong",[t._v("x")]),t._v(": "),n("RouterLink",{attrs:{to:"/api/#interactionmodefunction"}},[n("code",[t._v("InteractionModeFunction")])])],1),t._v(" "),n("p",[t._v("x mode returns the elements that hit-test at the current x coordinate")]),t._v(" "),n("h4",{attrs:{id:"defined-in-5"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),n("p",[n("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L745",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:745"),n("OutboundLink")],1)]),t._v(" "),n("hr"),t._v(" "),n("h3",{attrs:{id:"y"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#y"}},[t._v("#")]),t._v(" y")]),t._v(" "),n("p",[t._v("• "),n("strong",[t._v("y")]),t._v(": "),n("RouterLink",{attrs:{to:"/api/#interactionmodefunction"}},[n("code",[t._v("InteractionModeFunction")])])],1),t._v(" "),n("p",[t._v("y mode returns the elements that hit-test at the current y coordinate")]),t._v(" "),n("h4",{attrs:{id:"defined-in-6"}},[n("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),n("p",[n("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L749",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:749"),n("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=a.exports}}]);