(window.webpackJsonp=window.webpackJsonp||[]).push([[84],{415:function(t,e,r){"use strict";r.r(e);var s=r(6),a=Object(s.a)({},(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[r("h1",{attrs:{id:"interface-lineprops"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-lineprops"}},[t._v("#")]),t._v(" Interface: LineProps")]),t._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),r("h3",{attrs:{id:"points"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#points"}},[t._v("#")]),t._v(" points")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("points")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/interfaces/Point.html"}},[r("code",[t._v("Point")])]),t._v("[]")],1),t._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1771",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:1771"),r("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=a.exports}}]);