(window.webpackJsonp=window.webpackJsonp||[]).push([[111],{442:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-scriptabletooltipcontext-ttype"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-scriptabletooltipcontext-ttype"}},[t._v("#")]),t._v(" Interface: ScriptableTooltipContext<TType>")]),t._v(" "),a("h2",{attrs:{id:"type-parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"chart"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#chart"}},[t._v("#")]),t._v(" chart")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("chart")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<"),a("code",[t._v("TType")]),t._v(", "),a("code",[t._v("DistributiveArray")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v("["),a("code",[t._v("TType")]),t._v("]["),a("code",[t._v('"defaultDataPoint"')]),t._v("]>, "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2605",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2605"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"tooltip"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#tooltip"}},[t._v("#")]),t._v(" tooltip")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("tooltip")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/TooltipModel.html"}},[a("code",[t._v("TooltipModel")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2606",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2606"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"tooltipitems"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#tooltipitems"}},[t._v("#")]),t._v(" tooltipItems")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("tooltipItems")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/TooltipItem.html"}},[a("code",[t._v("TooltipItem")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">[]")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2607",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2607"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);