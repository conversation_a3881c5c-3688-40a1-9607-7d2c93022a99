(window.webpackJsonp=window.webpackJsonp||[]).push([[29],{360:function(t,e,r){"use strict";r.r(e);var a=r(6),s=Object(a.a)({},(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[r("h1",{attrs:{id:"interface-barcontrollerchartoptions"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-barcontrollerchartoptions"}},[t._v("#")]),t._v(" Interface: BarControllerChartOptions")]),t._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),r("h3",{attrs:{id:"skipnull"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#skipnull"}},[t._v("#")]),t._v(" skipNull")]),t._v(" "),r("p",[t._v("• "),r("code",[t._v("Optional")]),t._v(" "),r("strong",[t._v("skipNull")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[t._v("Should null or undefined values be omitted from drawing")]),t._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L142",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:142"),r("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);