(window.webpackJsonp=window.webpackJsonp||[]).push([[123],{452:function(t,e,r){"use strict";r.r(e);var a=r(6),n=Object(a.a)({},(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[r("h1",{attrs:{id:"interface-tooltipposition"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-tooltipposition"}},[t._v("#")]),t._v(" Interface: TooltipPosition")]),t._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),r("h3",{attrs:{id:"x"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#x"}},[t._v("#")]),t._v(" x")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("x")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2529",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2529"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"xalign"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#xalign"}},[t._v("#")]),t._v(" xAlign")]),t._v(" "),r("p",[t._v("• "),r("code",[t._v("Optional")]),t._v(" "),r("strong",[t._v("xAlign")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#tooltipxalignment"}},[r("code",[t._v("TooltipXAlignment")])])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2531",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2531"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"y"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#y"}},[t._v("#")]),t._v(" y")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("y")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2530",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2530"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"yalign"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#yalign"}},[t._v("#")]),t._v(" yAlign")]),t._v(" "),r("p",[t._v("• "),r("code",[t._v("Optional")]),t._v(" "),r("strong",[t._v("yAlign")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#tooltipyalignment"}},[r("code",[t._v("TooltipYAlignment")])])],1),t._v(" "),r("h4",{attrs:{id:"defined-in-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2532",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2532"),r("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=n.exports}}]);