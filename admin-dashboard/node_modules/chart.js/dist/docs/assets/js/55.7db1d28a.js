(window.webpackJsonp=window.webpackJsonp||[]).push([[55],{386:function(e,t,a){"use strict";a.r(t);var r=a(6),s=Object(r.a)({},(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ContentSlotsDistributor",{attrs:{"slot-key":e.$parent.slotKey}},[a("h1",{attrs:{id:"interface-coreinteractionoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-coreinteractionoptions"}},[e._v("#")]),e._v(" Interface: CoreInteractionOptions")]),e._v(" "),a("h2",{attrs:{id:"hierarchy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[e._v("#")]),e._v(" Hierarchy")]),e._v(" "),a("ul",[a("li",[a("p",[a("strong",[a("code",[e._v("CoreInteractionOptions")])])]),e._v(" "),a("p",[e._v("↳ "),a("RouterLink",{attrs:{to:"/api/interfaces/TooltipOptions.html"}},[a("code",[e._v("TooltipOptions")])])],1)])]),e._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[e._v("#")]),e._v(" Properties")]),e._v(" "),a("h3",{attrs:{id:"axis"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#axis"}},[e._v("#")]),e._v(" axis")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("axis")]),e._v(": "),a("RouterLink",{attrs:{to:"/api/#interactionaxis"}},[a("code",[e._v("InteractionAxis")])])],1),e._v(" "),a("p",[e._v("Defines which directions are used in calculating distances. Defaults to 'x' for 'index' mode and 'xy' in dataset and 'nearest' modes.")]),e._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1439",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1439"),a("OutboundLink")],1)]),e._v(" "),a("hr"),e._v(" "),a("h3",{attrs:{id:"includeinvisible"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#includeinvisible"}},[e._v("#")]),e._v(" includeInvisible")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("includeInvisible")]),e._v(": "),a("code",[e._v("boolean")])]),e._v(" "),a("p",[e._v("if true, the invisible points that are outside of the chart area will also be included when evaluating interactions.")]),e._v(" "),a("p",[a("strong",[a("code",[e._v("default")])]),e._v(" false")]),e._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1445",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1445"),a("OutboundLink")],1)]),e._v(" "),a("hr"),e._v(" "),a("h3",{attrs:{id:"intersect"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#intersect"}},[e._v("#")]),e._v(" intersect")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("intersect")]),e._v(": "),a("code",[e._v("boolean")])]),e._v(" "),a("p",[e._v("if true, the hover mode only applies when the mouse position intersects an item on the chart.")]),e._v(" "),a("p",[a("strong",[a("code",[e._v("default")])]),e._v(" true")]),e._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1434",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1434"),a("OutboundLink")],1)]),e._v(" "),a("hr"),e._v(" "),a("h3",{attrs:{id:"mode"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#mode"}},[e._v("#")]),e._v(" mode")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("mode")]),e._v(": keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/InteractionModeMap.html"}},[a("code",[e._v("InteractionModeMap")])])],1),e._v(" "),a("p",[e._v("Sets which elements appear in the tooltip. See Interaction Modes for details.")]),e._v(" "),a("p",[a("strong",[a("code",[e._v("default")])]),e._v(" 'nearest'")]),e._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1429",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1429"),a("OutboundLink")],1)])])}),[],!1,null,null,null);t.default=s.exports}}]);