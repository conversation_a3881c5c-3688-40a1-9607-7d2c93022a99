(window.webpackJsonp=window.webpackJsonp||[]).push([[66],{397:function(t,e,r){"use strict";r.r(e);var a=r(6),i=Object(a.a)({},(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[r("h1",{attrs:{id:"interface-elementoptionsbytype-ttype"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-elementoptionsbytype-ttype"}},[t._v("#")]),t._v(" Interface: ElementOptionsByType<TType>")]),t._v(" "),r("h2",{attrs:{id:"type-parameters"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),r("table",[r("thead",[r("tr",[r("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),r("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),r("tbody",[r("tr",[r("td",{staticStyle:{"text-align":"left"}},[r("code",[t._v("TType")])]),t._v(" "),r("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),r("RouterLink",{attrs:{to:"/api/#charttype"}},[r("code",[t._v("ChartType")])])],1)])])]),t._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),r("h3",{attrs:{id:"arc"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#arc"}},[t._v("#")]),t._v(" arc")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("arc")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarrayoptions"}},[r("code",[t._v("ScriptableAndArrayOptions")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/interfaces/ArcOptions.html"}},[r("code",[t._v("ArcOptions")])]),t._v(" & "),r("RouterLink",{attrs:{to:"/api/interfaces/ArcHoverOptions.html"}},[r("code",[t._v("ArcHoverOptions")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2040",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2040"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"bar"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#bar"}},[t._v("#")]),t._v(" bar")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("bar")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarrayoptions"}},[r("code",[t._v("ScriptableAndArrayOptions")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/interfaces/BarOptions.html"}},[r("code",[t._v("BarOptions")])]),t._v(" & "),r("RouterLink",{attrs:{to:"/api/interfaces/BarHoverOptions.html"}},[r("code",[t._v("BarHoverOptions")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2041",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2041"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"line"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#line"}},[t._v("#")]),t._v(" line")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("line")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarrayoptions"}},[r("code",[t._v("ScriptableAndArrayOptions")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/interfaces/LineOptions.html"}},[r("code",[t._v("LineOptions")])]),t._v(" & "),r("RouterLink",{attrs:{to:"/api/interfaces/LineHoverOptions.html"}},[r("code",[t._v("LineHoverOptions")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2042",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2042"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"point"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#point"}},[t._v("#")]),t._v(" point")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("point")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarrayoptions"}},[r("code",[t._v("ScriptableAndArrayOptions")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/interfaces/PointOptions.html"}},[r("code",[t._v("PointOptions")])]),t._v(" & "),r("RouterLink",{attrs:{to:"/api/interfaces/PointHoverOptions.html"}},[r("code",[t._v("PointHoverOptions")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableContext.html"}},[r("code",[t._v("ScriptableContext")])]),t._v("<"),r("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),r("h4",{attrs:{id:"defined-in-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2043",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2043"),r("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=i.exports}}]);