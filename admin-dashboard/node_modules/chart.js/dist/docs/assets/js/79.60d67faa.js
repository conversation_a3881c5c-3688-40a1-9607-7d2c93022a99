(window.webpackJsonp=window.webpackJsonp||[]).push([[79],{410:function(e,t,a){"use strict";a.r(t);var n=a(6),r=Object(n.a)({},(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ContentSlotsDistributor",{attrs:{"slot-key":e.$parent.slotKey}},[a("h1",{attrs:{id:"interface-linecontrollerchartoptions"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-linecontrollerchartoptions"}},[e._v("#")]),e._v(" Interface: LineControllerChartOptions")]),e._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[e._v("#")]),e._v(" Properties")]),e._v(" "),a("h3",{attrs:{id:"showline"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#showline"}},[e._v("#")]),e._v(" showLine")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("showLine")]),e._v(": "),a("code",[e._v("boolean")])]),e._v(" "),a("p",[e._v("If false, the lines between points are not drawn.")]),e._v(" "),a("p",[a("strong",[a("code",[e._v("default")])]),e._v(" true")]),e._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L216",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:216"),a("OutboundLink")],1)]),e._v(" "),a("hr"),e._v(" "),a("h3",{attrs:{id:"spangaps"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#spangaps"}},[e._v("#")]),e._v(" spanGaps")]),e._v(" "),a("p",[e._v("• "),a("strong",[e._v("spanGaps")]),e._v(": "),a("code",[e._v("number")]),e._v(" | "),a("code",[e._v("boolean")])]),e._v(" "),a("p",[e._v("If true, lines will be drawn between points with no or null data. If false, points with NaN data will create a break in the line. Can also be a number specifying the maximum gap length to span. The unit of the value depends on the scale used.")]),e._v(" "),a("p",[a("strong",[a("code",[e._v("default")])]),e._v(" false")]),e._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L211",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:211"),a("OutboundLink")],1)])])}),[],!1,null,null,null);t.default=r.exports}}]);