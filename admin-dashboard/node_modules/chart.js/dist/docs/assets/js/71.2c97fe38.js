(window.webpackJsonp=window.webpackJsonp||[]).push([[71],{402:function(t,e,r){"use strict";r.r(e);var a=r(6),s=Object(a.a)({},(function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[r("h1",{attrs:{id:"interface-gridlineoptions"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-gridlineoptions"}},[t._v("#")]),t._v(" Interface: GridLineOptions")]),t._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),r("h3",{attrs:{id:"bordercolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#bordercolor"}},[t._v("#")]),t._v(" borderColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])])],1),t._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2853",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2853"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderdash"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderdash"}},[t._v("#")]),t._v(" borderDash")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderDash")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v("[], "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[r("code",[t._v("ScriptableScaleContext")])]),t._v(">")],1),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" []")]),t._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2866",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2866"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderdashoffset"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderdashoffset"}},[t._v("#")]),t._v(" borderDashOffset")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderDashOffset")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[r("code",[t._v("ScriptableScaleContext")])]),t._v(">")],1),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2870",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2870"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"borderwidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#borderwidth"}},[t._v("#")]),t._v(" borderWidth")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("borderWidth")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("h4",{attrs:{id:"defined-in-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2854",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2854"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"circular"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#circular"}},[t._v("#")]),t._v(" circular")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("circular")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" false")]),t._v(" "),r("h4",{attrs:{id:"defined-in-5"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2858",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2858"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"color"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#color"}},[t._v("#")]),t._v(" color")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("color")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[r("code",[t._v("ScriptableScaleContext")])]),t._v(">")],1),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'rgba(0, 0, 0, 0.1)'")]),t._v(" "),r("h4",{attrs:{id:"defined-in-6"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2862",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2862"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"display"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#display"}},[t._v("#")]),t._v(" display")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("display")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" true")]),t._v(" "),r("h4",{attrs:{id:"defined-in-7"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2852",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2852"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"drawborder"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#drawborder"}},[t._v("#")]),t._v(" drawBorder")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("drawBorder")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" true")]),t._v(" "),r("h4",{attrs:{id:"defined-in-8"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2879",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2879"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"drawonchartarea"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#drawonchartarea"}},[t._v("#")]),t._v(" drawOnChartArea")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("drawOnChartArea")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" true")]),t._v(" "),r("h4",{attrs:{id:"defined-in-9"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2883",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2883"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"drawticks"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#drawticks"}},[t._v("#")]),t._v(" drawTicks")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("drawTicks")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" true")]),t._v(" "),r("h4",{attrs:{id:"defined-in-10"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2887",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2887"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"linewidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#linewidth"}},[t._v("#")]),t._v(" lineWidth")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("lineWidth")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[r("code",[t._v("ScriptableScaleContext")])]),t._v(">")],1),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 1")]),t._v(" "),r("h4",{attrs:{id:"defined-in-11"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2874",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2874"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"offset"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#offset"}},[t._v("#")]),t._v(" offset")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("offset")]),t._v(": "),r("code",[t._v("boolean")])]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" false")]),t._v(" "),r("h4",{attrs:{id:"defined-in-12"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2911",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2911"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"tickborderdash"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#tickborderdash"}},[t._v("#")]),t._v(" tickBorderDash")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("tickBorderDash")]),t._v(": "),r("code",[t._v("number")]),t._v("[]")]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" []")]),t._v(" "),r("h4",{attrs:{id:"defined-in-13"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2891",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2891"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"tickborderdashoffset"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#tickborderdashoffset"}},[t._v("#")]),t._v(" tickBorderDashOffset")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("tickBorderDashOffset")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptable"}},[r("code",[t._v("Scriptable")])]),t._v("<"),r("code",[t._v("number")]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[r("code",[t._v("ScriptableScaleContext")])]),t._v(">")],1),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),r("h4",{attrs:{id:"defined-in-14"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2895",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2895"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"tickcolor"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#tickcolor"}},[t._v("#")]),t._v(" tickColor")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("tickColor")]),t._v(": "),r("RouterLink",{attrs:{to:"/api/#scriptableandarray"}},[r("code",[t._v("ScriptableAndArray")])]),t._v("<"),r("RouterLink",{attrs:{to:"/api/#color"}},[r("code",[t._v("Color")])]),t._v(", "),r("RouterLink",{attrs:{to:"/api/interfaces/ScriptableScaleContext.html"}},[r("code",[t._v("ScriptableScaleContext")])]),t._v(">")],1),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 'rgba(0, 0, 0, 0.1)'")]),t._v(" "),r("h4",{attrs:{id:"defined-in-15"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-15"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2899",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2899"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"ticklength"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#ticklength"}},[t._v("#")]),t._v(" tickLength")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("tickLength")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 10")]),t._v(" "),r("h4",{attrs:{id:"defined-in-16"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-16"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2903",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2903"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"tickwidth"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#tickwidth"}},[t._v("#")]),t._v(" tickWidth")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("tickWidth")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 1")]),t._v(" "),r("h4",{attrs:{id:"defined-in-17"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-17"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2907",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2907"),r("OutboundLink")],1)]),t._v(" "),r("hr"),t._v(" "),r("h3",{attrs:{id:"z"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#z"}},[t._v("#")]),t._v(" z")]),t._v(" "),r("p",[t._v("• "),r("strong",[t._v("z")]),t._v(": "),r("code",[t._v("number")])]),t._v(" "),r("p",[r("strong",[r("code",[t._v("default")])]),t._v(" 0")]),t._v(" "),r("h4",{attrs:{id:"defined-in-18"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-18"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2915",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2915"),r("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);