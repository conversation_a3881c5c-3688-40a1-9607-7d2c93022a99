(window.webpackJsonp=window.webpackJsonp||[]).push([[11],{342:function(t,a,e){"use strict";e.r(a);var r=e(6),s=Object(r.a)({},(function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[e("h1",{attrs:{id:"class-animations"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#class-animations"}},[t._v("#")]),t._v(" Class: Animations")]),t._v(" "),e("h2",{attrs:{id:"constructors"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#constructors"}},[t._v("#")]),t._v(" Constructors")]),t._v(" "),e("h3",{attrs:{id:"constructor"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#constructor"}},[t._v("#")]),t._v(" constructor")]),t._v(" "),e("p",[t._v("• "),e("strong",[t._v("new Animations")]),t._v("("),e("code",[t._v("chart")]),t._v(", "),e("code",[t._v("animations")]),t._v(")")]),t._v(" "),e("h4",{attrs:{id:"parameters"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),e("table",[e("thead",[e("tr",[e("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),e("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),e("tbody",[e("tr",[e("td",{staticStyle:{"text-align":"left"}},[e("code",[t._v("chart")])]),t._v(" "),e("td",{staticStyle:{"text-align":"left"}},[e("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[e("code",[t._v("Chart")])]),t._v("<keyof "),e("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[e("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),e("code",[t._v("number")]),t._v(" | "),e("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[e("code",[t._v("ScatterDataPoint")])]),t._v(" | "),e("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[e("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),e("code",[t._v("unknown")]),t._v(">")],1)]),t._v(" "),e("tr",[e("td",{staticStyle:{"text-align":"left"}},[e("code",[t._v("animations")])]),t._v(" "),e("td",{staticStyle:{"text-align":"left"}},[e("code",[t._v("AnyObject")])])])])]),t._v(" "),e("h4",{attrs:{id:"defined-in"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),e("p",[e("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/animation.d.ts#L30",target:"_blank",rel:"noopener noreferrer"}},[t._v("animation.d.ts:30"),e("OutboundLink")],1)]),t._v(" "),e("h2",{attrs:{id:"methods"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),e("h3",{attrs:{id:"configure"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#configure"}},[t._v("#")]),t._v(" configure")]),t._v(" "),e("p",[t._v("▸ "),e("strong",[t._v("configure")]),t._v("("),e("code",[t._v("animations")]),t._v("): "),e("code",[t._v("void")])]),t._v(" "),e("h4",{attrs:{id:"parameters-2"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#parameters-2"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),e("table",[e("thead",[e("tr",[e("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),e("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),e("tbody",[e("tr",[e("td",{staticStyle:{"text-align":"left"}},[e("code",[t._v("animations")])]),t._v(" "),e("td",{staticStyle:{"text-align":"left"}},[e("code",[t._v("AnyObject")])])])])]),t._v(" "),e("h4",{attrs:{id:"returns"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),e("p",[e("code",[t._v("void")])]),t._v(" "),e("h4",{attrs:{id:"defined-in-2"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),e("p",[e("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/animation.d.ts#L31",target:"_blank",rel:"noopener noreferrer"}},[t._v("animation.d.ts:31"),e("OutboundLink")],1)]),t._v(" "),e("hr"),t._v(" "),e("h3",{attrs:{id:"update"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#update"}},[t._v("#")]),t._v(" update")]),t._v(" "),e("p",[t._v("▸ "),e("strong",[t._v("update")]),t._v("("),e("code",[t._v("target")]),t._v(", "),e("code",[t._v("values")]),t._v("): "),e("code",[t._v("boolean")])]),t._v(" "),e("h4",{attrs:{id:"parameters-3"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#parameters-3"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),e("table",[e("thead",[e("tr",[e("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),e("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),e("tbody",[e("tr",[e("td",{staticStyle:{"text-align":"left"}},[e("code",[t._v("target")])]),t._v(" "),e("td",{staticStyle:{"text-align":"left"}},[e("code",[t._v("AnyObject")])])]),t._v(" "),e("tr",[e("td",{staticStyle:{"text-align":"left"}},[e("code",[t._v("values")])]),t._v(" "),e("td",{staticStyle:{"text-align":"left"}},[e("code",[t._v("AnyObject")])])])])]),t._v(" "),e("h4",{attrs:{id:"returns-2"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),e("p",[e("code",[t._v("boolean")])]),t._v(" "),e("h4",{attrs:{id:"defined-in-3"}},[e("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),e("p",[e("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/animation.d.ts#L32",target:"_blank",rel:"noopener noreferrer"}},[t._v("animation.d.ts:32"),e("OutboundLink")],1)])])}),[],!1,null,null,null);a.default=s.exports}}]);