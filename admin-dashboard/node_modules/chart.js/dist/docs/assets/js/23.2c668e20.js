(window.webpackJsonp=window.webpackJsonp||[]).push([[23],{354:function(t,e,a){"use strict";a.r(e);var r=a(6),n=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-animationevent"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-animationevent"}},[t._v("#")]),t._v(" Interface: AnimationEvent")]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"chart"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#chart"}},[t._v("#")]),t._v(" chart")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("chart")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<keyof "),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v(", ("),a("code",[t._v("number")]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/ScatterDataPoint.html"}},[a("code",[t._v("ScatterDataPoint")])]),t._v(" | "),a("RouterLink",{attrs:{to:"/api/interfaces/BubbleDataPoint.html"}},[a("code",[t._v("BubbleDataPoint")])]),t._v(")[], "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/animation.d.ts#L13",target:"_blank",rel:"noopener noreferrer"}},[t._v("animation.d.ts:13"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"currentstep"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#currentstep"}},[t._v("#")]),t._v(" currentStep")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("currentStep")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/animation.d.ts#L16",target:"_blank",rel:"noopener noreferrer"}},[t._v("animation.d.ts:16"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"initial"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#initial"}},[t._v("#")]),t._v(" initial")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("initial")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/animation.d.ts#L15",target:"_blank",rel:"noopener noreferrer"}},[t._v("animation.d.ts:15"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"numsteps"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#numsteps"}},[t._v("#")]),t._v(" numSteps")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("numSteps")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/animation.d.ts#L14",target:"_blank",rel:"noopener noreferrer"}},[t._v("animation.d.ts:14"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=n.exports}}]);