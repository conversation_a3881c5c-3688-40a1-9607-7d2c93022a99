(window.webpackJsonp=window.webpackJsonp||[]).push([[121],{451:function(t,e,a){"use strict";a.r(e);var r=a(6),s=Object(r.a)({},(function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("ContentSlotsDistributor",{attrs:{"slot-key":t.$parent.slotKey}},[a("h1",{attrs:{id:"interface-tooltipmodel-ttype"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#interface-tooltipmodel-ttype"}},[t._v("#")]),t._v(" Interface: TooltipModel<TType>")]),t._v(" "),a("h2",{attrs:{id:"type-parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("TType")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("RouterLink",{attrs:{to:"/api/#charttype"}},[a("code",[t._v("ChartType")])])],1)])])]),t._v(" "),a("h2",{attrs:{id:"hierarchy"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hierarchy"}},[t._v("#")]),t._v(" Hierarchy")]),t._v(" "),a("ul",[a("li",[a("p",[a("RouterLink",{attrs:{to:"/api/#element"}},[a("code",[t._v("Element")])]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("RouterLink",{attrs:{to:"/api/interfaces/TooltipOptions.html"}},[a("code",[t._v("TooltipOptions")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">>")],1),t._v(" "),a("p",[t._v("↳ "),a("strong",[a("code",[t._v("TooltipModel")])])])])]),t._v(" "),a("h2",{attrs:{id:"properties"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[t._v("#")]),t._v(" Properties")]),t._v(" "),a("h3",{attrs:{id:"active"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#active"}},[t._v("#")]),t._v(" active")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("active")]),t._v(": "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[t._v("Element.active")]),t._v(" "),a("h4",{attrs:{id:"defined-in"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L7",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:7"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"afterbody"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#afterbody"}},[t._v("#")]),t._v(" afterBody")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("afterBody")]),t._v(": "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2503",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2503"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"beforebody"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#beforebody"}},[t._v("#")]),t._v(" beforeBody")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("beforeBody")]),t._v(": "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2501",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2501"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"body"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#body"}},[t._v("#")]),t._v(" body")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("body")]),t._v(": { "),a("code",[t._v("after")]),t._v(": "),a("code",[t._v("string")]),t._v("[] ; "),a("code",[t._v("before")]),t._v(": "),a("code",[t._v("string")]),t._v("[] ; "),a("code",[t._v("lines")]),t._v(": "),a("code",[t._v("string")]),t._v("[]  }[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2499",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2499"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"caretx"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#caretx"}},[t._v("#")]),t._v(" caretX")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("caretX")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-5"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2490",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2490"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"carety"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#carety"}},[t._v("#")]),t._v(" caretY")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("caretY")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-6"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-6"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2491",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2491"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"chart"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#chart"}},[t._v("#")]),t._v(" chart")]),t._v(" "),a("p",[t._v("• "),a("code",[t._v("Readonly")]),t._v(" "),a("strong",[t._v("chart")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/classes/Chart.html"}},[a("code",[t._v("Chart")])]),t._v("<"),a("code",[t._v("TType")]),t._v(", "),a("code",[t._v("DistributiveArray")]),t._v("<"),a("RouterLink",{attrs:{to:"/api/interfaces/ChartTypeRegistry.html"}},[a("code",[t._v("ChartTypeRegistry")])]),t._v("["),a("code",[t._v("TType")]),t._v("]["),a("code",[t._v('"defaultDataPoint"')]),t._v("]>, "),a("code",[t._v("unknown")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-7"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-7"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2475",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2475"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"datapoints"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#datapoints"}},[t._v("#")]),t._v(" dataPoints")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("dataPoints")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/TooltipItem.html"}},[a("code",[t._v("TooltipItem")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">[]")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-8"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-8"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2478",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2478"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"footer"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#footer"}},[t._v("#")]),t._v(" footer")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("footer")]),t._v(": "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-9"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-9"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2511",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2511"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"height"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#height"}},[t._v("#")]),t._v(" height")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("height")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-10"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-10"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2488",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2488"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"labelcolors"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#labelcolors"}},[t._v("#")]),t._v(" labelColors")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("labelColors")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/TooltipLabelStyle.html"}},[a("code",[t._v("TooltipLabelStyle")])]),t._v("[]")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-11"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-11"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2514",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2514"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"labelpointstyles"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#labelpointstyles"}},[t._v("#")]),t._v(" labelPointStyles")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("labelPointStyles")]),t._v(": { "),a("code",[t._v("pointStyle")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#pointstyle"}},[a("code",[t._v("PointStyle")])]),t._v(" ; "),a("code",[t._v("rotation")]),t._v(": "),a("code",[t._v("number")]),t._v("  }[]")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-12"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-12"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2516",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2516"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"labeltextcolors"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#labeltextcolors"}},[t._v("#")]),t._v(" labelTextColors")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("labelTextColors")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#color"}},[a("code",[t._v("Color")])]),t._v("[]")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-13"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-13"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2515",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2515"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"opacity"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#opacity"}},[t._v("#")]),t._v(" opacity")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("opacity")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-14"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-14"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2519",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2519"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"options"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#options"}},[t._v("#")]),t._v(" options")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("options")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/interfaces/TooltipOptions.html"}},[a("code",[t._v("TooltipOptions")])]),t._v("<"),a("code",[t._v("TType")]),t._v(">")],1),t._v(" "),a("h4",{attrs:{id:"overrides"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#overrides"}},[t._v("#")]),t._v(" Overrides")]),t._v(" "),a("p",[t._v("Element.options")]),t._v(" "),a("h4",{attrs:{id:"defined-in-15"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-15"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2522",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2522"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"title"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#title"}},[t._v("#")]),t._v(" title")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("title")]),t._v(": "),a("code",[t._v("string")]),t._v("[]")]),t._v(" "),a("h4",{attrs:{id:"defined-in-16"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-16"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2507",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2507"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"width"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#width"}},[t._v("#")]),t._v(" width")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("width")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-17"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-17"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2487",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2487"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"x"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#x"}},[t._v("#")]),t._v(" x")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("x")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"overrides-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#overrides-2"}},[t._v("#")]),t._v(" Overrides")]),t._v(" "),a("p",[t._v("Element.x")]),t._v(" "),a("h4",{attrs:{id:"defined-in-18"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-18"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2485",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2485"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"xalign"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#xalign"}},[t._v("#")]),t._v(" xAlign")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("xAlign")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#tooltipxalignment"}},[a("code",[t._v("TooltipXAlignment")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-19"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-19"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2481",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2481"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"y"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#y"}},[t._v("#")]),t._v(" y")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("y")]),t._v(": "),a("code",[t._v("number")])]),t._v(" "),a("h4",{attrs:{id:"overrides-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#overrides-3"}},[t._v("#")]),t._v(" Overrides")]),t._v(" "),a("p",[t._v("Element.y")]),t._v(" "),a("h4",{attrs:{id:"defined-in-20"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-20"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2486",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2486"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"yalign"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#yalign"}},[t._v("#")]),t._v(" yAlign")]),t._v(" "),a("p",[t._v("• "),a("strong",[t._v("yAlign")]),t._v(": "),a("RouterLink",{attrs:{to:"/api/#tooltipyalignment"}},[a("code",[t._v("TooltipYAlignment")])])],1),t._v(" "),a("h4",{attrs:{id:"defined-in-21"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-21"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2482",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2482"),a("OutboundLink")],1)]),t._v(" "),a("h2",{attrs:{id:"methods"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#methods"}},[t._v("#")]),t._v(" Methods")]),t._v(" "),a("h3",{attrs:{id:"getactiveelements"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getactiveelements"}},[t._v("#")]),t._v(" getActiveElements")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getActiveElements")]),t._v("(): "),a("RouterLink",{attrs:{to:"/api/interfaces/ActiveElement.html"}},[a("code",[t._v("ActiveElement")])]),t._v("[]")],1),t._v(" "),a("h4",{attrs:{id:"returns"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/ActiveElement.html"}},[a("code",[t._v("ActiveElement")])]),t._v("[]")],1),t._v(" "),a("h4",{attrs:{id:"defined-in-22"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-22"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2524",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2524"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"getprops"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#getprops"}},[t._v("#")]),t._v(" getProps")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("getProps")]),t._v("<"),a("code",[t._v("P")]),t._v(">("),a("code",[t._v("props")]),t._v(", "),a("code",[t._v("final?")]),t._v("): "),a("code",[t._v("Pick")]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("P")]),t._v("["),a("code",[t._v("number")]),t._v("]>")]),t._v(" "),a("h4",{attrs:{id:"type-parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#type-parameters-2"}},[t._v("#")]),t._v(" Type parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("P")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[t._v("extends "),a("code",[t._v("string")]),t._v("[]")])])])]),t._v(" "),a("h4",{attrs:{id:"parameters"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("props")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("P")])])]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("final?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-2"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("Pick")]),t._v("<"),a("code",[t._v("AnyObject")]),t._v(", "),a("code",[t._v("P")]),t._v("["),a("code",[t._v("number")]),t._v("]>")]),t._v(" "),a("h4",{attrs:{id:"inherited-from-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-2"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[t._v("Element.getProps")]),t._v(" "),a("h4",{attrs:{id:"defined-in-23"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-23"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L12",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:12"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"hasvalue"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#hasvalue"}},[t._v("#")]),t._v(" hasValue")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("hasValue")]),t._v("(): "),a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"returns-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-3"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("boolean")])]),t._v(" "),a("h4",{attrs:{id:"inherited-from-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-3"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[t._v("Element.hasValue")]),t._v(" "),a("h4",{attrs:{id:"defined-in-24"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-24"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L11",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:11"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"setactiveelements"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#setactiveelements"}},[t._v("#")]),t._v(" setActiveElements")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("setActiveElements")]),t._v("("),a("code",[t._v("active")]),t._v(", "),a("code",[t._v("eventPosition")]),t._v("): "),a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"parameters-2"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-2"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("active")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/ActiveDataPoint.html"}},[a("code",[t._v("ActiveDataPoint")])]),t._v("[]")],1)]),t._v(" "),a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("eventPosition")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("RouterLink",{attrs:{to:"/api/interfaces/Point.html"}},[a("code",[t._v("Point")])])],1)])])]),t._v(" "),a("h4",{attrs:{id:"returns-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-4"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("code",[t._v("void")])]),t._v(" "),a("h4",{attrs:{id:"defined-in-25"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-25"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2525",target:"_blank",rel:"noopener noreferrer"}},[t._v("index.esm.d.ts:2525"),a("OutboundLink")],1)]),t._v(" "),a("hr"),t._v(" "),a("h3",{attrs:{id:"tooltipposition"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#tooltipposition"}},[t._v("#")]),t._v(" tooltipPosition")]),t._v(" "),a("p",[t._v("▸ "),a("strong",[t._v("tooltipPosition")]),t._v("("),a("code",[t._v("useFinalPosition?")]),t._v("): "),a("RouterLink",{attrs:{to:"/api/interfaces/Point.html"}},[a("code",[t._v("Point")])])],1),t._v(" "),a("h4",{attrs:{id:"parameters-3"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#parameters-3"}},[t._v("#")]),t._v(" Parameters")]),t._v(" "),a("table",[a("thead",[a("tr",[a("th",{staticStyle:{"text-align":"left"}},[t._v("Name")]),t._v(" "),a("th",{staticStyle:{"text-align":"left"}},[t._v("Type")])])]),t._v(" "),a("tbody",[a("tr",[a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("useFinalPosition?")])]),t._v(" "),a("td",{staticStyle:{"text-align":"left"}},[a("code",[t._v("boolean")])])])])]),t._v(" "),a("h4",{attrs:{id:"returns-5"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#returns-5"}},[t._v("#")]),t._v(" Returns")]),t._v(" "),a("p",[a("RouterLink",{attrs:{to:"/api/interfaces/Point.html"}},[a("code",[t._v("Point")])])],1),t._v(" "),a("h4",{attrs:{id:"inherited-from-4"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#inherited-from-4"}},[t._v("#")]),t._v(" Inherited from")]),t._v(" "),a("p",[t._v("Element.tooltipPosition")]),t._v(" "),a("h4",{attrs:{id:"defined-in-26"}},[a("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-26"}},[t._v("#")]),t._v(" Defined in")]),t._v(" "),a("p",[a("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L10",target:"_blank",rel:"noopener noreferrer"}},[t._v("element.d.ts:10"),a("OutboundLink")],1)])])}),[],!1,null,null,null);e.default=s.exports}}]);