(window.webpackJsonp=window.webpackJsonp||[]).push([[48],{379:function(e,t,r){"use strict";r.r(t);var a=r(6),s=Object(a.a)({},(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("ContentSlotsDistributor",{attrs:{"slot-key":e.$parent.slotKey}},[r("h1",{attrs:{id:"interface-chartevent"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-chartevent"}},[e._v("#")]),e._v(" Interface: ChartEvent")]),e._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[e._v("#")]),e._v(" Properties")]),e._v(" "),r("h3",{attrs:{id:"native"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#native"}},[e._v("#")]),e._v(" native")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("native")]),e._v(": "),r("code",[e._v("Event")])]),e._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1407",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1407"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"type"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#type"}},[e._v("#")]),e._v(" type")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("type")]),e._v(": "),r("code",[e._v('"resize"')]),e._v(" | "),r("code",[e._v('"click"')]),e._v(" | "),r("code",[e._v('"contextmenu"')]),e._v(" | "),r("code",[e._v('"dblclick"')]),e._v(" | "),r("code",[e._v('"keydown"')]),e._v(" | "),r("code",[e._v('"keypress"')]),e._v(" | "),r("code",[e._v('"keyup"')]),e._v(" | "),r("code",[e._v('"mousedown"')]),e._v(" | "),r("code",[e._v('"mouseenter"')]),e._v(" | "),r("code",[e._v('"mousemove"')]),e._v(" | "),r("code",[e._v('"mouseout"')]),e._v(" | "),r("code",[e._v('"mouseup"')])]),e._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1394",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1394"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"x"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#x"}},[e._v("#")]),e._v(" x")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("x")]),e._v(": "),r("code",[e._v("number")])]),e._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1408",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1408"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"y"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#y"}},[e._v("#")]),e._v(" y")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("y")]),e._v(": "),r("code",[e._v("number")])]),e._v(" "),r("h4",{attrs:{id:"defined-in-4"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-4"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1409",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:1409"),r("OutboundLink")],1)])])}),[],!1,null,null,null);t.default=s.exports}}]);