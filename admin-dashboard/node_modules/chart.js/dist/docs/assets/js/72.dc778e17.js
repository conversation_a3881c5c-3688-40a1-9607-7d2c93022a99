(window.webpackJsonp=window.webpackJsonp||[]).push([[72],{403:function(e,t,r){"use strict";r.r(t);var a=r(6),n=Object(a.a)({},(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("ContentSlotsDistributor",{attrs:{"slot-key":e.$parent.slotKey}},[r("h1",{attrs:{id:"interface-interactionitem"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#interface-interactionitem"}},[e._v("#")]),e._v(" Interface: InteractionItem")]),e._v(" "),r("h2",{attrs:{id:"properties"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#properties"}},[e._v("#")]),e._v(" Properties")]),e._v(" "),r("h3",{attrs:{id:"datasetindex"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#datasetindex"}},[e._v("#")]),e._v(" datasetIndex")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("datasetIndex")]),e._v(": "),r("code",[e._v("number")])]),e._v(" "),r("h4",{attrs:{id:"defined-in"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L710",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:710"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"element"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#element"}},[e._v("#")]),e._v(" element")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("element")]),e._v(": "),r("RouterLink",{attrs:{to:"/api/#element"}},[r("code",[e._v("Element")])]),e._v("<"),r("code",[e._v("AnyObject")]),e._v(", "),r("code",[e._v("AnyObject")]),e._v(">")],1),e._v(" "),r("h4",{attrs:{id:"defined-in-2"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-2"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L709",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:709"),r("OutboundLink")],1)]),e._v(" "),r("hr"),e._v(" "),r("h3",{attrs:{id:"index"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#index"}},[e._v("#")]),e._v(" index")]),e._v(" "),r("p",[e._v("• "),r("strong",[e._v("index")]),e._v(": "),r("code",[e._v("number")])]),e._v(" "),r("h4",{attrs:{id:"defined-in-3"}},[r("a",{staticClass:"header-anchor",attrs:{href:"#defined-in-3"}},[e._v("#")]),e._v(" Defined in")]),e._v(" "),r("p",[r("a",{attrs:{href:"https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L711",target:"_blank",rel:"noopener noreferrer"}},[e._v("index.esm.d.ts:711"),r("OutboundLink")],1)])])}),[],!1,null,null,null);t.default=n.exports}}]);