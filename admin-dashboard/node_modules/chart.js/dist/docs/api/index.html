<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Chart.js - v3.9.1 | Chart.js</title>
    <meta name="generator" content="VuePress 1.8.3">
    <link rel="icon" href="/docs/3.9.1/favicon.ico">
    <meta name="description" content="Open source HTML5 Charts for your website">
    <link rel="preload" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css" as="style"><link rel="preload" href="/docs/3.9.1/assets/js/app.7e0dc8c8.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/2.3e2307f3.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/9.63ebb16b.js" as="script"><link rel="prefetch" href="/docs/3.9.1/assets/js/10.edbecfa9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/100.f7a12d8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/101.15c56dbb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/102.33b746ea.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/103.850a4486.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/104.8b4f368b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/105.1a3d6c41.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/106.d8f8b4c1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/107.38cf9b4f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/108.a30da8c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/109.6d56b7d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/11.b98e8151.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/110.c44e533c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/111.ccb9a835.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/112.2d2a2890.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/113.5ac0921f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/114.90ef8814.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/115.12a72d27.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/116.6b780970.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/117.79604442.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/118.7752bc89.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/119.d8e76ef0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/12.433f0c7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/120.ed76595e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/121.cc3d56f2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/122.c8651871.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/123.f7d5f223.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/124.88ce07a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/125.a9572036.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/126.879447b3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/127.3b8478f3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/128.87d67ad7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/129.f6241700.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/13.b6743084.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/130.a9cb856a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/131.45e904c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/132.abd9e4c2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/133.d5aa1db6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/134.4bfa3b5a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/135.8c7630ad.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/136.f813e062.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/137.6cab2863.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/138.03ec5265.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/139.924812c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/14.422fec10.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/140.a7967a5d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/141.34a018bb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/142.c5bfcdd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/143.05e388ba.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/144.a98bdbcc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/145.826b73e0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/146.001afadf.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/147.fcadaef3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/148.604863a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/149.6509ed9f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/15.09a69fc3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/150.fd8575ca.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/151.67ecd4a3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/152.481a7bd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/153.dbf3f3aa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/154.2aa9c67d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/155.e3367ebc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/156.a5bb942d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/157.09caef4e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/158.1f82587f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/159.4cebd9d3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/16.0414f390.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/160.47df8215.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/161.fac79692.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/162.9f2f870f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/163.4f1604f9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/164.7f8d8643.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/165.fe2baa69.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/166.c82b3c76.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/167.3c3f2ff1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/168.8d2e8e83.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/169.b10c84bd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/17.8dfcb176.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/170.b9db8312.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/171.2480fc36.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/172.81c5206a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/173.477dcbae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/174.05398ca3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/175.84ab69d0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/176.f48183c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/177.d0d35403.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/178.55d433fd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/179.9442c138.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/18.c331029e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/180.5c15d5d4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/181.911f367b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/182.72f663e8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/183.22b2258c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/184.15b21065.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/185.894ea40b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/186.f6394459.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/187.7bd9b3fe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/188.3baa9bcd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/189.18e63b11.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/19.13bdd658.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/190.b7ffb54a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/191.13061aba.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/192.1bf9bd61.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/193.8fa44455.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/194.49c9a3c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/195.eb1e8802.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/196.9b925823.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/197.adca6c8c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/198.2ae0961a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/199.cd9dca80.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/20.5d11c294.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/200.2eb9437f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/201.6dba10c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/202.9c5057c6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/203.98a3c2b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/204.50d5cf2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/205.e56e820b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/206.6ce5c41e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/207.8aa83d91.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/208.296ee160.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/209.13279349.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/21.7b54d7d8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/210.63e30420.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/211.0b325f23.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/212.be671e2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/213.81d2e607.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/214.ba403b5c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/215.2efcec5f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/216.e01d3100.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/217.906c8d54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/218.94e33827.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/219.21c5e01f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/22.c2daedd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/220.3ba160e4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/221.5c17138c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/222.aea004ce.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/223.cbb7b883.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/224.b130b37f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/225.32f90319.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/226.8c102c21.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/227.b3d60339.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/228.72f0ad18.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/229.3daa3b7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/23.2c668e20.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/230.7e5a85b1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/231.b83d12f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/232.e902f42d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/233.b4f254c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/234.d975df48.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/235.71a7be7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/236.05d87a5e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/237.d518e28b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/238.eb60e397.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/239.81fecfed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/24.af200d5b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/240.c0869bc2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/241.59dc896f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/242.6d423d39.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/243.a355eaa8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/244.ad729cf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/245.c3cd6bbe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/246.d0708528.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/247.13d65d78.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/248.1d222543.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/249.2d7a9bf1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/25.4f97f63f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/250.09ef38e2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/251.0cb90e8a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/252.2ff0def4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/253.61c7c505.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/254.20137eeb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/255.49c937e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/256.8d709dae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/257.b3ebdce7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/258.792f66d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/259.98c809e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/26.d1bb645c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/260.54ff10d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/261.928afea4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/27.2d5c4a6b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/28.72dee0f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/29.8820be26.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/3.947b8d98.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/30.343676b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/31.cff089f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/32.56d8546e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/33.6b642a06.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/34.360a9ea9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/35.205d7fac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/36.f2765bae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/37.6c33435e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/38.a778a6a2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/39.7dda160f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/4.ee88d25a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/40.bd778eac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/41.b79220f6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/42.c3157beb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/43.097368d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/44.2515f16e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/45.d5b4b7c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/46.bbab8d6e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/47.79aa575e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/48.45785af9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/49.0569a6eb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/5.00f814ac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/50.14bd3ba2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/51.e0968711.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/52.fe65ddf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/53.faa1ff3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/54.fcfed2c5.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/55.7db1d28a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/56.8b0e82b7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/57.9de2d983.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/58.b0f8ad0c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/59.f1ff4935.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/6.2bc86161.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/60.ac08de9a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/61.ea4fad75.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/62.5c85853b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/63.5ac99656.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/64.c6838e95.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/65.4c1b089e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/66.379a6b45.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/67.15703e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/68.ffdefd7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/69.bdeb7b9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/7.74f2ce90.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/70.3dadb5ed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/71.2c97fe38.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/72.dc778e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/73.d88fcb57.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/74.bba2165e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/75.15562a81.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/76.21f5a94e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/77.0725268e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/78.d6f610d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/79.60d67faa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/8.8928eb8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/80.99d71ee0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/81.f1500469.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/82.69f363a6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/83.f1fbcb2c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/84.b76e3156.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/85.efc4bd54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/86.85011b24.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/87.6a88d571.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/88.0f45cfe0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/89.e67ddb59.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/90.96ebfa9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/91.cdde4d3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/92.dc3f06d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/93.aaa04d52.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/94.2af5650c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/95.1d44ec16.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/96.9545127d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/97.8b18f487.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/98.cb259cd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/99.6251650a.js">
    <link rel="stylesheet" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css">
  </head>
  <body>
    <div id="app" data-server-rendered="true"><div class="theme-container"><header class="navbar"><div class="sidebar-button"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" viewBox="0 0 448 512" class="icon"><path fill="currentColor" d="M436 124H12c-6.627 0-12-5.373-12-12V80c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12z"></path></svg></div> <a href="/docs/3.9.1/" class="home-link router-link-active"><img src="/docs/3.9.1/favicon.ico" alt="Chart.js" class="logo"> <span class="site-name can-hide">Chart.js</span></a> <div class="links"><div class="search-box"><input aria-label="Search" autocomplete="off" spellcheck="false" value=""> <!----></div> <nav class="nav-links can-hide"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" aria-current="page" class="nav-link router-link-exact-active router-link-active">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav></div></header> <div class="sidebar-mask"></div> <aside class="sidebar"><nav class="nav-links"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" aria-current="page" class="nav-link router-link-exact-active router-link-active">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav>  <ul class="sidebar-links"><li><section class="sidebar-group depth-0"><p class="sidebar-heading open"><span>API</span> <!----></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/docs/3.9.1/api/" aria-current="page" class="active sidebar-link">Exports</a></li><li><section class="sidebar-group collapsable is-sub-group depth-1"><p class="sidebar-heading"><span>Enumerations</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable is-sub-group depth-1"><p class="sidebar-heading"><span>Classes</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable is-sub-group depth-1"><p class="sidebar-heading"><span>Interfaces</span> <span class="arrow right"></span></p> <!----></section></li></ul></section></li></ul> </aside> <main class="page"> <div class="theme-default-content content__default"><h1 id="chart-js-v3-9-1"><a href="#chart-js-v3-9-1" class="header-anchor">#</a> Chart.js - v3.9.1</h1> <h2 id="enumerations"><a href="#enumerations" class="header-anchor">#</a> Enumerations</h2> <ul><li><a href="/docs/3.9.1/api/enums/DecimationAlgorithm.html">DecimationAlgorithm</a></li> <li><a href="/docs/3.9.1/api/enums/UpdateModeEnum.html">UpdateModeEnum</a></li></ul> <h2 id="classes"><a href="#classes" class="header-anchor">#</a> Classes</h2> <ul><li><a href="/docs/3.9.1/api/classes/Animation.html">Animation</a></li> <li><a href="/docs/3.9.1/api/classes/Animations.html">Animations</a></li> <li><a href="/docs/3.9.1/api/classes/Animator.html">Animator</a></li> <li><a href="/docs/3.9.1/api/classes/BasePlatform.html">BasePlatform</a></li> <li><a href="/docs/3.9.1/api/classes/BasicPlatform.html">BasicPlatform</a></li> <li><a href="/docs/3.9.1/api/classes/Chart.html">Chart</a></li> <li><a href="/docs/3.9.1/api/classes/DatasetController.html">DatasetController</a></li> <li><a href="/docs/3.9.1/api/classes/DomPlatform.html">DomPlatform</a></li> <li><a href="/docs/3.9.1/api/classes/Scale.html">Scale</a></li></ul> <h2 id="interfaces"><a href="#interfaces" class="header-anchor">#</a> Interfaces</h2> <ul><li><a href="/docs/3.9.1/api/interfaces/ActiveDataPoint.html">ActiveDataPoint</a></li> <li><a href="/docs/3.9.1/api/interfaces/ActiveElement.html">ActiveElement</a></li> <li><a href="/docs/3.9.1/api/interfaces/AnimationEvent.html">AnimationEvent</a></li> <li><a href="/docs/3.9.1/api/interfaces/ArcBorderRadius.html">ArcBorderRadius</a></li> <li><a href="/docs/3.9.1/api/interfaces/ArcElement.html">ArcElement</a></li> <li><a href="/docs/3.9.1/api/interfaces/ArcHoverOptions.html">ArcHoverOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/ArcOptions.html">ArcOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/ArcProps.html">ArcProps</a></li> <li><a href="/docs/3.9.1/api/interfaces/BarControllerChartOptions.html">BarControllerChartOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/BarControllerDatasetOptions.html">BarControllerDatasetOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/BarElement.html">BarElement</a></li> <li><a href="/docs/3.9.1/api/interfaces/BarHoverOptions.html">BarHoverOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/BarOptions.html">BarOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/BarProps.html">BarProps</a></li> <li><a href="/docs/3.9.1/api/interfaces/BorderRadius.html">BorderRadius</a></li> <li><a href="/docs/3.9.1/api/interfaces/BubbleControllerDatasetOptions.html">BubbleControllerDatasetOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html">BubbleDataPoint</a></li> <li><a href="/docs/3.9.1/api/interfaces/CartesianScaleOptions.html">CartesianScaleOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/CartesianScaleTypeRegistry.html">CartesianScaleTypeRegistry</a></li> <li><a href="/docs/3.9.1/api/interfaces/ChartArea.html">ChartArea</a></li> <li><a href="/docs/3.9.1/api/interfaces/ChartComponent.html">ChartComponent</a></li> <li><a href="/docs/3.9.1/api/interfaces/ChartConfiguration.html">ChartConfiguration</a></li> <li><a href="/docs/3.9.1/api/interfaces/ChartConfigurationCustomTypesPerDataset.html">ChartConfigurationCustomTypesPerDataset</a></li> <li><a href="/docs/3.9.1/api/interfaces/ChartData.html">ChartData</a></li> <li><a href="/docs/3.9.1/api/interfaces/ChartDataCustomTypesPerDataset.html">ChartDataCustomTypesPerDataset</a></li> <li><a href="/docs/3.9.1/api/interfaces/ChartDatasetProperties.html">ChartDatasetProperties</a></li> <li><a href="/docs/3.9.1/api/interfaces/ChartDatasetPropertiesCustomTypesPerDataset.html">ChartDatasetPropertiesCustomTypesPerDataset</a></li> <li><a href="/docs/3.9.1/api/interfaces/ChartEvent.html">ChartEvent</a></li> <li><a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html">ChartTypeRegistry</a></li> <li><a href="/docs/3.9.1/api/interfaces/CommonElementOptions.html">CommonElementOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/CommonHoverOptions.html">CommonHoverOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/ComplexFillTarget.html">ComplexFillTarget</a></li> <li><a href="/docs/3.9.1/api/interfaces/ControllerDatasetOptions.html">ControllerDatasetOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/CoreChartOptions.html">CoreChartOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/CoreInteractionOptions.html">CoreInteractionOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/CoreScaleOptions.html">CoreScaleOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/DatasetControllerChartComponent.html">DatasetControllerChartComponent</a></li> <li><a href="/docs/3.9.1/api/interfaces/DateAdapter.html">DateAdapter</a></li> <li><a href="/docs/3.9.1/api/interfaces/Defaults.html">Defaults</a></li> <li><a href="/docs/3.9.1/api/interfaces/DoughnutAnimationOptions.html">DoughnutAnimationOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/DoughnutController.html">DoughnutController</a></li> <li><a href="/docs/3.9.1/api/interfaces/DoughnutControllerChartOptions.html">DoughnutControllerChartOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/DoughnutControllerDatasetOptions.html">DoughnutControllerDatasetOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/DoughnutMetaExtensions.html">DoughnutMetaExtensions</a></li> <li><a href="/docs/3.9.1/api/interfaces/Element.html">Element</a></li> <li><a href="/docs/3.9.1/api/interfaces/ElementOptionsByType.html">ElementOptionsByType</a></li> <li><a href="/docs/3.9.1/api/interfaces/ExtendedPlugin.html">ExtendedPlugin</a></li> <li><a href="/docs/3.9.1/api/interfaces/FillerControllerDatasetOptions.html">FillerControllerDatasetOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/FillerOptions.html">FillerOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/FontSpec.html">FontSpec</a></li> <li><a href="/docs/3.9.1/api/interfaces/GridLineOptions.html">GridLineOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/InteractionItem.html">InteractionItem</a></li> <li><a href="/docs/3.9.1/api/interfaces/InteractionModeMap.html">InteractionModeMap</a></li> <li><a href="/docs/3.9.1/api/interfaces/InteractionOptions.html">InteractionOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/LayoutItem.html">LayoutItem</a></li> <li><a href="/docs/3.9.1/api/interfaces/LegendElement.html">LegendElement</a></li> <li><a href="/docs/3.9.1/api/interfaces/LegendItem.html">LegendItem</a></li> <li><a href="/docs/3.9.1/api/interfaces/LegendOptions.html">LegendOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/LineControllerChartOptions.html">LineControllerChartOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/LineControllerDatasetOptions.html">LineControllerDatasetOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/LineElement.html">LineElement</a></li> <li><a href="/docs/3.9.1/api/interfaces/LineHoverOptions.html">LineHoverOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/LineOptions.html">LineOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/LineProps.html">LineProps</a></li> <li><a href="/docs/3.9.1/api/interfaces/ParsingOptions.html">ParsingOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/Plugin.html">Plugin</a></li> <li><a href="/docs/3.9.1/api/interfaces/PluginChartOptions.html">PluginChartOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/PluginOptionsByType.html">PluginOptionsByType</a></li> <li><a href="/docs/3.9.1/api/interfaces/Point.html">Point</a></li> <li><a href="/docs/3.9.1/api/interfaces/PointElement.html">PointElement</a></li> <li><a href="/docs/3.9.1/api/interfaces/PointHoverOptions.html">PointHoverOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/PointOptions.html">PointOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/PointPrefixedHoverOptions.html">PointPrefixedHoverOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/PointPrefixedOptions.html">PointPrefixedOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/PointProps.html">PointProps</a></li> <li><a href="/docs/3.9.1/api/interfaces/PolarAreaController.html">PolarAreaController</a></li> <li><a href="/docs/3.9.1/api/interfaces/PolarAreaControllerChartOptions.html">PolarAreaControllerChartOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/PolarAreaControllerDatasetOptions.html">PolarAreaControllerDatasetOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/RadarControllerDatasetOptions.html">RadarControllerDatasetOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/RadialLinearScale.html">RadialLinearScale</a></li> <li><a href="/docs/3.9.1/api/interfaces/RadialScaleTypeRegistry.html">RadialScaleTypeRegistry</a></li> <li><a href="/docs/3.9.1/api/interfaces/Registry.html">Registry</a></li> <li><a href="/docs/3.9.1/api/interfaces/ScaleTypeRegistry.html">ScaleTypeRegistry</a></li> <li><a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html">ScatterDataPoint</a></li> <li><a href="/docs/3.9.1/api/interfaces/ScriptableCartesianScaleContext.html">ScriptableCartesianScaleContext</a></li> <li><a href="/docs/3.9.1/api/interfaces/ScriptableChartContext.html">ScriptableChartContext</a></li> <li><a href="/docs/3.9.1/api/interfaces/ScriptableContext.html">ScriptableContext</a></li> <li><a href="/docs/3.9.1/api/interfaces/ScriptableLineSegmentContext.html">ScriptableLineSegmentContext</a></li> <li><a href="/docs/3.9.1/api/interfaces/ScriptableScaleContext.html">ScriptableScaleContext</a></li> <li><a href="/docs/3.9.1/api/interfaces/ScriptableScalePointLabelContext.html">ScriptableScalePointLabelContext</a></li> <li><a href="/docs/3.9.1/api/interfaces/ScriptableTooltipContext.html">ScriptableTooltipContext</a></li> <li><a href="/docs/3.9.1/api/interfaces/Segment.html">Segment</a></li> <li><a href="/docs/3.9.1/api/interfaces/Tick.html">Tick</a></li> <li><a href="/docs/3.9.1/api/interfaces/TickOptions.html">TickOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/TimeScale.html">TimeScale</a></li> <li><a href="/docs/3.9.1/api/interfaces/TitleOptions.html">TitleOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/Tooltip.html">Tooltip</a></li> <li><a href="/docs/3.9.1/api/interfaces/TooltipCallbacks.html">TooltipCallbacks</a></li> <li><a href="/docs/3.9.1/api/interfaces/TooltipItem.html">TooltipItem</a></li> <li><a href="/docs/3.9.1/api/interfaces/TooltipLabelStyle.html">TooltipLabelStyle</a></li> <li><a href="/docs/3.9.1/api/interfaces/TooltipModel.html">TooltipModel</a></li> <li><a href="/docs/3.9.1/api/interfaces/TooltipOptions.html">TooltipOptions</a></li> <li><a href="/docs/3.9.1/api/interfaces/TooltipPosition.html">TooltipPosition</a></li> <li><a href="/docs/3.9.1/api/interfaces/TooltipPositionerMap.html">TooltipPositionerMap</a></li> <li><a href="/docs/3.9.1/api/interfaces/TypedRegistry.html">TypedRegistry</a></li> <li><a href="/docs/3.9.1/api/interfaces/VisualElement.html">VisualElement</a></li></ul> <h2 id="type-aliases"><a href="#type-aliases" class="header-anchor">#</a> Type aliases</h2> <h3 id="align"><a href="#align" class="header-anchor">#</a> Align</h3> <p>Ƭ <strong>Align</strong>: <code>&quot;start&quot;</code> | <code>&quot;center&quot;</code> | <code>&quot;end&quot;</code></p> <h4 id="defined-in"><a href="#defined-in" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1682" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1682<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="animationoptions"><a href="#animationoptions" class="header-anchor">#</a> AnimationOptions</h3> <p>Ƭ <strong>AnimationOptions</strong>&lt;<code>TType</code>&gt;: <code>Object</code></p> <h4 id="type-parameters"><a href="#type-parameters" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr></tbody></table> <h4 id="type-declaration"><a href="#type-declaration" class="header-anchor">#</a> Type declaration</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>animation</code></td> <td style="text-align:left;"><code>false</code> | <a href="/docs/3.9.1/api/#animationspec"><code>AnimationSpec</code></a>&lt;<code>TType</code>&gt; &amp; { <code>onComplete?</code>: (<code>event</code>: <a href="/docs/3.9.1/api/interfaces/AnimationEvent.html"><code>AnimationEvent</code></a>) =&gt; <code>void</code> ; <code>onProgress?</code>: (<code>event</code>: <a href="/docs/3.9.1/api/interfaces/AnimationEvent.html"><code>AnimationEvent</code></a>) =&gt; <code>void</code>  }</td></tr> <tr><td style="text-align:left;"><code>animations</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/#animationsspec"><code>AnimationsSpec</code></a>&lt;<code>TType</code>&gt;</td></tr> <tr><td style="text-align:left;"><code>transitions</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/#transitionsspec"><code>TransitionsSpec</code></a>&lt;<code>TType</code>&gt;</td></tr></tbody></table> <h4 id="defined-in-2"><a href="#defined-in-2" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1639" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1639<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="animationspec"><a href="#animationspec" class="header-anchor">#</a> AnimationSpec</h3> <p>Ƭ <strong>AnimationSpec</strong>&lt;<code>TType</code>&gt;: <code>Object</code></p> <h4 id="type-parameters-2"><a href="#type-parameters-2" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr></tbody></table> <h4 id="type-declaration-2"><a href="#type-declaration-2" class="header-anchor">#</a> Type declaration</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>delay?</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<code>number</code>, <a href="/docs/3.9.1/api/interfaces/ScriptableContext.html"><code>ScriptableContext</code></a>&lt;<code>TType</code>&gt;&gt;</td> <td style="text-align:left;">Delay before starting the animations.  <strong><code>default</code></strong> 0</td></tr> <tr><td style="text-align:left;"><code>duration?</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<code>number</code>, <a href="/docs/3.9.1/api/interfaces/ScriptableContext.html"><code>ScriptableContext</code></a>&lt;<code>TType</code>&gt;&gt;</td> <td style="text-align:left;">The number of milliseconds an animation takes.  <strong><code>default</code></strong> 1000</td></tr> <tr><td style="text-align:left;"><code>easing?</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<a href="/docs/3.9.1/api/#easingfunction"><code>EasingFunction</code></a>, <a href="/docs/3.9.1/api/interfaces/ScriptableContext.html"><code>ScriptableContext</code></a>&lt;<code>TType</code>&gt;&gt;</td> <td style="text-align:left;">Easing function to use  <strong><code>default</code></strong> 'easeOutQuart'</td></tr> <tr><td style="text-align:left;"><code>loop?</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<code>boolean</code>, <a href="/docs/3.9.1/api/interfaces/ScriptableContext.html"><code>ScriptableContext</code></a>&lt;<code>TType</code>&gt;&gt;</td> <td style="text-align:left;">If set to true, the animations loop endlessly.  <strong><code>default</code></strong> false</td></tr></tbody></table> <h4 id="defined-in-3"><a href="#defined-in-3" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1583" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1583<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="animationsspec"><a href="#animationsspec" class="header-anchor">#</a> AnimationsSpec</h3> <p>Ƭ <strong>AnimationsSpec</strong>&lt;<code>TType</code>&gt;: <code>Object</code></p> <h4 id="type-parameters-3"><a href="#type-parameters-3" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr></tbody></table> <h4 id="index-signature"><a href="#index-signature" class="header-anchor">#</a> Index signature</h4> <p>▪ [name: <code>string</code>]: <code>false</code> | <a href="/docs/3.9.1/api/#animationspec"><code>AnimationSpec</code></a>&lt;<code>TType</code>&gt; &amp; { <code>from</code>: <a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<a href="/docs/3.9.1/api/#color"><code>Color</code></a> | <code>number</code> | <code>boolean</code>, <a href="/docs/3.9.1/api/interfaces/ScriptableContext.html"><code>ScriptableContext</code></a>&lt;<code>TType</code>&gt;&gt; ; <code>properties</code>: <code>string</code>[] ; <code>to</code>: <a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<a href="/docs/3.9.1/api/#color"><code>Color</code></a> | <code>number</code> | <code>boolean</code>, <a href="/docs/3.9.1/api/interfaces/ScriptableContext.html"><code>ScriptableContext</code></a>&lt;<code>TType</code>&gt;&gt; ; <code>type</code>: <code>&quot;color&quot;</code> | <code>&quot;number&quot;</code> | <code>&quot;boolean&quot;</code> ; <code>fn</code>: &lt;T&gt;(<code>from</code>: <code>T</code>, <code>to</code>: <code>T</code>, <code>factor</code>: <code>number</code>) =&gt; <code>T</code>  }</p> <h4 id="defined-in-4"><a href="#defined-in-4" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1608" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1608<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="barcontroller"><a href="#barcontroller" class="header-anchor">#</a> BarController</h3> <p>Ƭ <strong>BarController</strong>: <a href="/docs/3.9.1/api/classes/DatasetController.html"><code>DatasetController</code></a></p> <h4 id="defined-in-5"><a href="#defined-in-5" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L145" target="_blank" rel="noopener noreferrer">index.esm.d.ts:145<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="bubblecontroller"><a href="#bubblecontroller" class="header-anchor">#</a> BubbleController</h3> <p>Ƭ <strong>BubbleController</strong>: <a href="/docs/3.9.1/api/classes/DatasetController.html"><code>DatasetController</code></a></p> <h4 id="defined-in-6"><a href="#defined-in-6" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L173" target="_blank" rel="noopener noreferrer">index.esm.d.ts:173<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="cartesiantickoptions"><a href="#cartesiantickoptions" class="header-anchor">#</a> CartesianTickOptions</h3> <p>Ƭ <strong>CartesianTickOptions</strong>: <a href="/docs/3.9.1/api/interfaces/TickOptions.html"><code>TickOptions</code></a> &amp; { <code>align</code>: <a href="/docs/3.9.1/api/#align"><code>Align</code></a> | <code>&quot;inner&quot;</code> ; <code>autoSkip</code>: <code>boolean</code> ; <code>autoSkipPadding</code>: <code>number</code> ; <code>crossAlign</code>: <code>&quot;near&quot;</code> | <code>&quot;center&quot;</code> | <code>&quot;far&quot;</code> ; <code>includeBounds</code>: <code>boolean</code> ; <code>labelOffset</code>: <code>number</code> ; <code>maxRotation</code>: <code>number</code> ; <code>maxTicksLimit</code>: <code>number</code> ; <code>minRotation</code>: <code>number</code> ; <code>mirror</code>: <code>boolean</code> ; <code>padding</code>: <code>number</code> ; <code>sampleSize</code>: <code>number</code>  }</p> <h4 id="defined-in-7"><a href="#defined-in-7" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2982" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2982<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="categoryscale"><a href="#categoryscale" class="header-anchor">#</a> CategoryScale</h3> <p>Ƭ <strong>CategoryScale</strong>&lt;<code>O</code>&gt;: <a href="/docs/3.9.1/api/classes/Scale.html"><code>Scale</code></a>&lt;<code>O</code>&gt;</p> <h4 id="type-parameters-4"><a href="#type-parameters-4" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#categoryscaleoptions"><code>CategoryScaleOptions</code></a> = <a href="/docs/3.9.1/api/#categoryscaleoptions"><code>CategoryScaleOptions</code></a></td></tr></tbody></table> <h4 id="defined-in-8"><a href="#defined-in-8" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3147" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3147<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="categoryscaleoptions"><a href="#categoryscaleoptions" class="header-anchor">#</a> CategoryScaleOptions</h3> <p>Ƭ <strong>CategoryScaleOptions</strong>: <code>Omit</code>&lt;<a href="/docs/3.9.1/api/interfaces/CartesianScaleOptions.html"><code>CartesianScaleOptions</code></a>, <code>&quot;min&quot;</code> | <code>&quot;max&quot;</code>&gt; &amp; { <code>labels</code>: <code>string</code>[] | <code>string</code>[][] ; <code>max</code>: <code>string</code> | <code>number</code> ; <code>min</code>: <code>string</code> | <code>number</code>  }</p> <h4 id="defined-in-9"><a href="#defined-in-9" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3141" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3141<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="chartcomponentlike"><a href="#chartcomponentlike" class="header-anchor">#</a> ChartComponentLike</h3> <p>Ƭ <strong>ChartComponentLike</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> | <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a>[] | { [key: string]: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a>;  } | <a href="/docs/3.9.1/api/interfaces/Plugin.html"><code>Plugin</code></a> | <a href="/docs/3.9.1/api/interfaces/Plugin.html"><code>Plugin</code></a>[]</p> <h4 id="defined-in-10"><a href="#defined-in-10" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1113" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1113<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="chartdataset"><a href="#chartdataset" class="header-anchor">#</a> ChartDataset</h3> <p>Ƭ <strong>ChartDataset</strong>&lt;<code>TType</code>, <code>TData</code>&gt;: <code>DeepPartial</code>&lt;{ [key in ChartType]: Object &amp; ChartTypeRegistry[key][&quot;datasetOptions&quot;] }[<code>TType</code>]&gt; &amp; <a href="/docs/3.9.1/api/interfaces/ChartDatasetProperties.html"><code>ChartDatasetProperties</code></a>&lt;<code>TType</code>, <code>TData</code>&gt;</p> <h4 id="type-parameters-5"><a href="#type-parameters-5" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a> = <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr> <tr><td style="text-align:left;"><code>TData</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/#defaultdatapoint"><code>DefaultDataPoint</code></a>&lt;<code>TType</code>&gt;</td></tr></tbody></table> <h4 id="defined-in-11"><a href="#defined-in-11" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3659" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3659<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="chartdatasetcustomtypesperdataset"><a href="#chartdatasetcustomtypesperdataset" class="header-anchor">#</a> ChartDatasetCustomTypesPerDataset</h3> <p>Ƭ <strong>ChartDatasetCustomTypesPerDataset</strong>&lt;<code>TType</code>, <code>TData</code>&gt;: <code>DeepPartial</code>&lt;{ [key in ChartType]: Object &amp; ChartTypeRegistry[key][&quot;datasetOptions&quot;] }[<code>TType</code>]&gt; &amp; <a href="/docs/3.9.1/api/interfaces/ChartDatasetPropertiesCustomTypesPerDataset.html"><code>ChartDatasetPropertiesCustomTypesPerDataset</code></a>&lt;<code>TType</code>, <code>TData</code>&gt;</p> <h4 id="type-parameters-6"><a href="#type-parameters-6" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a> = <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr> <tr><td style="text-align:left;"><code>TData</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/#defaultdatapoint"><code>DefaultDataPoint</code></a>&lt;<code>TType</code>&gt;</td></tr></tbody></table> <h4 id="defined-in-12"><a href="#defined-in-12" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3666" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3666<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="chartitem"><a href="#chartitem" class="header-anchor">#</a> ChartItem</h3> <p>Ƭ <strong>ChartItem</strong>: <code>string</code> | <code>CanvasRenderingContext2D</code> | <code>HTMLCanvasElement</code> | { <code>canvas</code>: <code>HTMLCanvasElement</code>  } | <code>ArrayLike</code>&lt;<code>CanvasRenderingContext2D</code> | <code>HTMLCanvasElement</code>&gt;</p> <h4 id="defined-in-13"><a href="#defined-in-13" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L554" target="_blank" rel="noopener noreferrer">index.esm.d.ts:554<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="chartmeta"><a href="#chartmeta" class="header-anchor">#</a> ChartMeta</h3> <p>Ƭ <strong>ChartMeta</strong>&lt;<code>TElement</code>, <code>TDatasetElement</code>, <code>TType</code>&gt;: <code>DeepPartial</code>&lt;{ [key in ChartType]: ChartTypeRegistry[key][&quot;metaExtensions&quot;] }[<code>TType</code>]&gt; &amp; <code>ChartMetaCommon</code>&lt;<code>TElement</code>, <code>TDatasetElement</code>&gt;</p> <h4 id="type-parameters-7"><a href="#type-parameters-7" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TElement</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#element"><code>Element</code></a> = <a href="/docs/3.9.1/api/#element"><code>Element</code></a></td></tr> <tr><td style="text-align:left;"><code>TDatasetElement</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#element"><code>Element</code></a> = <a href="/docs/3.9.1/api/#element"><code>Element</code></a></td></tr> <tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a> = <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr></tbody></table> <h4 id="defined-in-14"><a href="#defined-in-14" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L460" target="_blank" rel="noopener noreferrer">index.esm.d.ts:460<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="chartoptions"><a href="#chartoptions" class="header-anchor">#</a> ChartOptions</h3> <p>Ƭ <strong>ChartOptions</strong>&lt;<code>TType</code>&gt;: <code>DeepPartial</code>&lt;<a href="/docs/3.9.1/api/interfaces/CoreChartOptions.html"><code>CoreChartOptions</code></a>&lt;<code>TType</code>&gt; &amp; <a href="/docs/3.9.1/api/#elementchartoptions"><code>ElementChartOptions</code></a>&lt;<code>TType</code>&gt; &amp; <a href="/docs/3.9.1/api/interfaces/PluginChartOptions.html"><code>PluginChartOptions</code></a>&lt;<code>TType</code>&gt; &amp; <a href="/docs/3.9.1/api/#datasetchartoptions"><code>DatasetChartOptions</code></a>&lt;<code>TType</code>&gt; &amp; <a href="/docs/3.9.1/api/#scalechartoptions"><code>ScaleChartOptions</code></a>&lt;<code>TType</code>&gt; &amp; <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>[<code>TType</code>][<code>&quot;chartOptions&quot;</code>]&gt;</p> <h4 id="type-parameters-8"><a href="#type-parameters-8" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a> = <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr></tbody></table> <h4 id="defined-in-15"><a href="#defined-in-15" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3636" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3636<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="charttype"><a href="#charttype" class="header-anchor">#</a> ChartType</h3> <p>Ƭ <strong>ChartType</strong>: keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a></p> <h4 id="defined-in-16"><a href="#defined-in-16" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3615" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3615<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="color"><a href="#color" class="header-anchor">#</a> Color</h3> <p>Ƭ <strong>Color</strong>: <code>string</code> | <code>CanvasGradient</code> | <code>CanvasPattern</code></p> <h4 id="defined-in-17"><a href="#defined-in-17" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/color.d.ts#L1" target="_blank" rel="noopener noreferrer">color.d.ts:1<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="datasetchartoptions"><a href="#datasetchartoptions" class="header-anchor">#</a> DatasetChartOptions</h3> <p>Ƭ <strong>DatasetChartOptions</strong>&lt;<code>TType</code>&gt;: { [key in TType]: Object }</p> <h4 id="type-parameters-9"><a href="#type-parameters-9" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a> = <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr></tbody></table> <h4 id="defined-in-18"><a href="#defined-in-18" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3624" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3624<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="decimationoptions"><a href="#decimationoptions" class="header-anchor">#</a> DecimationOptions</h3> <p>Ƭ <strong>DecimationOptions</strong>: <code>LttbDecimationOptions</code> | <code>MinMaxDecimationOptions</code></p> <h4 id="defined-in-19"><a href="#defined-in-19" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2130" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2130<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="defaultdatapoint"><a href="#defaultdatapoint" class="header-anchor">#</a> DefaultDataPoint</h3> <p>Ƭ <strong>DefaultDataPoint</strong>&lt;<code>TType</code>&gt;: <code>DistributiveArray</code>&lt;<a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>[<code>TType</code>][<code>&quot;defaultDataPoint&quot;</code>]&gt;</p> <h4 id="type-parameters-10"><a href="#type-parameters-10" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr></tbody></table> <h4 id="defined-in-20"><a href="#defined-in-20" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3645" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3645<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="doughnutdatapoint"><a href="#doughnutdatapoint" class="header-anchor">#</a> DoughnutDataPoint</h3> <p>Ƭ <strong>DoughnutDataPoint</strong>: <code>number</code></p> <h4 id="defined-in-21"><a href="#defined-in-21" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L331" target="_blank" rel="noopener noreferrer">index.esm.d.ts:331<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="easingfunction"><a href="#easingfunction" class="header-anchor">#</a> EasingFunction</h3> <p>Ƭ <strong>EasingFunction</strong>: <code>&quot;linear&quot;</code> | <code>&quot;easeInQuad&quot;</code> | <code>&quot;easeOutQuad&quot;</code> | <code>&quot;easeInOutQuad&quot;</code> | <code>&quot;easeInCubic&quot;</code> | <code>&quot;easeOutCubic&quot;</code> | <code>&quot;easeInOutCubic&quot;</code> | <code>&quot;easeInQuart&quot;</code> | <code>&quot;easeOutQuart&quot;</code> | <code>&quot;easeInOutQuart&quot;</code> | <code>&quot;easeInQuint&quot;</code> | <code>&quot;easeOutQuint&quot;</code> | <code>&quot;easeInOutQuint&quot;</code> | <code>&quot;easeInSine&quot;</code> | <code>&quot;easeOutSine&quot;</code> | <code>&quot;easeInOutSine&quot;</code> | <code>&quot;easeInExpo&quot;</code> | <code>&quot;easeOutExpo&quot;</code> | <code>&quot;easeInOutExpo&quot;</code> | <code>&quot;easeInCirc&quot;</code> | <code>&quot;easeOutCirc&quot;</code> | <code>&quot;easeInOutCirc&quot;</code> | <code>&quot;easeInElastic&quot;</code> | <code>&quot;easeOutElastic&quot;</code> | <code>&quot;easeInOutElastic&quot;</code> | <code>&quot;easeInBack&quot;</code> | <code>&quot;easeOutBack&quot;</code> | <code>&quot;easeInOutBack&quot;</code> | <code>&quot;easeInBounce&quot;</code> | <code>&quot;easeOutBounce&quot;</code> | <code>&quot;easeInOutBounce&quot;</code></p> <h4 id="defined-in-22"><a href="#defined-in-22" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1550" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1550<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="elementchartoptions"><a href="#elementchartoptions" class="header-anchor">#</a> ElementChartOptions</h3> <p>Ƭ <strong>ElementChartOptions</strong>&lt;<code>TType</code>&gt;: <code>Object</code></p> <h4 id="type-parameters-11"><a href="#type-parameters-11" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a> = <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr></tbody></table> <h4 id="type-declaration-3"><a href="#type-declaration-3" class="header-anchor">#</a> Type declaration</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>elements</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/ElementOptionsByType.html"><code>ElementOptionsByType</code></a>&lt;<code>TType</code>&gt;</td></tr></tbody></table> <h4 id="defined-in-23"><a href="#defined-in-23" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2046" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2046<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="filltarget"><a href="#filltarget" class="header-anchor">#</a> FillTarget</h3> <p>Ƭ <strong>FillTarget</strong>: <code>number</code> | <code>string</code> | { <code>value</code>: <code>number</code>  } | <code>&quot;start&quot;</code> | <code>&quot;end&quot;</code> | <code>&quot;origin&quot;</code> | <code>&quot;stack&quot;</code> | <code>&quot;shape&quot;</code> | <code>boolean</code></p> <h4 id="defined-in-24"><a href="#defined-in-24" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2138" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2138<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="interactionaxis"><a href="#interactionaxis" class="header-anchor">#</a> InteractionAxis</h3> <p>Ƭ <strong>InteractionAxis</strong>: <code>&quot;x&quot;</code> | <code>&quot;y&quot;</code> | <code>&quot;xy&quot;</code> | <code>&quot;r&quot;</code></p> <h4 id="defined-in-25"><a href="#defined-in-25" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1422" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1422<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="interactionmode"><a href="#interactionmode" class="header-anchor">#</a> InteractionMode</h3> <p>Ƭ <strong>InteractionMode</strong>: keyof <a href="/docs/3.9.1/api/interfaces/InteractionModeMap.html"><code>InteractionModeMap</code></a></p> <h4 id="defined-in-26"><a href="#defined-in-26" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L752" target="_blank" rel="noopener noreferrer">index.esm.d.ts:752<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="interactionmodefunction"><a href="#interactionmodefunction" class="header-anchor">#</a> InteractionModeFunction</h3> <p>Ƭ <strong>InteractionModeFunction</strong>: (<code>chart</code>: <a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>, <code>e</code>: <a href="/docs/3.9.1/api/interfaces/ChartEvent.html"><code>ChartEvent</code></a>, <code>options</code>: <a href="/docs/3.9.1/api/interfaces/InteractionOptions.html"><code>InteractionOptions</code></a>, <code>useFinalPosition?</code>: <code>boolean</code>) =&gt; <a href="/docs/3.9.1/api/interfaces/InteractionItem.html"><code>InteractionItem</code></a>[]</p> <h4 id="type-declaration-4"><a href="#type-declaration-4" class="header-anchor">#</a> Type declaration</h4> <p>▸ (<code>chart</code>, <code>e</code>, <code>options</code>, <code>useFinalPosition?</code>): <a href="/docs/3.9.1/api/interfaces/InteractionItem.html"><code>InteractionItem</code></a>[]</p> <h5 id="parameters"><a href="#parameters" class="header-anchor">#</a> Parameters</h5> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a></td></tr> <tr><td style="text-align:left;"><code>e</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/ChartEvent.html"><code>ChartEvent</code></a></td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/InteractionOptions.html"><code>InteractionOptions</code></a></td></tr> <tr><td style="text-align:left;"><code>useFinalPosition?</code></td> <td style="text-align:left;"><code>boolean</code></td></tr></tbody></table> <h5 id="returns"><a href="#returns" class="header-anchor">#</a> Returns</h5> <p><a href="/docs/3.9.1/api/interfaces/InteractionItem.html"><code>InteractionItem</code></a>[]</p> <h4 id="defined-in-27"><a href="#defined-in-27" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L714" target="_blank" rel="noopener noreferrer">index.esm.d.ts:714<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="layoutposition"><a href="#layoutposition" class="header-anchor">#</a> LayoutPosition</h3> <p>Ƭ <strong>LayoutPosition</strong>: <code>&quot;left&quot;</code> | <code>&quot;top&quot;</code> | <code>&quot;right&quot;</code> | <code>&quot;bottom&quot;</code> | <code>&quot;center&quot;</code> | <code>&quot;chartArea&quot;</code> | { [scaleId: string]: <code>number</code>;  }</p> <h4 id="defined-in-28"><a href="#defined-in-28" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L3" target="_blank" rel="noopener noreferrer">layout.d.ts:3<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="linecontroller"><a href="#linecontroller" class="header-anchor">#</a> LineController</h3> <p>Ƭ <strong>LineController</strong>: <a href="/docs/3.9.1/api/classes/DatasetController.html"><code>DatasetController</code></a></p> <h4 id="defined-in-29"><a href="#defined-in-29" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L219" target="_blank" rel="noopener noreferrer">index.esm.d.ts:219<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="linearscale"><a href="#linearscale" class="header-anchor">#</a> LinearScale</h3> <p>Ƭ <strong>LinearScale</strong>&lt;<code>O</code>&gt;: <a href="/docs/3.9.1/api/classes/Scale.html"><code>Scale</code></a>&lt;<code>O</code>&gt;</p> <h4 id="type-parameters-12"><a href="#type-parameters-12" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#linearscaleoptions"><code>LinearScaleOptions</code></a> = <a href="/docs/3.9.1/api/#linearscaleoptions"><code>LinearScaleOptions</code></a></td></tr></tbody></table> <h4 id="defined-in-30"><a href="#defined-in-30" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3196" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3196<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="linearscaleoptions"><a href="#linearscaleoptions" class="header-anchor">#</a> LinearScaleOptions</h3> <p>Ƭ <strong>LinearScaleOptions</strong>: <a href="/docs/3.9.1/api/interfaces/CartesianScaleOptions.html"><code>CartesianScaleOptions</code></a> &amp; { <code>beginAtZero</code>: <code>boolean</code> ; <code>grace?</code>: <code>string</code> | <code>number</code> ; <code>suggestedMax?</code>: <code>number</code> ; <code>suggestedMin?</code>: <code>number</code> ; <code>ticks</code>: { <code>count</code>: <code>number</code> ; <code>format</code>: <code>Intl.NumberFormatOptions</code> ; <code>precision</code>: <code>number</code> ; <code>stepSize</code>: <code>number</code>  }  }</p> <h4 id="defined-in-31"><a href="#defined-in-31" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3153" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3153<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="logarithmicscale"><a href="#logarithmicscale" class="header-anchor">#</a> LogarithmicScale</h3> <p>Ƭ <strong>LogarithmicScale</strong>&lt;<code>O</code>&gt;: <a href="/docs/3.9.1/api/classes/Scale.html"><code>Scale</code></a>&lt;<code>O</code>&gt;</p> <h4 id="type-parameters-13"><a href="#type-parameters-13" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#logarithmicscaleoptions"><code>LogarithmicScaleOptions</code></a> = <a href="/docs/3.9.1/api/#logarithmicscaleoptions"><code>LogarithmicScaleOptions</code></a></td></tr></tbody></table> <h4 id="defined-in-32"><a href="#defined-in-32" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3220" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3220<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="logarithmicscaleoptions"><a href="#logarithmicscaleoptions" class="header-anchor">#</a> LogarithmicScaleOptions</h3> <p>Ƭ <strong>LogarithmicScaleOptions</strong>: <a href="/docs/3.9.1/api/interfaces/CartesianScaleOptions.html"><code>CartesianScaleOptions</code></a> &amp; { <code>suggestedMax?</code>: <code>number</code> ; <code>suggestedMin?</code>: <code>number</code> ; <code>ticks</code>: { <code>format</code>: <code>Intl.NumberFormatOptions</code>  }  }</p> <h4 id="defined-in-33"><a href="#defined-in-33" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3202" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3202<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="overrides"><a href="#overrides" class="header-anchor">#</a> Overrides</h3> <p>Ƭ <strong>Overrides</strong>: { [key in ChartType]: CoreChartOptions&lt;key&gt; &amp; ElementChartOptions&lt;key&gt; &amp; PluginChartOptions&lt;key&gt; &amp; DatasetChartOptions&lt;ChartType&gt; &amp; ScaleChartOptions&lt;key&gt; &amp; ChartTypeRegistry[key][&quot;chartOptions&quot;] }</p> <h4 id="defined-in-34"><a href="#defined-in-34" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L691" target="_blank" rel="noopener noreferrer">index.esm.d.ts:691<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="parseddatatype"><a href="#parseddatatype" class="header-anchor">#</a> ParsedDataType</h3> <p>Ƭ <strong>ParsedDataType</strong>&lt;<code>TType</code>&gt;: <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>[<code>TType</code>][<code>&quot;parsedDataType&quot;</code>]</p> <h4 id="type-parameters-14"><a href="#type-parameters-14" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a> = <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr></tbody></table> <h4 id="defined-in-35"><a href="#defined-in-35" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3647" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3647<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="pieanimationoptions"><a href="#pieanimationoptions" class="header-anchor">#</a> PieAnimationOptions</h3> <p>Ƭ <strong>PieAnimationOptions</strong>: <a href="/docs/3.9.1/api/interfaces/DoughnutAnimationOptions.html"><code>DoughnutAnimationOptions</code></a></p> <h4 id="defined-in-36"><a href="#defined-in-36" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L354" target="_blank" rel="noopener noreferrer">index.esm.d.ts:354<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="piecontroller"><a href="#piecontroller" class="header-anchor">#</a> PieController</h3> <p>Ƭ <strong>PieController</strong>: <a href="/docs/3.9.1/api/#doughnutcontroller"><code>DoughnutController</code></a></p> <h4 id="defined-in-37"><a href="#defined-in-37" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L359" target="_blank" rel="noopener noreferrer">index.esm.d.ts:359<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="piecontrollerchartoptions"><a href="#piecontrollerchartoptions" class="header-anchor">#</a> PieControllerChartOptions</h3> <p>Ƭ <strong>PieControllerChartOptions</strong>: <a href="/docs/3.9.1/api/interfaces/DoughnutControllerChartOptions.html"><code>DoughnutControllerChartOptions</code></a></p> <h4 id="defined-in-38"><a href="#defined-in-38" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L353" target="_blank" rel="noopener noreferrer">index.esm.d.ts:353<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="piecontrollerdatasetoptions"><a href="#piecontrollerdatasetoptions" class="header-anchor">#</a> PieControllerDatasetOptions</h3> <p>Ƭ <strong>PieControllerDatasetOptions</strong>: <a href="/docs/3.9.1/api/interfaces/DoughnutControllerDatasetOptions.html"><code>DoughnutControllerDatasetOptions</code></a></p> <h4 id="defined-in-39"><a href="#defined-in-39" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L352" target="_blank" rel="noopener noreferrer">index.esm.d.ts:352<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="piedatapoint"><a href="#piedatapoint" class="header-anchor">#</a> PieDataPoint</h3> <p>Ƭ <strong>PieDataPoint</strong>: <a href="/docs/3.9.1/api/#doughnutdatapoint"><code>DoughnutDataPoint</code></a></p> <h4 id="defined-in-40"><a href="#defined-in-40" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L356" target="_blank" rel="noopener noreferrer">index.esm.d.ts:356<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="piemetaextensions"><a href="#piemetaextensions" class="header-anchor">#</a> PieMetaExtensions</h3> <p>Ƭ <strong>PieMetaExtensions</strong>: <a href="/docs/3.9.1/api/interfaces/DoughnutMetaExtensions.html"><code>DoughnutMetaExtensions</code></a></p> <h4 id="defined-in-41"><a href="#defined-in-41" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L357" target="_blank" rel="noopener noreferrer">index.esm.d.ts:357<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="pointstyle"><a href="#pointstyle" class="header-anchor">#</a> PointStyle</h3> <p>Ƭ <strong>PointStyle</strong>: <code>&quot;circle&quot;</code> | <code>&quot;cross&quot;</code> | <code>&quot;crossRot&quot;</code> | <code>&quot;dash&quot;</code> | <code>&quot;line&quot;</code> | <code>&quot;rect&quot;</code> | <code>&quot;rectRounded&quot;</code> | <code>&quot;rectRot&quot;</code> | <code>&quot;star&quot;</code> | <code>&quot;triangle&quot;</code> | <code>HTMLImageElement</code> | <code>HTMLCanvasElement</code></p> <h4 id="defined-in-42"><a href="#defined-in-42" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1865" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1865<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="polarareaanimationoptions"><a href="#polarareaanimationoptions" class="header-anchor">#</a> PolarAreaAnimationOptions</h3> <p>Ƭ <strong>PolarAreaAnimationOptions</strong>: <a href="/docs/3.9.1/api/interfaces/DoughnutAnimationOptions.html"><code>DoughnutAnimationOptions</code></a></p> <h4 id="defined-in-43"><a href="#defined-in-43" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L373" target="_blank" rel="noopener noreferrer">index.esm.d.ts:373<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="radarcontroller"><a href="#radarcontroller" class="header-anchor">#</a> RadarController</h3> <p>Ƭ <strong>RadarController</strong>: <a href="/docs/3.9.1/api/classes/DatasetController.html"><code>DatasetController</code></a></p> <h4 id="defined-in-44"><a href="#defined-in-44" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L420" target="_blank" rel="noopener noreferrer">index.esm.d.ts:420<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="radarcontrollerchartoptions"><a href="#radarcontrollerchartoptions" class="header-anchor">#</a> RadarControllerChartOptions</h3> <p>Ƭ <strong>RadarControllerChartOptions</strong>: <a href="/docs/3.9.1/api/interfaces/LineControllerChartOptions.html"><code>LineControllerChartOptions</code></a></p> <h4 id="defined-in-45"><a href="#defined-in-45" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L418" target="_blank" rel="noopener noreferrer">index.esm.d.ts:418<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="radiallinearscaleoptions"><a href="#radiallinearscaleoptions" class="header-anchor">#</a> RadialLinearScaleOptions</h3> <p>Ƭ <strong>RadialLinearScaleOptions</strong>: <a href="/docs/3.9.1/api/interfaces/CoreScaleOptions.html"><code>CoreScaleOptions</code></a> &amp; { <code>angleLines</code>: { <code>borderDash</code>: <a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<code>number</code>[], <a href="/docs/3.9.1/api/interfaces/ScriptableScaleContext.html"><code>ScriptableScaleContext</code></a>&gt; ; <code>borderDashOffset</code>: <a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<code>number</code>, <a href="/docs/3.9.1/api/interfaces/ScriptableScaleContext.html"><code>ScriptableScaleContext</code></a>&gt; ; <code>color</code>: <a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<a href="/docs/3.9.1/api/#color"><code>Color</code></a>, <a href="/docs/3.9.1/api/interfaces/ScriptableScaleContext.html"><code>ScriptableScaleContext</code></a>&gt; ; <code>display</code>: <code>boolean</code> ; <code>lineWidth</code>: <a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<code>number</code>, <a href="/docs/3.9.1/api/interfaces/ScriptableScaleContext.html"><code>ScriptableScaleContext</code></a>&gt;  } ; <code>animate</code>: <code>boolean</code> ; <code>beginAtZero</code>: <code>boolean</code> ; <code>grid</code>: <a href="/docs/3.9.1/api/interfaces/GridLineOptions.html"><code>GridLineOptions</code></a> ; <code>max</code>: <code>number</code> ; <code>min</code>: <code>number</code> ; <code>pointLabels</code>: { <code>backdropColor</code>: <a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<a href="/docs/3.9.1/api/#color"><code>Color</code></a>, <a href="/docs/3.9.1/api/interfaces/ScriptableScalePointLabelContext.html"><code>ScriptableScalePointLabelContext</code></a>&gt; ; <code>backdropPadding</code>: <a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ChartArea.html"><code>ChartArea</code></a>, <a href="/docs/3.9.1/api/interfaces/ScriptableScalePointLabelContext.html"><code>ScriptableScalePointLabelContext</code></a>&gt; ; <code>borderRadius</code>: <a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<code>number</code> | <a href="/docs/3.9.1/api/interfaces/BorderRadius.html"><code>BorderRadius</code></a>, <a href="/docs/3.9.1/api/interfaces/ScriptableScalePointLabelContext.html"><code>ScriptableScalePointLabelContext</code></a>&gt; ; <code>centerPointLabels</code>: <code>boolean</code> ; <code>color</code>: <a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<a href="/docs/3.9.1/api/#color"><code>Color</code></a>, <a href="/docs/3.9.1/api/interfaces/ScriptableScalePointLabelContext.html"><code>ScriptableScalePointLabelContext</code></a>&gt; ; <code>display</code>: <code>boolean</code> ; <code>font</code>: <a href="/docs/3.9.1/api/#scriptableandscriptableoptions"><code>ScriptableAndScriptableOptions</code></a>&lt;<code>Partial</code>&lt;<a href="/docs/3.9.1/api/interfaces/FontSpec.html"><code>FontSpec</code></a>&gt;, <a href="/docs/3.9.1/api/interfaces/ScriptableScalePointLabelContext.html"><code>ScriptableScalePointLabelContext</code></a>&gt; ; <code>padding</code>: <a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<code>number</code>, <a href="/docs/3.9.1/api/interfaces/ScriptableScalePointLabelContext.html"><code>ScriptableScalePointLabelContext</code></a>&gt; ; <code>callback</code>: (<code>label</code>: <code>string</code>, <code>index</code>: <code>number</code>) =&gt; <code>string</code> | <code>number</code> | <code>string</code>[] | <code>number</code>[]  } ; <code>startAngle</code>: <code>number</code> ; <code>suggestedMax</code>: <code>number</code> ; <code>suggestedMin</code>: <code>number</code> ; <code>ticks</code>: <a href="/docs/3.9.1/api/#radialtickoptions"><code>RadialTickOptions</code></a>  }</p> <h4 id="defined-in-46"><a href="#defined-in-46" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3356" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3356<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="radialtickoptions"><a href="#radialtickoptions" class="header-anchor">#</a> RadialTickOptions</h3> <p>Ƭ <strong>RadialTickOptions</strong>: <a href="/docs/3.9.1/api/interfaces/TickOptions.html"><code>TickOptions</code></a> &amp; { <code>count</code>: <code>number</code> ; <code>format</code>: <code>Intl.NumberFormatOptions</code> ; <code>maxTicksLimit</code>: <code>number</code> ; <code>precision</code>: <code>number</code> ; <code>stepSize</code>: <code>number</code>  }</p> <h4 id="defined-in-47"><a href="#defined-in-47" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3328" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3328<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="scalechartoptions"><a href="#scalechartoptions" class="header-anchor">#</a> ScaleChartOptions</h3> <p>Ƭ <strong>ScaleChartOptions</strong>&lt;<code>TType</code>&gt;: <code>Object</code></p> <h4 id="type-parameters-15"><a href="#type-parameters-15" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a> = <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr></tbody></table> <h4 id="type-declaration-5"><a href="#type-declaration-5" class="header-anchor">#</a> Type declaration</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>scales</code></td> <td style="text-align:left;"><code>Object</code></td></tr></tbody></table> <h4 id="defined-in-48"><a href="#defined-in-48" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3630" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3630<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="scaleoptions"><a href="#scaleoptions" class="header-anchor">#</a> ScaleOptions</h3> <p>Ƭ <strong>ScaleOptions</strong>&lt;<code>TScale</code>&gt;: <code>DeepPartial</code>&lt;<a href="/docs/3.9.1/api/#scaleoptionsbytype"><code>ScaleOptionsByType</code></a>&lt;<code>TScale</code>&gt;&gt;</p> <h4 id="type-parameters-16"><a href="#type-parameters-16" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TScale</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#scaletype"><code>ScaleType</code></a> = <a href="/docs/3.9.1/api/#scaletype"><code>ScaleType</code></a></td></tr></tbody></table> <h4 id="defined-in-49"><a href="#defined-in-49" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3622" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3622<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="scaleoptionsbytype"><a href="#scaleoptionsbytype" class="header-anchor">#</a> ScaleOptionsByType</h3> <p>Ƭ <strong>ScaleOptionsByType</strong>&lt;<code>TScale</code>&gt;: { [key in ScaleType]: Object &amp; ScaleTypeRegistry[key][&quot;options&quot;] }[<code>TScale</code>]</p> <h4 id="type-parameters-17"><a href="#type-parameters-17" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TScale</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#scaletype"><code>ScaleType</code></a> = <a href="/docs/3.9.1/api/#scaletype"><code>ScaleType</code></a></td></tr></tbody></table> <h4 id="defined-in-50"><a href="#defined-in-50" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3617" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3617<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="scaletype"><a href="#scaletype" class="header-anchor">#</a> ScaleType</h3> <p>Ƭ <strong>ScaleType</strong>: keyof <a href="/docs/3.9.1/api/interfaces/ScaleTypeRegistry.html"><code>ScaleTypeRegistry</code></a></p> <h4 id="defined-in-51"><a href="#defined-in-51" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3511" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3511<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="scattercontroller"><a href="#scattercontroller" class="header-anchor">#</a> ScatterController</h3> <p>Ƭ <strong>ScatterController</strong>: <a href="/docs/3.9.1/api/#linecontroller"><code>LineController</code></a></p> <h4 id="defined-in-52"><a href="#defined-in-52" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L234" target="_blank" rel="noopener noreferrer">index.esm.d.ts:234<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="scattercontrollerchartoptions"><a href="#scattercontrollerchartoptions" class="header-anchor">#</a> ScatterControllerChartOptions</h3> <p>Ƭ <strong>ScatterControllerChartOptions</strong>: <a href="/docs/3.9.1/api/interfaces/LineControllerChartOptions.html"><code>LineControllerChartOptions</code></a></p> <h4 id="defined-in-53"><a href="#defined-in-53" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L232" target="_blank" rel="noopener noreferrer">index.esm.d.ts:232<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="scattercontrollerdatasetoptions"><a href="#scattercontrollerdatasetoptions" class="header-anchor">#</a> ScatterControllerDatasetOptions</h3> <p>Ƭ <strong>ScatterControllerDatasetOptions</strong>: <a href="/docs/3.9.1/api/interfaces/LineControllerDatasetOptions.html"><code>LineControllerDatasetOptions</code></a></p> <h4 id="defined-in-54"><a href="#defined-in-54" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L225" target="_blank" rel="noopener noreferrer">index.esm.d.ts:225<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="scriptable"><a href="#scriptable" class="header-anchor">#</a> Scriptable</h3> <p>Ƭ <strong>Scriptable</strong>&lt;<code>T</code>, <code>TContext</code>&gt;: <code>T</code> | (<code>ctx</code>: <code>TContext</code>, <code>options</code>: <code>AnyObject</code>) =&gt; <code>T</code> | <code>undefined</code></p> <h4 id="type-parameters-18"><a href="#type-parameters-18" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>T</code></td></tr> <tr><td style="text-align:left;"><code>TContext</code></td></tr></tbody></table> <h4 id="defined-in-55"><a href="#defined-in-55" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L39" target="_blank" rel="noopener noreferrer">index.esm.d.ts:39<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="scriptableandarray"><a href="#scriptableandarray" class="header-anchor">#</a> ScriptableAndArray</h3> <p>Ƭ <strong>ScriptableAndArray</strong>&lt;<code>T</code>, <code>TContext</code>&gt;: readonly <code>T</code>[] | <a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<code>T</code>, <code>TContext</code>&gt;</p> <h4 id="type-parameters-19"><a href="#type-parameters-19" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>T</code></td></tr> <tr><td style="text-align:left;"><code>TContext</code></td></tr></tbody></table> <h4 id="defined-in-56"><a href="#defined-in-56" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L42" target="_blank" rel="noopener noreferrer">index.esm.d.ts:42<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="scriptableandarrayoptions"><a href="#scriptableandarrayoptions" class="header-anchor">#</a> ScriptableAndArrayOptions</h3> <p>Ƭ <strong>ScriptableAndArrayOptions</strong>&lt;<code>T</code>, <code>TContext</code>&gt;: { [P in keyof T]: ScriptableAndArray&lt;T[P], TContext&gt; }</p> <h4 id="type-parameters-20"><a href="#type-parameters-20" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>T</code></td></tr> <tr><td style="text-align:left;"><code>TContext</code></td></tr></tbody></table> <h4 id="defined-in-57"><a href="#defined-in-57" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L43" target="_blank" rel="noopener noreferrer">index.esm.d.ts:43<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="scriptableandscriptableoptions"><a href="#scriptableandscriptableoptions" class="header-anchor">#</a> ScriptableAndScriptableOptions</h3> <p>Ƭ <strong>ScriptableAndScriptableOptions</strong>&lt;<code>T</code>, <code>TContext</code>&gt;: <a href="/docs/3.9.1/api/#scriptable"><code>Scriptable</code></a>&lt;<code>T</code>, <code>TContext</code>&gt; | <a href="/docs/3.9.1/api/#scriptableoptions"><code>ScriptableOptions</code></a>&lt;<code>T</code>, <code>TContext</code>&gt;</p> <h4 id="type-parameters-21"><a href="#type-parameters-21" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>T</code></td></tr> <tr><td style="text-align:left;"><code>TContext</code></td></tr></tbody></table> <h4 id="defined-in-58"><a href="#defined-in-58" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L41" target="_blank" rel="noopener noreferrer">index.esm.d.ts:41<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="scriptableoptions"><a href="#scriptableoptions" class="header-anchor">#</a> ScriptableOptions</h3> <p>Ƭ <strong>ScriptableOptions</strong>&lt;<code>T</code>, <code>TContext</code>&gt;: { [P in keyof T]: Scriptable&lt;T[P], TContext&gt; }</p> <h4 id="type-parameters-22"><a href="#type-parameters-22" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>T</code></td></tr> <tr><td style="text-align:left;"><code>TContext</code></td></tr></tbody></table> <h4 id="defined-in-59"><a href="#defined-in-59" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L40" target="_blank" rel="noopener noreferrer">index.esm.d.ts:40<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="textalign"><a href="#textalign" class="header-anchor">#</a> TextAlign</h3> <p>Ƭ <strong>TextAlign</strong>: <code>&quot;left&quot;</code> | <code>&quot;center&quot;</code> | <code>&quot;right&quot;</code></p> <h4 id="defined-in-60"><a href="#defined-in-60" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1681" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1681<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="timescaleoptions"><a href="#timescaleoptions" class="header-anchor">#</a> TimeScaleOptions</h3> <p>Ƭ <strong>TimeScaleOptions</strong>: <code>Omit</code>&lt;<a href="/docs/3.9.1/api/interfaces/CartesianScaleOptions.html"><code>CartesianScaleOptions</code></a>, <code>&quot;min&quot;</code> | <code>&quot;max&quot;</code>&gt; &amp; { <code>adapters</code>: { <code>date</code>: <code>unknown</code>  } ; <code>bounds</code>: <code>&quot;ticks&quot;</code> | <code>&quot;data&quot;</code> ; <code>max</code>: <code>string</code> | <code>number</code> ; <code>min</code>: <code>string</code> | <code>number</code> ; <code>offsetAfterAutoskip</code>: <code>boolean</code> ; <code>suggestedMax</code>: <code>string</code> | <code>number</code> ; <code>suggestedMin</code>: <code>string</code> | <code>number</code> ; <code>ticks</code>: { <code>source</code>: <code>&quot;labels&quot;</code> | <code>&quot;auto&quot;</code> | <code>&quot;data&quot;</code>  } ; <code>time</code>: { <code>displayFormats</code>: { [key: string]: <code>string</code>;  } ; <code>isoWeekday</code>: <code>boolean</code> | <code>number</code> ; <code>minUnit</code>: <a href="/docs/3.9.1/api/#timeunit"><code>TimeUnit</code></a> ; <code>parser</code>: <code>string</code> | (<code>v</code>: <code>unknown</code>) =&gt; <code>number</code> ; <code>round</code>: <code>false</code> | <a href="/docs/3.9.1/api/#timeunit"><code>TimeUnit</code></a> ; <code>stepSize</code>: <code>number</code> ; <code>tooltipFormat</code>: <code>string</code> ; <code>unit</code>: <code>false</code> | <a href="/docs/3.9.1/api/#timeunit"><code>TimeUnit</code></a>  }  }</p> <h4 id="defined-in-61"><a href="#defined-in-61" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3226" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3226<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="timeseriesscale"><a href="#timeseriesscale" class="header-anchor">#</a> TimeSeriesScale</h3> <p>Ƭ <strong>TimeSeriesScale</strong>&lt;<code>O</code>&gt;: <a href="/docs/3.9.1/api/#timescale"><code>TimeScale</code></a>&lt;<code>O</code>&gt;</p> <h4 id="type-parameters-23"><a href="#type-parameters-23" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#timescaleoptions"><code>TimeScaleOptions</code></a> = <a href="/docs/3.9.1/api/#timescaleoptions"><code>TimeScaleOptions</code></a></td></tr></tbody></table> <h4 id="defined-in-62"><a href="#defined-in-62" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3322" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3322<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="timeunit"><a href="#timeunit" class="header-anchor">#</a> TimeUnit</h3> <p>Ƭ <strong>TimeUnit</strong>: <code>&quot;millisecond&quot;</code> | <code>&quot;second&quot;</code> | <code>&quot;minute&quot;</code> | <code>&quot;hour&quot;</code> | <code>&quot;day&quot;</code> | <code>&quot;week&quot;</code> | <code>&quot;month&quot;</code> | <code>&quot;quarter&quot;</code> | <code>&quot;year&quot;</code></p> <h4 id="defined-in-63"><a href="#defined-in-63" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/adapters.d.ts#L3" target="_blank" rel="noopener noreferrer">adapters.d.ts:3<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="tooltippositioner"><a href="#tooltippositioner" class="header-anchor">#</a> TooltipPositioner</h3> <p>Ƭ <strong>TooltipPositioner</strong>: keyof <a href="/docs/3.9.1/api/interfaces/TooltipPositionerMap.html"><code>TooltipPositionerMap</code></a></p> <h4 id="defined-in-64"><a href="#defined-in-64" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2546" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2546<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="tooltippositionerfunction"><a href="#tooltippositionerfunction" class="header-anchor">#</a> TooltipPositionerFunction</h3> <p>Ƭ <strong>TooltipPositionerFunction</strong>&lt;<code>TType</code>&gt;: (<code>this</code>: <a href="/docs/3.9.1/api/interfaces/TooltipModel.html"><code>TooltipModel</code></a>&lt;<code>TType</code>&gt;, <code>items</code>: readonly <a href="/docs/3.9.1/api/interfaces/ActiveElement.html"><code>ActiveElement</code></a>[], <code>eventPosition</code>: <a href="/docs/3.9.1/api/interfaces/Point.html"><code>Point</code></a>) =&gt; <a href="/docs/3.9.1/api/interfaces/TooltipPosition.html"><code>TooltipPosition</code></a> | <code>false</code></p> <h4 id="type-parameters-24"><a href="#type-parameters-24" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr></tbody></table> <h4 id="type-declaration-6"><a href="#type-declaration-6" class="header-anchor">#</a> Type declaration</h4> <p>▸ (<code>this</code>, <code>items</code>, <code>eventPosition</code>): <a href="/docs/3.9.1/api/interfaces/TooltipPosition.html"><code>TooltipPosition</code></a> | <code>false</code></p> <h5 id="parameters-2"><a href="#parameters-2" class="header-anchor">#</a> Parameters</h5> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>this</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/TooltipModel.html"><code>TooltipModel</code></a>&lt;<code>TType</code>&gt;</td></tr> <tr><td style="text-align:left;"><code>items</code></td> <td style="text-align:left;">readonly <a href="/docs/3.9.1/api/interfaces/ActiveElement.html"><code>ActiveElement</code></a>[]</td></tr> <tr><td style="text-align:left;"><code>eventPosition</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/Point.html"><code>Point</code></a></td></tr></tbody></table> <h5 id="returns-2"><a href="#returns-2" class="header-anchor">#</a> Returns</h5> <p><a href="/docs/3.9.1/api/interfaces/TooltipPosition.html"><code>TooltipPosition</code></a> | <code>false</code></p> <h4 id="defined-in-65"><a href="#defined-in-65" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2535" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2535<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="tooltipxalignment"><a href="#tooltipxalignment" class="header-anchor">#</a> TooltipXAlignment</h3> <p>Ƭ <strong>TooltipXAlignment</strong>: <code>&quot;left&quot;</code> | <code>&quot;center&quot;</code> | <code>&quot;right&quot;</code></p> <h4 id="defined-in-66"><a href="#defined-in-66" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2444" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2444<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="tooltipyalignment"><a href="#tooltipyalignment" class="header-anchor">#</a> TooltipYAlignment</h3> <p>Ƭ <strong>TooltipYAlignment</strong>: <code>&quot;top&quot;</code> | <code>&quot;center&quot;</code> | <code>&quot;bottom&quot;</code></p> <h4 id="defined-in-67"><a href="#defined-in-67" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2445" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2445<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="transitionspec"><a href="#transitionspec" class="header-anchor">#</a> TransitionSpec</h3> <p>Ƭ <strong>TransitionSpec</strong>&lt;<code>TType</code>&gt;: <code>Object</code></p> <h4 id="type-parameters-25"><a href="#type-parameters-25" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr></tbody></table> <h4 id="type-declaration-7"><a href="#type-declaration-7" class="header-anchor">#</a> Type declaration</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>animation</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/#animationspec"><code>AnimationSpec</code></a>&lt;<code>TType</code>&gt;</td></tr> <tr><td style="text-align:left;"><code>animations</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/#animationsspec"><code>AnimationsSpec</code></a>&lt;<code>TType</code>&gt;</td></tr></tbody></table> <h4 id="defined-in-68"><a href="#defined-in-68" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1630" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1630<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="transitionsspec"><a href="#transitionsspec" class="header-anchor">#</a> TransitionsSpec</h3> <p>Ƭ <strong>TransitionsSpec</strong>&lt;<code>TType</code>&gt;: <code>Object</code></p> <h4 id="type-parameters-26"><a href="#type-parameters-26" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr></tbody></table> <h4 id="index-signature-2"><a href="#index-signature-2" class="header-anchor">#</a> Index signature</h4> <p>▪ [mode: <code>string</code>]: <a href="/docs/3.9.1/api/#transitionspec"><code>TransitionSpec</code></a>&lt;<code>TType</code>&gt;</p> <h4 id="defined-in-69"><a href="#defined-in-69" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1635" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1635<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="updatemode"><a href="#updatemode" class="header-anchor">#</a> UpdateMode</h3> <p>Ƭ <strong>UpdateMode</strong>: keyof typeof <a href="/docs/3.9.1/api/enums/UpdateModeEnum.html"><code>UpdateModeEnum</code></a></p> <h4 id="defined-in-70"><a href="#defined-in-70" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L571" target="_blank" rel="noopener noreferrer">index.esm.d.ts:571<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <h2 id="variables"><a href="#variables" class="header-anchor">#</a> Variables</h2> <h3 id="arcelement"><a href="#arcelement" class="header-anchor">#</a> ArcElement</h3> <p>• <strong>ArcElement</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#arcelement"><code>ArcElement</code></a>&lt;<a href="/docs/3.9.1/api/interfaces/ArcProps.html"><code>ArcProps</code></a>, <a href="/docs/3.9.1/api/interfaces/ArcOptions.html"><code>ArcOptions</code></a>&gt;  }</p> <h4 id="defined-in-71"><a href="#defined-in-71" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1765" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1765<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="barcontroller-2"><a href="#barcontroller-2" class="header-anchor">#</a> BarController</h3> <p>• <strong>BarController</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#barcontroller"><code>BarController</code></a>  }</p> <h4 id="defined-in-72"><a href="#defined-in-72" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L146" target="_blank" rel="noopener noreferrer">index.esm.d.ts:146<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="barelement"><a href="#barelement" class="header-anchor">#</a> BarElement</h3> <p>• <strong>BarElement</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#barelement"><code>BarElement</code></a>&lt;<a href="/docs/3.9.1/api/interfaces/BarProps.html"><code>BarProps</code></a>, <a href="/docs/3.9.1/api/interfaces/BarOptions.html"><code>BarOptions</code></a>&gt;  }</p> <h4 id="defined-in-73"><a href="#defined-in-73" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2034" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2034<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="bubblecontroller-2"><a href="#bubblecontroller-2" class="header-anchor">#</a> BubbleController</h3> <p>• <strong>BubbleController</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#bubblecontroller"><code>BubbleController</code></a>  }</p> <h4 id="defined-in-74"><a href="#defined-in-74" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L174" target="_blank" rel="noopener noreferrer">index.esm.d.ts:174<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="categoryscale-2"><a href="#categoryscale-2" class="header-anchor">#</a> CategoryScale</h3> <p>• <strong>CategoryScale</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#categoryscale"><code>CategoryScale</code></a>&lt;<a href="/docs/3.9.1/api/#categoryscaleoptions"><code>CategoryScaleOptions</code></a>&gt;  }</p> <h4 id="defined-in-75"><a href="#defined-in-75" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3148" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3148<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="decimation"><a href="#decimation" class="header-anchor">#</a> Decimation</h3> <p>• <strong>Decimation</strong>: <a href="/docs/3.9.1/api/interfaces/Plugin.html"><code>Plugin</code></a></p> <h4 id="defined-in-76"><a href="#defined-in-76" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2110" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2110<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="doughnutcontroller"><a href="#doughnutcontroller" class="header-anchor">#</a> DoughnutController</h3> <p>• <strong>DoughnutController</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#doughnutcontroller"><code>DoughnutController</code></a>  }</p> <h4 id="defined-in-77"><a href="#defined-in-77" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L343" target="_blank" rel="noopener noreferrer">index.esm.d.ts:343<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="element"><a href="#element" class="header-anchor">#</a> Element</h3> <p>• <strong>Element</strong>: <code>Object</code></p> <h4 id="type-declaration-8"><a href="#type-declaration-8" class="header-anchor">#</a> Type declaration</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>prototype</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/#element"><code>Element</code></a>&lt;<code>AnyObject</code>, <code>AnyObject</code>&gt;</td></tr></tbody></table> <h4 id="defined-in-78"><a href="#defined-in-78" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L14" target="_blank" rel="noopener noreferrer">element.d.ts:14<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="filler"><a href="#filler" class="header-anchor">#</a> Filler</h3> <p>• <strong>Filler</strong>: <a href="/docs/3.9.1/api/interfaces/Plugin.html"><code>Plugin</code></a></p> <h4 id="defined-in-79"><a href="#defined-in-79" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2132" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2132<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="interaction"><a href="#interaction" class="header-anchor">#</a> Interaction</h3> <p>• <strong>Interaction</strong>: <code>Object</code></p> <h4 id="type-declaration-9"><a href="#type-declaration-9" class="header-anchor">#</a> Type declaration</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>modes</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/InteractionModeMap.html"><code>InteractionModeMap</code></a></td></tr> <tr><td style="text-align:left;"><code>evaluateInteractionItems</code></td> <td style="text-align:left;">(<code>chart</code>: <a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;, <code>axis</code>: <a href="/docs/3.9.1/api/#interactionaxis"><code>InteractionAxis</code></a>, <code>position</code>: <a href="/docs/3.9.1/api/interfaces/Point.html"><code>Point</code></a>, <code>handler</code>: (<code>element</code>: <a href="/docs/3.9.1/api/#element"><code>Element</code></a>&lt;<code>AnyObject</code>, <code>AnyObject</code>&gt; &amp; <a href="/docs/3.9.1/api/interfaces/VisualElement.html"><code>VisualElement</code></a>, <code>datasetIndex</code>: <code>number</code>, <code>index</code>: <code>number</code>) =&gt; <code>void</code>, <code>intersect?</code>: <code>boolean</code>) =&gt; <a href="/docs/3.9.1/api/interfaces/InteractionItem.html"><code>InteractionItem</code></a>[]</td></tr></tbody></table> <h4 id="defined-in-80"><a href="#defined-in-80" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L754" target="_blank" rel="noopener noreferrer">index.esm.d.ts:754<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="legend"><a href="#legend" class="header-anchor">#</a> Legend</h3> <p>• <strong>Legend</strong>: <a href="/docs/3.9.1/api/interfaces/Plugin.html"><code>Plugin</code></a></p> <h4 id="defined-in-81"><a href="#defined-in-81" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2162" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2162<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="linecontroller-2"><a href="#linecontroller-2" class="header-anchor">#</a> LineController</h3> <p>• <strong>LineController</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#linecontroller"><code>LineController</code></a>  }</p> <h4 id="defined-in-82"><a href="#defined-in-82" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L220" target="_blank" rel="noopener noreferrer">index.esm.d.ts:220<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="lineelement"><a href="#lineelement" class="header-anchor">#</a> LineElement</h3> <p>• <strong>LineElement</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#lineelement"><code>LineElement</code></a>&lt;<a href="/docs/3.9.1/api/interfaces/LineProps.html"><code>LineProps</code></a>, <a href="/docs/3.9.1/api/interfaces/LineOptions.html"><code>LineOptions</code></a>&gt;  }</p> <h4 id="defined-in-83"><a href="#defined-in-83" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1855" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1855<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="linearscale-2"><a href="#linearscale-2" class="header-anchor">#</a> LinearScale</h3> <p>• <strong>LinearScale</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#linearscale"><code>LinearScale</code></a>&lt;<a href="/docs/3.9.1/api/#linearscaleoptions"><code>LinearScaleOptions</code></a>&gt;  }</p> <h4 id="defined-in-84"><a href="#defined-in-84" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3197" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3197<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="logarithmicscale-2"><a href="#logarithmicscale-2" class="header-anchor">#</a> LogarithmicScale</h3> <p>• <strong>LogarithmicScale</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#logarithmicscale"><code>LogarithmicScale</code></a>&lt;<a href="/docs/3.9.1/api/#logarithmicscaleoptions"><code>LogarithmicScaleOptions</code></a>&gt;  }</p> <h4 id="defined-in-85"><a href="#defined-in-85" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3221" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3221<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="piecontroller-2"><a href="#piecontroller-2" class="header-anchor">#</a> PieController</h3> <p>• <strong>PieController</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#doughnutcontroller"><code>DoughnutController</code></a>  }</p> <h4 id="defined-in-86"><a href="#defined-in-86" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L360" target="_blank" rel="noopener noreferrer">index.esm.d.ts:360<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="pointelement"><a href="#pointelement" class="header-anchor">#</a> PointElement</h3> <p>• <strong>PointElement</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#pointelement"><code>PointElement</code></a>&lt;<a href="/docs/3.9.1/api/interfaces/PointProps.html"><code>PointProps</code></a>, <a href="/docs/3.9.1/api/interfaces/PointOptions.html"><code>PointOptions</code></a>&gt;  }</p> <h4 id="defined-in-87"><a href="#defined-in-87" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1972" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1972<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="polarareacontroller"><a href="#polarareacontroller" class="header-anchor">#</a> PolarAreaController</h3> <p>• <strong>PolarAreaController</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#polarareacontroller"><code>PolarAreaController</code></a>  }</p> <h4 id="defined-in-88"><a href="#defined-in-88" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L388" target="_blank" rel="noopener noreferrer">index.esm.d.ts:388<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="radarcontroller-2"><a href="#radarcontroller-2" class="header-anchor">#</a> RadarController</h3> <p>• <strong>RadarController</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#radarcontroller"><code>RadarController</code></a>  }</p> <h4 id="defined-in-89"><a href="#defined-in-89" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L421" target="_blank" rel="noopener noreferrer">index.esm.d.ts:421<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="radiallinearscale"><a href="#radiallinearscale" class="header-anchor">#</a> RadialLinearScale</h3> <p>• <strong>RadialLinearScale</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#radiallinearscale"><code>RadialLinearScale</code></a>&lt;<a href="/docs/3.9.1/api/#radiallinearscaleoptions"><code>RadialLinearScaleOptions</code></a>&gt;  }</p> <h4 id="defined-in-90"><a href="#defined-in-90" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3479" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3479<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="scattercontroller-2"><a href="#scattercontroller-2" class="header-anchor">#</a> ScatterController</h3> <p>• <strong>ScatterController</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#linecontroller"><code>LineController</code></a>  }</p> <h4 id="defined-in-91"><a href="#defined-in-91" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L235" target="_blank" rel="noopener noreferrer">index.esm.d.ts:235<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="subtitle"><a href="#subtitle" class="header-anchor">#</a> SubTitle</h3> <p>• <strong>SubTitle</strong>: <a href="/docs/3.9.1/api/interfaces/Plugin.html"><code>Plugin</code></a></p> <h4 id="defined-in-92"><a href="#defined-in-92" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2402" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2402<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="ticks"><a href="#ticks" class="header-anchor">#</a> Ticks</h3> <p>• <strong>Ticks</strong>: <code>Object</code></p> <h4 id="type-declaration-10"><a href="#type-declaration-10" class="header-anchor">#</a> Type declaration</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>formatters</code></td> <td style="text-align:left;"><code>Object</code></td></tr> <tr><td style="text-align:left;"><code>formatters.logarithmic</code></td> <td style="text-align:left;">[object Object]</td></tr> <tr><td style="text-align:left;"><code>formatters.numeric</code></td> <td style="text-align:left;">[object Object]</td></tr> <tr><td style="text-align:left;"><code>formatters.values</code></td> <td style="text-align:left;">[object Object]</td></tr></tbody></table> <h4 id="defined-in-93"><a href="#defined-in-93" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1356" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1356<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="timescale"><a href="#timescale" class="header-anchor">#</a> TimeScale</h3> <p>• <strong>TimeScale</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#timescale"><code>TimeScale</code></a>&lt;<a href="/docs/3.9.1/api/#timescaleoptions"><code>TimeScaleOptions</code></a>&gt;  }</p> <h4 id="defined-in-94"><a href="#defined-in-94" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3317" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3317<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="timeseriesscale-2"><a href="#timeseriesscale-2" class="header-anchor">#</a> TimeSeriesScale</h3> <p>• <strong>TimeSeriesScale</strong>: <a href="/docs/3.9.1/api/interfaces/ChartComponent.html"><code>ChartComponent</code></a> &amp; { <code>prototype</code>: <a href="/docs/3.9.1/api/#timeseriesscale"><code>TimeSeriesScale</code></a>&lt;<a href="/docs/3.9.1/api/#timescaleoptions"><code>TimeScaleOptions</code></a>&gt;  }</p> <h4 id="defined-in-95"><a href="#defined-in-95" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L3323" target="_blank" rel="noopener noreferrer">index.esm.d.ts:3323<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="title"><a href="#title" class="header-anchor">#</a> Title</h3> <p>• <strong>Title</strong>: <a href="/docs/3.9.1/api/interfaces/Plugin.html"><code>Plugin</code></a></p> <h4 id="defined-in-96"><a href="#defined-in-96" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2403" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2403<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="tooltip"><a href="#tooltip" class="header-anchor">#</a> Tooltip</h3> <p>• <strong>Tooltip</strong>: <a href="/docs/3.9.1/api/#tooltip"><code>Tooltip</code></a></p> <h4 id="defined-in-97"><a href="#defined-in-97" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2552" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2552<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="adapters"><a href="#adapters" class="header-anchor">#</a> _adapters</h3> <p>• <strong>_adapters</strong>: <code>Object</code></p> <h4 id="type-declaration-11"><a href="#type-declaration-11" class="header-anchor">#</a> Type declaration</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>_date</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/DateAdapter.html"><code>DateAdapter</code></a></td></tr></tbody></table> <h4 id="defined-in-98"><a href="#defined-in-98" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/adapters.d.ts#L68" target="_blank" rel="noopener noreferrer">adapters.d.ts:68<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="defaults"><a href="#defaults" class="header-anchor">#</a> defaults</h3> <p>• <strong>defaults</strong>: <a href="/docs/3.9.1/api/interfaces/Defaults.html"><code>Defaults</code></a></p> <h4 id="defined-in-99"><a href="#defined-in-99" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L701" target="_blank" rel="noopener noreferrer">index.esm.d.ts:701<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="layouts"><a href="#layouts" class="header-anchor">#</a> layouts</h3> <p>• <strong>layouts</strong>: <code>Object</code></p> <h4 id="type-declaration-12"><a href="#type-declaration-12" class="header-anchor">#</a> Type declaration</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>addBox</code></td> <td style="text-align:left;">(<code>chart</code>: <a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;, <code>item</code>: <a href="/docs/3.9.1/api/interfaces/LayoutItem.html"><code>LayoutItem</code></a>) =&gt; <code>void</code></td></tr> <tr><td style="text-align:left;"><code>configure</code></td> <td style="text-align:left;">(<code>chart</code>: <a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;, <code>item</code>: <a href="/docs/3.9.1/api/interfaces/LayoutItem.html"><code>LayoutItem</code></a>, <code>options</code>: { <code>fullSize?</code>: <code>number</code> ; <code>position?</code>: <a href="/docs/3.9.1/api/#layoutposition"><code>LayoutPosition</code></a> ; <code>weight?</code>: <code>number</code>  }) =&gt; <code>void</code></td></tr> <tr><td style="text-align:left;"><code>removeBox</code></td> <td style="text-align:left;">(<code>chart</code>: <a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;, <code>layoutItem</code>: <a href="/docs/3.9.1/api/interfaces/LayoutItem.html"><code>LayoutItem</code></a>) =&gt; <code>void</code></td></tr> <tr><td style="text-align:left;"><code>update</code></td> <td style="text-align:left;">(<code>chart</code>: <a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;, <code>width</code>: <code>number</code>, <code>height</code>: <code>number</code>) =&gt; <code>void</code></td></tr></tbody></table> <h4 id="defined-in-100"><a href="#defined-in-100" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L769" target="_blank" rel="noopener noreferrer">index.esm.d.ts:769<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="registerables"><a href="#registerables" class="header-anchor">#</a> registerables</h3> <p>• <strong>registerables</strong>: readonly <a href="/docs/3.9.1/api/#chartcomponentlike"><code>ChartComponentLike</code></a>[]</p> <h4 id="defined-in-101"><a href="#defined-in-101" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L552" target="_blank" rel="noopener noreferrer">index.esm.d.ts:552<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="registry"><a href="#registry" class="header-anchor">#</a> registry</h3> <p>• <strong>registry</strong>: <a href="/docs/3.9.1/api/interfaces/Registry.html"><code>Registry</code></a></p> <h4 id="defined-in-102"><a href="#defined-in-102" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1139" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1139<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p></div> <footer class="page-edit"><!----> <!----></footer> <div class="page-nav"><p class="inner"><!----> <span class="next"><a href="/docs/3.9.1/api/enums/DecimationAlgorithm.html">
        DecimationAlgorithm
      </a>
      →
    </span></p></div> </main></div><div class="global-ui"></div></div>
    <script src="/docs/3.9.1/assets/js/app.7e0dc8c8.js" defer></script><script src="/docs/3.9.1/assets/js/2.3e2307f3.js" defer></script><script src="/docs/3.9.1/assets/js/9.63ebb16b.js" defer></script>
  </body>
</html>
