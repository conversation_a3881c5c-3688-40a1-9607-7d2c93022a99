<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Class: Scale | Chart.js</title>
    <meta name="generator" content="VuePress 1.8.3">
    <link rel="icon" href="/docs/3.9.1/favicon.ico">
    <meta name="description" content="Open source HTML5 Charts for your website">
    <link rel="preload" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css" as="style"><link rel="preload" href="/docs/3.9.1/assets/js/app.7e0dc8c8.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/2.3e2307f3.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/18.c331029e.js" as="script"><link rel="prefetch" href="/docs/3.9.1/assets/js/10.edbecfa9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/100.f7a12d8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/101.15c56dbb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/102.33b746ea.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/103.850a4486.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/104.8b4f368b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/105.1a3d6c41.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/106.d8f8b4c1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/107.38cf9b4f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/108.a30da8c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/109.6d56b7d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/11.b98e8151.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/110.c44e533c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/111.ccb9a835.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/112.2d2a2890.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/113.5ac0921f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/114.90ef8814.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/115.12a72d27.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/116.6b780970.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/117.79604442.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/118.7752bc89.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/119.d8e76ef0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/12.433f0c7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/120.ed76595e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/121.cc3d56f2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/122.c8651871.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/123.f7d5f223.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/124.88ce07a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/125.a9572036.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/126.879447b3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/127.3b8478f3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/128.87d67ad7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/129.f6241700.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/13.b6743084.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/130.a9cb856a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/131.45e904c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/132.abd9e4c2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/133.d5aa1db6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/134.4bfa3b5a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/135.8c7630ad.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/136.f813e062.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/137.6cab2863.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/138.03ec5265.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/139.924812c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/14.422fec10.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/140.a7967a5d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/141.34a018bb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/142.c5bfcdd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/143.05e388ba.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/144.a98bdbcc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/145.826b73e0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/146.001afadf.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/147.fcadaef3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/148.604863a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/149.6509ed9f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/15.09a69fc3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/150.fd8575ca.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/151.67ecd4a3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/152.481a7bd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/153.dbf3f3aa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/154.2aa9c67d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/155.e3367ebc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/156.a5bb942d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/157.09caef4e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/158.1f82587f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/159.4cebd9d3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/16.0414f390.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/160.47df8215.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/161.fac79692.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/162.9f2f870f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/163.4f1604f9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/164.7f8d8643.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/165.fe2baa69.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/166.c82b3c76.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/167.3c3f2ff1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/168.8d2e8e83.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/169.b10c84bd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/17.8dfcb176.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/170.b9db8312.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/171.2480fc36.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/172.81c5206a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/173.477dcbae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/174.05398ca3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/175.84ab69d0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/176.f48183c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/177.d0d35403.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/178.55d433fd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/179.9442c138.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/180.5c15d5d4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/181.911f367b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/182.72f663e8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/183.22b2258c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/184.15b21065.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/185.894ea40b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/186.f6394459.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/187.7bd9b3fe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/188.3baa9bcd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/189.18e63b11.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/19.13bdd658.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/190.b7ffb54a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/191.13061aba.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/192.1bf9bd61.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/193.8fa44455.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/194.49c9a3c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/195.eb1e8802.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/196.9b925823.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/197.adca6c8c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/198.2ae0961a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/199.cd9dca80.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/20.5d11c294.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/200.2eb9437f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/201.6dba10c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/202.9c5057c6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/203.98a3c2b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/204.50d5cf2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/205.e56e820b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/206.6ce5c41e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/207.8aa83d91.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/208.296ee160.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/209.13279349.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/21.7b54d7d8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/210.63e30420.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/211.0b325f23.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/212.be671e2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/213.81d2e607.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/214.ba403b5c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/215.2efcec5f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/216.e01d3100.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/217.906c8d54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/218.94e33827.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/219.21c5e01f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/22.c2daedd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/220.3ba160e4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/221.5c17138c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/222.aea004ce.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/223.cbb7b883.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/224.b130b37f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/225.32f90319.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/226.8c102c21.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/227.b3d60339.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/228.72f0ad18.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/229.3daa3b7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/23.2c668e20.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/230.7e5a85b1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/231.b83d12f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/232.e902f42d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/233.b4f254c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/234.d975df48.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/235.71a7be7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/236.05d87a5e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/237.d518e28b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/238.eb60e397.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/239.81fecfed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/24.af200d5b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/240.c0869bc2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/241.59dc896f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/242.6d423d39.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/243.a355eaa8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/244.ad729cf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/245.c3cd6bbe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/246.d0708528.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/247.13d65d78.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/248.1d222543.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/249.2d7a9bf1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/25.4f97f63f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/250.09ef38e2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/251.0cb90e8a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/252.2ff0def4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/253.61c7c505.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/254.20137eeb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/255.49c937e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/256.8d709dae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/257.b3ebdce7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/258.792f66d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/259.98c809e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/26.d1bb645c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/260.54ff10d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/261.928afea4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/27.2d5c4a6b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/28.72dee0f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/29.8820be26.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/3.947b8d98.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/30.343676b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/31.cff089f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/32.56d8546e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/33.6b642a06.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/34.360a9ea9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/35.205d7fac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/36.f2765bae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/37.6c33435e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/38.a778a6a2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/39.7dda160f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/4.ee88d25a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/40.bd778eac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/41.b79220f6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/42.c3157beb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/43.097368d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/44.2515f16e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/45.d5b4b7c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/46.bbab8d6e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/47.79aa575e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/48.45785af9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/49.0569a6eb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/5.00f814ac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/50.14bd3ba2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/51.e0968711.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/52.fe65ddf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/53.faa1ff3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/54.fcfed2c5.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/55.7db1d28a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/56.8b0e82b7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/57.9de2d983.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/58.b0f8ad0c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/59.f1ff4935.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/6.2bc86161.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/60.ac08de9a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/61.ea4fad75.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/62.5c85853b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/63.5ac99656.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/64.c6838e95.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/65.4c1b089e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/66.379a6b45.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/67.15703e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/68.ffdefd7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/69.bdeb7b9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/7.74f2ce90.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/70.3dadb5ed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/71.2c97fe38.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/72.dc778e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/73.d88fcb57.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/74.bba2165e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/75.15562a81.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/76.21f5a94e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/77.0725268e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/78.d6f610d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/79.60d67faa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/8.8928eb8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/80.99d71ee0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/81.f1500469.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/82.69f363a6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/83.f1fbcb2c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/84.b76e3156.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/85.efc4bd54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/86.85011b24.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/87.6a88d571.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/88.0f45cfe0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/89.e67ddb59.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/9.63ebb16b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/90.96ebfa9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/91.cdde4d3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/92.dc3f06d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/93.aaa04d52.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/94.2af5650c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/95.1d44ec16.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/96.9545127d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/97.8b18f487.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/98.cb259cd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/99.6251650a.js">
    <link rel="stylesheet" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css">
  </head>
  <body>
    <div id="app" data-server-rendered="true"><div class="theme-container"><header class="navbar"><div class="sidebar-button"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" viewBox="0 0 448 512" class="icon"><path fill="currentColor" d="M436 124H12c-6.627 0-12-5.373-12-12V80c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12z"></path></svg></div> <a href="/docs/3.9.1/" class="home-link router-link-active"><img src="/docs/3.9.1/favicon.ico" alt="Chart.js" class="logo"> <span class="site-name can-hide">Chart.js</span></a> <div class="links"><div class="search-box"><input aria-label="Search" autocomplete="off" spellcheck="false" value=""> <!----></div> <nav class="nav-links can-hide"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" class="nav-link router-link-active">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav></div></header> <div class="sidebar-mask"></div> <aside class="sidebar"><nav class="nav-links"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" class="nav-link router-link-active">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav>  <ul class="sidebar-links"><li><section class="sidebar-group depth-0"><p class="sidebar-heading open"><span>API</span> <!----></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/docs/3.9.1/api/" aria-current="page" class="sidebar-link">Exports</a></li><li><section class="sidebar-group collapsable is-sub-group depth-1"><p class="sidebar-heading"><span>Enumerations</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable is-sub-group depth-1"><p class="sidebar-heading open"><span>Classes</span> <span class="arrow down"></span></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/docs/3.9.1/api/classes/Animation.html" class="sidebar-link">Animation</a></li><li><a href="/docs/3.9.1/api/classes/Animations.html" class="sidebar-link">Animations</a></li><li><a href="/docs/3.9.1/api/classes/Animator.html" class="sidebar-link">Animator</a></li><li><a href="/docs/3.9.1/api/classes/BasePlatform.html" class="sidebar-link">BasePlatform</a></li><li><a href="/docs/3.9.1/api/classes/BasicPlatform.html" class="sidebar-link">BasicPlatform</a></li><li><a href="/docs/3.9.1/api/classes/Chart.html" class="sidebar-link">Chart</a></li><li><a href="/docs/3.9.1/api/classes/DatasetController.html" class="sidebar-link">DatasetController</a></li><li><a href="/docs/3.9.1/api/classes/DomPlatform.html" class="sidebar-link">DomPlatform</a></li><li><a href="/docs/3.9.1/api/classes/Scale.html" aria-current="page" class="active sidebar-link">Scale</a></li></ul></section></li><li><section class="sidebar-group collapsable is-sub-group depth-1"><p class="sidebar-heading"><span>Interfaces</span> <span class="arrow right"></span></p> <!----></section></li></ul></section></li></ul> </aside> <main class="page"> <div class="theme-default-content content__default"><h1 id="class-scale-o"><a href="#class-scale-o" class="header-anchor">#</a> Class: Scale&lt;O&gt;</h1> <h2 id="type-parameters"><a href="#type-parameters" class="header-anchor">#</a> Type parameters</h2> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/interfaces/CoreScaleOptions.html"><code>CoreScaleOptions</code></a> = <a href="/docs/3.9.1/api/interfaces/CoreScaleOptions.html"><code>CoreScaleOptions</code></a></td></tr></tbody></table> <h2 id="hierarchy"><a href="#hierarchy" class="header-anchor">#</a> Hierarchy</h2> <ul><li><p><a href="/docs/3.9.1/api/#element"><code>Element</code></a>&lt;<code>unknown</code>, <code>O</code>&gt;</p></li> <li><p><a href="/docs/3.9.1/api/interfaces/LayoutItem.html"><code>LayoutItem</code></a></p> <p>↳ <strong><code>Scale</code></strong></p> <p>↳↳ <a href="/docs/3.9.1/api/interfaces/TimeScale.html"><code>TimeScale</code></a></p> <p>↳↳ <a href="/docs/3.9.1/api/interfaces/RadialLinearScale.html"><code>RadialLinearScale</code></a></p></li></ul> <h2 id="constructors"><a href="#constructors" class="header-anchor">#</a> Constructors</h2> <h3 id="constructor"><a href="#constructor" class="header-anchor">#</a> constructor</h3> <p>• <strong>new Scale</strong>&lt;<code>O</code>&gt;(<code>cfg</code>)</p> <h4 id="type-parameters-2"><a href="#type-parameters-2" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/interfaces/CoreScaleOptions.html"><code>CoreScaleOptions</code></a> = <a href="/docs/3.9.1/api/interfaces/CoreScaleOptions.html"><code>CoreScaleOptions</code></a></td></tr></tbody></table> <h4 id="parameters"><a href="#parameters" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>cfg</code></td> <td style="text-align:left;"><code>Object</code></td></tr> <tr><td style="text-align:left;"><code>cfg.chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td></tr> <tr><td style="text-align:left;"><code>cfg.ctx</code></td> <td style="text-align:left;"><code>CanvasRenderingContext2D</code></td></tr> <tr><td style="text-align:left;"><code>cfg.id</code></td> <td style="text-align:left;"><code>string</code></td></tr> <tr><td style="text-align:left;"><code>cfg.type</code></td> <td style="text-align:left;"><code>string</code></td></tr></tbody></table> <h4 id="inherited-from"><a href="#inherited-from" class="header-anchor">#</a> Inherited from</h4> <p>Element&lt;unknown, O&gt;.constructor</p> <h4 id="defined-in"><a href="#defined-in" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1337" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1337<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <h2 id="properties"><a href="#properties" class="header-anchor">#</a> Properties</h2> <h3 id="active"><a href="#active" class="header-anchor">#</a> active</h3> <p>• <code>Readonly</code> <strong>active</strong>: <code>boolean</code></p> <h4 id="inherited-from-2"><a href="#inherited-from-2" class="header-anchor">#</a> Inherited from</h4> <p>Element.active</p> <h4 id="defined-in-2"><a href="#defined-in-2" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L7" target="_blank" rel="noopener noreferrer">element.d.ts:7<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="axis"><a href="#axis" class="header-anchor">#</a> axis</h3> <p>• <strong>axis</strong>: <code>string</code></p> <h4 id="defined-in-3"><a href="#defined-in-3" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1239" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1239<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="bottom"><a href="#bottom" class="header-anchor">#</a> bottom</h3> <p>• <strong>bottom</strong>: <code>number</code></p> <p>Bottom edge of the item. Set by layout system and cannot be used in update</p> <h4 id="inherited-from-3"><a href="#inherited-from-3" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/LayoutItem.html">LayoutItem</a>.<a href="/docs/3.9.1/api/interfaces/LayoutItem.html#bottom">bottom</a></p> <h4 id="defined-in-4"><a href="#defined-in-4" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L41" target="_blank" rel="noopener noreferrer">layout.d.ts:41<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="chart"><a href="#chart" class="header-anchor">#</a> chart</h3> <p>• <code>Readonly</code> <strong>chart</strong>: <a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</p> <h4 id="defined-in-5"><a href="#defined-in-5" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1229" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1229<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="ctx"><a href="#ctx" class="header-anchor">#</a> ctx</h3> <p>• <code>Readonly</code> <strong>ctx</strong>: <code>CanvasRenderingContext2D</code></p> <h4 id="defined-in-6"><a href="#defined-in-6" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1228" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1228<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="fullsize"><a href="#fullsize" class="header-anchor">#</a> fullSize</h3> <p>• <strong>fullSize</strong>: <code>boolean</code></p> <p>if true, and the item is horizontal, then push vertical boxes down</p> <h4 id="inherited-from-4"><a href="#inherited-from-4" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/LayoutItem.html">LayoutItem</a>.<a href="/docs/3.9.1/api/interfaces/LayoutItem.html#fullsize">fullSize</a></p> <h4 id="defined-in-7"><a href="#defined-in-7" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L17" target="_blank" rel="noopener noreferrer">layout.d.ts:17<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="height"><a href="#height" class="header-anchor">#</a> height</h3> <p>• <strong>height</strong>: <code>number</code></p> <p>Height of item. Must be valid after update()</p> <h4 id="inherited-from-5"><a href="#inherited-from-5" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/LayoutItem.html">LayoutItem</a>.<a href="/docs/3.9.1/api/interfaces/LayoutItem.html#height">height</a></p> <h4 id="defined-in-8"><a href="#defined-in-8" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L25" target="_blank" rel="noopener noreferrer">layout.d.ts:25<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="id"><a href="#id" class="header-anchor">#</a> id</h3> <p>• <code>Readonly</code> <strong>id</strong>: <code>string</code></p> <h4 id="defined-in-9"><a href="#defined-in-9" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1226" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1226<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="labelrotation"><a href="#labelrotation" class="header-anchor">#</a> labelRotation</h3> <p>• <strong>labelRotation</strong>: <code>number</code></p> <h4 id="defined-in-10"><a href="#defined-in-10" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1240" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1240<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="left"><a href="#left" class="header-anchor">#</a> left</h3> <p>• <strong>left</strong>: <code>number</code></p> <p>Left edge of the item. Set by layout system and cannot be used in update</p> <h4 id="inherited-from-6"><a href="#inherited-from-6" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/LayoutItem.html">LayoutItem</a>.<a href="/docs/3.9.1/api/interfaces/LayoutItem.html#left">left</a></p> <h4 id="defined-in-11"><a href="#defined-in-11" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L29" target="_blank" rel="noopener noreferrer">layout.d.ts:29<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="max"><a href="#max" class="header-anchor">#</a> max</h3> <p>• <strong>max</strong>: <code>number</code></p> <h4 id="defined-in-12"><a href="#defined-in-12" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1242" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1242<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="maxheight"><a href="#maxheight" class="header-anchor">#</a> maxHeight</h3> <p>• <strong>maxHeight</strong>: <code>number</code></p> <h4 id="defined-in-13"><a href="#defined-in-13" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1232" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1232<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="maxwidth"><a href="#maxwidth" class="header-anchor">#</a> maxWidth</h3> <p>• <strong>maxWidth</strong>: <code>number</code></p> <h4 id="defined-in-14"><a href="#defined-in-14" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1231" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1231<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="min"><a href="#min" class="header-anchor">#</a> min</h3> <p>• <strong>min</strong>: <code>number</code></p> <h4 id="defined-in-15"><a href="#defined-in-15" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1241" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1241<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="options"><a href="#options" class="header-anchor">#</a> options</h3> <p>• <code>Readonly</code> <strong>options</strong>: <code>O</code></p> <h4 id="inherited-from-7"><a href="#inherited-from-7" class="header-anchor">#</a> Inherited from</h4> <p>Element.options</p> <h4 id="defined-in-16"><a href="#defined-in-16" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L8" target="_blank" rel="noopener noreferrer">element.d.ts:8<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="paddingbottom"><a href="#paddingbottom" class="header-anchor">#</a> paddingBottom</h3> <p>• <strong>paddingBottom</strong>: <code>number</code></p> <h4 id="defined-in-17"><a href="#defined-in-17" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1235" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1235<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="paddingleft"><a href="#paddingleft" class="header-anchor">#</a> paddingLeft</h3> <p>• <strong>paddingLeft</strong>: <code>number</code></p> <h4 id="defined-in-18"><a href="#defined-in-18" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1236" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1236<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="paddingright"><a href="#paddingright" class="header-anchor">#</a> paddingRight</h3> <p>• <strong>paddingRight</strong>: <code>number</code></p> <h4 id="defined-in-19"><a href="#defined-in-19" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1237" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1237<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="paddingtop"><a href="#paddingtop" class="header-anchor">#</a> paddingTop</h3> <p>• <strong>paddingTop</strong>: <code>number</code></p> <h4 id="defined-in-20"><a href="#defined-in-20" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1234" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1234<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="position"><a href="#position" class="header-anchor">#</a> position</h3> <p>• <strong>position</strong>: <a href="/docs/3.9.1/api/#layoutposition"><code>LayoutPosition</code></a></p> <p>The position of the item in the chart layout. Possible values are</p> <h4 id="inherited-from-8"><a href="#inherited-from-8" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/LayoutItem.html">LayoutItem</a>.<a href="/docs/3.9.1/api/interfaces/LayoutItem.html#position">position</a></p> <h4 id="defined-in-21"><a href="#defined-in-21" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L9" target="_blank" rel="noopener noreferrer">layout.d.ts:9<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="right"><a href="#right" class="header-anchor">#</a> right</h3> <p>• <strong>right</strong>: <code>number</code></p> <p>Right edge of the item. Set by layout system and cannot be used in update</p> <h4 id="inherited-from-9"><a href="#inherited-from-9" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/LayoutItem.html">LayoutItem</a>.<a href="/docs/3.9.1/api/interfaces/LayoutItem.html#right">right</a></p> <h4 id="defined-in-22"><a href="#defined-in-22" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L37" target="_blank" rel="noopener noreferrer">layout.d.ts:37<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="ticks"><a href="#ticks" class="header-anchor">#</a> ticks</h3> <p>• <strong>ticks</strong>: <a href="/docs/3.9.1/api/interfaces/Tick.html"><code>Tick</code></a>[]</p> <h4 id="defined-in-23"><a href="#defined-in-23" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1243" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1243<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="top"><a href="#top" class="header-anchor">#</a> top</h3> <p>• <strong>top</strong>: <code>number</code></p> <p>Top edge of the item. Set by layout system and cannot be used in update</p> <h4 id="inherited-from-10"><a href="#inherited-from-10" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/LayoutItem.html">LayoutItem</a>.<a href="/docs/3.9.1/api/interfaces/LayoutItem.html#top">top</a></p> <h4 id="defined-in-24"><a href="#defined-in-24" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L33" target="_blank" rel="noopener noreferrer">layout.d.ts:33<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="type"><a href="#type" class="header-anchor">#</a> type</h3> <p>• <code>Readonly</code> <strong>type</strong>: <code>string</code></p> <h4 id="defined-in-25"><a href="#defined-in-25" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1227" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1227<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="weight"><a href="#weight" class="header-anchor">#</a> weight</h3> <p>• <strong>weight</strong>: <code>number</code></p> <p>The weight used to sort the item. Higher weights are further away from the chart area</p> <h4 id="inherited-from-11"><a href="#inherited-from-11" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/LayoutItem.html">LayoutItem</a>.<a href="/docs/3.9.1/api/interfaces/LayoutItem.html#weight">weight</a></p> <h4 id="defined-in-26"><a href="#defined-in-26" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L13" target="_blank" rel="noopener noreferrer">layout.d.ts:13<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="width"><a href="#width" class="header-anchor">#</a> width</h3> <p>• <strong>width</strong>: <code>number</code></p> <p>Width of item. Must be valid after update()</p> <h4 id="inherited-from-12"><a href="#inherited-from-12" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/LayoutItem.html">LayoutItem</a>.<a href="/docs/3.9.1/api/interfaces/LayoutItem.html#width">width</a></p> <h4 id="defined-in-27"><a href="#defined-in-27" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L21" target="_blank" rel="noopener noreferrer">layout.d.ts:21<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="x"><a href="#x" class="header-anchor">#</a> x</h3> <p>• <code>Readonly</code> <strong>x</strong>: <code>number</code></p> <h4 id="inherited-from-13"><a href="#inherited-from-13" class="header-anchor">#</a> Inherited from</h4> <p>Element.x</p> <h4 id="defined-in-28"><a href="#defined-in-28" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L5" target="_blank" rel="noopener noreferrer">element.d.ts:5<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="y"><a href="#y" class="header-anchor">#</a> y</h3> <p>• <code>Readonly</code> <strong>y</strong>: <code>number</code></p> <h4 id="inherited-from-14"><a href="#inherited-from-14" class="header-anchor">#</a> Inherited from</h4> <p>Element.y</p> <h4 id="defined-in-29"><a href="#defined-in-29" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L6" target="_blank" rel="noopener noreferrer">element.d.ts:6<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <h2 id="methods"><a href="#methods" class="header-anchor">#</a> Methods</h2> <h3 id="afterbuildticks"><a href="#afterbuildticks" class="header-anchor">#</a> afterBuildTicks</h3> <p>▸ <strong>afterBuildTicks</strong>(): <code>void</code></p> <h4 id="returns"><a href="#returns" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-30"><a href="#defined-in-30" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1323" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1323<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="aftercalculatelabelrotation"><a href="#aftercalculatelabelrotation" class="header-anchor">#</a> afterCalculateLabelRotation</h3> <p>▸ <strong>afterCalculateLabelRotation</strong>(): <code>void</code></p> <h4 id="returns-2"><a href="#returns-2" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-31"><a href="#defined-in-31" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1329" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1329<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterdatalimits"><a href="#afterdatalimits" class="header-anchor">#</a> afterDataLimits</h3> <p>▸ <strong>afterDataLimits</strong>(): <code>void</code></p> <h4 id="returns-3"><a href="#returns-3" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-32"><a href="#defined-in-32" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1320" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1320<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterfit"><a href="#afterfit" class="header-anchor">#</a> afterFit</h3> <p>▸ <strong>afterFit</strong>(): <code>void</code></p> <h4 id="returns-4"><a href="#returns-4" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-33"><a href="#defined-in-33" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1332" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1332<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="aftersetdimensions"><a href="#aftersetdimensions" class="header-anchor">#</a> afterSetDimensions</h3> <p>▸ <strong>afterSetDimensions</strong>(): <code>void</code></p> <h4 id="returns-5"><a href="#returns-5" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-34"><a href="#defined-in-34" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1317" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1317<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterticktolabelconversion"><a href="#afterticktolabelconversion" class="header-anchor">#</a> afterTickToLabelConversion</h3> <p>▸ <strong>afterTickToLabelConversion</strong>(): <code>void</code></p> <h4 id="returns-6"><a href="#returns-6" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-35"><a href="#defined-in-35" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1326" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1326<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterupdate"><a href="#afterupdate" class="header-anchor">#</a> afterUpdate</h3> <p>▸ <strong>afterUpdate</strong>(): <code>void</code></p> <h4 id="returns-7"><a href="#returns-7" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-36"><a href="#defined-in-36" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1314" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1314<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforebuildticks"><a href="#beforebuildticks" class="header-anchor">#</a> beforeBuildTicks</h3> <p>▸ <strong>beforeBuildTicks</strong>(): <code>void</code></p> <h4 id="returns-8"><a href="#returns-8" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-37"><a href="#defined-in-37" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1321" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1321<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforecalculatelabelrotation"><a href="#beforecalculatelabelrotation" class="header-anchor">#</a> beforeCalculateLabelRotation</h3> <p>▸ <strong>beforeCalculateLabelRotation</strong>(): <code>void</code></p> <h4 id="returns-9"><a href="#returns-9" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-38"><a href="#defined-in-38" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1327" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1327<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforedatalimits"><a href="#beforedatalimits" class="header-anchor">#</a> beforeDataLimits</h3> <p>▸ <strong>beforeDataLimits</strong>(): <code>void</code></p> <h4 id="returns-10"><a href="#returns-10" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-39"><a href="#defined-in-39" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1318" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1318<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforefit"><a href="#beforefit" class="header-anchor">#</a> beforeFit</h3> <p>▸ <strong>beforeFit</strong>(): <code>void</code></p> <h4 id="returns-11"><a href="#returns-11" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-40"><a href="#defined-in-40" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1330" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1330<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforelayout"><a href="#beforelayout" class="header-anchor">#</a> beforeLayout</h3> <p>▸ <code>Optional</code> <strong>beforeLayout</strong>(): <code>void</code></p> <p>Called before the layout process starts</p> <h4 id="returns-12"><a href="#returns-12" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="inherited-from-15"><a href="#inherited-from-15" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/LayoutItem.html">LayoutItem</a>.<a href="/docs/3.9.1/api/interfaces/LayoutItem.html#beforelayout">beforeLayout</a></p> <h4 id="defined-in-41"><a href="#defined-in-41" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L46" target="_blank" rel="noopener noreferrer">layout.d.ts:46<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforesetdimensions"><a href="#beforesetdimensions" class="header-anchor">#</a> beforeSetDimensions</h3> <p>▸ <strong>beforeSetDimensions</strong>(): <code>void</code></p> <h4 id="returns-13"><a href="#returns-13" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-42"><a href="#defined-in-42" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1315" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1315<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforeticktolabelconversion"><a href="#beforeticktolabelconversion" class="header-anchor">#</a> beforeTickToLabelConversion</h3> <p>▸ <strong>beforeTickToLabelConversion</strong>(): <code>void</code></p> <h4 id="returns-14"><a href="#returns-14" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-43"><a href="#defined-in-43" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1324" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1324<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforeupdate"><a href="#beforeupdate" class="header-anchor">#</a> beforeUpdate</h3> <p>▸ <strong>beforeUpdate</strong>(): <code>void</code></p> <h4 id="returns-15"><a href="#returns-15" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-44"><a href="#defined-in-44" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1312" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1312<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="buildticks"><a href="#buildticks" class="header-anchor">#</a> buildTicks</h3> <p>▸ <strong>buildTicks</strong>(): <a href="/docs/3.9.1/api/interfaces/Tick.html"><code>Tick</code></a>[]</p> <h4 id="returns-16"><a href="#returns-16" class="header-anchor">#</a> Returns</h4> <p><a href="/docs/3.9.1/api/interfaces/Tick.html"><code>Tick</code></a>[]</p> <h4 id="defined-in-45"><a href="#defined-in-45" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1322" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1322<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="calculatelabelrotation"><a href="#calculatelabelrotation" class="header-anchor">#</a> calculateLabelRotation</h3> <p>▸ <strong>calculateLabelRotation</strong>(): <code>void</code></p> <h4 id="returns-17"><a href="#returns-17" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-46"><a href="#defined-in-46" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1328" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1328<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="configure"><a href="#configure" class="header-anchor">#</a> configure</h3> <p>▸ <strong>configure</strong>(): <code>void</code></p> <h4 id="returns-18"><a href="#returns-18" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-47"><a href="#defined-in-47" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1313" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1313<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="determinedatalimits"><a href="#determinedatalimits" class="header-anchor">#</a> determineDataLimits</h3> <p>▸ <strong>determineDataLimits</strong>(): <code>void</code></p> <h4 id="returns-19"><a href="#returns-19" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-48"><a href="#defined-in-48" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1319" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1319<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="draw"><a href="#draw" class="header-anchor">#</a> draw</h3> <p>▸ <strong>draw</strong>(<code>chartArea</code>): <code>void</code></p> <p>Draws the element</p> <h4 id="parameters-2"><a href="#parameters-2" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chartArea</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/ChartArea.html"><code>ChartArea</code></a></td></tr></tbody></table> <h4 id="returns-20"><a href="#returns-20" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="inherited-from-16"><a href="#inherited-from-16" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/LayoutItem.html">LayoutItem</a>.<a href="/docs/3.9.1/api/interfaces/LayoutItem.html#draw">draw</a></p> <h4 id="defined-in-49"><a href="#defined-in-49" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L50" target="_blank" rel="noopener noreferrer">layout.d.ts:50<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="drawgrid"><a href="#drawgrid" class="header-anchor">#</a> drawGrid</h3> <p>▸ <strong>drawGrid</strong>(<code>chartArea</code>): <code>void</code></p> <h4 id="parameters-3"><a href="#parameters-3" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chartArea</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/ChartArea.html"><code>ChartArea</code></a></td></tr></tbody></table> <h4 id="returns-21"><a href="#returns-21" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-50"><a href="#defined-in-50" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1248" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1248<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="drawlabels"><a href="#drawlabels" class="header-anchor">#</a> drawLabels</h3> <p>▸ <strong>drawLabels</strong>(<code>chartArea</code>): <code>void</code></p> <h4 id="parameters-4"><a href="#parameters-4" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chartArea</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/ChartArea.html"><code>ChartArea</code></a></td></tr></tbody></table> <h4 id="returns-22"><a href="#returns-22" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-51"><a href="#defined-in-51" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1247" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1247<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="drawtitle"><a href="#drawtitle" class="header-anchor">#</a> drawTitle</h3> <p>▸ <strong>drawTitle</strong>(<code>chartArea</code>): <code>void</code></p> <h4 id="parameters-5"><a href="#parameters-5" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chartArea</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/ChartArea.html"><code>ChartArea</code></a></td></tr></tbody></table> <h4 id="returns-23"><a href="#returns-23" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-52"><a href="#defined-in-52" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1246" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1246<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="fit"><a href="#fit" class="header-anchor">#</a> fit</h3> <p>▸ <strong>fit</strong>(): <code>void</code></p> <h4 id="returns-24"><a href="#returns-24" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-53"><a href="#defined-in-53" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1331" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1331<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="generateticklabels"><a href="#generateticklabels" class="header-anchor">#</a> generateTickLabels</h3> <p>▸ <strong>generateTickLabels</strong>(<code>ticks</code>): <code>void</code></p> <h4 id="parameters-6"><a href="#parameters-6" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>ticks</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/Tick.html"><code>Tick</code></a>[]</td></tr></tbody></table> <h4 id="returns-25"><a href="#returns-25" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-54"><a href="#defined-in-54" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1325" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1325<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getbasepixel"><a href="#getbasepixel" class="header-anchor">#</a> getBasePixel</h3> <p>▸ <strong>getBasePixel</strong>(): <code>number</code></p> <p>Returns the pixel for the minimum chart value
The coordinate (0, 0) is at the upper-left corner of the canvas</p> <h4 id="returns-26"><a href="#returns-26" class="header-anchor">#</a> Returns</h4> <p><code>number</code></p> <h4 id="defined-in-55"><a href="#defined-in-55" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1304" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1304<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getbasevalue"><a href="#getbasevalue" class="header-anchor">#</a> getBaseValue</h3> <p>▸ <strong>getBaseValue</strong>(): <code>number</code></p> <h4 id="returns-27"><a href="#returns-27" class="header-anchor">#</a> Returns</h4> <p><code>number</code></p> <h4 id="defined-in-56"><a href="#defined-in-56" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1298" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1298<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getdecimalforpixel"><a href="#getdecimalforpixel" class="header-anchor">#</a> getDecimalForPixel</h3> <p>▸ <strong>getDecimalForPixel</strong>(<code>pixel</code>): <code>number</code></p> <h4 id="parameters-7"><a href="#parameters-7" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>pixel</code></td> <td style="text-align:left;"><code>number</code></td></tr></tbody></table> <h4 id="returns-28"><a href="#returns-28" class="header-anchor">#</a> Returns</h4> <p><code>number</code></p> <h4 id="defined-in-57"><a href="#defined-in-57" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1254" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1254<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getlabelforvalue"><a href="#getlabelforvalue" class="header-anchor">#</a> getLabelForValue</h3> <p>▸ <strong>getLabelForValue</strong>(<code>value</code>): <code>string</code></p> <p>Used to get the label to display in the tooltip for the given value</p> <h4 id="parameters-8"><a href="#parameters-8" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>value</code></td> <td style="text-align:left;"><code>number</code></td></tr></tbody></table> <h4 id="returns-29"><a href="#returns-29" class="header-anchor">#</a> Returns</h4> <p><code>string</code></p> <h4 id="defined-in-58"><a href="#defined-in-58" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1274" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1274<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getlabels"><a href="#getlabels" class="header-anchor">#</a> getLabels</h3> <p>▸ <strong>getLabels</strong>(): <code>string</code>[]</p> <h4 id="returns-30"><a href="#returns-30" class="header-anchor">#</a> Returns</h4> <p><code>string</code>[]</p> <h4 id="defined-in-59"><a href="#defined-in-59" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1311" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1311<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getlinewidthforvalue"><a href="#getlinewidthforvalue" class="header-anchor">#</a> getLineWidthForValue</h3> <p>▸ <strong>getLineWidthForValue</strong>(<code>value</code>): <code>number</code></p> <p>Returns the grid line width at given value</p> <h4 id="parameters-9"><a href="#parameters-9" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>value</code></td> <td style="text-align:left;"><code>number</code></td></tr></tbody></table> <h4 id="returns-31"><a href="#returns-31" class="header-anchor">#</a> Returns</h4> <p><code>number</code></p> <h4 id="defined-in-60"><a href="#defined-in-60" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1279" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1279<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getmatchingvisiblemetas"><a href="#getmatchingvisiblemetas" class="header-anchor">#</a> getMatchingVisibleMetas</h3> <p>▸ <strong>getMatchingVisibleMetas</strong>(<code>type?</code>): <a href="/docs/3.9.1/api/#chartmeta"><code>ChartMeta</code></a>&lt;<a href="/docs/3.9.1/api/#element"><code>Element</code></a>&lt;<code>AnyObject</code>, <code>AnyObject</code>&gt;, <a href="/docs/3.9.1/api/#element"><code>Element</code></a>&lt;<code>AnyObject</code>, <code>AnyObject</code>&gt;, keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>&gt;[]</p> <h4 id="parameters-10"><a href="#parameters-10" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>type?</code></td> <td style="text-align:left;"><code>string</code></td></tr></tbody></table> <h4 id="returns-32"><a href="#returns-32" class="header-anchor">#</a> Returns</h4> <p><a href="/docs/3.9.1/api/#chartmeta"><code>ChartMeta</code></a>&lt;<a href="/docs/3.9.1/api/#element"><code>Element</code></a>&lt;<code>AnyObject</code>, <code>AnyObject</code>&gt;, <a href="/docs/3.9.1/api/#element"><code>Element</code></a>&lt;<code>AnyObject</code>, <code>AnyObject</code>&gt;, keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>&gt;[]</p> <h4 id="defined-in-61"><a href="#defined-in-61" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1244" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1244<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getminmax"><a href="#getminmax" class="header-anchor">#</a> getMinMax</h3> <p>▸ <strong>getMinMax</strong>(<code>canStack</code>): <code>Object</code></p> <h4 id="parameters-11"><a href="#parameters-11" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>canStack</code></td> <td style="text-align:left;"><code>boolean</code></td></tr></tbody></table> <h4 id="returns-33"><a href="#returns-33" class="header-anchor">#</a> Returns</h4> <p><code>Object</code></p> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>max</code></td> <td style="text-align:left;"><code>number</code></td></tr> <tr><td style="text-align:left;"><code>min</code></td> <td style="text-align:left;"><code>number</code></td></tr></tbody></table> <h4 id="defined-in-62"><a href="#defined-in-62" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1309" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1309<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getpadding"><a href="#getpadding" class="header-anchor">#</a> getPadding</h3> <p>▸ <code>Optional</code> <strong>getPadding</strong>(): <a href="/docs/3.9.1/api/interfaces/ChartArea.html"><code>ChartArea</code></a></p> <p>Returns an object with padding on the edges</p> <h4 id="returns-34"><a href="#returns-34" class="header-anchor">#</a> Returns</h4> <p><a href="/docs/3.9.1/api/interfaces/ChartArea.html"><code>ChartArea</code></a></p> <h4 id="inherited-from-17"><a href="#inherited-from-17" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/LayoutItem.html">LayoutItem</a>.<a href="/docs/3.9.1/api/interfaces/LayoutItem.html#getpadding">getPadding</a></p> <h4 id="defined-in-63"><a href="#defined-in-63" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L54" target="_blank" rel="noopener noreferrer">layout.d.ts:54<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getpixelfordecimal"><a href="#getpixelfordecimal" class="header-anchor">#</a> getPixelForDecimal</h3> <p>▸ <strong>getPixelForDecimal</strong>(<code>decimal</code>): <code>number</code></p> <p>Utility for getting the pixel location of a percentage of scale
The coordinate (0, 0) is at the upper-left corner of the canvas</p> <h4 id="parameters-12"><a href="#parameters-12" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>decimal</code></td> <td style="text-align:left;"><code>number</code></td></tr></tbody></table> <h4 id="returns-35"><a href="#returns-35" class="header-anchor">#</a> Returns</h4> <p><code>number</code></p> <h4 id="defined-in-64"><a href="#defined-in-64" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1261" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1261<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getpixelfortick"><a href="#getpixelfortick" class="header-anchor">#</a> getPixelForTick</h3> <p>▸ <strong>getPixelForTick</strong>(<code>index</code>): <code>number</code></p> <p>Returns the location of the tick at the given index
The coordinate (0, 0) is at the upper-left corner of the canvas</p> <h4 id="parameters-13"><a href="#parameters-13" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>index</code></td> <td style="text-align:left;"><code>number</code></td></tr></tbody></table> <h4 id="returns-36"><a href="#returns-36" class="header-anchor">#</a> Returns</h4> <p><code>number</code></p> <h4 id="defined-in-65"><a href="#defined-in-65" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1268" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1268<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getpixelforvalue"><a href="#getpixelforvalue" class="header-anchor">#</a> getPixelForValue</h3> <p>▸ <strong>getPixelForValue</strong>(<code>value</code>, <code>index?</code>): <code>number</code></p> <p>Returns the location of the given data point. Value can either be an index or a numerical value
The coordinate (0, 0) is at the upper-left corner of the canvas</p> <h4 id="parameters-14"><a href="#parameters-14" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>value</code></td> <td style="text-align:left;"><code>number</code></td></tr> <tr><td style="text-align:left;"><code>index?</code></td> <td style="text-align:left;"><code>number</code></td></tr></tbody></table> <h4 id="returns-37"><a href="#returns-37" class="header-anchor">#</a> Returns</h4> <p><code>number</code></p> <h4 id="defined-in-66"><a href="#defined-in-66" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1288" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1288<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getprops"><a href="#getprops" class="header-anchor">#</a> getProps</h3> <p>▸ <strong>getProps</strong>&lt;<code>P</code>&gt;(<code>props</code>, <code>final?</code>): <code>Pick</code>&lt;<code>unknown</code>, <code>P</code>[<code>number</code>]&gt;</p> <h4 id="type-parameters-3"><a href="#type-parameters-3" class="header-anchor">#</a> Type parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>P</code></td> <td style="text-align:left;">extends <code>never</code>[]</td></tr></tbody></table> <h4 id="parameters-15"><a href="#parameters-15" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>props</code></td> <td style="text-align:left;"><code>P</code></td></tr> <tr><td style="text-align:left;"><code>final?</code></td> <td style="text-align:left;"><code>boolean</code></td></tr></tbody></table> <h4 id="returns-38"><a href="#returns-38" class="header-anchor">#</a> Returns</h4> <p><code>Pick</code>&lt;<code>unknown</code>, <code>P</code>[<code>number</code>]&gt;</p> <h4 id="inherited-from-18"><a href="#inherited-from-18" class="header-anchor">#</a> Inherited from</h4> <p>Element.getProps</p> <h4 id="defined-in-67"><a href="#defined-in-67" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L12" target="_blank" rel="noopener noreferrer">element.d.ts:12<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getticks"><a href="#getticks" class="header-anchor">#</a> getTicks</h3> <p>▸ <strong>getTicks</strong>(): <a href="/docs/3.9.1/api/interfaces/Tick.html"><code>Tick</code></a>[]</p> <h4 id="returns-39"><a href="#returns-39" class="header-anchor">#</a> Returns</h4> <p><a href="/docs/3.9.1/api/interfaces/Tick.html"><code>Tick</code></a>[]</p> <h4 id="defined-in-68"><a href="#defined-in-68" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1310" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1310<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getuserbounds"><a href="#getuserbounds" class="header-anchor">#</a> getUserBounds</h3> <p>▸ <strong>getUserBounds</strong>(): <code>Object</code></p> <h4 id="returns-40"><a href="#returns-40" class="header-anchor">#</a> Returns</h4> <p><code>Object</code></p> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>max</code></td> <td style="text-align:left;"><code>number</code></td></tr> <tr><td style="text-align:left;"><code>maxDefined</code></td> <td style="text-align:left;"><code>boolean</code></td></tr> <tr><td style="text-align:left;"><code>min</code></td> <td style="text-align:left;"><code>number</code></td></tr> <tr><td style="text-align:left;"><code>minDefined</code></td> <td style="text-align:left;"><code>boolean</code></td></tr></tbody></table> <h4 id="defined-in-69"><a href="#defined-in-69" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1308" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1308<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="getvalueforpixel"><a href="#getvalueforpixel" class="header-anchor">#</a> getValueForPixel</h3> <p>▸ <strong>getValueForPixel</strong>(<code>pixel</code>): <code>number</code></p> <p>Used to get the data value from a given pixel. This is the inverse of getPixelForValue
The coordinate (0, 0) is at the upper-left corner of the canvas</p> <h4 id="parameters-16"><a href="#parameters-16" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>pixel</code></td> <td style="text-align:left;"><code>number</code></td></tr></tbody></table> <h4 id="returns-41"><a href="#returns-41" class="header-anchor">#</a> Returns</h4> <p><code>number</code></p> <h4 id="defined-in-70"><a href="#defined-in-70" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1296" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1296<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="hasvalue"><a href="#hasvalue" class="header-anchor">#</a> hasValue</h3> <p>▸ <strong>hasValue</strong>(): <code>boolean</code></p> <h4 id="returns-42"><a href="#returns-42" class="header-anchor">#</a> Returns</h4> <p><code>boolean</code></p> <h4 id="inherited-from-19"><a href="#inherited-from-19" class="header-anchor">#</a> Inherited from</h4> <p>Element.hasValue</p> <h4 id="defined-in-71"><a href="#defined-in-71" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L11" target="_blank" rel="noopener noreferrer">element.d.ts:11<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="init"><a href="#init" class="header-anchor">#</a> init</h3> <p>▸ <strong>init</strong>(<code>options</code>): <code>void</code></p> <h4 id="parameters-17"><a href="#parameters-17" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td></tr></tbody></table> <h4 id="returns-43"><a href="#returns-43" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-72"><a href="#defined-in-72" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1306" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1306<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="isfullsize"><a href="#isfullsize" class="header-anchor">#</a> isFullSize</h3> <p>▸ <strong>isFullSize</strong>(): <code>boolean</code></p> <h4 id="returns-44"><a href="#returns-44" class="header-anchor">#</a> Returns</h4> <p><code>boolean</code></p> <h4 id="defined-in-73"><a href="#defined-in-73" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1334" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1334<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="ishorizontal"><a href="#ishorizontal" class="header-anchor">#</a> isHorizontal</h3> <p>▸ <strong>isHorizontal</strong>(): <code>boolean</code></p> <p>returns true if the layout item is horizontal (ie. top or bottom)</p> <h4 id="returns-45"><a href="#returns-45" class="header-anchor">#</a> Returns</h4> <p><code>boolean</code></p> <h4 id="inherited-from-20"><a href="#inherited-from-20" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/LayoutItem.html">LayoutItem</a>.<a href="/docs/3.9.1/api/interfaces/LayoutItem.html#ishorizontal">isHorizontal</a></p> <h4 id="defined-in-74"><a href="#defined-in-74" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L58" target="_blank" rel="noopener noreferrer">layout.d.ts:58<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="parse"><a href="#parse" class="header-anchor">#</a> parse</h3> <p>▸ <strong>parse</strong>(<code>raw</code>, <code>index</code>): <code>unknown</code></p> <h4 id="parameters-18"><a href="#parameters-18" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>raw</code></td> <td style="text-align:left;"><code>unknown</code></td></tr> <tr><td style="text-align:left;"><code>index</code></td> <td style="text-align:left;"><code>number</code></td></tr></tbody></table> <h4 id="returns-46"><a href="#returns-46" class="header-anchor">#</a> Returns</h4> <p><code>unknown</code></p> <h4 id="defined-in-75"><a href="#defined-in-75" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1307" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1307<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="setdimensions"><a href="#setdimensions" class="header-anchor">#</a> setDimensions</h3> <p>▸ <strong>setDimensions</strong>(): <code>void</code></p> <h4 id="returns-47"><a href="#returns-47" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-76"><a href="#defined-in-76" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1316" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1316<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="tooltipposition"><a href="#tooltipposition" class="header-anchor">#</a> tooltipPosition</h3> <p>▸ <strong>tooltipPosition</strong>(<code>useFinalPosition?</code>): <a href="/docs/3.9.1/api/interfaces/Point.html"><code>Point</code></a></p> <h4 id="parameters-19"><a href="#parameters-19" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>useFinalPosition?</code></td> <td style="text-align:left;"><code>boolean</code></td></tr></tbody></table> <h4 id="returns-48"><a href="#returns-48" class="header-anchor">#</a> Returns</h4> <p><a href="/docs/3.9.1/api/interfaces/Point.html"><code>Point</code></a></p> <h4 id="inherited-from-21"><a href="#inherited-from-21" class="header-anchor">#</a> Inherited from</h4> <p>Element.tooltipPosition</p> <h4 id="defined-in-77"><a href="#defined-in-77" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/element.d.ts#L10" target="_blank" rel="noopener noreferrer">element.d.ts:10<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="update"><a href="#update" class="header-anchor">#</a> update</h3> <p>▸ <strong>update</strong>(<code>width</code>, <code>height</code>, <code>margins?</code>): <code>void</code></p> <p>Takes two parameters: width and height.</p> <h4 id="parameters-20"><a href="#parameters-20" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>width</code></td> <td style="text-align:left;"><code>number</code></td></tr> <tr><td style="text-align:left;"><code>height</code></td> <td style="text-align:left;"><code>number</code></td></tr> <tr><td style="text-align:left;"><code>margins?</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/ChartArea.html"><code>ChartArea</code></a></td></tr></tbody></table> <h4 id="returns-49"><a href="#returns-49" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="inherited-from-22"><a href="#inherited-from-22" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/LayoutItem.html">LayoutItem</a>.<a href="/docs/3.9.1/api/interfaces/LayoutItem.html#update">update</a></p> <h4 id="defined-in-78"><a href="#defined-in-78" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/layout.d.ts#L64" target="_blank" rel="noopener noreferrer">layout.d.ts:64<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p></div> <footer class="page-edit"><!----> <!----></footer> <div class="page-nav"><p class="inner"><span class="prev">
      ←
      <a href="/docs/3.9.1/api/classes/DomPlatform.html" class="prev">
        DomPlatform
      </a></span> <span class="next"><a href="/docs/3.9.1/api/interfaces/ActiveDataPoint.html">
        ActiveDataPoint
      </a>
      →
    </span></p></div> </main></div><div class="global-ui"></div></div>
    <script src="/docs/3.9.1/assets/js/app.7e0dc8c8.js" defer></script><script src="/docs/3.9.1/assets/js/2.3e2307f3.js" defer></script><script src="/docs/3.9.1/assets/js/18.c331029e.js" defer></script>
  </body>
</html>
