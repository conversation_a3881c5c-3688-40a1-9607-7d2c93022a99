<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Interface: Plugin | Chart.js</title>
    <meta name="generator" content="VuePress 1.8.3">
    <link rel="icon" href="/docs/3.9.1/favicon.ico">
    <meta name="description" content="Open source HTML5 Charts for your website">
    <link rel="preload" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css" as="style"><link rel="preload" href="/docs/3.9.1/assets/js/app.7e0dc8c8.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/2.3e2307f3.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/86.85011b24.js" as="script"><link rel="prefetch" href="/docs/3.9.1/assets/js/10.edbecfa9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/100.f7a12d8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/101.15c56dbb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/102.33b746ea.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/103.850a4486.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/104.8b4f368b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/105.1a3d6c41.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/106.d8f8b4c1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/107.38cf9b4f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/108.a30da8c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/109.6d56b7d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/11.b98e8151.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/110.c44e533c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/111.ccb9a835.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/112.2d2a2890.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/113.5ac0921f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/114.90ef8814.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/115.12a72d27.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/116.6b780970.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/117.79604442.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/118.7752bc89.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/119.d8e76ef0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/12.433f0c7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/120.ed76595e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/121.cc3d56f2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/122.c8651871.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/123.f7d5f223.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/124.88ce07a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/125.a9572036.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/126.879447b3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/127.3b8478f3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/128.87d67ad7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/129.f6241700.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/13.b6743084.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/130.a9cb856a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/131.45e904c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/132.abd9e4c2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/133.d5aa1db6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/134.4bfa3b5a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/135.8c7630ad.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/136.f813e062.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/137.6cab2863.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/138.03ec5265.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/139.924812c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/14.422fec10.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/140.a7967a5d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/141.34a018bb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/142.c5bfcdd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/143.05e388ba.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/144.a98bdbcc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/145.826b73e0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/146.001afadf.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/147.fcadaef3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/148.604863a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/149.6509ed9f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/15.09a69fc3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/150.fd8575ca.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/151.67ecd4a3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/152.481a7bd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/153.dbf3f3aa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/154.2aa9c67d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/155.e3367ebc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/156.a5bb942d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/157.09caef4e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/158.1f82587f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/159.4cebd9d3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/16.0414f390.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/160.47df8215.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/161.fac79692.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/162.9f2f870f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/163.4f1604f9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/164.7f8d8643.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/165.fe2baa69.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/166.c82b3c76.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/167.3c3f2ff1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/168.8d2e8e83.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/169.b10c84bd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/17.8dfcb176.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/170.b9db8312.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/171.2480fc36.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/172.81c5206a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/173.477dcbae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/174.05398ca3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/175.84ab69d0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/176.f48183c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/177.d0d35403.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/178.55d433fd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/179.9442c138.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/18.c331029e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/180.5c15d5d4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/181.911f367b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/182.72f663e8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/183.22b2258c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/184.15b21065.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/185.894ea40b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/186.f6394459.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/187.7bd9b3fe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/188.3baa9bcd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/189.18e63b11.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/19.13bdd658.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/190.b7ffb54a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/191.13061aba.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/192.1bf9bd61.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/193.8fa44455.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/194.49c9a3c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/195.eb1e8802.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/196.9b925823.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/197.adca6c8c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/198.2ae0961a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/199.cd9dca80.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/20.5d11c294.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/200.2eb9437f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/201.6dba10c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/202.9c5057c6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/203.98a3c2b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/204.50d5cf2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/205.e56e820b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/206.6ce5c41e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/207.8aa83d91.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/208.296ee160.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/209.13279349.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/21.7b54d7d8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/210.63e30420.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/211.0b325f23.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/212.be671e2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/213.81d2e607.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/214.ba403b5c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/215.2efcec5f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/216.e01d3100.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/217.906c8d54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/218.94e33827.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/219.21c5e01f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/22.c2daedd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/220.3ba160e4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/221.5c17138c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/222.aea004ce.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/223.cbb7b883.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/224.b130b37f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/225.32f90319.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/226.8c102c21.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/227.b3d60339.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/228.72f0ad18.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/229.3daa3b7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/23.2c668e20.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/230.7e5a85b1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/231.b83d12f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/232.e902f42d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/233.b4f254c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/234.d975df48.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/235.71a7be7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/236.05d87a5e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/237.d518e28b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/238.eb60e397.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/239.81fecfed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/24.af200d5b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/240.c0869bc2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/241.59dc896f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/242.6d423d39.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/243.a355eaa8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/244.ad729cf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/245.c3cd6bbe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/246.d0708528.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/247.13d65d78.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/248.1d222543.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/249.2d7a9bf1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/25.4f97f63f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/250.09ef38e2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/251.0cb90e8a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/252.2ff0def4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/253.61c7c505.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/254.20137eeb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/255.49c937e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/256.8d709dae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/257.b3ebdce7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/258.792f66d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/259.98c809e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/26.d1bb645c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/260.54ff10d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/261.928afea4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/27.2d5c4a6b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/28.72dee0f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/29.8820be26.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/3.947b8d98.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/30.343676b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/31.cff089f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/32.56d8546e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/33.6b642a06.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/34.360a9ea9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/35.205d7fac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/36.f2765bae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/37.6c33435e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/38.a778a6a2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/39.7dda160f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/4.ee88d25a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/40.bd778eac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/41.b79220f6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/42.c3157beb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/43.097368d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/44.2515f16e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/45.d5b4b7c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/46.bbab8d6e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/47.79aa575e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/48.45785af9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/49.0569a6eb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/5.00f814ac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/50.14bd3ba2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/51.e0968711.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/52.fe65ddf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/53.faa1ff3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/54.fcfed2c5.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/55.7db1d28a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/56.8b0e82b7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/57.9de2d983.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/58.b0f8ad0c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/59.f1ff4935.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/6.2bc86161.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/60.ac08de9a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/61.ea4fad75.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/62.5c85853b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/63.5ac99656.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/64.c6838e95.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/65.4c1b089e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/66.379a6b45.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/67.15703e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/68.ffdefd7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/69.bdeb7b9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/7.74f2ce90.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/70.3dadb5ed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/71.2c97fe38.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/72.dc778e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/73.d88fcb57.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/74.bba2165e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/75.15562a81.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/76.21f5a94e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/77.0725268e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/78.d6f610d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/79.60d67faa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/8.8928eb8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/80.99d71ee0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/81.f1500469.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/82.69f363a6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/83.f1fbcb2c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/84.b76e3156.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/85.efc4bd54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/87.6a88d571.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/88.0f45cfe0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/89.e67ddb59.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/9.63ebb16b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/90.96ebfa9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/91.cdde4d3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/92.dc3f06d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/93.aaa04d52.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/94.2af5650c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/95.1d44ec16.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/96.9545127d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/97.8b18f487.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/98.cb259cd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/99.6251650a.js">
    <link rel="stylesheet" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css">
  </head>
  <body>
    <div id="app" data-server-rendered="true"><div class="theme-container"><header class="navbar"><div class="sidebar-button"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" viewBox="0 0 448 512" class="icon"><path fill="currentColor" d="M436 124H12c-6.627 0-12-5.373-12-12V80c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12z"></path></svg></div> <a href="/docs/3.9.1/" class="home-link router-link-active"><img src="/docs/3.9.1/favicon.ico" alt="Chart.js" class="logo"> <span class="site-name can-hide">Chart.js</span></a> <div class="links"><div class="search-box"><input aria-label="Search" autocomplete="off" spellcheck="false" value=""> <!----></div> <nav class="nav-links can-hide"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" class="nav-link router-link-active">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav></div></header> <div class="sidebar-mask"></div> <aside class="sidebar"><nav class="nav-links"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" class="nav-link router-link-active">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav>  <ul class="sidebar-links"><li><section class="sidebar-group depth-0"><p class="sidebar-heading open"><span>API</span> <!----></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/docs/3.9.1/api/" aria-current="page" class="sidebar-link">Exports</a></li><li><section class="sidebar-group collapsable is-sub-group depth-1"><p class="sidebar-heading"><span>Enumerations</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable is-sub-group depth-1"><p class="sidebar-heading"><span>Classes</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable is-sub-group depth-1"><p class="sidebar-heading open"><span>Interfaces</span> <span class="arrow down"></span></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/docs/3.9.1/api/interfaces/ActiveDataPoint.html" class="sidebar-link">ActiveDataPoint</a></li><li><a href="/docs/3.9.1/api/interfaces/ActiveElement.html" class="sidebar-link">ActiveElement</a></li><li><a href="/docs/3.9.1/api/interfaces/AnimationEvent.html" class="sidebar-link">AnimationEvent</a></li><li><a href="/docs/3.9.1/api/interfaces/ArcBorderRadius.html" class="sidebar-link">ArcBorderRadius</a></li><li><a href="/docs/3.9.1/api/interfaces/ArcElement.html" class="sidebar-link">ArcElement</a></li><li><a href="/docs/3.9.1/api/interfaces/ArcHoverOptions.html" class="sidebar-link">ArcHoverOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/ArcOptions.html" class="sidebar-link">ArcOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/ArcProps.html" class="sidebar-link">ArcProps</a></li><li><a href="/docs/3.9.1/api/interfaces/BarControllerChartOptions.html" class="sidebar-link">BarControllerChartOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/BarControllerDatasetOptions.html" class="sidebar-link">BarControllerDatasetOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/BarElement.html" class="sidebar-link">BarElement</a></li><li><a href="/docs/3.9.1/api/interfaces/BarHoverOptions.html" class="sidebar-link">BarHoverOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/BarOptions.html" class="sidebar-link">BarOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/BarProps.html" class="sidebar-link">BarProps</a></li><li><a href="/docs/3.9.1/api/interfaces/BorderRadius.html" class="sidebar-link">BorderRadius</a></li><li><a href="/docs/3.9.1/api/interfaces/BubbleControllerDatasetOptions.html" class="sidebar-link">BubbleControllerDatasetOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html" class="sidebar-link">BubbleDataPoint</a></li><li><a href="/docs/3.9.1/api/interfaces/CartesianScaleOptions.html" class="sidebar-link">CartesianScaleOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/CartesianScaleTypeRegistry.html" class="sidebar-link">CartesianScaleTypeRegistry</a></li><li><a href="/docs/3.9.1/api/interfaces/ChartArea.html" class="sidebar-link">ChartArea</a></li><li><a href="/docs/3.9.1/api/interfaces/ChartComponent.html" class="sidebar-link">ChartComponent</a></li><li><a href="/docs/3.9.1/api/interfaces/ChartConfiguration.html" class="sidebar-link">ChartConfiguration</a></li><li><a href="/docs/3.9.1/api/interfaces/ChartConfigurationCustomTypesPerDataset.html" class="sidebar-link">ChartConfigurationCustomTypesPerDataset</a></li><li><a href="/docs/3.9.1/api/interfaces/ChartData.html" class="sidebar-link">ChartData</a></li><li><a href="/docs/3.9.1/api/interfaces/ChartDataCustomTypesPerDataset.html" class="sidebar-link">ChartDataCustomTypesPerDataset</a></li><li><a href="/docs/3.9.1/api/interfaces/ChartDatasetProperties.html" class="sidebar-link">ChartDatasetProperties</a></li><li><a href="/docs/3.9.1/api/interfaces/ChartDatasetPropertiesCustomTypesPerDataset.html" class="sidebar-link">ChartDatasetPropertiesCustomTypesPerDataset</a></li><li><a href="/docs/3.9.1/api/interfaces/ChartEvent.html" class="sidebar-link">ChartEvent</a></li><li><a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html" class="sidebar-link">ChartTypeRegistry</a></li><li><a href="/docs/3.9.1/api/interfaces/CommonElementOptions.html" class="sidebar-link">CommonElementOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/CommonHoverOptions.html" class="sidebar-link">CommonHoverOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/ComplexFillTarget.html" class="sidebar-link">ComplexFillTarget</a></li><li><a href="/docs/3.9.1/api/interfaces/ControllerDatasetOptions.html" class="sidebar-link">ControllerDatasetOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/CoreChartOptions.html" class="sidebar-link">CoreChartOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/CoreInteractionOptions.html" class="sidebar-link">CoreInteractionOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/CoreScaleOptions.html" class="sidebar-link">CoreScaleOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/DatasetControllerChartComponent.html" class="sidebar-link">DatasetControllerChartComponent</a></li><li><a href="/docs/3.9.1/api/interfaces/DateAdapter.html" class="sidebar-link">DateAdapter</a></li><li><a href="/docs/3.9.1/api/interfaces/Defaults.html" class="sidebar-link">Defaults</a></li><li><a href="/docs/3.9.1/api/interfaces/DoughnutAnimationOptions.html" class="sidebar-link">DoughnutAnimationOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/DoughnutController.html" class="sidebar-link">DoughnutController</a></li><li><a href="/docs/3.9.1/api/interfaces/DoughnutControllerChartOptions.html" class="sidebar-link">DoughnutControllerChartOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/DoughnutControllerDatasetOptions.html" class="sidebar-link">DoughnutControllerDatasetOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/DoughnutMetaExtensions.html" class="sidebar-link">DoughnutMetaExtensions</a></li><li><a href="/docs/3.9.1/api/interfaces/Element.html" class="sidebar-link">Element</a></li><li><a href="/docs/3.9.1/api/interfaces/ElementOptionsByType.html" class="sidebar-link">ElementOptionsByType</a></li><li><a href="/docs/3.9.1/api/interfaces/ExtendedPlugin.html" class="sidebar-link">ExtendedPlugin</a></li><li><a href="/docs/3.9.1/api/interfaces/FillerControllerDatasetOptions.html" class="sidebar-link">FillerControllerDatasetOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/FillerOptions.html" class="sidebar-link">FillerOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/FontSpec.html" class="sidebar-link">FontSpec</a></li><li><a href="/docs/3.9.1/api/interfaces/GridLineOptions.html" class="sidebar-link">GridLineOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/InteractionItem.html" class="sidebar-link">InteractionItem</a></li><li><a href="/docs/3.9.1/api/interfaces/InteractionModeMap.html" class="sidebar-link">InteractionModeMap</a></li><li><a href="/docs/3.9.1/api/interfaces/InteractionOptions.html" class="sidebar-link">InteractionOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/LayoutItem.html" class="sidebar-link">LayoutItem</a></li><li><a href="/docs/3.9.1/api/interfaces/LegendElement.html" class="sidebar-link">LegendElement</a></li><li><a href="/docs/3.9.1/api/interfaces/LegendItem.html" class="sidebar-link">LegendItem</a></li><li><a href="/docs/3.9.1/api/interfaces/LegendOptions.html" class="sidebar-link">LegendOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/LineControllerChartOptions.html" class="sidebar-link">LineControllerChartOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/LineControllerDatasetOptions.html" class="sidebar-link">LineControllerDatasetOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/LineElement.html" class="sidebar-link">LineElement</a></li><li><a href="/docs/3.9.1/api/interfaces/LineHoverOptions.html" class="sidebar-link">LineHoverOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/LineOptions.html" class="sidebar-link">LineOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/LineProps.html" class="sidebar-link">LineProps</a></li><li><a href="/docs/3.9.1/api/interfaces/ParsingOptions.html" class="sidebar-link">ParsingOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/Plugin.html" aria-current="page" class="active sidebar-link">Plugin</a></li><li><a href="/docs/3.9.1/api/interfaces/PluginChartOptions.html" class="sidebar-link">PluginChartOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/PluginOptionsByType.html" class="sidebar-link">PluginOptionsByType</a></li><li><a href="/docs/3.9.1/api/interfaces/Point.html" class="sidebar-link">Point</a></li><li><a href="/docs/3.9.1/api/interfaces/PointElement.html" class="sidebar-link">PointElement</a></li><li><a href="/docs/3.9.1/api/interfaces/PointHoverOptions.html" class="sidebar-link">PointHoverOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/PointOptions.html" class="sidebar-link">PointOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/PointPrefixedHoverOptions.html" class="sidebar-link">PointPrefixedHoverOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/PointPrefixedOptions.html" class="sidebar-link">PointPrefixedOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/PointProps.html" class="sidebar-link">PointProps</a></li><li><a href="/docs/3.9.1/api/interfaces/PolarAreaController.html" class="sidebar-link">PolarAreaController</a></li><li><a href="/docs/3.9.1/api/interfaces/PolarAreaControllerChartOptions.html" class="sidebar-link">PolarAreaControllerChartOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/PolarAreaControllerDatasetOptions.html" class="sidebar-link">PolarAreaControllerDatasetOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/RadarControllerDatasetOptions.html" class="sidebar-link">RadarControllerDatasetOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/RadialLinearScale.html" class="sidebar-link">RadialLinearScale</a></li><li><a href="/docs/3.9.1/api/interfaces/RadialScaleTypeRegistry.html" class="sidebar-link">RadialScaleTypeRegistry</a></li><li><a href="/docs/3.9.1/api/interfaces/Registry.html" class="sidebar-link">Registry</a></li><li><a href="/docs/3.9.1/api/interfaces/ScaleTypeRegistry.html" class="sidebar-link">ScaleTypeRegistry</a></li><li><a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html" class="sidebar-link">ScatterDataPoint</a></li><li><a href="/docs/3.9.1/api/interfaces/ScriptableCartesianScaleContext.html" class="sidebar-link">ScriptableCartesianScaleContext</a></li><li><a href="/docs/3.9.1/api/interfaces/ScriptableChartContext.html" class="sidebar-link">ScriptableChartContext</a></li><li><a href="/docs/3.9.1/api/interfaces/ScriptableContext.html" class="sidebar-link">ScriptableContext</a></li><li><a href="/docs/3.9.1/api/interfaces/ScriptableLineSegmentContext.html" class="sidebar-link">ScriptableLineSegmentContext</a></li><li><a href="/docs/3.9.1/api/interfaces/ScriptableScaleContext.html" class="sidebar-link">ScriptableScaleContext</a></li><li><a href="/docs/3.9.1/api/interfaces/ScriptableScalePointLabelContext.html" class="sidebar-link">ScriptableScalePointLabelContext</a></li><li><a href="/docs/3.9.1/api/interfaces/ScriptableTooltipContext.html" class="sidebar-link">ScriptableTooltipContext</a></li><li><a href="/docs/3.9.1/api/interfaces/Segment.html" class="sidebar-link">Segment</a></li><li><a href="/docs/3.9.1/api/interfaces/Tick.html" class="sidebar-link">Tick</a></li><li><a href="/docs/3.9.1/api/interfaces/TickOptions.html" class="sidebar-link">TickOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/TimeScale.html" class="sidebar-link">TimeScale</a></li><li><a href="/docs/3.9.1/api/interfaces/TitleOptions.html" class="sidebar-link">TitleOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/Tooltip.html" class="sidebar-link">Tooltip</a></li><li><a href="/docs/3.9.1/api/interfaces/TooltipCallbacks.html" class="sidebar-link">TooltipCallbacks</a></li><li><a href="/docs/3.9.1/api/interfaces/TooltipItem.html" class="sidebar-link">TooltipItem</a></li><li><a href="/docs/3.9.1/api/interfaces/TooltipLabelStyle.html" class="sidebar-link">TooltipLabelStyle</a></li><li><a href="/docs/3.9.1/api/interfaces/TooltipModel.html" class="sidebar-link">TooltipModel</a></li><li><a href="/docs/3.9.1/api/interfaces/TooltipOptions.html" class="sidebar-link">TooltipOptions</a></li><li><a href="/docs/3.9.1/api/interfaces/TooltipPosition.html" class="sidebar-link">TooltipPosition</a></li><li><a href="/docs/3.9.1/api/interfaces/TooltipPositionerMap.html" class="sidebar-link">TooltipPositionerMap</a></li><li><a href="/docs/3.9.1/api/interfaces/TypedRegistry.html" class="sidebar-link">TypedRegistry</a></li><li><a href="/docs/3.9.1/api/interfaces/VisualElement.html" class="sidebar-link">VisualElement</a></li></ul></section></li></ul></section></li></ul> </aside> <main class="page"> <div class="theme-default-content content__default"><h1 id="interface-plugin-ttype-o"><a href="#interface-plugin-ttype-o" class="header-anchor">#</a> Interface: Plugin&lt;TType, O&gt;</h1> <h2 id="type-parameters"><a href="#type-parameters" class="header-anchor">#</a> Type parameters</h2> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>TType</code></td> <td style="text-align:left;">extends <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a> = <a href="/docs/3.9.1/api/#charttype"><code>ChartType</code></a></td></tr> <tr><td style="text-align:left;"><code>O</code></td> <td style="text-align:left;"><code>AnyObject</code></td></tr></tbody></table> <h2 id="hierarchy"><a href="#hierarchy" class="header-anchor">#</a> Hierarchy</h2> <ul><li><p><a href="/docs/3.9.1/api/interfaces/ExtendedPlugin.html"><code>ExtendedPlugin</code></a>&lt;<code>TType</code>, <code>O</code>&gt;</p> <p>↳ <strong><code>Plugin</code></strong></p> <p>↳↳ <a href="/docs/3.9.1/api/interfaces/Tooltip.html"><code>Tooltip</code></a></p></li></ul> <h2 id="properties"><a href="#properties" class="header-anchor">#</a> Properties</h2> <h3 id="id"><a href="#id" class="header-anchor">#</a> id</h3> <p>• <strong>id</strong>: <code>string</code></p> <h4 id="defined-in"><a href="#defined-in" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L808" target="_blank" rel="noopener noreferrer">index.esm.d.ts:808<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <h2 id="methods"><a href="#methods" class="header-anchor">#</a> Methods</h2> <h3 id="afterbuildticks"><a href="#afterbuildticks" class="header-anchor">#</a> afterBuildTicks</h3> <p>▸ <code>Optional</code> <strong>afterBuildTicks</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called after scale has build its ticks. This hook is called separately for each scale in the chart.</p> <h4 id="parameters"><a href="#parameters" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.scale</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Scale.html"><code>Scale</code></a>&lt;<a href="/docs/3.9.1/api/interfaces/CoreScaleOptions.html"><code>CoreScaleOptions</code></a>&gt;</td> <td style="text-align:left;">The scale.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns"><a href="#returns" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-2"><a href="#defined-in-2" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L967" target="_blank" rel="noopener noreferrer">index.esm.d.ts:967<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterdatalimits"><a href="#afterdatalimits" class="header-anchor">#</a> afterDataLimits</h3> <p>▸ <code>Optional</code> <strong>afterDataLimits</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called after scale data limits are calculated. This hook is called separately for each scale in the chart.</p> <h4 id="parameters-2"><a href="#parameters-2" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.scale</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Scale.html"><code>Scale</code></a>&lt;<a href="/docs/3.9.1/api/interfaces/CoreScaleOptions.html"><code>CoreScaleOptions</code></a>&gt;</td> <td style="text-align:left;">The scale.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-2"><a href="#returns-2" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-3"><a href="#defined-in-3" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L951" target="_blank" rel="noopener noreferrer">index.esm.d.ts:951<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterdatasetdraw"><a href="#afterdatasetdraw" class="header-anchor">#</a> afterDatasetDraw</h3> <p>▸ <code>Optional</code> <strong>afterDatasetDraw</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called after the <code>chart</code> datasets at the given <code>args.index</code> have been drawn
(datasets are drawn in the reverse order). Note that this hook will not be called
if the datasets drawing has been previously cancelled.</p> <h4 id="parameters-3"><a href="#parameters-3" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.index</code></td> <td style="text-align:left;"><code>number</code></td> <td style="text-align:left;">The dataset index.</td></tr> <tr><td style="text-align:left;"><code>args.meta</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/#chartmeta"><code>ChartMeta</code></a>&lt;<a href="/docs/3.9.1/api/#element"><code>Element</code></a>&lt;<code>AnyObject</code>, <code>AnyObject</code>&gt;, <a href="/docs/3.9.1/api/#element"><code>Element</code></a>&lt;<code>AnyObject</code>, <code>AnyObject</code>&gt;, keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>&gt;</td> <td style="text-align:left;">The dataset metadata.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-3"><a href="#returns-3" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-4"><a href="#defined-in-4" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1049" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1049<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterdatasetupdate"><a href="#afterdatasetupdate" class="header-anchor">#</a> afterDatasetUpdate</h3> <p>▸ <code>Optional</code> <strong>afterDatasetUpdate</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called after the <code>chart</code> datasets at the given <code>args.index</code> has been updated. Note
that this hook will not be called if the datasets update has been previously cancelled.</p> <h4 id="parameters-4"><a href="#parameters-4" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.cancelable</code></td> <td style="text-align:left;"><code>false</code></td> <td style="text-align:left;">-</td></tr> <tr><td style="text-align:left;"><code>args.index</code></td> <td style="text-align:left;"><code>number</code></td> <td style="text-align:left;">The dataset index.</td></tr> <tr><td style="text-align:left;"><code>args.meta</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/#chartmeta"><code>ChartMeta</code></a>&lt;<a href="/docs/3.9.1/api/#element"><code>Element</code></a>&lt;<code>AnyObject</code>, <code>AnyObject</code>&gt;, <a href="/docs/3.9.1/api/#element"><code>Element</code></a>&lt;<code>AnyObject</code>, <code>AnyObject</code>&gt;, keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>&gt;</td> <td style="text-align:left;">The dataset metadata.</td></tr> <tr><td style="text-align:left;"><code>args.mode</code></td> <td style="text-align:left;"><code>&quot;resize&quot;</code> | <code>&quot;reset&quot;</code> | <code>&quot;none&quot;</code> | <code>&quot;hide&quot;</code> | <code>&quot;show&quot;</code> | <code>&quot;normal&quot;</code> | <code>&quot;active&quot;</code></td> <td style="text-align:left;">The update mode.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-4"><a href="#returns-4" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-5"><a href="#defined-in-5" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L926" target="_blank" rel="noopener noreferrer">index.esm.d.ts:926<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterdatasetsdraw"><a href="#afterdatasetsdraw" class="header-anchor">#</a> afterDatasetsDraw</h3> <p>▸ <code>Optional</code> <strong>afterDatasetsDraw</strong>(<code>chart</code>, <code>args</code>, <code>options</code>, <code>cancelable</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called after the <code>chart</code> datasets have been drawn. Note that this hook
will not be called if the datasets drawing has been previously cancelled.</p> <h4 id="parameters-5"><a href="#parameters-5" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>EmptyObject</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr> <tr><td style="text-align:left;"><code>cancelable</code></td> <td style="text-align:left;"><code>false</code></td> <td style="text-align:left;">-</td></tr></tbody></table> <h4 id="returns-5"><a href="#returns-5" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-6"><a href="#defined-in-6" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1026" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1026<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterdatasetsupdate"><a href="#afterdatasetsupdate" class="header-anchor">#</a> afterDatasetsUpdate</h3> <p>▸ <code>Optional</code> <strong>afterDatasetsUpdate</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called after the <code>chart</code> datasets have been updated. Note that this hook
will not be called if the datasets update has been previously cancelled.</p> <p><strong><code>since</code></strong> version 2.1.5</p> <h4 id="parameters-6"><a href="#parameters-6" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.cancelable</code></td> <td style="text-align:left;"><code>true</code></td> <td style="text-align:left;">-</td></tr> <tr><td style="text-align:left;"><code>args.mode</code></td> <td style="text-align:left;"><code>&quot;resize&quot;</code> | <code>&quot;reset&quot;</code> | <code>&quot;none&quot;</code> | <code>&quot;hide&quot;</code> | <code>&quot;show&quot;</code> | <code>&quot;normal&quot;</code> | <code>&quot;active&quot;</code></td> <td style="text-align:left;">The update mode.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-6"><a href="#returns-6" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-7"><a href="#defined-in-7" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L903" target="_blank" rel="noopener noreferrer">index.esm.d.ts:903<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterdestroy"><a href="#afterdestroy" class="header-anchor">#</a> afterDestroy</h3> <p>▸ <code>Optional</code> <strong>afterDestroy</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p>Called after the chart has been destroyed.</p> <h4 id="parameters-7"><a href="#parameters-7" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>EmptyObject</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-7"><a href="#returns-7" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-8"><a href="#defined-in-8" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1102" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1102<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterdraw"><a href="#afterdraw" class="header-anchor">#</a> afterDraw</h3> <p>▸ <code>Optional</code> <strong>afterDraw</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called after the <code>chart</code> has been drawn. Note that this hook will not be called
if the drawing has been previously cancelled.</p> <h4 id="parameters-8"><a href="#parameters-8" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>EmptyObject</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-8"><a href="#returns-8" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-9"><a href="#defined-in-9" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1009" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1009<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterevent"><a href="#afterevent" class="header-anchor">#</a> afterEvent</h3> <p>▸ <code>Optional</code> <strong>afterEvent</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called after the <code>event</code> has been consumed. Note that this hook
will not be called if the <code>event</code> has been previously discarded.</p> <h4 id="parameters-9"><a href="#parameters-9" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.cancelable</code></td> <td style="text-align:left;"><code>false</code></td> <td style="text-align:left;">-</td></tr> <tr><td style="text-align:left;"><code>args.changed?</code></td> <td style="text-align:left;"><code>boolean</code></td> <td style="text-align:left;">-</td></tr> <tr><td style="text-align:left;"><code>args.event</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/ChartEvent.html"><code>ChartEvent</code></a></td> <td style="text-align:left;">The event object.</td></tr> <tr><td style="text-align:left;"><code>args.inChartArea</code></td> <td style="text-align:left;"><code>boolean</code></td> <td style="text-align:left;">The event position is inside chartArea</td></tr> <tr><td style="text-align:left;"><code>args.replay</code></td> <td style="text-align:left;"><code>boolean</code></td> <td style="text-align:left;">True if this event is replayed from <code>Chart.update</code></td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-9"><a href="#returns-9" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-10"><a href="#defined-in-10" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1072" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1072<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterinit"><a href="#afterinit" class="header-anchor">#</a> afterInit</h3> <p>▸ <code>Optional</code> <strong>afterInit</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called after <code>chart</code> has been initialized and before the first update.</p> <h4 id="parameters-10"><a href="#parameters-10" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>EmptyObject</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-10"><a href="#returns-10" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-11"><a href="#defined-in-11" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L847" target="_blank" rel="noopener noreferrer">index.esm.d.ts:847<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterlayout"><a href="#afterlayout" class="header-anchor">#</a> afterLayout</h3> <p>▸ <code>Optional</code> <strong>afterLayout</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called after the <code>chart</code> has been laid out. Note that this hook will not
be called if the layout update has been previously cancelled.</p> <h4 id="parameters-11"><a href="#parameters-11" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>EmptyObject</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-11"><a href="#returns-11" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-12"><a href="#defined-in-12" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L975" target="_blank" rel="noopener noreferrer">index.esm.d.ts:975<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterrender"><a href="#afterrender" class="header-anchor">#</a> afterRender</h3> <p>▸ <code>Optional</code> <strong>afterRender</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called after the <code>chart</code> has been fully rendered (and animation completed). Note
that this hook will not be called if the rendering has been previously cancelled.</p> <h4 id="parameters-12"><a href="#parameters-12" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>EmptyObject</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-12"><a href="#returns-12" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-13"><a href="#defined-in-13" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L992" target="_blank" rel="noopener noreferrer">index.esm.d.ts:992<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="aftertooltipdraw"><a href="#aftertooltipdraw" class="header-anchor">#</a> afterTooltipDraw</h3> <p>▸ <code>Optional</code> <strong>afterTooltipDraw</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called after drawing the <code>tooltip</code>. Note that this hook will not
be called if the tooltip drawing has been previously cancelled.</p> <h4 id="parameters-13"><a href="#parameters-13" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.tooltip</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/TooltipModel.html"><code>TooltipModel</code></a>&lt;<code>TType</code>&gt;</td> <td style="text-align:left;">The tooltip.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-13"><a href="#returns-13" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="inherited-from"><a href="#inherited-from" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/ExtendedPlugin.html">ExtendedPlugin</a>.<a href="/docs/3.9.1/api/interfaces/ExtendedPlugin.html#aftertooltipdraw">afterTooltipDraw</a></p> <h4 id="defined-in-14"><a href="#defined-in-14" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2601" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2601<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="afterupdate"><a href="#afterupdate" class="header-anchor">#</a> afterUpdate</h3> <p>▸ <code>Optional</code> <strong>afterUpdate</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called after <code>chart</code> has been updated and before rendering. Note that this
hook will not be called if the chart update has been previously cancelled.</p> <h4 id="parameters-14"><a href="#parameters-14" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.mode</code></td> <td style="text-align:left;"><code>&quot;resize&quot;</code> | <code>&quot;reset&quot;</code> | <code>&quot;none&quot;</code> | <code>&quot;hide&quot;</code> | <code>&quot;show&quot;</code> | <code>&quot;normal&quot;</code> | <code>&quot;active&quot;</code></td> <td style="text-align:left;">The update mode</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-14"><a href="#returns-14" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-15"><a href="#defined-in-15" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L866" target="_blank" rel="noopener noreferrer">index.esm.d.ts:866<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforebuildticks"><a href="#beforebuildticks" class="header-anchor">#</a> beforeBuildTicks</h3> <p>▸ <code>Optional</code> <strong>beforeBuildTicks</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called before scale builds its ticks. This hook is called separately for each scale in the chart.</p> <h4 id="parameters-15"><a href="#parameters-15" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.scale</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Scale.html"><code>Scale</code></a>&lt;<a href="/docs/3.9.1/api/interfaces/CoreScaleOptions.html"><code>CoreScaleOptions</code></a>&gt;</td> <td style="text-align:left;">The scale.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-15"><a href="#returns-15" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-16"><a href="#defined-in-16" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L959" target="_blank" rel="noopener noreferrer">index.esm.d.ts:959<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforedatalimits"><a href="#beforedatalimits" class="header-anchor">#</a> beforeDataLimits</h3> <p>▸ <code>Optional</code> <strong>beforeDataLimits</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called before scale data limits are calculated. This hook is called separately for each scale in the chart.</p> <h4 id="parameters-16"><a href="#parameters-16" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.scale</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Scale.html"><code>Scale</code></a>&lt;<a href="/docs/3.9.1/api/interfaces/CoreScaleOptions.html"><code>CoreScaleOptions</code></a>&gt;</td> <td style="text-align:left;">The scale.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-16"><a href="#returns-16" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-17"><a href="#defined-in-17" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L943" target="_blank" rel="noopener noreferrer">index.esm.d.ts:943<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforedatasetdraw"><a href="#beforedatasetdraw" class="header-anchor">#</a> beforeDatasetDraw</h3> <p>▸ <code>Optional</code> <strong>beforeDatasetDraw</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>boolean</code> | <code>void</code></p> <p><strong><code>desc</code></strong> Called before drawing the <code>chart</code> dataset at the given <code>args.index</code> (datasets
are drawn in the reverse order). If any plugin returns <code>false</code>, the datasets drawing
is cancelled until another <code>render</code> is triggered.</p> <h4 id="parameters-17"><a href="#parameters-17" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.index</code></td> <td style="text-align:left;"><code>number</code></td> <td style="text-align:left;">The dataset index.</td></tr> <tr><td style="text-align:left;"><code>args.meta</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/#chartmeta"><code>ChartMeta</code></a>&lt;<a href="/docs/3.9.1/api/#element"><code>Element</code></a>&lt;<code>AnyObject</code>, <code>AnyObject</code>&gt;, <a href="/docs/3.9.1/api/#element"><code>Element</code></a>&lt;<code>AnyObject</code>, <code>AnyObject</code>&gt;, keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>&gt;</td> <td style="text-align:left;">The dataset metadata.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-17"><a href="#returns-17" class="header-anchor">#</a> Returns</h4> <p><code>boolean</code> | <code>void</code></p> <p><code>false</code> to cancel the chart datasets drawing.</p> <h4 id="defined-in-18"><a href="#defined-in-18" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1038" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1038<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforedatasetupdate"><a href="#beforedatasetupdate" class="header-anchor">#</a> beforeDatasetUpdate</h3> <p>▸ <code>Optional</code> <strong>beforeDatasetUpdate</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>boolean</code> | <code>void</code></p> <p><strong><code>desc</code></strong> Called before updating the <code>chart</code> dataset at the given <code>args.index</code>. If any plugin
returns <code>false</code>, the datasets update is cancelled until another <code>update</code> is triggered.</p> <h4 id="parameters-18"><a href="#parameters-18" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.cancelable</code></td> <td style="text-align:left;"><code>true</code></td> <td style="text-align:left;">-</td></tr> <tr><td style="text-align:left;"><code>args.index</code></td> <td style="text-align:left;"><code>number</code></td> <td style="text-align:left;">The dataset index.</td></tr> <tr><td style="text-align:left;"><code>args.meta</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/#chartmeta"><code>ChartMeta</code></a>&lt;<a href="/docs/3.9.1/api/#element"><code>Element</code></a>&lt;<code>AnyObject</code>, <code>AnyObject</code>&gt;, <a href="/docs/3.9.1/api/#element"><code>Element</code></a>&lt;<code>AnyObject</code>, <code>AnyObject</code>&gt;, keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>&gt;</td> <td style="text-align:left;">The dataset metadata.</td></tr> <tr><td style="text-align:left;"><code>args.mode</code></td> <td style="text-align:left;"><code>&quot;resize&quot;</code> | <code>&quot;reset&quot;</code> | <code>&quot;none&quot;</code> | <code>&quot;hide&quot;</code> | <code>&quot;show&quot;</code> | <code>&quot;normal&quot;</code> | <code>&quot;active&quot;</code></td> <td style="text-align:left;">The update mode.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-18"><a href="#returns-18" class="header-anchor">#</a> Returns</h4> <p><code>boolean</code> | <code>void</code></p> <p><code>false</code> to cancel the chart datasets drawing.</p> <h4 id="defined-in-19"><a href="#defined-in-19" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L915" target="_blank" rel="noopener noreferrer">index.esm.d.ts:915<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforedatasetsdraw"><a href="#beforedatasetsdraw" class="header-anchor">#</a> beforeDatasetsDraw</h3> <p>▸ <code>Optional</code> <strong>beforeDatasetsDraw</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>boolean</code> | <code>void</code></p> <p><strong><code>desc</code></strong> Called before drawing the <code>chart</code> datasets. If any plugin returns <code>false</code>,
the datasets drawing is cancelled until another <code>render</code> is triggered.</p> <h4 id="parameters-19"><a href="#parameters-19" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.cancelable</code></td> <td style="text-align:left;"><code>true</code></td> <td style="text-align:left;">-</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-19"><a href="#returns-19" class="header-anchor">#</a> Returns</h4> <p><code>boolean</code> | <code>void</code></p> <p><code>false</code> to cancel the chart datasets drawing.</p> <h4 id="defined-in-20"><a href="#defined-in-20" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1018" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1018<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforedatasetsupdate"><a href="#beforedatasetsupdate" class="header-anchor">#</a> beforeDatasetsUpdate</h3> <p>▸ <code>Optional</code> <strong>beforeDatasetsUpdate</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>boolean</code> | <code>void</code></p> <p><strong><code>desc</code></strong> Called before updating the <code>chart</code> datasets. If any plugin returns <code>false</code>,
the datasets update is cancelled until another <code>update</code> is triggered.</p> <p><strong><code>since</code></strong> version 2.1.5</p> <h4 id="parameters-20"><a href="#parameters-20" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.mode</code></td> <td style="text-align:left;"><code>&quot;resize&quot;</code> | <code>&quot;reset&quot;</code> | <code>&quot;none&quot;</code> | <code>&quot;hide&quot;</code> | <code>&quot;show&quot;</code> | <code>&quot;normal&quot;</code> | <code>&quot;active&quot;</code></td> <td style="text-align:left;">The update mode.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-20"><a href="#returns-20" class="header-anchor">#</a> Returns</h4> <p><code>boolean</code> | <code>void</code></p> <p>false to cancel the datasets update.</p> <h4 id="defined-in-21"><a href="#defined-in-21" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L893" target="_blank" rel="noopener noreferrer">index.esm.d.ts:893<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforedestroy"><a href="#beforedestroy" class="header-anchor">#</a> beforeDestroy</h3> <p>▸ <code>Optional</code> <strong>beforeDestroy</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p>Called before the chart is being destroyed.</p> <h4 id="parameters-21"><a href="#parameters-21" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>EmptyObject</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-21"><a href="#returns-21" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-22"><a href="#defined-in-22" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1087" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1087<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforedraw"><a href="#beforedraw" class="header-anchor">#</a> beforeDraw</h3> <p>▸ <code>Optional</code> <strong>beforeDraw</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>boolean</code> | <code>void</code></p> <p><strong><code>desc</code></strong> Called before drawing <code>chart</code> at every animation frame. If any plugin returns <code>false</code>,
the frame drawing is cancelled untilanother <code>render</code> is triggered.</p> <h4 id="parameters-22"><a href="#parameters-22" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.cancelable</code></td> <td style="text-align:left;"><code>true</code></td> <td style="text-align:left;">-</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-22"><a href="#returns-22" class="header-anchor">#</a> Returns</h4> <p><code>boolean</code> | <code>void</code></p> <p><code>false</code> to cancel the chart drawing.</p> <h4 id="defined-in-23"><a href="#defined-in-23" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1001" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1001<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforeelementsupdate"><a href="#beforeelementsupdate" class="header-anchor">#</a> beforeElementsUpdate</h3> <p>▸ <code>Optional</code> <strong>beforeElementsUpdate</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called during the update process, before any chart elements have been created.
This can be used for data decimation by changing the data array inside a dataset.</p> <h4 id="parameters-23"><a href="#parameters-23" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>EmptyObject</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-23"><a href="#returns-23" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-24"><a href="#defined-in-24" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L874" target="_blank" rel="noopener noreferrer">index.esm.d.ts:874<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforeevent"><a href="#beforeevent" class="header-anchor">#</a> beforeEvent</h3> <p>▸ <code>Optional</code> <strong>beforeEvent</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>boolean</code> | <code>void</code></p> <p><strong><code>desc</code></strong> Called before processing the specified <code>event</code>. If any plugin returns <code>false</code>,
the event will be discarded.</p> <h4 id="parameters-24"><a href="#parameters-24" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.cancelable</code></td> <td style="text-align:left;"><code>true</code></td> <td style="text-align:left;">-</td></tr> <tr><td style="text-align:left;"><code>args.event</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/ChartEvent.html"><code>ChartEvent</code></a></td> <td style="text-align:left;">The event object.</td></tr> <tr><td style="text-align:left;"><code>args.inChartArea</code></td> <td style="text-align:left;"><code>boolean</code></td> <td style="text-align:left;">The event position is inside chartArea</td></tr> <tr><td style="text-align:left;"><code>args.replay</code></td> <td style="text-align:left;"><code>boolean</code></td> <td style="text-align:left;">True if this event is replayed from <code>Chart.update</code></td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-24"><a href="#returns-24" class="header-anchor">#</a> Returns</h4> <p><code>boolean</code> | <code>void</code></p> <h4 id="defined-in-25"><a href="#defined-in-25" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1060" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1060<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforeinit"><a href="#beforeinit" class="header-anchor">#</a> beforeInit</h3> <p>▸ <code>Optional</code> <strong>beforeInit</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called before initializing <code>chart</code>.</p> <h4 id="parameters-25"><a href="#parameters-25" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>EmptyObject</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-25"><a href="#returns-25" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-26"><a href="#defined-in-26" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L840" target="_blank" rel="noopener noreferrer">index.esm.d.ts:840<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforelayout"><a href="#beforelayout" class="header-anchor">#</a> beforeLayout</h3> <p>▸ <code>Optional</code> <strong>beforeLayout</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>boolean</code> | <code>void</code></p> <p><strong><code>desc</code></strong> Called before laying out <code>chart</code>. If any plugin returns <code>false</code>,
the layout update is cancelled until another <code>update</code> is triggered.</p> <h4 id="parameters-26"><a href="#parameters-26" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.cancelable</code></td> <td style="text-align:left;"><code>true</code></td> <td style="text-align:left;">-</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-26"><a href="#returns-26" class="header-anchor">#</a> Returns</h4> <p><code>boolean</code> | <code>void</code></p> <p><code>false</code> to cancel the chart layout.</p> <h4 id="defined-in-27"><a href="#defined-in-27" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L935" target="_blank" rel="noopener noreferrer">index.esm.d.ts:935<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforerender"><a href="#beforerender" class="header-anchor">#</a> beforeRender</h3> <p>▸ <code>Optional</code> <strong>beforeRender</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>boolean</code> | <code>void</code></p> <p><strong><code>desc</code></strong> Called before rendering <code>chart</code>. If any plugin returns <code>false</code>,
the rendering is cancelled until another <code>render</code> is triggered.</p> <h4 id="parameters-27"><a href="#parameters-27" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.cancelable</code></td> <td style="text-align:left;"><code>true</code></td> <td style="text-align:left;">-</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-27"><a href="#returns-27" class="header-anchor">#</a> Returns</h4> <p><code>boolean</code> | <code>void</code></p> <p><code>false</code> to cancel the chart rendering.</p> <h4 id="defined-in-28"><a href="#defined-in-28" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L984" target="_blank" rel="noopener noreferrer">index.esm.d.ts:984<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforetooltipdraw"><a href="#beforetooltipdraw" class="header-anchor">#</a> beforeTooltipDraw</h3> <p>▸ <code>Optional</code> <strong>beforeTooltipDraw</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>boolean</code> | <code>void</code></p> <p><strong><code>desc</code></strong> Called before drawing the <code>tooltip</code>. If any plugin returns <code>false</code>,
the tooltip drawing is cancelled until another <code>render</code> is triggered.</p> <h4 id="parameters-28"><a href="#parameters-28" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.tooltip</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/interfaces/TooltipModel.html"><code>TooltipModel</code></a>&lt;<code>TType</code>&gt;</td> <td style="text-align:left;">The tooltip.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-28"><a href="#returns-28" class="header-anchor">#</a> Returns</h4> <p><code>boolean</code> | <code>void</code></p> <p><code>false</code> to cancel the chart tooltip drawing.</p> <h4 id="inherited-from-2"><a href="#inherited-from-2" class="header-anchor">#</a> Inherited from</h4> <p><a href="/docs/3.9.1/api/interfaces/ExtendedPlugin.html">ExtendedPlugin</a>.<a href="/docs/3.9.1/api/interfaces/ExtendedPlugin.html#beforetooltipdraw">beforeTooltipDraw</a></p> <h4 id="defined-in-29"><a href="#defined-in-29" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L2592" target="_blank" rel="noopener noreferrer">index.esm.d.ts:2592<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="beforeupdate"><a href="#beforeupdate" class="header-anchor">#</a> beforeUpdate</h3> <p>▸ <code>Optional</code> <strong>beforeUpdate</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>boolean</code> | <code>void</code></p> <p><strong><code>desc</code></strong> Called before updating <code>chart</code>. If any plugin returns <code>false</code>, the update
is cancelled (and thus subsequent render(s)) until another <code>update</code> is triggered.</p> <h4 id="parameters-29"><a href="#parameters-29" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.cancelable</code></td> <td style="text-align:left;"><code>true</code></td> <td style="text-align:left;">-</td></tr> <tr><td style="text-align:left;"><code>args.mode</code></td> <td style="text-align:left;"><code>&quot;resize&quot;</code> | <code>&quot;reset&quot;</code> | <code>&quot;none&quot;</code> | <code>&quot;hide&quot;</code> | <code>&quot;show&quot;</code> | <code>&quot;normal&quot;</code> | <code>&quot;active&quot;</code></td> <td style="text-align:left;">The update mode</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-29"><a href="#returns-29" class="header-anchor">#</a> Returns</h4> <p><code>boolean</code> | <code>void</code></p> <p><code>false</code> to cancel the chart update.</p> <h4 id="defined-in-30"><a href="#defined-in-30" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L857" target="_blank" rel="noopener noreferrer">index.esm.d.ts:857<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="destroy"><a href="#destroy" class="header-anchor">#</a> destroy</h3> <p>▸ <code>Optional</code> <strong>destroy</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p>Called after the chart has been destroyed.</p> <p><strong><code>deprecated</code></strong> since version 3.7.0 in favour of afterDestroy</p> <h4 id="parameters-30"><a href="#parameters-30" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>EmptyObject</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-30"><a href="#returns-30" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-31"><a href="#defined-in-31" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1095" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1095<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="install"><a href="#install" class="header-anchor">#</a> install</h3> <p>▸ <code>Optional</code> <strong>install</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called when plugin is installed for this chart instance. This hook is also invoked for disabled plugins (options === false).</p> <p><strong><code>since</code></strong> 3.0.0</p> <h4 id="parameters-31"><a href="#parameters-31" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>EmptyObject</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-31"><a href="#returns-31" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-32"><a href="#defined-in-32" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L817" target="_blank" rel="noopener noreferrer">index.esm.d.ts:817<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="reset"><a href="#reset" class="header-anchor">#</a> reset</h3> <p>▸ <code>Optional</code> <strong>reset</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called during chart reset</p> <p><strong><code>since</code></strong> version 3.0.0</p> <h4 id="parameters-32"><a href="#parameters-32" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>EmptyObject</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-32"><a href="#returns-32" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-33"><a href="#defined-in-33" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L882" target="_blank" rel="noopener noreferrer">index.esm.d.ts:882<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="resize"><a href="#resize" class="header-anchor">#</a> resize</h3> <p>▸ <code>Optional</code> <strong>resize</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called after the chart as been resized.</p> <h4 id="parameters-33"><a href="#parameters-33" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>args.size</code></td> <td style="text-align:left;"><code>Object</code></td> <td style="text-align:left;">The new canvas display size (eq. canvas.style width &amp; height).</td></tr> <tr><td style="text-align:left;"><code>args.size.height</code></td> <td style="text-align:left;"><code>number</code></td> <td style="text-align:left;">-</td></tr> <tr><td style="text-align:left;"><code>args.size.width</code></td> <td style="text-align:left;"><code>number</code></td> <td style="text-align:left;">-</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-33"><a href="#returns-33" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-34"><a href="#defined-in-34" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1080" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1080<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="start"><a href="#start" class="header-anchor">#</a> start</h3> <p>▸ <code>Optional</code> <strong>start</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called when a plugin is starting. This happens when chart is created or plugin is enabled.</p> <p><strong><code>since</code></strong> 3.0.0</p> <h4 id="parameters-34"><a href="#parameters-34" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>EmptyObject</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-34"><a href="#returns-34" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-35"><a href="#defined-in-35" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L825" target="_blank" rel="noopener noreferrer">index.esm.d.ts:825<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="stop"><a href="#stop" class="header-anchor">#</a> stop</h3> <p>▸ <code>Optional</code> <strong>stop</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p><strong><code>desc</code></strong> Called when a plugin stopping. This happens when chart is destroyed or plugin is disabled.</p> <p><strong><code>since</code></strong> 3.0.0</p> <h4 id="parameters-35"><a href="#parameters-35" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>EmptyObject</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-35"><a href="#returns-35" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-36"><a href="#defined-in-36" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L833" target="_blank" rel="noopener noreferrer">index.esm.d.ts:833<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p> <hr> <h3 id="uninstall"><a href="#uninstall" class="header-anchor">#</a> uninstall</h3> <p>▸ <code>Optional</code> <strong>uninstall</strong>(<code>chart</code>, <code>args</code>, <code>options</code>): <code>void</code></p> <p>Called after chart is destroyed on all plugins that were installed for that chart. This hook is also invoked for disabled plugins (options === false).</p> <p><strong><code>since</code></strong> 3.0.0</p> <h4 id="parameters-36"><a href="#parameters-36" class="header-anchor">#</a> Parameters</h4> <table><thead><tr><th style="text-align:left;">Name</th> <th style="text-align:left;">Type</th> <th style="text-align:left;">Description</th></tr></thead> <tbody><tr><td style="text-align:left;"><code>chart</code></td> <td style="text-align:left;"><a href="/docs/3.9.1/api/classes/Chart.html"><code>Chart</code></a>&lt;keyof <a href="/docs/3.9.1/api/interfaces/ChartTypeRegistry.html"><code>ChartTypeRegistry</code></a>, (<code>number</code> | <a href="/docs/3.9.1/api/interfaces/ScatterDataPoint.html"><code>ScatterDataPoint</code></a> | <a href="/docs/3.9.1/api/interfaces/BubbleDataPoint.html"><code>BubbleDataPoint</code></a>)[], <code>unknown</code>&gt;</td> <td style="text-align:left;">The chart instance.</td></tr> <tr><td style="text-align:left;"><code>args</code></td> <td style="text-align:left;"><code>EmptyObject</code></td> <td style="text-align:left;">The call arguments.</td></tr> <tr><td style="text-align:left;"><code>options</code></td> <td style="text-align:left;"><code>O</code></td> <td style="text-align:left;">The plugin options.</td></tr></tbody></table> <h4 id="returns-36"><a href="#returns-36" class="header-anchor">#</a> Returns</h4> <p><code>void</code></p> <h4 id="defined-in-37"><a href="#defined-in-37" class="header-anchor">#</a> Defined in</h4> <p><a href="https://github.com/chartjs/Chart.js/blob/5ea4b3a/types/index.esm.d.ts#L1110" target="_blank" rel="noopener noreferrer">index.esm.d.ts:1110<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></p></div> <footer class="page-edit"><!----> <!----></footer> <div class="page-nav"><p class="inner"><span class="prev">
      ←
      <a href="/docs/3.9.1/api/interfaces/ParsingOptions.html" class="prev">
        ParsingOptions
      </a></span> <span class="next"><a href="/docs/3.9.1/api/interfaces/PluginChartOptions.html">
        PluginChartOptions
      </a>
      →
    </span></p></div> </main></div><div class="global-ui"></div></div>
    <script src="/docs/3.9.1/assets/js/app.7e0dc8c8.js" defer></script><script src="/docs/3.9.1/assets/js/2.3e2307f3.js" defer></script><script src="/docs/3.9.1/assets/js/86.85011b24.js" defer></script>
  </body>
</html>
