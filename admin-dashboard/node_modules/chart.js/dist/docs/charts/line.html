<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Line Chart | Chart.js</title>
    <meta name="generator" content="VuePress 1.8.3">
    <link rel="icon" href="/docs/3.9.1/favicon.ico">
    <meta name="description" content="Open source HTML5 Charts for your website">
    <link rel="preload" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css" as="style"><link rel="preload" href="/docs/3.9.1/assets/js/app.7e0dc8c8.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/2.3e2307f3.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/146.001afadf.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/3.947b8d98.js" as="script"><link rel="prefetch" href="/docs/3.9.1/assets/js/10.edbecfa9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/100.f7a12d8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/101.15c56dbb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/102.33b746ea.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/103.850a4486.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/104.8b4f368b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/105.1a3d6c41.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/106.d8f8b4c1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/107.38cf9b4f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/108.a30da8c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/109.6d56b7d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/11.b98e8151.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/110.c44e533c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/111.ccb9a835.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/112.2d2a2890.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/113.5ac0921f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/114.90ef8814.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/115.12a72d27.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/116.6b780970.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/117.79604442.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/118.7752bc89.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/119.d8e76ef0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/12.433f0c7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/120.ed76595e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/121.cc3d56f2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/122.c8651871.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/123.f7d5f223.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/124.88ce07a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/125.a9572036.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/126.879447b3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/127.3b8478f3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/128.87d67ad7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/129.f6241700.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/13.b6743084.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/130.a9cb856a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/131.45e904c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/132.abd9e4c2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/133.d5aa1db6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/134.4bfa3b5a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/135.8c7630ad.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/136.f813e062.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/137.6cab2863.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/138.03ec5265.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/139.924812c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/14.422fec10.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/140.a7967a5d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/141.34a018bb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/142.c5bfcdd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/143.05e388ba.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/144.a98bdbcc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/145.826b73e0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/147.fcadaef3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/148.604863a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/149.6509ed9f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/15.09a69fc3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/150.fd8575ca.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/151.67ecd4a3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/152.481a7bd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/153.dbf3f3aa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/154.2aa9c67d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/155.e3367ebc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/156.a5bb942d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/157.09caef4e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/158.1f82587f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/159.4cebd9d3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/16.0414f390.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/160.47df8215.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/161.fac79692.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/162.9f2f870f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/163.4f1604f9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/164.7f8d8643.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/165.fe2baa69.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/166.c82b3c76.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/167.3c3f2ff1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/168.8d2e8e83.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/169.b10c84bd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/17.8dfcb176.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/170.b9db8312.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/171.2480fc36.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/172.81c5206a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/173.477dcbae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/174.05398ca3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/175.84ab69d0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/176.f48183c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/177.d0d35403.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/178.55d433fd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/179.9442c138.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/18.c331029e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/180.5c15d5d4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/181.911f367b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/182.72f663e8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/183.22b2258c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/184.15b21065.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/185.894ea40b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/186.f6394459.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/187.7bd9b3fe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/188.3baa9bcd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/189.18e63b11.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/19.13bdd658.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/190.b7ffb54a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/191.13061aba.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/192.1bf9bd61.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/193.8fa44455.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/194.49c9a3c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/195.eb1e8802.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/196.9b925823.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/197.adca6c8c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/198.2ae0961a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/199.cd9dca80.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/20.5d11c294.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/200.2eb9437f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/201.6dba10c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/202.9c5057c6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/203.98a3c2b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/204.50d5cf2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/205.e56e820b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/206.6ce5c41e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/207.8aa83d91.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/208.296ee160.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/209.13279349.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/21.7b54d7d8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/210.63e30420.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/211.0b325f23.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/212.be671e2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/213.81d2e607.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/214.ba403b5c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/215.2efcec5f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/216.e01d3100.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/217.906c8d54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/218.94e33827.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/219.21c5e01f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/22.c2daedd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/220.3ba160e4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/221.5c17138c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/222.aea004ce.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/223.cbb7b883.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/224.b130b37f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/225.32f90319.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/226.8c102c21.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/227.b3d60339.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/228.72f0ad18.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/229.3daa3b7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/23.2c668e20.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/230.7e5a85b1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/231.b83d12f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/232.e902f42d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/233.b4f254c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/234.d975df48.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/235.71a7be7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/236.05d87a5e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/237.d518e28b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/238.eb60e397.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/239.81fecfed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/24.af200d5b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/240.c0869bc2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/241.59dc896f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/242.6d423d39.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/243.a355eaa8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/244.ad729cf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/245.c3cd6bbe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/246.d0708528.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/247.13d65d78.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/248.1d222543.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/249.2d7a9bf1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/25.4f97f63f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/250.09ef38e2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/251.0cb90e8a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/252.2ff0def4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/253.61c7c505.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/254.20137eeb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/255.49c937e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/256.8d709dae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/257.b3ebdce7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/258.792f66d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/259.98c809e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/26.d1bb645c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/260.54ff10d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/261.928afea4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/27.2d5c4a6b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/28.72dee0f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/29.8820be26.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/30.343676b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/31.cff089f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/32.56d8546e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/33.6b642a06.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/34.360a9ea9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/35.205d7fac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/36.f2765bae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/37.6c33435e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/38.a778a6a2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/39.7dda160f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/4.ee88d25a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/40.bd778eac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/41.b79220f6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/42.c3157beb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/43.097368d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/44.2515f16e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/45.d5b4b7c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/46.bbab8d6e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/47.79aa575e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/48.45785af9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/49.0569a6eb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/5.00f814ac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/50.14bd3ba2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/51.e0968711.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/52.fe65ddf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/53.faa1ff3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/54.fcfed2c5.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/55.7db1d28a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/56.8b0e82b7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/57.9de2d983.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/58.b0f8ad0c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/59.f1ff4935.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/6.2bc86161.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/60.ac08de9a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/61.ea4fad75.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/62.5c85853b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/63.5ac99656.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/64.c6838e95.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/65.4c1b089e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/66.379a6b45.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/67.15703e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/68.ffdefd7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/69.bdeb7b9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/7.74f2ce90.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/70.3dadb5ed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/71.2c97fe38.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/72.dc778e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/73.d88fcb57.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/74.bba2165e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/75.15562a81.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/76.21f5a94e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/77.0725268e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/78.d6f610d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/79.60d67faa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/8.8928eb8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/80.99d71ee0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/81.f1500469.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/82.69f363a6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/83.f1fbcb2c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/84.b76e3156.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/85.efc4bd54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/86.85011b24.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/87.6a88d571.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/88.0f45cfe0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/89.e67ddb59.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/9.63ebb16b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/90.96ebfa9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/91.cdde4d3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/92.dc3f06d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/93.aaa04d52.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/94.2af5650c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/95.1d44ec16.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/96.9545127d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/97.8b18f487.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/98.cb259cd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/99.6251650a.js">
    <link rel="stylesheet" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css">
  </head>
  <body>
    <div id="app" data-server-rendered="true"><div class="theme-container"><header class="navbar"><div class="sidebar-button"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" viewBox="0 0 448 512" class="icon"><path fill="currentColor" d="M436 124H12c-6.627 0-12-5.373-12-12V80c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12z"></path></svg></div> <a href="/docs/3.9.1/" class="home-link router-link-active"><img src="/docs/3.9.1/favicon.ico" alt="Chart.js" class="logo"> <span class="site-name can-hide">Chart.js</span></a> <div class="links"><div class="search-box"><input aria-label="Search" autocomplete="off" spellcheck="false" value=""> <!----></div> <nav class="nav-links can-hide"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" class="nav-link">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav></div></header> <div class="sidebar-mask"></div> <aside class="sidebar"><nav class="nav-links"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" class="nav-link">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav>  <ul class="sidebar-links"><li><a href="/docs/3.9.1/" aria-current="page" class="sidebar-link">Chart.js</a></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Getting Started</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>General</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Configuration</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading open"><span>Chart Types</span> <span class="arrow down"></span></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/docs/3.9.1/charts/area.html" class="sidebar-link">Area Chart</a></li><li><a href="/docs/3.9.1/charts/bar.html" class="sidebar-link">Bar Chart</a></li><li><a href="/docs/3.9.1/charts/bubble.html" class="sidebar-link">Bubble Chart</a></li><li><a href="/docs/3.9.1/charts/doughnut.html" class="sidebar-link">Doughnut and Pie Charts</a></li><li><a href="/docs/3.9.1/charts/line.html" aria-current="page" class="active sidebar-link">Line Chart</a></li><li><a href="/docs/3.9.1/charts/mixed.html" class="sidebar-link">Mixed Chart Types</a></li><li><a href="/docs/3.9.1/charts/polar.html" class="sidebar-link">Polar Area Chart</a></li><li><a href="/docs/3.9.1/charts/radar.html" class="sidebar-link">Radar Chart</a></li><li><a href="/docs/3.9.1/charts/scatter.html" class="sidebar-link">Scatter Chart</a></li></ul></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Axes</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Developers</span> <span class="arrow right"></span></p> <!----></section></li></ul> </aside> <main class="page"> <div class="theme-default-content content__default"><h1 id="line-chart"><a href="#line-chart" class="header-anchor">#</a> Line Chart</h1> <p>A line chart is a way of plotting data points on a line. Often, it is used to show trend data, or the comparison of two data sets.</p> <div class="chart-editor" data-v-365c20ab><div class="chart-view" data-v-365c20ab><canvas></canvas></div> <div class="chart-actions" data-v-2afd21f1 data-v-365c20ab></div> <div class="code-editor" data-v-66ca8197 data-v-365c20ab><div class="code-editor-header" data-v-66ca8197><div class="code-editor-tabs" data-v-66ca8197><button class="code-editor-tab active" data-v-66ca8197>
        config
      </button><button class="code-editor-tab" data-v-66ca8197>
        setup
      </button></div> <div class="code-editor-tools" data-v-66ca8197><!----> <a href="https://github.com/chartjs/Chart.js/blob/master/docs/charts/line.md" title="View on GitHub" target="_blank" class="code-editor-tool fab fa-github fa-lg" data-v-66ca8197></a></div></div> <div class="code-editor-views" data-v-66ca8197><div class="editor-textarea ps" data-v-05f11386 data-v-66ca8197><div class="editor-textarea-content" data-v-05f11386><div class="prism-editor-wrapper" data-v-05f11386><div class="prism-editor__container"><textarea spellCheck="false" autocapitalize="off" autocomplete="off" autocorrect="off" data-gramm="false" placeholder="" data-testid="textarea" class="prism-editor__textarea">const config = {
  type: 'line',
  data: data,
};</textarea><pre data-testid="preview" class="prism-editor__editor"><span class="token keyword">const</span> config <span class="token operator">=</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'line'</span><span class="token punctuation">,</span>
  <span class="token literal-property property">data</span><span class="token operator">:</span> data<span class="token punctuation">,</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span><br /></pre></div></div></div></div><div class="editor-textarea ps" style="display:none;" data-v-05f11386 data-v-66ca8197><div class="editor-textarea-content" data-v-05f11386><div class="prism-editor-wrapper" data-v-05f11386><div class="prism-editor__container"><textarea spellCheck="false" autocapitalize="off" autocomplete="off" autocorrect="off" data-gramm="false" placeholder="" data-testid="textarea" class="prism-editor__textarea">const labels = Utils.months({count: 7});
const data = {
  labels: labels,
  datasets: [{
    label: 'My First Dataset',
    data: [65, 59, 80, 81, 56, 55, 40],
    fill: false,
    borderColor: 'rgb(75, 192, 192)',
    tension: 0.1
  }]
};</textarea><pre data-testid="preview" class="prism-editor__editor"><span class="token keyword">const</span> labels <span class="token operator">=</span> Utils<span class="token punctuation">.</span><span class="token function">months</span><span class="token punctuation">(</span><span class="token punctuation">{</span><span class="token literal-property property">count</span><span class="token operator">:</span> <span class="token number">7</span><span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword">const</span> data <span class="token operator">=</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">labels</span><span class="token operator">:</span> labels<span class="token punctuation">,</span>
  <span class="token literal-property property">datasets</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token punctuation">{</span>
    <span class="token literal-property property">label</span><span class="token operator">:</span> <span class="token string">'My First Dataset'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token number">65</span><span class="token punctuation">,</span> <span class="token number">59</span><span class="token punctuation">,</span> <span class="token number">80</span><span class="token punctuation">,</span> <span class="token number">81</span><span class="token punctuation">,</span> <span class="token number">56</span><span class="token punctuation">,</span> <span class="token number">55</span><span class="token punctuation">,</span> <span class="token number">40</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token literal-property property">fill</span><span class="token operator">:</span> <span class="token boolean">false</span><span class="token punctuation">,</span>
    <span class="token literal-property property">borderColor</span><span class="token operator">:</span> <span class="token string">'rgb(75, 192, 192)'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">tension</span><span class="token operator">:</span> <span class="token number">0.1</span>
  <span class="token punctuation">}</span><span class="token punctuation">]</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span><br /></pre></div></div></div></div></div></div></div><h2 id="dataset-properties"><a href="#dataset-properties" class="header-anchor">#</a> Dataset Properties</h2> <p>Namespaces:</p> <ul><li><code>data.datasets[index]</code> - options for this dataset only</li> <li><code>options.datasets.line</code> - options for all line datasets</li> <li><code>options.elements.line</code> - options for all <a href="/docs/3.9.1/configuration/elements.html#line-configuration">line elements</a></li> <li><code>options.elements.point</code> - options for all <a href="/docs/3.9.1/configuration/elements.html#point-configuration">point elements</a></li> <li><code>options</code> - options for the whole chart</li></ul> <p>The line chart allows a number of properties to be specified for each dataset. These are used to set display properties for a specific dataset. For example, the colour of a line is generally set this way.</p> <table><thead><tr><th>Name</th> <th>Type</th> <th style="text-align:center;"><a href="/docs/3.9.1/general/options.html#scriptable-options">Scriptable</a></th> <th style="text-align:center;"><a href="/docs/3.9.1/general/options.html#indexable-options">Indexable</a></th> <th>Default</th></tr></thead> <tbody><tr><td><a href="#line-styling"><code>backgroundColor</code></a></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>'rgba(0, 0, 0, 0.1)'</code></td></tr> <tr><td><a href="#line-styling"><code>borderCapStyle</code></a></td> <td><code>string</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>'butt'</code></td></tr> <tr><td><a href="#line-styling"><code>borderColor</code></a></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>'rgba(0, 0, 0, 0.1)'</code></td></tr> <tr><td><a href="#line-styling"><code>borderDash</code></a></td> <td><code>number[]</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>[]</code></td></tr> <tr><td><a href="#line-styling"><code>borderDashOffset</code></a></td> <td><code>number</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>0.0</code></td></tr> <tr><td><a href="#line-styling"><code>borderJoinStyle</code></a></td> <td><code>'round'</code>|<code>'bevel'</code>|<code>'miter'</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>'miter'</code></td></tr> <tr><td><a href="#line-styling"><code>borderWidth</code></a></td> <td><code>number</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>3</code></td></tr> <tr><td><a href="#general"><code>clip</code></a></td> <td><code>number</code>|<code>object</code>|<code>false</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>undefined</code></td></tr> <tr><td><a href="#cubicinterpolationmode"><code>cubicInterpolationMode</code></a></td> <td><code>string</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>'default'</code></td></tr> <tr><td><a href="#data-structure"><code>data</code></a></td> <td><code>object</code>|<code>object[]</code>| <code>number[]</code>|<code>string[]</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><strong>required</strong></td></tr> <tr><td><a href="#general"><code>drawActiveElementsOnTop</code></a></td> <td><code>boolean</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>true</code></td></tr> <tr><td><a href="#line-styling"><code>fill</code></a></td> <td><code>boolean</code>|<code>string</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>false</code></td></tr> <tr><td><a href="#line-styling"><code>hoverBackgroundColor</code></a></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>undefined</code></td></tr> <tr><td><a href="#line-styling"><code>hoverBorderCapStyle</code></a></td> <td><code>string</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>undefined</code></td></tr> <tr><td><a href="#line-styling"><code>hoverBorderColor</code></a></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>undefined</code></td></tr> <tr><td><a href="#line-styling"><code>hoverBorderDash</code></a></td> <td><code>number[]</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>undefined</code></td></tr> <tr><td><a href="#line-styling"><code>hoverBorderDashOffset</code></a></td> <td><code>number</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>undefined</code></td></tr> <tr><td><a href="#line-styling"><code>hoverBorderJoinStyle</code></a></td> <td><code>'round'</code>|<code>'bevel'</code>|<code>'miter'</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>undefined</code></td></tr> <tr><td><a href="#line-styling"><code>hoverBorderWidth</code></a></td> <td><code>number</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>undefined</code></td></tr> <tr><td><a href="#general"><code>indexAxis</code></a></td> <td><code>string</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>'x'</code></td></tr> <tr><td><a href="#general"><code>label</code></a></td> <td><code>string</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>''</code></td></tr> <tr><td><a href="#general"><code>order</code></a></td> <td><code>number</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>0</code></td></tr> <tr><td><a href="#point-styling"><code>pointBackgroundColor</code></a></td> <td><code>Color</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>'rgba(0, 0, 0, 0.1)'</code></td></tr> <tr><td><a href="#point-styling"><code>pointBorderColor</code></a></td> <td><code>Color</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>'rgba(0, 0, 0, 0.1)'</code></td></tr> <tr><td><a href="#point-styling"><code>pointBorderWidth</code></a></td> <td><code>number</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>1</code></td></tr> <tr><td><a href="#point-styling"><code>pointHitRadius</code></a></td> <td><code>number</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>1</code></td></tr> <tr><td><a href="#interactions"><code>pointHoverBackgroundColor</code></a></td> <td><code>Color</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>undefined</code></td></tr> <tr><td><a href="#interactions"><code>pointHoverBorderColor</code></a></td> <td><code>Color</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>undefined</code></td></tr> <tr><td><a href="#interactions"><code>pointHoverBorderWidth</code></a></td> <td><code>number</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>1</code></td></tr> <tr><td><a href="#interactions"><code>pointHoverRadius</code></a></td> <td><code>number</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>4</code></td></tr> <tr><td><a href="#point-styling"><code>pointRadius</code></a></td> <td><code>number</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>3</code></td></tr> <tr><td><a href="#point-styling"><code>pointRotation</code></a></td> <td><code>number</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>0</code></td></tr> <tr><td><a href="#point-styling"><code>pointStyle</code></a></td> <td><a href="/docs/3.9.1/configuration/elements.html#types"><code>pointStyle</code></a></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>'circle'</code></td></tr> <tr><td><a href="#segment"><code>segment</code></a></td> <td><code>object</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>undefined</code></td></tr> <tr><td><a href="#line-styling"><code>showLine</code></a></td> <td><code>boolean</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>true</code></td></tr> <tr><td><a href="#line-styling"><code>spanGaps</code></a></td> <td><code>boolean</code>|<code>number</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>undefined</code></td></tr> <tr><td><a href="#general"><code>stack</code></a></td> <td><code>string</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>'line'</code></td></tr> <tr><td><a href="#stepped"><code>stepped</code></a></td> <td><code>boolean</code>|<code>string</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>false</code></td></tr> <tr><td><a href="#line-styling"><code>tension</code></a></td> <td><code>number</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>0</code></td></tr> <tr><td><a href="#general"><code>xAxisID</code></a></td> <td><code>string</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td>first x axis</td></tr> <tr><td><a href="#general"><code>yAxisID</code></a></td> <td><code>string</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td>first y axis</td></tr></tbody></table> <p>All these values, if <code>undefined</code>, fallback to the scopes described in <a href="../general/options">option resolution</a></p> <h3 id="general"><a href="#general" class="header-anchor">#</a> General</h3> <table><thead><tr><th>Name</th> <th>Description</th></tr></thead> <tbody><tr><td><code>clip</code></td> <td>How to clip relative to chartArea. Positive value allows overflow, negative value clips that many pixels inside chartArea. <code>0</code> = clip at chartArea. Clipping can also be configured per side: <code>clip: {left: 5, top: false, right: -2, bottom: 0}</code></td></tr> <tr><td><code>drawActiveElementsOnTop</code></td> <td>Draw the active points of a dataset over the other points of the dataset</td></tr> <tr><td><code>indexAxis</code></td> <td>The base axis of the dataset. <code>'x'</code> for horizontal lines and <code>'y'</code> for vertical lines.</td></tr> <tr><td><code>label</code></td> <td>The label for the dataset which appears in the legend and tooltips.</td></tr> <tr><td><code>order</code></td> <td>The drawing order of dataset. Also affects order for stacking, tooltip and legend. <a href="/docs/3.9.1/charts/mixed.html#drawing-order">more</a></td></tr> <tr><td><code>stack</code></td> <td>The ID of the group to which this dataset belongs to (when stacked, each group will be a separate stack). <a href="#stacked-area-chart">more</a></td></tr> <tr><td><code>xAxisID</code></td> <td>The ID of the x-axis to plot this dataset on.</td></tr> <tr><td><code>yAxisID</code></td> <td>The ID of the y-axis to plot this dataset on.</td></tr></tbody></table> <h3 id="point-styling"><a href="#point-styling" class="header-anchor">#</a> Point Styling</h3> <p>The style of each point can be controlled with the following properties:</p> <table><thead><tr><th>Name</th> <th>Description</th></tr></thead> <tbody><tr><td><code>pointBackgroundColor</code></td> <td>The fill color for points.</td></tr> <tr><td><code>pointBorderColor</code></td> <td>The border color for points.</td></tr> <tr><td><code>pointBorderWidth</code></td> <td>The width of the point border in pixels.</td></tr> <tr><td><code>pointHitRadius</code></td> <td>The pixel size of the non-displayed point that reacts to mouse events.</td></tr> <tr><td><code>pointRadius</code></td> <td>The radius of the point shape. If set to 0, the point is not rendered.</td></tr> <tr><td><code>pointRotation</code></td> <td>The rotation of the point in degrees.</td></tr> <tr><td><code>pointStyle</code></td> <td>Style of the point. <a href="/docs/3.9.1/configuration/elements.html#point-styles">more...</a></td></tr></tbody></table> <p>All these values, if <code>undefined</code>, fallback first to the dataset options then to the associated <a href="/docs/3.9.1/configuration/elements.html#point-configuration"><code>elements.point.*</code></a> options.</p> <h3 id="line-styling"><a href="#line-styling" class="header-anchor">#</a> Line Styling</h3> <p>The style of the line can be controlled with the following properties:</p> <table><thead><tr><th>Name</th> <th>Description</th></tr></thead> <tbody><tr><td><code>backgroundColor</code></td> <td>The line fill color.</td></tr> <tr><td><code>borderCapStyle</code></td> <td>Cap style of the line. See <a href="https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineCap" target="_blank" rel="noopener noreferrer">MDN<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a>.</td></tr> <tr><td><code>borderColor</code></td> <td>The line color.</td></tr> <tr><td><code>borderDash</code></td> <td>Length and spacing of dashes. See <a href="https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/setLineDash" target="_blank" rel="noopener noreferrer">MDN<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a>.</td></tr> <tr><td><code>borderDashOffset</code></td> <td>Offset for line dashes. See <a href="https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineDashOffset" target="_blank" rel="noopener noreferrer">MDN<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a>.</td></tr> <tr><td><code>borderJoinStyle</code></td> <td>Line joint style. See <a href="https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/lineJoin" target="_blank" rel="noopener noreferrer">MDN<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a>.</td></tr> <tr><td><code>borderWidth</code></td> <td>The line width (in pixels).</td></tr> <tr><td><code>fill</code></td> <td>How to fill the area under the line. See <a href="/docs/3.9.1/charts/area.html">area charts</a>.</td></tr> <tr><td><code>tension</code></td> <td>Bezier curve tension of the line. Set to 0 to draw straightlines. This option is ignored if monotone cubic interpolation is used.</td></tr> <tr><td><code>showLine</code></td> <td>If false, the line is not drawn for this dataset.</td></tr> <tr><td><code>spanGaps</code></td> <td>If true, lines will be drawn between points with no or null data. If false, points with <code>null</code> data will create a break in the line. Can also be a number specifying the maximum gap length to span. The unit of the value depends on the scale used.</td></tr></tbody></table> <p>If the value is <code>undefined</code>, the values fallback to the associated <a href="/docs/3.9.1/configuration/elements.html#line-configuration"><code>elements.line.*</code></a> options.</p> <h3 id="interactions"><a href="#interactions" class="header-anchor">#</a> Interactions</h3> <p>The interaction with each point can be controlled with the following properties:</p> <table><thead><tr><th>Name</th> <th>Description</th></tr></thead> <tbody><tr><td><code>pointHoverBackgroundColor</code></td> <td>Point background color when hovered.</td></tr> <tr><td><code>pointHoverBorderColor</code></td> <td>Point border color when hovered.</td></tr> <tr><td><code>pointHoverBorderWidth</code></td> <td>Border width of point when hovered.</td></tr> <tr><td><code>pointHoverRadius</code></td> <td>The radius of the point when hovered.</td></tr></tbody></table> <h3 id="cubicinterpolationmode"><a href="#cubicinterpolationmode" class="header-anchor">#</a> cubicInterpolationMode</h3> <p>The following interpolation modes are supported.</p> <ul><li><code>'default'</code></li> <li><code>'monotone'</code></li></ul> <p>The <code>'default'</code> algorithm uses a custom weighted cubic interpolation, which produces pleasant curves for all types of datasets.</p> <p>The <code>'monotone'</code> algorithm is more suited to <code>y = f(x)</code> datasets: it preserves monotonicity (or piecewise monotonicity) of the dataset being interpolated, and ensures local extremums (if any) stay at input data points.</p> <p>If left untouched (<code>undefined</code>), the global <code>options.elements.line.cubicInterpolationMode</code> property is used.</p> <h3 id="segment"><a href="#segment" class="header-anchor">#</a> Segment</h3> <p>Line segment styles can be overridden by scriptable options in the <code>segment</code> object. Currently all of the <code>border*</code> and <code>backgroundColor</code> options are supported. The segment styles are resolved for each section of the line between each point. <code>undefined</code> fallbacks to main line styles.</p> <div class="custom-block tip"><p class="custom-block-title">TIP</p> <p>To be able to style gaps, you need the <a href="#line-styling"><code>spanGaps</code></a> option enabled.</p></div> <p>Context for the scriptable segment contains the following properties:</p> <ul><li><code>type</code>: <code>'segment'</code></li> <li><code>p0</code>: first point element</li> <li><code>p1</code>: second point element</li> <li><code>p0DataIndex</code>: index of first point in the data array</li> <li><code>p1DataIndex</code>: index of second point in the data array</li> <li><code>datasetIndex</code>: dataset index</li></ul> <p><a href="/docs/3.9.1/samples/line/segments.html">Example usage</a></p> <h3 id="stepped"><a href="#stepped" class="header-anchor">#</a> Stepped</h3> <p>The following values are supported for <code>stepped</code>.</p> <ul><li><code>false</code>: No Step Interpolation (default)</li> <li><code>true</code>: Step-before Interpolation (eq. <code>'before'</code>)</li> <li><code>'before'</code>: Step-before Interpolation</li> <li><code>'after'</code>: Step-after Interpolation</li> <li><code>'middle'</code>: Step-middle Interpolation</li></ul> <p>If the <code>stepped</code> value is set to anything other than false, <code>tension</code> will be ignored.</p> <h2 id="default-options"><a href="#default-options" class="header-anchor">#</a> Default Options</h2> <p>It is common to want to apply a configuration setting to all created line charts. The global line chart settings are stored in <code>Chart.overrides.line</code>. Changing the global options only affects charts created after the change. Existing charts are not changed.</p> <p>For example, to configure all line charts with <code>spanGaps = true</code> you would do:</p> <div class="language-javascript extra-class"><pre class="language-javascript"><code>Chart<span class="token punctuation">.</span>overrides<span class="token punctuation">.</span>line<span class="token punctuation">.</span>spanGaps <span class="token operator">=</span> <span class="token boolean">true</span><span class="token punctuation">;</span>
</code></pre></div><h2 id="data-structure"><a href="#data-structure" class="header-anchor">#</a> Data Structure</h2> <p>All of the supported <a href="/docs/3.9.1/general/data-structures.html">data structures</a> can be used with line charts.</p> <h2 id="stacked-area-chart"><a href="#stacked-area-chart" class="header-anchor">#</a> Stacked Area Chart</h2> <p>Line charts can be configured into stacked area charts by changing the settings on the y-axis to enable stacking. Stacked area charts can be used to show how one data trend is made up of a number of smaller pieces.</p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token keyword">const</span> stackedLine <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Chart</span><span class="token punctuation">(</span>ctx<span class="token punctuation">,</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'line'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">data</span><span class="token operator">:</span> data<span class="token punctuation">,</span>
    <span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">scales</span><span class="token operator">:</span> <span class="token punctuation">{</span>
            <span class="token literal-property property">y</span><span class="token operator">:</span> <span class="token punctuation">{</span>
                <span class="token literal-property property">stacked</span><span class="token operator">:</span> <span class="token boolean">true</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></div><h2 id="vertical-line-chart"><a href="#vertical-line-chart" class="header-anchor">#</a> Vertical Line Chart</h2> <p>A vertical line chart is a variation on the horizontal line chart.
To achieve this you will have to set the <code>indexAxis</code> property in the options object to <code>'y'</code>.
The default for this property is <code>'x'</code> and thus will show horizontal lines.</p> <div class="chart-editor" data-v-365c20ab><div class="chart-view" data-v-365c20ab><canvas></canvas></div> <div class="chart-actions" data-v-2afd21f1 data-v-365c20ab></div> <div class="code-editor" data-v-66ca8197 data-v-365c20ab><div class="code-editor-header" data-v-66ca8197><div class="code-editor-tabs" data-v-66ca8197><button class="code-editor-tab active" data-v-66ca8197>
        config
      </button><button class="code-editor-tab" data-v-66ca8197>
        setup
      </button></div> <div class="code-editor-tools" data-v-66ca8197><!----> <a href="https://github.com/chartjs/Chart.js/blob/master/docs/charts/line.md" title="View on GitHub" target="_blank" class="code-editor-tool fab fa-github fa-lg" data-v-66ca8197></a></div></div> <div class="code-editor-views" data-v-66ca8197><div class="editor-textarea ps" data-v-05f11386 data-v-66ca8197><div class="editor-textarea-content" data-v-05f11386><div class="prism-editor-wrapper" data-v-05f11386><div class="prism-editor__container"><textarea spellCheck="false" autocapitalize="off" autocomplete="off" autocorrect="off" data-gramm="false" placeholder="" data-testid="textarea" class="prism-editor__textarea">const config = {
  type: 'line',
  data: data,
  options: {
    indexAxis: 'y',
    scales: {
      x: {
        beginAtZero: true
      }
    }
  }
};</textarea><pre data-testid="preview" class="prism-editor__editor"><span class="token keyword">const</span> config <span class="token operator">=</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'line'</span><span class="token punctuation">,</span>
  <span class="token literal-property property">data</span><span class="token operator">:</span> data<span class="token punctuation">,</span>
  <span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">indexAxis</span><span class="token operator">:</span> <span class="token string">'y'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">scales</span><span class="token operator">:</span> <span class="token punctuation">{</span>
      <span class="token literal-property property">x</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">beginAtZero</span><span class="token operator">:</span> <span class="token boolean">true</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span><br /></pre></div></div></div></div><div class="editor-textarea ps" style="display:none;" data-v-05f11386 data-v-66ca8197><div class="editor-textarea-content" data-v-05f11386><div class="prism-editor-wrapper" data-v-05f11386><div class="prism-editor__container"><textarea spellCheck="false" autocapitalize="off" autocomplete="off" autocorrect="off" data-gramm="false" placeholder="" data-testid="textarea" class="prism-editor__textarea">const labels = Utils.months({count: 7});
const data = {
  labels: labels,
  datasets: [{
    axis: 'y',
    label: 'My First Dataset',
    data: [65, 59, 80, 81, 56, 55, 40],
    fill: false,
    backgroundColor: [
      'rgba(255, 99, 132, 0.2)',
      'rgba(255, 159, 64, 0.2)',
      'rgba(255, 205, 86, 0.2)',
      'rgba(75, 192, 192, 0.2)',
      'rgba(54, 162, 235, 0.2)',
      'rgba(153, 102, 255, 0.2)',
      'rgba(201, 203, 207, 0.2)'
    ],
    borderColor: [
      'rgb(255, 99, 132)',
      'rgb(255, 159, 64)',
      'rgb(255, 205, 86)',
      'rgb(75, 192, 192)',
      'rgb(54, 162, 235)',
      'rgb(153, 102, 255)',
      'rgb(201, 203, 207)'
    ],
    borderWidth: 1
  }]
};</textarea><pre data-testid="preview" class="prism-editor__editor"><span class="token keyword">const</span> labels <span class="token operator">=</span> Utils<span class="token punctuation">.</span><span class="token function">months</span><span class="token punctuation">(</span><span class="token punctuation">{</span><span class="token literal-property property">count</span><span class="token operator">:</span> <span class="token number">7</span><span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword">const</span> data <span class="token operator">=</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">labels</span><span class="token operator">:</span> labels<span class="token punctuation">,</span>
  <span class="token literal-property property">datasets</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token punctuation">{</span>
    <span class="token literal-property property">axis</span><span class="token operator">:</span> <span class="token string">'y'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">label</span><span class="token operator">:</span> <span class="token string">'My First Dataset'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token number">65</span><span class="token punctuation">,</span> <span class="token number">59</span><span class="token punctuation">,</span> <span class="token number">80</span><span class="token punctuation">,</span> <span class="token number">81</span><span class="token punctuation">,</span> <span class="token number">56</span><span class="token punctuation">,</span> <span class="token number">55</span><span class="token punctuation">,</span> <span class="token number">40</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token literal-property property">fill</span><span class="token operator">:</span> <span class="token boolean">false</span><span class="token punctuation">,</span>
    <span class="token literal-property property">backgroundColor</span><span class="token operator">:</span> <span class="token punctuation">[</span>
      <span class="token string">'rgba(255, 99, 132, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(255, 159, 64, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(255, 205, 86, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(75, 192, 192, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(54, 162, 235, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(153, 102, 255, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(201, 203, 207, 0.2)'</span>
    <span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token literal-property property">borderColor</span><span class="token operator">:</span> <span class="token punctuation">[</span>
      <span class="token string">'rgb(255, 99, 132)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(255, 159, 64)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(255, 205, 86)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(75, 192, 192)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(54, 162, 235)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(153, 102, 255)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(201, 203, 207)'</span>
    <span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token literal-property property">borderWidth</span><span class="token operator">:</span> <span class="token number">1</span>
  <span class="token punctuation">}</span><span class="token punctuation">]</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span><br /></pre></div></div></div></div></div></div></div><h3 id="config-options"><a href="#config-options" class="header-anchor">#</a> Config Options</h3> <p>The configuration options for the vertical line chart are the same as for the <a href="#configuration-options">line chart</a>. However, any options specified on the x-axis in a line chart, are applied to the y-axis in a vertical line chart.</p> <h2 id="internal-data-format"><a href="#internal-data-format" class="header-anchor">#</a> Internal data format</h2> <p><code>{x, y}</code></p></div> <footer class="page-edit"><!----> <div class="last-updated"><span class="prefix">Last Updated:</span> <span class="time">8/3/2022, 12:46:38 PM</span></div></footer> <div class="page-nav"><p class="inner"><span class="prev">
      ←
      <a href="/docs/3.9.1/charts/doughnut.html" class="prev">
        Doughnut and Pie Charts
      </a></span> <span class="next"><a href="/docs/3.9.1/charts/mixed.html">
        Mixed Chart Types
      </a>
      →
    </span></p></div> </main></div><div class="global-ui"></div></div>
    <script src="/docs/3.9.1/assets/js/app.7e0dc8c8.js" defer></script><script src="/docs/3.9.1/assets/js/2.3e2307f3.js" defer></script><script src="/docs/3.9.1/assets/js/146.001afadf.js" defer></script><script src="/docs/3.9.1/assets/js/3.947b8d98.js" defer></script>
  </body>
</html>
