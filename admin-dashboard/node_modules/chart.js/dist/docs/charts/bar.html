<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Bar Chart | Chart.js</title>
    <meta name="generator" content="VuePress 1.8.3">
    <link rel="icon" href="/docs/3.9.1/favicon.ico">
    <meta name="description" content="Open source HTML5 Charts for your website">
    <link rel="preload" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css" as="style"><link rel="preload" href="/docs/3.9.1/assets/js/app.7e0dc8c8.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/2.3e2307f3.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/143.05e388ba.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/3.947b8d98.js" as="script"><link rel="prefetch" href="/docs/3.9.1/assets/js/10.edbecfa9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/100.f7a12d8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/101.15c56dbb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/102.33b746ea.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/103.850a4486.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/104.8b4f368b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/105.1a3d6c41.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/106.d8f8b4c1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/107.38cf9b4f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/108.a30da8c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/109.6d56b7d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/11.b98e8151.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/110.c44e533c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/111.ccb9a835.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/112.2d2a2890.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/113.5ac0921f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/114.90ef8814.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/115.12a72d27.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/116.6b780970.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/117.79604442.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/118.7752bc89.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/119.d8e76ef0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/12.433f0c7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/120.ed76595e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/121.cc3d56f2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/122.c8651871.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/123.f7d5f223.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/124.88ce07a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/125.a9572036.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/126.879447b3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/127.3b8478f3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/128.87d67ad7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/129.f6241700.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/13.b6743084.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/130.a9cb856a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/131.45e904c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/132.abd9e4c2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/133.d5aa1db6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/134.4bfa3b5a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/135.8c7630ad.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/136.f813e062.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/137.6cab2863.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/138.03ec5265.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/139.924812c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/14.422fec10.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/140.a7967a5d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/141.34a018bb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/142.c5bfcdd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/144.a98bdbcc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/145.826b73e0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/146.001afadf.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/147.fcadaef3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/148.604863a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/149.6509ed9f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/15.09a69fc3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/150.fd8575ca.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/151.67ecd4a3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/152.481a7bd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/153.dbf3f3aa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/154.2aa9c67d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/155.e3367ebc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/156.a5bb942d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/157.09caef4e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/158.1f82587f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/159.4cebd9d3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/16.0414f390.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/160.47df8215.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/161.fac79692.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/162.9f2f870f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/163.4f1604f9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/164.7f8d8643.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/165.fe2baa69.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/166.c82b3c76.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/167.3c3f2ff1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/168.8d2e8e83.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/169.b10c84bd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/17.8dfcb176.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/170.b9db8312.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/171.2480fc36.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/172.81c5206a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/173.477dcbae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/174.05398ca3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/175.84ab69d0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/176.f48183c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/177.d0d35403.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/178.55d433fd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/179.9442c138.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/18.c331029e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/180.5c15d5d4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/181.911f367b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/182.72f663e8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/183.22b2258c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/184.15b21065.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/185.894ea40b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/186.f6394459.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/187.7bd9b3fe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/188.3baa9bcd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/189.18e63b11.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/19.13bdd658.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/190.b7ffb54a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/191.13061aba.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/192.1bf9bd61.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/193.8fa44455.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/194.49c9a3c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/195.eb1e8802.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/196.9b925823.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/197.adca6c8c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/198.2ae0961a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/199.cd9dca80.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/20.5d11c294.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/200.2eb9437f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/201.6dba10c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/202.9c5057c6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/203.98a3c2b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/204.50d5cf2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/205.e56e820b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/206.6ce5c41e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/207.8aa83d91.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/208.296ee160.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/209.13279349.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/21.7b54d7d8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/210.63e30420.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/211.0b325f23.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/212.be671e2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/213.81d2e607.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/214.ba403b5c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/215.2efcec5f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/216.e01d3100.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/217.906c8d54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/218.94e33827.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/219.21c5e01f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/22.c2daedd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/220.3ba160e4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/221.5c17138c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/222.aea004ce.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/223.cbb7b883.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/224.b130b37f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/225.32f90319.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/226.8c102c21.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/227.b3d60339.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/228.72f0ad18.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/229.3daa3b7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/23.2c668e20.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/230.7e5a85b1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/231.b83d12f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/232.e902f42d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/233.b4f254c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/234.d975df48.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/235.71a7be7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/236.05d87a5e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/237.d518e28b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/238.eb60e397.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/239.81fecfed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/24.af200d5b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/240.c0869bc2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/241.59dc896f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/242.6d423d39.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/243.a355eaa8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/244.ad729cf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/245.c3cd6bbe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/246.d0708528.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/247.13d65d78.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/248.1d222543.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/249.2d7a9bf1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/25.4f97f63f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/250.09ef38e2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/251.0cb90e8a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/252.2ff0def4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/253.61c7c505.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/254.20137eeb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/255.49c937e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/256.8d709dae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/257.b3ebdce7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/258.792f66d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/259.98c809e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/26.d1bb645c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/260.54ff10d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/261.928afea4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/27.2d5c4a6b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/28.72dee0f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/29.8820be26.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/30.343676b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/31.cff089f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/32.56d8546e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/33.6b642a06.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/34.360a9ea9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/35.205d7fac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/36.f2765bae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/37.6c33435e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/38.a778a6a2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/39.7dda160f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/4.ee88d25a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/40.bd778eac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/41.b79220f6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/42.c3157beb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/43.097368d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/44.2515f16e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/45.d5b4b7c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/46.bbab8d6e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/47.79aa575e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/48.45785af9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/49.0569a6eb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/5.00f814ac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/50.14bd3ba2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/51.e0968711.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/52.fe65ddf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/53.faa1ff3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/54.fcfed2c5.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/55.7db1d28a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/56.8b0e82b7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/57.9de2d983.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/58.b0f8ad0c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/59.f1ff4935.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/6.2bc86161.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/60.ac08de9a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/61.ea4fad75.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/62.5c85853b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/63.5ac99656.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/64.c6838e95.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/65.4c1b089e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/66.379a6b45.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/67.15703e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/68.ffdefd7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/69.bdeb7b9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/7.74f2ce90.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/70.3dadb5ed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/71.2c97fe38.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/72.dc778e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/73.d88fcb57.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/74.bba2165e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/75.15562a81.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/76.21f5a94e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/77.0725268e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/78.d6f610d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/79.60d67faa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/8.8928eb8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/80.99d71ee0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/81.f1500469.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/82.69f363a6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/83.f1fbcb2c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/84.b76e3156.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/85.efc4bd54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/86.85011b24.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/87.6a88d571.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/88.0f45cfe0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/89.e67ddb59.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/9.63ebb16b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/90.96ebfa9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/91.cdde4d3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/92.dc3f06d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/93.aaa04d52.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/94.2af5650c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/95.1d44ec16.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/96.9545127d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/97.8b18f487.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/98.cb259cd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/99.6251650a.js">
    <link rel="stylesheet" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css">
  </head>
  <body>
    <div id="app" data-server-rendered="true"><div class="theme-container"><header class="navbar"><div class="sidebar-button"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" viewBox="0 0 448 512" class="icon"><path fill="currentColor" d="M436 124H12c-6.627 0-12-5.373-12-12V80c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12z"></path></svg></div> <a href="/docs/3.9.1/" class="home-link router-link-active"><img src="/docs/3.9.1/favicon.ico" alt="Chart.js" class="logo"> <span class="site-name can-hide">Chart.js</span></a> <div class="links"><div class="search-box"><input aria-label="Search" autocomplete="off" spellcheck="false" value=""> <!----></div> <nav class="nav-links can-hide"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" class="nav-link">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav></div></header> <div class="sidebar-mask"></div> <aside class="sidebar"><nav class="nav-links"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" class="nav-link">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav>  <ul class="sidebar-links"><li><a href="/docs/3.9.1/" aria-current="page" class="sidebar-link">Chart.js</a></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Getting Started</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>General</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Configuration</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading open"><span>Chart Types</span> <span class="arrow down"></span></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/docs/3.9.1/charts/area.html" class="sidebar-link">Area Chart</a></li><li><a href="/docs/3.9.1/charts/bar.html" aria-current="page" class="active sidebar-link">Bar Chart</a></li><li><a href="/docs/3.9.1/charts/bubble.html" class="sidebar-link">Bubble Chart</a></li><li><a href="/docs/3.9.1/charts/doughnut.html" class="sidebar-link">Doughnut and Pie Charts</a></li><li><a href="/docs/3.9.1/charts/line.html" class="sidebar-link">Line Chart</a></li><li><a href="/docs/3.9.1/charts/mixed.html" class="sidebar-link">Mixed Chart Types</a></li><li><a href="/docs/3.9.1/charts/polar.html" class="sidebar-link">Polar Area Chart</a></li><li><a href="/docs/3.9.1/charts/radar.html" class="sidebar-link">Radar Chart</a></li><li><a href="/docs/3.9.1/charts/scatter.html" class="sidebar-link">Scatter Chart</a></li></ul></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Axes</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Developers</span> <span class="arrow right"></span></p> <!----></section></li></ul> </aside> <main class="page"> <div class="theme-default-content content__default"><h1 id="bar-chart"><a href="#bar-chart" class="header-anchor">#</a> Bar Chart</h1> <p>A bar chart provides a way of showing data values represented as vertical bars. It is sometimes used to show trend data, and the comparison of multiple data sets side by side.</p> <div class="chart-editor" data-v-365c20ab><div class="chart-view" data-v-365c20ab><canvas></canvas></div> <div class="chart-actions" data-v-2afd21f1 data-v-365c20ab></div> <div class="code-editor" data-v-66ca8197 data-v-365c20ab><div class="code-editor-header" data-v-66ca8197><div class="code-editor-tabs" data-v-66ca8197><button class="code-editor-tab active" data-v-66ca8197>
        config
      </button><button class="code-editor-tab" data-v-66ca8197>
        setup
      </button></div> <div class="code-editor-tools" data-v-66ca8197><!----> <a href="https://github.com/chartjs/Chart.js/blob/master/docs/charts/bar.md" title="View on GitHub" target="_blank" class="code-editor-tool fab fa-github fa-lg" data-v-66ca8197></a></div></div> <div class="code-editor-views" data-v-66ca8197><div class="editor-textarea ps" data-v-05f11386 data-v-66ca8197><div class="editor-textarea-content" data-v-05f11386><div class="prism-editor-wrapper" data-v-05f11386><div class="prism-editor__container"><textarea spellCheck="false" autocapitalize="off" autocomplete="off" autocorrect="off" data-gramm="false" placeholder="" data-testid="textarea" class="prism-editor__textarea">const config = {
  type: 'bar',
  data: data,
  options: {
    scales: {
      y: {
        beginAtZero: true
      }
    }
  },
};</textarea><pre data-testid="preview" class="prism-editor__editor"><span class="token keyword">const</span> config <span class="token operator">=</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'bar'</span><span class="token punctuation">,</span>
  <span class="token literal-property property">data</span><span class="token operator">:</span> data<span class="token punctuation">,</span>
  <span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">scales</span><span class="token operator">:</span> <span class="token punctuation">{</span>
      <span class="token literal-property property">y</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">beginAtZero</span><span class="token operator">:</span> <span class="token boolean">true</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span><br /></pre></div></div></div></div><div class="editor-textarea ps" style="display:none;" data-v-05f11386 data-v-66ca8197><div class="editor-textarea-content" data-v-05f11386><div class="prism-editor-wrapper" data-v-05f11386><div class="prism-editor__container"><textarea spellCheck="false" autocapitalize="off" autocomplete="off" autocorrect="off" data-gramm="false" placeholder="" data-testid="textarea" class="prism-editor__textarea">const labels = Utils.months({count: 7});
const data = {
  labels: labels,
  datasets: [{
    label: 'My First Dataset',
    data: [65, 59, 80, 81, 56, 55, 40],
    backgroundColor: [
      'rgba(255, 99, 132, 0.2)',
      'rgba(255, 159, 64, 0.2)',
      'rgba(255, 205, 86, 0.2)',
      'rgba(75, 192, 192, 0.2)',
      'rgba(54, 162, 235, 0.2)',
      'rgba(153, 102, 255, 0.2)',
      'rgba(201, 203, 207, 0.2)'
    ],
    borderColor: [
      'rgb(255, 99, 132)',
      'rgb(255, 159, 64)',
      'rgb(255, 205, 86)',
      'rgb(75, 192, 192)',
      'rgb(54, 162, 235)',
      'rgb(153, 102, 255)',
      'rgb(201, 203, 207)'
    ],
    borderWidth: 1
  }]
};</textarea><pre data-testid="preview" class="prism-editor__editor"><span class="token keyword">const</span> labels <span class="token operator">=</span> Utils<span class="token punctuation">.</span><span class="token function">months</span><span class="token punctuation">(</span><span class="token punctuation">{</span><span class="token literal-property property">count</span><span class="token operator">:</span> <span class="token number">7</span><span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword">const</span> data <span class="token operator">=</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">labels</span><span class="token operator">:</span> labels<span class="token punctuation">,</span>
  <span class="token literal-property property">datasets</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token punctuation">{</span>
    <span class="token literal-property property">label</span><span class="token operator">:</span> <span class="token string">'My First Dataset'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token number">65</span><span class="token punctuation">,</span> <span class="token number">59</span><span class="token punctuation">,</span> <span class="token number">80</span><span class="token punctuation">,</span> <span class="token number">81</span><span class="token punctuation">,</span> <span class="token number">56</span><span class="token punctuation">,</span> <span class="token number">55</span><span class="token punctuation">,</span> <span class="token number">40</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token literal-property property">backgroundColor</span><span class="token operator">:</span> <span class="token punctuation">[</span>
      <span class="token string">'rgba(255, 99, 132, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(255, 159, 64, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(255, 205, 86, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(75, 192, 192, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(54, 162, 235, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(153, 102, 255, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(201, 203, 207, 0.2)'</span>
    <span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token literal-property property">borderColor</span><span class="token operator">:</span> <span class="token punctuation">[</span>
      <span class="token string">'rgb(255, 99, 132)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(255, 159, 64)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(255, 205, 86)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(75, 192, 192)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(54, 162, 235)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(153, 102, 255)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(201, 203, 207)'</span>
    <span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token literal-property property">borderWidth</span><span class="token operator">:</span> <span class="token number">1</span>
  <span class="token punctuation">}</span><span class="token punctuation">]</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span><br /></pre></div></div></div></div></div></div></div><h2 id="dataset-properties"><a href="#dataset-properties" class="header-anchor">#</a> Dataset Properties</h2> <p>Namespaces:</p> <ul><li><code>data.datasets[index]</code> - options for this dataset only</li> <li><code>options.datasets.bar</code> - options for all bar datasets</li> <li><code>options.elements.bar</code> - options for all <a href="/docs/3.9.1/configuration/elements.html#bar-configuration">bar elements</a></li> <li><code>options</code> - options for the whole chart</li></ul> <p>The bar chart allows a number of properties to be specified for each dataset.
These are used to set display properties for a specific dataset. For example,
the color of the bars is generally set this way.
Only the <code>data</code> option needs to be specified in the dataset namespace.</p> <table><thead><tr><th>Name</th> <th>Type</th> <th style="text-align:center;"><a href="/docs/3.9.1/general/options.html#scriptable-options">Scriptable</a></th> <th style="text-align:center;"><a href="/docs/3.9.1/general/options.html#indexable-options">Indexable</a></th> <th>Default</th></tr></thead> <tbody><tr><td><a href="#styling"><code>backgroundColor</code></a></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>'rgba(0, 0, 0, 0.1)'</code></td></tr> <tr><td><a href="#general"><code>base</code></a></td> <td><code>number</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td></td></tr> <tr><td><a href="#barpercentage"><code>barPercentage</code></a></td> <td><code>number</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>0.9</code></td></tr> <tr><td><a href="#barthickness"><code>barThickness</code></a></td> <td><code>number</code>|<code>string</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td></td></tr> <tr><td><a href="#styling"><code>borderColor</code></a></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>'rgba(0, 0, 0, 0.1)'</code></td></tr> <tr><td><a href="#borderskipped"><code>borderSkipped</code></a></td> <td><code>string</code>|<code>boolean</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>'start'</code></td></tr> <tr><td><a href="#borderwidth"><code>borderWidth</code></a></td> <td><code>number</code>|<code>object</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>0</code></td></tr> <tr><td><a href="#borderradius"><code>borderRadius</code></a></td> <td><code>number</code>|<code>object</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>0</code></td></tr> <tr><td><a href="#categorypercentage"><code>categoryPercentage</code></a></td> <td><code>number</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>0.8</code></td></tr> <tr><td><a href="#general"><code>clip</code></a></td> <td><code>number</code>|<code>object</code>|<code>false</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td></td></tr> <tr><td><a href="#data-structure"><code>data</code></a></td> <td><code>object</code>|<code>object[]</code>| <code>number[]</code>|<code>string[]</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><strong>required</strong></td></tr> <tr><td><a href="#general"><code>grouped</code></a></td> <td><code>boolean</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>true</code></td></tr> <tr><td><a href="#interactions"><code>hoverBackgroundColor</code></a></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td></td></tr> <tr><td><a href="#interactions"><code>hoverBorderColor</code></a></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td></td></tr> <tr><td><a href="#interactions"><code>hoverBorderWidth</code></a></td> <td><code>number</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>1</code></td></tr> <tr><td><a href="#interactions"><code>hoverBorderRadius</code></a></td> <td><code>number</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>0</code></td></tr> <tr><td><a href="#general"><code>indexAxis</code></a></td> <td><code>string</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>'x'</code></td></tr> <tr><td><a href="#inflateamount"><code>inflateAmount</code></a></td> <td><code>number</code>|<code>'auto'</code></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">Yes</td> <td><code>'auto'</code></td></tr> <tr><td><a href="#maxbarthickness"><code>maxBarThickness</code></a></td> <td><code>number</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td></td></tr> <tr><td><a href="#styling"><code>minBarLength</code></a></td> <td><code>number</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td></td></tr> <tr><td><a href="#general"><code>label</code></a></td> <td><code>string</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>''</code></td></tr> <tr><td><a href="#general"><code>order</code></a></td> <td><code>number</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>0</code></td></tr> <tr><td><a href="/docs/3.9.1/configuration/elements.html#point-styles"><code>pointStyle</code></a></td> <td><a href="/docs/3.9.1/configuration/elements.html#types"><code>pointStyle</code></a></td> <td style="text-align:center;">Yes</td> <td style="text-align:center;">-</td> <td><code>'circle'</code></td></tr> <tr><td><a href="#general"><code>skipNull</code></a></td> <td><code>boolean</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td></td></tr> <tr><td><a href="#general"><code>stack</code></a></td> <td><code>string</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td><code>'bar'</code></td></tr> <tr><td><a href="#general"><code>xAxisID</code></a></td> <td><code>string</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td>first x axis</td></tr> <tr><td><a href="#general"><code>yAxisID</code></a></td> <td><code>string</code></td> <td style="text-align:center;">-</td> <td style="text-align:center;">-</td> <td>first y axis</td></tr></tbody></table> <p>All these values, if <code>undefined</code>, fallback to the scopes described in <a href="../general/options">option resolution</a></p> <h3 id="example-dataset-configuration"><a href="#example-dataset-configuration" class="header-anchor">#</a> Example dataset configuration</h3> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">datasets</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token punctuation">{</span>
        <span class="token literal-property property">barPercentage</span><span class="token operator">:</span> <span class="token number">0.5</span><span class="token punctuation">,</span>
        <span class="token literal-property property">barThickness</span><span class="token operator">:</span> <span class="token number">6</span><span class="token punctuation">,</span>
        <span class="token literal-property property">maxBarThickness</span><span class="token operator">:</span> <span class="token number">8</span><span class="token punctuation">,</span>
        <span class="token literal-property property">minBarLength</span><span class="token operator">:</span> <span class="token number">2</span><span class="token punctuation">,</span>
        <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token number">10</span><span class="token punctuation">,</span> <span class="token number">20</span><span class="token punctuation">,</span> <span class="token number">30</span><span class="token punctuation">,</span> <span class="token number">40</span><span class="token punctuation">,</span> <span class="token number">50</span><span class="token punctuation">,</span> <span class="token number">60</span><span class="token punctuation">,</span> <span class="token number">70</span><span class="token punctuation">]</span>
    <span class="token punctuation">}</span><span class="token punctuation">]</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre></div><h3 id="general"><a href="#general" class="header-anchor">#</a> General</h3> <table><thead><tr><th>Name</th> <th>Description</th></tr></thead> <tbody><tr><td><code>base</code></td> <td>Base value for the bar in data units along the value axis. If not set, defaults to the value axis base value.</td></tr> <tr><td><code>clip</code></td> <td>How to clip relative to chartArea. Positive value allows overflow, negative value clips that many pixels inside chartArea. <code>0</code> = clip at chartArea. Clipping can also be configured per side: <code>clip: {left: 5, top: false, right: -2, bottom: 0}</code></td></tr> <tr><td><code>grouped</code></td> <td>Should the bars be grouped on index axis. When <code>true</code>, all the datasets at same index value will be placed next to each other centering on that index value. When <code>false</code>, each bar is placed on its actual index-axis value.</td></tr> <tr><td><code>indexAxis</code></td> <td>The base axis of the dataset. <code>'x'</code> for vertical bars and <code>'y'</code> for horizontal bars.</td></tr> <tr><td><code>label</code></td> <td>The label for the dataset which appears in the legend and tooltips.</td></tr> <tr><td><code>order</code></td> <td>The drawing order of dataset. Also affects order for stacking, tooltip and legend. <a href="/docs/3.9.1/charts/mixed.html#drawing-order">more</a></td></tr> <tr><td><code>skipNull</code></td> <td>If <code>true</code>, null or undefined values will not be used for spacing calculations when determining bar size.</td></tr> <tr><td><code>stack</code></td> <td>The ID of the group to which this dataset belongs to (when stacked, each group will be a separate stack). <a href="#stacked-bar-chart">more</a></td></tr> <tr><td><code>xAxisID</code></td> <td>The ID of the x-axis to plot this dataset on.</td></tr> <tr><td><code>yAxisID</code></td> <td>The ID of the y-axis to plot this dataset on.</td></tr></tbody></table> <h3 id="styling"><a href="#styling" class="header-anchor">#</a> Styling</h3> <p>The style of each bar can be controlled with the following properties:</p> <table><thead><tr><th>Name</th> <th>Description</th></tr></thead> <tbody><tr><td><code>backgroundColor</code></td> <td>The bar background color.</td></tr> <tr><td><code>borderColor</code></td> <td>The bar border color.</td></tr> <tr><td><a href="#borderskipped"><code>borderSkipped</code></a></td> <td>The edge to skip when drawing bar.</td></tr> <tr><td><a href="#borderwidth"><code>borderWidth</code></a></td> <td>The bar border width (in pixels).</td></tr> <tr><td><a href="#borderradius"><code>borderRadius</code></a></td> <td>The bar border radius (in pixels).</td></tr> <tr><td><code>minBarLength</code></td> <td>Set this to ensure that bars have a minimum length in pixels.</td></tr> <tr><td><code>pointStyle</code></td> <td>Style of the point for legend. <a href="/docs/3.9.1/configuration/elements.html#point-styles">more...</a></td></tr></tbody></table> <p>All these values, if <code>undefined</code>, fallback to the associated <a href="/docs/3.9.1/configuration/elements.html#bar-configuration"><code>elements.bar.*</code></a> options.</p> <h4 id="borderskipped"><a href="#borderskipped" class="header-anchor">#</a> borderSkipped</h4> <p>This setting is used to avoid drawing the bar stroke at the base of the fill, or disable the border radius.
In general, this does not need to be changed except when creating chart types
that derive from a bar chart.</p> <div class="custom-block tip"><p class="custom-block-title">Note</p> <p>For negative bars in a vertical chart, <code>top</code> and <code>bottom</code> are flipped. Same goes for <code>left</code> and <code>right</code> in a horizontal chart.</p></div> <p>Options are:</p> <ul><li><code>'start'</code></li> <li><code>'end'</code></li> <li><code>'middle'</code> (only valid on stacked bars: the borders between bars are skipped)</li> <li><code>'bottom'</code></li> <li><code>'left'</code></li> <li><code>'top'</code></li> <li><code>'right'</code></li> <li><code>false</code> (don't skip any borders)</li> <li><code>true</code> (skip all borders)</li></ul> <h4 id="borderwidth"><a href="#borderwidth" class="header-anchor">#</a> borderWidth</h4> <p>If this value is a number, it is applied to all sides of the rectangle (left, top, right, bottom), except <a href="#borderskipped"><code>borderSkipped</code></a>. If this value is an object, the <code>left</code> property defines the left border width. Similarly, the <code>right</code>, <code>top</code>, and <code>bottom</code> properties can also be specified. Omitted borders and <a href="#borderskipped"><code>borderSkipped</code></a> are skipped.</p> <h4 id="borderradius"><a href="#borderradius" class="header-anchor">#</a> borderRadius</h4> <p>If this value is a number, it is applied to all corners of the rectangle (topLeft, topRight, bottomLeft, bottomRight), except corners touching the <a href="#borderskipped"><code>borderSkipped</code></a>. If this value is an object, the <code>topLeft</code> property defines the top-left corners border radius. Similarly, the <code>topRight</code>, <code>bottomLeft</code>, and <code>bottomRight</code> properties can also be specified. Omitted corners and those touching the <a href="#borderskipped"><code>borderSkipped</code></a> are skipped. For example if the <code>top</code> border is skipped, the border radius for the corners <code>topLeft</code> and <code>topRight</code> will be skipped as well.</p> <div class="custom-block tip"><p class="custom-block-title">Stacked Charts</p> <p>When the border radius is supplied as a number and the chart is stacked, the radius will only be applied to the bars that are at the edges of the stack or where the bar is floating. The object syntax can be used to override this behavior.</p></div> <h4 id="inflateamount"><a href="#inflateamount" class="header-anchor">#</a> inflateAmount</h4> <p>This option can be used to inflate the rects that are used to draw the bars. This can be used to hide artifacts between bars when <code>barPercentage</code>(#barpercentage) * <code>categoryPercentage</code>(#categorypercentage) is 1. The default value <code>'auto'</code> should work in most cases.</p> <h3 id="interactions"><a href="#interactions" class="header-anchor">#</a> Interactions</h3> <p>The interaction with each bar can be controlled with the following properties:</p> <table><thead><tr><th>Name</th> <th>Description</th></tr></thead> <tbody><tr><td><code>hoverBackgroundColor</code></td> <td>The bar background color when hovered.</td></tr> <tr><td><code>hoverBorderColor</code></td> <td>The bar border color when hovered.</td></tr> <tr><td><code>hoverBorderWidth</code></td> <td>The bar border width when hovered (in pixels).</td></tr> <tr><td><code>hoverBorderRadius</code></td> <td>The bar border radius when hovered (in pixels).</td></tr></tbody></table> <p>All these values, if <code>undefined</code>, fallback to the associated <a href="/docs/3.9.1/configuration/elements.html#bar-configuration"><code>elements.bar.*</code></a> options.</p> <h3 id="barpercentage"><a href="#barpercentage" class="header-anchor">#</a> barPercentage</h3> <p>Percent (0-1) of the available width each bar should be within the category width. 1.0 will take the whole category width and put the bars right next to each other. <a href="#barpercentage-vs-categorypercentage">more...</a></p> <h3 id="categorypercentage"><a href="#categorypercentage" class="header-anchor">#</a> categoryPercentage</h3> <p>Percent (0-1) of the available width each category should be within the sample width. <a href="#barpercentage-vs-categorypercentage">more...</a></p> <h3 id="barthickness"><a href="#barthickness" class="header-anchor">#</a> barThickness</h3> <p>If this value is a number, it is applied to the width of each bar, in pixels. When this is enforced, <code>barPercentage</code> and <code>categoryPercentage</code> are ignored.</p> <p>If set to <code>'flex'</code>, the base sample widths are calculated automatically based on the previous and following samples so that they take the full available widths without overlap. Then, bars are sized using <code>barPercentage</code> and <code>categoryPercentage</code>. There is no gap when the percentage options are 1. This mode generates bars with different widths when data are not evenly spaced.</p> <p>If not set (default), the base sample widths are calculated using the smallest interval that prevents bar overlapping, and bars are sized using <code>barPercentage</code> and <code>categoryPercentage</code>. This mode always generates bars equally sized.</p> <h3 id="maxbarthickness"><a href="#maxbarthickness" class="header-anchor">#</a> maxBarThickness</h3> <p>Set this to ensure that bars are not sized thicker than this.</p> <h2 id="scale-configuration"><a href="#scale-configuration" class="header-anchor">#</a> Scale Configuration</h2> <p>The bar chart sets unique default values for the following configuration from the associated <code>scale</code> options:</p> <table><thead><tr><th>Name</th> <th>Type</th> <th>Default</th> <th>Description</th></tr></thead> <tbody><tr><td><code>offset</code></td> <td><code>boolean</code></td> <td><code>true</code></td> <td>If true, extra space is added to both edges and the axis is scaled to fit into the chart area.</td></tr> <tr><td><code>grid.offset</code></td> <td><code>boolean</code></td> <td><code>true</code></td> <td>If true, the bars for a particular data point fall between the grid lines. The grid line will move to the left by one half of the tick interval. If false, the grid line will go right down the middle of the bars. <a href="#offsetgridlines">more...</a></td></tr></tbody></table> <h3 id="example-scale-configuration"><a href="#example-scale-configuration" class="header-anchor">#</a> Example scale configuration</h3> <div class="language-javascript extra-class"><pre class="language-javascript"><code>options <span class="token operator">=</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">scales</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">x</span><span class="token operator">:</span> <span class="token punctuation">{</span>
            <span class="token literal-property property">grid</span><span class="token operator">:</span> <span class="token punctuation">{</span>
              <span class="token literal-property property">offset</span><span class="token operator">:</span> <span class="token boolean">true</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
</code></pre></div><h3 id="offset-grid-lines"><a href="#offset-grid-lines" class="header-anchor">#</a> Offset Grid Lines</h3> <p>If true, the bars for a particular data point fall between the grid lines. The grid line will move to the left by one half of the tick interval, which is the space between the grid lines. If false, the grid line will go right down the middle of the bars. This is set to true for a category scale in a bar chart while false for other scales or chart types by default.</p> <h2 id="default-options"><a href="#default-options" class="header-anchor">#</a> Default Options</h2> <p>It is common to want to apply a configuration setting to all created bar charts. The global bar chart settings are stored in <code>Chart.overrides.bar</code>. Changing the global options only affects charts created after the change. Existing charts are not changed.</p> <h2 id="barpercentage-vs-categorypercentage"><a href="#barpercentage-vs-categorypercentage" class="header-anchor">#</a> barPercentage vs categoryPercentage</h2> <p>The following shows the relationship between the bar percentage option and the category percentage option.</p> <div class="language- extra-class"><pre class="language-text"><code>// categoryPercentage: 1.0
// barPercentage: 1.0
Bar:        | 1.0 | 1.0 |
Category:   |    1.0    |
Sample:     |===========|
// categoryPercentage: 1.0
// barPercentage: 0.5
Bar:          |.5|  |.5|
Category:  |      1.0     |
Sample:    |==============|
// categoryPercentage: 0.5
// barPercentage: 1.0
Bar:             |1.0||1.0|
Category:        |   .5   |
Sample:     |==================|
</code></pre></div><h2 id="data-structure"><a href="#data-structure" class="header-anchor">#</a> Data Structure</h2> <p>All of the supported <a href="/docs/3.9.1/general/data-structures.html">data structures</a> can be used with bar charts.</p> <h2 id="stacked-bar-chart"><a href="#stacked-bar-chart" class="header-anchor">#</a> Stacked Bar Chart</h2> <p>Bar charts can be configured into stacked bar charts by changing the settings on the X and Y axes to enable stacking. Stacked bar charts can be used to show how one data series is made up of a number of smaller pieces.</p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token keyword">const</span> stackedBar <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Chart</span><span class="token punctuation">(</span>ctx<span class="token punctuation">,</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'bar'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">data</span><span class="token operator">:</span> data<span class="token punctuation">,</span>
    <span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">scales</span><span class="token operator">:</span> <span class="token punctuation">{</span>
            <span class="token literal-property property">x</span><span class="token operator">:</span> <span class="token punctuation">{</span>
                <span class="token literal-property property">stacked</span><span class="token operator">:</span> <span class="token boolean">true</span>
            <span class="token punctuation">}</span><span class="token punctuation">,</span>
            <span class="token literal-property property">y</span><span class="token operator">:</span> <span class="token punctuation">{</span>
                <span class="token literal-property property">stacked</span><span class="token operator">:</span> <span class="token boolean">true</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></div><h2 id="horizontal-bar-chart"><a href="#horizontal-bar-chart" class="header-anchor">#</a> Horizontal Bar Chart</h2> <p>A horizontal bar chart is a variation on a vertical bar chart. It is sometimes used to show trend data, and the comparison of multiple data sets side by side.
To achieve this you will have to set the <code>indexAxis</code> property in the options object to <code>'y'</code>.
The default for this property is <code>'x'</code> and thus will show vertical bars.</p> <div class="chart-editor" data-v-365c20ab><div class="chart-view" data-v-365c20ab><canvas></canvas></div> <div class="chart-actions" data-v-2afd21f1 data-v-365c20ab></div> <div class="code-editor" data-v-66ca8197 data-v-365c20ab><div class="code-editor-header" data-v-66ca8197><div class="code-editor-tabs" data-v-66ca8197><button class="code-editor-tab active" data-v-66ca8197>
        config
      </button><button class="code-editor-tab" data-v-66ca8197>
        setup
      </button></div> <div class="code-editor-tools" data-v-66ca8197><!----> <a href="https://github.com/chartjs/Chart.js/blob/master/docs/charts/bar.md" title="View on GitHub" target="_blank" class="code-editor-tool fab fa-github fa-lg" data-v-66ca8197></a></div></div> <div class="code-editor-views" data-v-66ca8197><div class="editor-textarea ps" data-v-05f11386 data-v-66ca8197><div class="editor-textarea-content" data-v-05f11386><div class="prism-editor-wrapper" data-v-05f11386><div class="prism-editor__container"><textarea spellCheck="false" autocapitalize="off" autocomplete="off" autocorrect="off" data-gramm="false" placeholder="" data-testid="textarea" class="prism-editor__textarea">const config = {
  type: 'bar',
  data,
  options: {
    indexAxis: 'y',
  }
};</textarea><pre data-testid="preview" class="prism-editor__editor"><span class="token keyword">const</span> config <span class="token operator">=</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'bar'</span><span class="token punctuation">,</span>
  data<span class="token punctuation">,</span>
  <span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">indexAxis</span><span class="token operator">:</span> <span class="token string">'y'</span><span class="token punctuation">,</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span><br /></pre></div></div></div></div><div class="editor-textarea ps" style="display:none;" data-v-05f11386 data-v-66ca8197><div class="editor-textarea-content" data-v-05f11386><div class="prism-editor-wrapper" data-v-05f11386><div class="prism-editor__container"><textarea spellCheck="false" autocapitalize="off" autocomplete="off" autocorrect="off" data-gramm="false" placeholder="" data-testid="textarea" class="prism-editor__textarea">const labels = Utils.months({count: 7});
const data = {
  labels: labels,
  datasets: [{
    axis: 'y',
    label: 'My First Dataset',
    data: [65, 59, 80, 81, 56, 55, 40],
    fill: false,
    backgroundColor: [
      'rgba(255, 99, 132, 0.2)',
      'rgba(255, 159, 64, 0.2)',
      'rgba(255, 205, 86, 0.2)',
      'rgba(75, 192, 192, 0.2)',
      'rgba(54, 162, 235, 0.2)',
      'rgba(153, 102, 255, 0.2)',
      'rgba(201, 203, 207, 0.2)'
    ],
    borderColor: [
      'rgb(255, 99, 132)',
      'rgb(255, 159, 64)',
      'rgb(255, 205, 86)',
      'rgb(75, 192, 192)',
      'rgb(54, 162, 235)',
      'rgb(153, 102, 255)',
      'rgb(201, 203, 207)'
    ],
    borderWidth: 1
  }]
};</textarea><pre data-testid="preview" class="prism-editor__editor"><span class="token keyword">const</span> labels <span class="token operator">=</span> Utils<span class="token punctuation">.</span><span class="token function">months</span><span class="token punctuation">(</span><span class="token punctuation">{</span><span class="token literal-property property">count</span><span class="token operator">:</span> <span class="token number">7</span><span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword">const</span> data <span class="token operator">=</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">labels</span><span class="token operator">:</span> labels<span class="token punctuation">,</span>
  <span class="token literal-property property">datasets</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token punctuation">{</span>
    <span class="token literal-property property">axis</span><span class="token operator">:</span> <span class="token string">'y'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">label</span><span class="token operator">:</span> <span class="token string">'My First Dataset'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token number">65</span><span class="token punctuation">,</span> <span class="token number">59</span><span class="token punctuation">,</span> <span class="token number">80</span><span class="token punctuation">,</span> <span class="token number">81</span><span class="token punctuation">,</span> <span class="token number">56</span><span class="token punctuation">,</span> <span class="token number">55</span><span class="token punctuation">,</span> <span class="token number">40</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token literal-property property">fill</span><span class="token operator">:</span> <span class="token boolean">false</span><span class="token punctuation">,</span>
    <span class="token literal-property property">backgroundColor</span><span class="token operator">:</span> <span class="token punctuation">[</span>
      <span class="token string">'rgba(255, 99, 132, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(255, 159, 64, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(255, 205, 86, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(75, 192, 192, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(54, 162, 235, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(153, 102, 255, 0.2)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgba(201, 203, 207, 0.2)'</span>
    <span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token literal-property property">borderColor</span><span class="token operator">:</span> <span class="token punctuation">[</span>
      <span class="token string">'rgb(255, 99, 132)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(255, 159, 64)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(255, 205, 86)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(75, 192, 192)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(54, 162, 235)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(153, 102, 255)'</span><span class="token punctuation">,</span>
      <span class="token string">'rgb(201, 203, 207)'</span>
    <span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token literal-property property">borderWidth</span><span class="token operator">:</span> <span class="token number">1</span>
  <span class="token punctuation">}</span><span class="token punctuation">]</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span><br /></pre></div></div></div></div></div></div></div><h3 id="horizontal-bar-chart-config-options"><a href="#horizontal-bar-chart-config-options" class="header-anchor">#</a> Horizontal Bar Chart config Options</h3> <p>The configuration options for the horizontal bar chart are the same as for the <a href="#scale-configuration">bar chart</a>. However, any options specified on the x-axis in a bar chart, are applied to the y-axis in a horizontal bar chart.</p> <h2 id="internal-data-format"><a href="#internal-data-format" class="header-anchor">#</a> Internal data format</h2> <p><code>{x, y, _custom}</code> where <code>_custom</code> is an optional object defining stacked bar properties: <code>{start, end, barStart, barEnd, min, max}</code>. <code>start</code> and <code>end</code> are the input values. Those two are repeated in <code>barStart</code> (closer to origin), <code>barEnd</code> (further from origin), <code>min</code> and <code>max</code>.</p></div> <footer class="page-edit"><!----> <div class="last-updated"><span class="prefix">Last Updated:</span> <span class="time">8/3/2022, 12:46:38 PM</span></div></footer> <div class="page-nav"><p class="inner"><span class="prev">
      ←
      <a href="/docs/3.9.1/charts/area.html" class="prev">
        Area Chart
      </a></span> <span class="next"><a href="/docs/3.9.1/charts/bubble.html">
        Bubble Chart
      </a>
      →
    </span></p></div> </main></div><div class="global-ui"></div></div>
    <script src="/docs/3.9.1/assets/js/app.7e0dc8c8.js" defer></script><script src="/docs/3.9.1/assets/js/2.3e2307f3.js" defer></script><script src="/docs/3.9.1/assets/js/143.05e388ba.js" defer></script><script src="/docs/3.9.1/assets/js/3.947b8d98.js" defer></script>
  </body>
</html>
