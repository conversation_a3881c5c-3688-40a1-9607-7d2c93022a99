<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Axes | Chart.js</title>
    <meta name="generator" content="VuePress 1.8.3">
    <link rel="icon" href="/docs/3.9.1/favicon.ico">
    <meta name="description" content="Open source HTML5 Charts for your website">
    <link rel="preload" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css" as="style"><link rel="preload" href="/docs/3.9.1/assets/js/app.7e0dc8c8.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/2.3e2307f3.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/137.6cab2863.js" as="script"><link rel="prefetch" href="/docs/3.9.1/assets/js/10.edbecfa9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/100.f7a12d8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/101.15c56dbb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/102.33b746ea.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/103.850a4486.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/104.8b4f368b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/105.1a3d6c41.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/106.d8f8b4c1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/107.38cf9b4f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/108.a30da8c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/109.6d56b7d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/11.b98e8151.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/110.c44e533c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/111.ccb9a835.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/112.2d2a2890.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/113.5ac0921f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/114.90ef8814.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/115.12a72d27.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/116.6b780970.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/117.79604442.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/118.7752bc89.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/119.d8e76ef0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/12.433f0c7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/120.ed76595e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/121.cc3d56f2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/122.c8651871.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/123.f7d5f223.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/124.88ce07a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/125.a9572036.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/126.879447b3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/127.3b8478f3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/128.87d67ad7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/129.f6241700.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/13.b6743084.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/130.a9cb856a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/131.45e904c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/132.abd9e4c2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/133.d5aa1db6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/134.4bfa3b5a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/135.8c7630ad.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/136.f813e062.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/138.03ec5265.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/139.924812c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/14.422fec10.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/140.a7967a5d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/141.34a018bb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/142.c5bfcdd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/143.05e388ba.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/144.a98bdbcc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/145.826b73e0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/146.001afadf.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/147.fcadaef3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/148.604863a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/149.6509ed9f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/15.09a69fc3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/150.fd8575ca.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/151.67ecd4a3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/152.481a7bd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/153.dbf3f3aa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/154.2aa9c67d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/155.e3367ebc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/156.a5bb942d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/157.09caef4e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/158.1f82587f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/159.4cebd9d3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/16.0414f390.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/160.47df8215.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/161.fac79692.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/162.9f2f870f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/163.4f1604f9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/164.7f8d8643.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/165.fe2baa69.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/166.c82b3c76.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/167.3c3f2ff1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/168.8d2e8e83.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/169.b10c84bd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/17.8dfcb176.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/170.b9db8312.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/171.2480fc36.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/172.81c5206a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/173.477dcbae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/174.05398ca3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/175.84ab69d0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/176.f48183c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/177.d0d35403.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/178.55d433fd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/179.9442c138.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/18.c331029e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/180.5c15d5d4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/181.911f367b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/182.72f663e8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/183.22b2258c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/184.15b21065.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/185.894ea40b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/186.f6394459.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/187.7bd9b3fe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/188.3baa9bcd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/189.18e63b11.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/19.13bdd658.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/190.b7ffb54a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/191.13061aba.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/192.1bf9bd61.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/193.8fa44455.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/194.49c9a3c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/195.eb1e8802.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/196.9b925823.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/197.adca6c8c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/198.2ae0961a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/199.cd9dca80.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/20.5d11c294.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/200.2eb9437f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/201.6dba10c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/202.9c5057c6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/203.98a3c2b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/204.50d5cf2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/205.e56e820b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/206.6ce5c41e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/207.8aa83d91.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/208.296ee160.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/209.13279349.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/21.7b54d7d8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/210.63e30420.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/211.0b325f23.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/212.be671e2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/213.81d2e607.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/214.ba403b5c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/215.2efcec5f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/216.e01d3100.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/217.906c8d54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/218.94e33827.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/219.21c5e01f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/22.c2daedd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/220.3ba160e4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/221.5c17138c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/222.aea004ce.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/223.cbb7b883.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/224.b130b37f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/225.32f90319.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/226.8c102c21.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/227.b3d60339.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/228.72f0ad18.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/229.3daa3b7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/23.2c668e20.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/230.7e5a85b1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/231.b83d12f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/232.e902f42d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/233.b4f254c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/234.d975df48.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/235.71a7be7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/236.05d87a5e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/237.d518e28b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/238.eb60e397.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/239.81fecfed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/24.af200d5b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/240.c0869bc2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/241.59dc896f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/242.6d423d39.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/243.a355eaa8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/244.ad729cf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/245.c3cd6bbe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/246.d0708528.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/247.13d65d78.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/248.1d222543.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/249.2d7a9bf1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/25.4f97f63f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/250.09ef38e2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/251.0cb90e8a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/252.2ff0def4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/253.61c7c505.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/254.20137eeb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/255.49c937e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/256.8d709dae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/257.b3ebdce7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/258.792f66d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/259.98c809e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/26.d1bb645c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/260.54ff10d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/261.928afea4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/27.2d5c4a6b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/28.72dee0f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/29.8820be26.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/3.947b8d98.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/30.343676b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/31.cff089f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/32.56d8546e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/33.6b642a06.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/34.360a9ea9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/35.205d7fac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/36.f2765bae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/37.6c33435e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/38.a778a6a2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/39.7dda160f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/4.ee88d25a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/40.bd778eac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/41.b79220f6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/42.c3157beb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/43.097368d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/44.2515f16e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/45.d5b4b7c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/46.bbab8d6e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/47.79aa575e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/48.45785af9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/49.0569a6eb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/5.00f814ac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/50.14bd3ba2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/51.e0968711.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/52.fe65ddf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/53.faa1ff3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/54.fcfed2c5.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/55.7db1d28a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/56.8b0e82b7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/57.9de2d983.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/58.b0f8ad0c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/59.f1ff4935.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/6.2bc86161.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/60.ac08de9a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/61.ea4fad75.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/62.5c85853b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/63.5ac99656.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/64.c6838e95.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/65.4c1b089e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/66.379a6b45.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/67.15703e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/68.ffdefd7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/69.bdeb7b9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/7.74f2ce90.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/70.3dadb5ed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/71.2c97fe38.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/72.dc778e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/73.d88fcb57.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/74.bba2165e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/75.15562a81.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/76.21f5a94e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/77.0725268e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/78.d6f610d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/79.60d67faa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/8.8928eb8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/80.99d71ee0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/81.f1500469.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/82.69f363a6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/83.f1fbcb2c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/84.b76e3156.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/85.efc4bd54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/86.85011b24.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/87.6a88d571.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/88.0f45cfe0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/89.e67ddb59.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/9.63ebb16b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/90.96ebfa9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/91.cdde4d3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/92.dc3f06d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/93.aaa04d52.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/94.2af5650c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/95.1d44ec16.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/96.9545127d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/97.8b18f487.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/98.cb259cd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/99.6251650a.js">
    <link rel="stylesheet" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css">
  </head>
  <body>
    <div id="app" data-server-rendered="true"><div class="theme-container"><header class="navbar"><div class="sidebar-button"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" viewBox="0 0 448 512" class="icon"><path fill="currentColor" d="M436 124H12c-6.627 0-12-5.373-12-12V80c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12z"></path></svg></div> <a href="/docs/3.9.1/" class="home-link router-link-active"><img src="/docs/3.9.1/favicon.ico" alt="Chart.js" class="logo"> <span class="site-name can-hide">Chart.js</span></a> <div class="links"><div class="search-box"><input aria-label="Search" autocomplete="off" spellcheck="false" value=""> <!----></div> <nav class="nav-links can-hide"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" class="nav-link">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav></div></header> <div class="sidebar-mask"></div> <aside class="sidebar"><nav class="nav-links"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" class="nav-link">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav>  <ul class="sidebar-links"><li><a href="/docs/3.9.1/" aria-current="page" class="sidebar-link">Chart.js</a></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Getting Started</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>General</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Configuration</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Chart Types</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading open"><span>Axes</span> <span class="arrow down"></span></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/docs/3.9.1/axes/" aria-current="page" class="active sidebar-link">Axes</a></li><li><section class="sidebar-group collapsable is-sub-group depth-1"><p class="sidebar-heading"><span>Cartesian</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable is-sub-group depth-1"><p class="sidebar-heading"><span>Radial</span> <span class="arrow right"></span></p> <!----></section></li><li><a href="/docs/3.9.1/axes/labelling.html" class="sidebar-link">Labeling Axes</a></li><li><a href="/docs/3.9.1/axes/styling.html" class="sidebar-link">Styling</a></li></ul></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Developers</span> <span class="arrow right"></span></p> <!----></section></li></ul> </aside> <main class="page"> <div class="theme-default-content content__default"><h1 id="axes"><a href="#axes" class="header-anchor">#</a> Axes</h1> <p>Axes are an integral part of a chart. They are used to determine how data maps to a pixel value on the chart. In a cartesian chart, there is 1 or more X-axis and 1 or more Y-axis to map points onto the 2-dimensional canvas. These axes are known as <a href="/docs/3.9.1/axes/cartesian/">'cartesian axes'</a>.</p> <p>In a radial chart, such as a radar chart or a polar area chart, there is a single axis that maps points in the angular and radial directions. These are known as <a href="/docs/3.9.1/axes/radial/">'radial axes'</a>.</p> <p>Scales in Chart.js &gt;v2.0 are significantly more powerful, but also different than those of v1.0.</p> <ul><li>Multiple X &amp; Y axes are supported.</li> <li>A built-in label auto-skip feature detects would-be overlapping ticks and labels and removes every nth label to keep things displaying normally.</li> <li>Scale titles are supported.</li> <li>New scale types can be extended without writing an entirely new chart type.</li></ul> <h2 id="default-scales"><a href="#default-scales" class="header-anchor">#</a> Default scales</h2> <p>The default <code>scaleId</code>'s for carterian charts are <code>'x'</code> and <code>'y'</code>. For radial charts: <code>'r'</code>.
Each dataset is mapped to a scale for each axis (x, y or r) it requires. The scaleId's that a dataset is mapped to, is determined by the <code>xAxisID</code>, <code>yAxisID</code> or <code>rAxisID</code>.
If the ID for an axis is not specified, first scale for that axis is used. If no scale for an axis is found, a new scale is created.</p> <p>Some examples:</p> <p>The following chart will have <code>'x'</code> and <code>'y'</code> scales:</p> <div class="language-js extra-class"><pre class="language-js"><code><span class="token keyword">let</span> chart <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Chart</span><span class="token punctuation">(</span>ctx<span class="token punctuation">,</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'line'</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></div><p>The following chart will have scales <code>'x'</code> and <code>'myScale'</code>:</p> <div class="language-js extra-class"><pre class="language-js"><code><span class="token keyword">let</span> chart <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Chart</span><span class="token punctuation">(</span>ctx<span class="token punctuation">,</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'bar'</span><span class="token punctuation">,</span>
  <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">datasets</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token punctuation">{</span>
      <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token number">1</span><span class="token punctuation">,</span> <span class="token number">2</span><span class="token punctuation">,</span> <span class="token number">3</span><span class="token punctuation">]</span>
    <span class="token punctuation">}</span><span class="token punctuation">]</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">scales</span><span class="token operator">:</span> <span class="token punctuation">{</span>
      <span class="token literal-property property">myScale</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'logarithmic'</span><span class="token punctuation">,</span>
        <span class="token literal-property property">position</span><span class="token operator">:</span> <span class="token string">'right'</span><span class="token punctuation">,</span> <span class="token comment">// `axis` is determined by the position as `'y'`</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></div><p>The following chart will have scales <code>'xAxis'</code> and <code>'yAxis'</code>:</p> <div class="language-js extra-class"><pre class="language-js"><code><span class="token keyword">let</span> chart <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Chart</span><span class="token punctuation">(</span>ctx<span class="token punctuation">,</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'bar'</span><span class="token punctuation">,</span>
  <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">datasets</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token punctuation">{</span>
      <span class="token literal-property property">yAxisID</span><span class="token operator">:</span> <span class="token string">'yAxis'</span>
    <span class="token punctuation">}</span><span class="token punctuation">]</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">scales</span><span class="token operator">:</span> <span class="token punctuation">{</span>
      <span class="token literal-property property">xAxis</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token comment">// The axis for this scale is determined from the first letter of the id as `'x'`</span>
        <span class="token comment">// It is recommended to specify `position` and / or `axis` explicitly.</span>
        <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'time'</span><span class="token punctuation">,</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></div><p>The following chart will have <code>'r'</code> scale:</p> <div class="language-js extra-class"><pre class="language-js"><code><span class="token keyword">let</span> chart <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Chart</span><span class="token punctuation">(</span>ctx<span class="token punctuation">,</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'radar'</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></div><p>The following chart will have <code>'myScale'</code> scale:</p> <div class="language-js extra-class"><pre class="language-js"><code><span class="token keyword">let</span> chart <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Chart</span><span class="token punctuation">(</span>ctx<span class="token punctuation">,</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'radar'</span><span class="token punctuation">,</span>
  <span class="token literal-property property">scales</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">myScale</span><span class="token operator">:</span> <span class="token punctuation">{</span>
      <span class="token literal-property property">axis</span><span class="token operator">:</span> <span class="token string">'r'</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></div><h2 id="common-configuration"><a href="#common-configuration" class="header-anchor">#</a> Common Configuration</h2> <div class="custom-block tip"><p class="custom-block-title">Note</p> <p>These are only the common options supported by all axes. Please see specific axis documentation for all of the available options for that axis.</p></div> <h3 id="common-options-to-all-axes"><a href="#common-options-to-all-axes" class="header-anchor">#</a> Common options to all axes</h3> <p>Namespace: <code>options.scales[scaleId]</code></p> <table><thead><tr><th>Name</th> <th>Type</th> <th>Default</th> <th>Description</th></tr></thead> <tbody><tr><td><code>type</code></td> <td><code>string</code></td> <td></td> <td>Type of scale being employed. Custom scales can be created and registered with a string key. This allows changing the type of an axis for a chart.</td></tr> <tr><td><code>alignToPixels</code></td> <td><code>boolean</code></td> <td><code>false</code></td> <td>Align pixel values to device pixels.</td></tr> <tr><td><code>backgroundColor</code></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td></td> <td>Background color of the scale area.</td></tr> <tr><td><code>display</code></td> <td><code>boolean</code>|<code>string</code></td> <td><code>true</code></td> <td>Controls the axis global visibility (visible when <code>true</code>, hidden when <code>false</code>). When <code>display: 'auto'</code>, the axis is visible only if at least one associated dataset is visible.</td></tr> <tr><td><code>grid</code></td> <td><code>object</code></td> <td></td> <td>Grid line configuration. <a href="/docs/3.9.1/axes/styling.html#grid-line-configuration">more...</a></td></tr> <tr><td><code>min</code></td> <td><code>number</code></td> <td></td> <td>User defined minimum number for the scale, overrides minimum value from data. <a href="/docs/3.9.1/axes/#axis-range-settings">more...</a></td></tr> <tr><td><code>max</code></td> <td><code>number</code></td> <td></td> <td>User defined maximum number for the scale, overrides maximum value from data. <a href="/docs/3.9.1/axes/#axis-range-settings">more...</a></td></tr> <tr><td><code>reverse</code></td> <td><code>boolean</code></td> <td><code>false</code></td> <td>Reverse the scale.</td></tr> <tr><td><code>stacked</code></td> <td><code>boolean</code>|<code>string</code></td> <td><code>false</code></td> <td>Should the data be stacked. <a href="/docs/3.9.1/axes/#stacking">more...</a></td></tr> <tr><td><code>suggestedMax</code></td> <td><code>number</code></td> <td></td> <td>Adjustment used when calculating the maximum data value. <a href="/docs/3.9.1/axes/#axis-range-settings">more...</a></td></tr> <tr><td><code>suggestedMin</code></td> <td><code>number</code></td> <td></td> <td>Adjustment used when calculating the minimum data value. <a href="/docs/3.9.1/axes/#axis-range-settings">more...</a></td></tr> <tr><td><code>ticks</code></td> <td><code>object</code></td> <td></td> <td>Tick configuration. <a href="/docs/3.9.1/axes/#tick-configuration">more...</a></td></tr> <tr><td><code>weight</code></td> <td><code>number</code></td> <td><code>0</code></td> <td>The weight used to sort the axis. Higher weights are further away from the chart area.</td></tr></tbody></table> <h2 id="tick-configuration"><a href="#tick-configuration" class="header-anchor">#</a> Tick Configuration</h2> <div class="custom-block tip"><p class="custom-block-title">Note</p> <p>These are only the common tick options supported by all axes. Please see specific axis documentation for all of the available tick options for that axis.</p></div> <h3 id="common-tick-options-to-all-axes"><a href="#common-tick-options-to-all-axes" class="header-anchor">#</a> Common tick options to all axes</h3> <p>Namespace: <code>options.scales[scaleId].ticks</code></p> <table><thead><tr><th>Name</th> <th>Type</th> <th style="text-align:center;">Scriptable</th> <th>Default</th> <th>Description</th></tr></thead> <tbody><tr><td><code>backdropColor</code></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td style="text-align:center;">Yes</td> <td><code>'rgba(255, 255, 255, 0.75)'</code></td> <td>Color of label backdrops.</td></tr> <tr><td><code>backdropPadding</code></td> <td><a href="/docs/3.9.1/general/padding.html"><code>Padding</code></a></td> <td style="text-align:center;"></td> <td><code>2</code></td> <td>Padding of label backdrop.</td></tr> <tr><td><code>callback</code></td> <td><code>function</code></td> <td style="text-align:center;"></td> <td></td> <td>Returns the string representation of the tick value as it should be displayed on the chart. See <a href="/docs/3.9.1/axes/labelling.html#creating-custom-tick-formats">callback</a>.</td></tr> <tr><td><code>display</code></td> <td><code>boolean</code></td> <td style="text-align:center;"></td> <td><code>true</code></td> <td>If true, show tick labels.</td></tr> <tr><td><code>color</code></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td style="text-align:center;">Yes</td> <td><code>Chart.defaults.color</code></td> <td>Color of ticks.</td></tr> <tr><td><code>font</code></td> <td><code>Font</code></td> <td style="text-align:center;">Yes</td> <td><code>Chart.defaults.font</code></td> <td>See <a href="/docs/3.9.1/general/fonts.html">Fonts</a></td></tr> <tr><td><code>major</code></td> <td><code>object</code></td> <td style="text-align:center;"></td> <td><code>{}</code></td> <td><a href="/docs/3.9.1/axes/styling.html#major-tick-configuration">Major ticks configuration</a>.</td></tr> <tr><td><code>padding</code></td> <td><code>number</code></td> <td style="text-align:center;"></td> <td><code>3</code></td> <td>Sets the offset of the tick labels from the axis</td></tr> <tr><td><code>showLabelBackdrop</code></td> <td><code>boolean</code></td> <td style="text-align:center;">Yes</td> <td><code>true</code> for radial scale, <code>false</code> otherwise</td> <td>If true, draw a background behind the tick labels.</td></tr> <tr><td><code>textStrokeColor</code></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td style="text-align:center;">Yes</td> <td>``</td> <td>The color of the stroke around the text.</td></tr> <tr><td><code>textStrokeWidth</code></td> <td><code>number</code></td> <td style="text-align:center;">Yes</td> <td><code>0</code></td> <td>Stroke width around the text.</td></tr> <tr><td><code>z</code></td> <td><code>number</code></td> <td style="text-align:center;"></td> <td><code>0</code></td> <td>z-index of tick layer. Useful when ticks are drawn on chart area. Values &lt;= 0 are drawn under datasets, &gt; 0 on top.</td></tr></tbody></table> <h2 id="axis-range-settings"><a href="#axis-range-settings" class="header-anchor">#</a> Axis Range Settings</h2> <p>Given the number of axis range settings, it is important to understand how they all interact with each other.</p> <p>The <code>suggestedMax</code> and <code>suggestedMin</code> settings only change the data values that are used to scale the axis. These are useful for extending the range of the axis while maintaining the auto fit behaviour.</p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token keyword">let</span> minDataValue <span class="token operator">=</span> Math<span class="token punctuation">.</span><span class="token function">min</span><span class="token punctuation">(</span>mostNegativeValue<span class="token punctuation">,</span> options<span class="token punctuation">.</span>suggestedMin<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword">let</span> maxDataValue <span class="token operator">=</span> Math<span class="token punctuation">.</span><span class="token function">max</span><span class="token punctuation">(</span>mostPositiveValue<span class="token punctuation">,</span> options<span class="token punctuation">.</span>suggestedMax<span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></div><p>In this example, the largest positive value is 50, but the data maximum is expanded out to 100. However, because the lowest data value is below the <code>suggestedMin</code> setting, it is ignored.</p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token keyword">let</span> chart <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Chart</span><span class="token punctuation">(</span>ctx<span class="token punctuation">,</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'line'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">datasets</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token punctuation">{</span>
            <span class="token literal-property property">label</span><span class="token operator">:</span> <span class="token string">'First dataset'</span><span class="token punctuation">,</span>
            <span class="token literal-property property">data</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token number">0</span><span class="token punctuation">,</span> <span class="token number">20</span><span class="token punctuation">,</span> <span class="token number">40</span><span class="token punctuation">,</span> <span class="token number">50</span><span class="token punctuation">]</span>
        <span class="token punctuation">}</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
        <span class="token literal-property property">labels</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token string">'January'</span><span class="token punctuation">,</span> <span class="token string">'February'</span><span class="token punctuation">,</span> <span class="token string">'March'</span><span class="token punctuation">,</span> <span class="token string">'April'</span><span class="token punctuation">]</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">scales</span><span class="token operator">:</span> <span class="token punctuation">{</span>
            <span class="token literal-property property">y</span><span class="token operator">:</span> <span class="token punctuation">{</span>
                <span class="token literal-property property">suggestedMin</span><span class="token operator">:</span> <span class="token number">50</span><span class="token punctuation">,</span>
                <span class="token literal-property property">suggestedMax</span><span class="token operator">:</span> <span class="token number">100</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></div><p>In contrast to the <code>suggested*</code> settings, the <code>min</code> and <code>max</code> settings set explicit ends to the axes. When these are set, some data points may not be visible.</p> <h2 id="stacking"><a href="#stacking" class="header-anchor">#</a> Stacking</h2> <p>By default data is not stacked. If the <code>stacked</code> option of the value scale (y-axis on horizontal chart) is <code>true</code>, positive and negative values are stacked separately. Additionally a <code>stack</code> option can be defined per dataset to further divide into stack groups <a href="/docs/3.9.1/general/data-structures/#dataset-configuration">more...</a>.
For some charts, you might want to stack positive and negative values together. That can be achieved by specifying <code>stacked: 'single'</code>.</p> <h2 id="callbacks"><a href="#callbacks" class="header-anchor">#</a> Callbacks</h2> <p>There are a number of config callbacks that can be used to change parameters in the scale at different points in the update process. The options are supplied at the top level of the axis options.</p> <p>Namespace: <code>options.scales[scaleId]</code></p> <table><thead><tr><th>Name</th> <th>Arguments</th> <th>Description</th></tr></thead> <tbody><tr><td><code>beforeUpdate</code></td> <td><code>axis</code></td> <td>Callback called before the update process starts.</td></tr> <tr><td><code>beforeSetDimensions</code></td> <td><code>axis</code></td> <td>Callback that runs before dimensions are set.</td></tr> <tr><td><code>afterSetDimensions</code></td> <td><code>axis</code></td> <td>Callback that runs after dimensions are set.</td></tr> <tr><td><code>beforeDataLimits</code></td> <td><code>axis</code></td> <td>Callback that runs before data limits are determined.</td></tr> <tr><td><code>afterDataLimits</code></td> <td><code>axis</code></td> <td>Callback that runs after data limits are determined.</td></tr> <tr><td><code>beforeBuildTicks</code></td> <td><code>axis</code></td> <td>Callback that runs before ticks are created.</td></tr> <tr><td><code>afterBuildTicks</code></td> <td><code>axis</code></td> <td>Callback that runs after ticks are created. Useful for filtering ticks.</td></tr> <tr><td><code>beforeTickToLabelConversion</code></td> <td><code>axis</code></td> <td>Callback that runs before ticks are converted into strings.</td></tr> <tr><td><code>afterTickToLabelConversion</code></td> <td><code>axis</code></td> <td>Callback that runs after ticks are converted into strings.</td></tr> <tr><td><code>beforeCalculateLabelRotation</code></td> <td><code>axis</code></td> <td>Callback that runs before tick rotation is determined.</td></tr> <tr><td><code>afterCalculateLabelRotation</code></td> <td><code>axis</code></td> <td>Callback that runs after tick rotation is determined.</td></tr> <tr><td><code>beforeFit</code></td> <td><code>axis</code></td> <td>Callback that runs before the scale fits to the canvas.</td></tr> <tr><td><code>afterFit</code></td> <td><code>axis</code></td> <td>Callback that runs after the scale fits to the canvas.</td></tr> <tr><td><code>afterUpdate</code></td> <td><code>axis</code></td> <td>Callback that runs at the end of the update process.</td></tr></tbody></table> <h3 id="updating-axis-defaults"><a href="#updating-axis-defaults" class="header-anchor">#</a> Updating Axis Defaults</h3> <p>The default configuration for a scale can be easily changed. All you need to do is set the new options to <code>Chart.defaults.scales[type]</code>.</p> <p>For example, to set the minimum value of 0 for all linear scales, you would do the following. Any linear scales created after this time would now have a minimum of 0.</p> <div class="language-javascript extra-class"><pre class="language-javascript"><code>Chart<span class="token punctuation">.</span>defaults<span class="token punctuation">.</span>scales<span class="token punctuation">.</span>linear<span class="token punctuation">.</span>min <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>
</code></pre></div><h2 id="creating-new-axes"><a href="#creating-new-axes" class="header-anchor">#</a> Creating New Axes</h2> <p>To create a new axis, see the <a href="/docs/3.9.1/developers/axes.html">developer docs</a>.</p></div> <footer class="page-edit"><!----> <div class="last-updated"><span class="prefix">Last Updated:</span> <span class="time">8/3/2022, 12:46:38 PM</span></div></footer> <div class="page-nav"><p class="inner"><span class="prev">
      ←
      <a href="/docs/3.9.1/charts/scatter.html" class="prev">
        Scatter Chart
      </a></span> <span class="next"><a href="/docs/3.9.1/axes/cartesian/">
        Cartesian Axes
      </a>
      →
    </span></p></div> </main></div><div class="global-ui"></div></div>
    <script src="/docs/3.9.1/assets/js/app.7e0dc8c8.js" defer></script><script src="/docs/3.9.1/assets/js/2.3e2307f3.js" defer></script><script src="/docs/3.9.1/assets/js/137.6cab2863.js" defer></script>
  </body>
</html>
