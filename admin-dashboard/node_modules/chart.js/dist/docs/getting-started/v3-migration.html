<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>3.x Migration Guide | Chart.js</title>
    <meta name="generator" content="VuePress 1.8.3">
    <link rel="icon" href="/docs/3.9.1/favicon.ico">
    <meta name="description" content="Open source HTML5 Charts for your website">
    <link rel="preload" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css" as="style"><link rel="preload" href="/docs/3.9.1/assets/js/app.7e0dc8c8.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/2.3e2307f3.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/183.22b2258c.js" as="script"><link rel="prefetch" href="/docs/3.9.1/assets/js/10.edbecfa9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/100.f7a12d8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/101.15c56dbb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/102.33b746ea.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/103.850a4486.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/104.8b4f368b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/105.1a3d6c41.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/106.d8f8b4c1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/107.38cf9b4f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/108.a30da8c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/109.6d56b7d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/11.b98e8151.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/110.c44e533c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/111.ccb9a835.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/112.2d2a2890.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/113.5ac0921f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/114.90ef8814.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/115.12a72d27.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/116.6b780970.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/117.79604442.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/118.7752bc89.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/119.d8e76ef0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/12.433f0c7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/120.ed76595e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/121.cc3d56f2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/122.c8651871.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/123.f7d5f223.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/124.88ce07a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/125.a9572036.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/126.879447b3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/127.3b8478f3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/128.87d67ad7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/129.f6241700.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/13.b6743084.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/130.a9cb856a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/131.45e904c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/132.abd9e4c2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/133.d5aa1db6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/134.4bfa3b5a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/135.8c7630ad.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/136.f813e062.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/137.6cab2863.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/138.03ec5265.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/139.924812c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/14.422fec10.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/140.a7967a5d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/141.34a018bb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/142.c5bfcdd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/143.05e388ba.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/144.a98bdbcc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/145.826b73e0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/146.001afadf.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/147.fcadaef3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/148.604863a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/149.6509ed9f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/15.09a69fc3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/150.fd8575ca.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/151.67ecd4a3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/152.481a7bd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/153.dbf3f3aa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/154.2aa9c67d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/155.e3367ebc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/156.a5bb942d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/157.09caef4e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/158.1f82587f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/159.4cebd9d3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/16.0414f390.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/160.47df8215.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/161.fac79692.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/162.9f2f870f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/163.4f1604f9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/164.7f8d8643.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/165.fe2baa69.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/166.c82b3c76.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/167.3c3f2ff1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/168.8d2e8e83.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/169.b10c84bd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/17.8dfcb176.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/170.b9db8312.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/171.2480fc36.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/172.81c5206a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/173.477dcbae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/174.05398ca3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/175.84ab69d0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/176.f48183c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/177.d0d35403.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/178.55d433fd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/179.9442c138.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/18.c331029e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/180.5c15d5d4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/181.911f367b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/182.72f663e8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/184.15b21065.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/185.894ea40b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/186.f6394459.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/187.7bd9b3fe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/188.3baa9bcd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/189.18e63b11.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/19.13bdd658.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/190.b7ffb54a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/191.13061aba.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/192.1bf9bd61.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/193.8fa44455.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/194.49c9a3c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/195.eb1e8802.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/196.9b925823.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/197.adca6c8c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/198.2ae0961a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/199.cd9dca80.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/20.5d11c294.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/200.2eb9437f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/201.6dba10c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/202.9c5057c6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/203.98a3c2b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/204.50d5cf2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/205.e56e820b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/206.6ce5c41e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/207.8aa83d91.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/208.296ee160.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/209.13279349.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/21.7b54d7d8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/210.63e30420.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/211.0b325f23.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/212.be671e2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/213.81d2e607.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/214.ba403b5c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/215.2efcec5f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/216.e01d3100.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/217.906c8d54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/218.94e33827.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/219.21c5e01f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/22.c2daedd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/220.3ba160e4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/221.5c17138c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/222.aea004ce.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/223.cbb7b883.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/224.b130b37f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/225.32f90319.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/226.8c102c21.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/227.b3d60339.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/228.72f0ad18.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/229.3daa3b7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/23.2c668e20.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/230.7e5a85b1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/231.b83d12f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/232.e902f42d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/233.b4f254c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/234.d975df48.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/235.71a7be7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/236.05d87a5e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/237.d518e28b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/238.eb60e397.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/239.81fecfed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/24.af200d5b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/240.c0869bc2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/241.59dc896f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/242.6d423d39.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/243.a355eaa8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/244.ad729cf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/245.c3cd6bbe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/246.d0708528.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/247.13d65d78.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/248.1d222543.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/249.2d7a9bf1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/25.4f97f63f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/250.09ef38e2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/251.0cb90e8a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/252.2ff0def4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/253.61c7c505.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/254.20137eeb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/255.49c937e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/256.8d709dae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/257.b3ebdce7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/258.792f66d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/259.98c809e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/26.d1bb645c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/260.54ff10d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/261.928afea4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/27.2d5c4a6b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/28.72dee0f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/29.8820be26.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/3.947b8d98.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/30.343676b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/31.cff089f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/32.56d8546e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/33.6b642a06.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/34.360a9ea9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/35.205d7fac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/36.f2765bae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/37.6c33435e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/38.a778a6a2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/39.7dda160f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/4.ee88d25a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/40.bd778eac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/41.b79220f6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/42.c3157beb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/43.097368d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/44.2515f16e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/45.d5b4b7c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/46.bbab8d6e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/47.79aa575e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/48.45785af9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/49.0569a6eb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/5.00f814ac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/50.14bd3ba2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/51.e0968711.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/52.fe65ddf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/53.faa1ff3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/54.fcfed2c5.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/55.7db1d28a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/56.8b0e82b7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/57.9de2d983.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/58.b0f8ad0c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/59.f1ff4935.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/6.2bc86161.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/60.ac08de9a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/61.ea4fad75.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/62.5c85853b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/63.5ac99656.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/64.c6838e95.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/65.4c1b089e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/66.379a6b45.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/67.15703e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/68.ffdefd7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/69.bdeb7b9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/7.74f2ce90.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/70.3dadb5ed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/71.2c97fe38.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/72.dc778e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/73.d88fcb57.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/74.bba2165e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/75.15562a81.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/76.21f5a94e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/77.0725268e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/78.d6f610d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/79.60d67faa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/8.8928eb8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/80.99d71ee0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/81.f1500469.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/82.69f363a6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/83.f1fbcb2c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/84.b76e3156.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/85.efc4bd54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/86.85011b24.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/87.6a88d571.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/88.0f45cfe0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/89.e67ddb59.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/9.63ebb16b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/90.96ebfa9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/91.cdde4d3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/92.dc3f06d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/93.aaa04d52.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/94.2af5650c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/95.1d44ec16.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/96.9545127d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/97.8b18f487.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/98.cb259cd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/99.6251650a.js">
    <link rel="stylesheet" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css">
  </head>
  <body>
    <div id="app" data-server-rendered="true"><div class="theme-container"><header class="navbar"><div class="sidebar-button"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" viewBox="0 0 448 512" class="icon"><path fill="currentColor" d="M436 124H12c-6.627 0-12-5.373-12-12V80c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12z"></path></svg></div> <a href="/docs/3.9.1/" class="home-link router-link-active"><img src="/docs/3.9.1/favicon.ico" alt="Chart.js" class="logo"> <span class="site-name can-hide">Chart.js</span></a> <div class="links"><div class="search-box"><input aria-label="Search" autocomplete="off" spellcheck="false" value=""> <!----></div> <nav class="nav-links can-hide"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" class="nav-link">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav></div></header> <div class="sidebar-mask"></div> <aside class="sidebar"><nav class="nav-links"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" class="nav-link">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav>  <ul class="sidebar-links"><li><a href="/docs/3.9.1/" aria-current="page" class="sidebar-link">Chart.js</a></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading open"><span>Getting Started</span> <span class="arrow down"></span></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/docs/3.9.1/getting-started/" aria-current="page" class="sidebar-link">Getting Started</a></li><li><a href="/docs/3.9.1/getting-started/installation.html" class="sidebar-link">Installation</a></li><li><a href="/docs/3.9.1/getting-started/integration.html" class="sidebar-link">Integration</a></li><li><a href="/docs/3.9.1/getting-started/usage.html" class="sidebar-link">Usage</a></li><li><a href="/docs/3.9.1/getting-started/v3-migration.html" aria-current="page" class="active sidebar-link">3.x Migration Guide</a></li></ul></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>General</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Configuration</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Chart Types</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Axes</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Developers</span> <span class="arrow right"></span></p> <!----></section></li></ul> </aside> <main class="page"> <div class="theme-default-content content__default"><h1 id="_3-x-migration-guide"><a href="#_3-x-migration-guide" class="header-anchor">#</a> 3.x Migration Guide</h1> <p>Chart.js 3.0 introduces a number of breaking changes. Chart.js 2.0 was released in April 2016. In the years since then, as Chart.js has grown in popularity and feature set, we've learned some lessons about how to better create a charting library. In order to improve performance, offer new features, and improve maintainability, it was necessary to break backwards compatibility, but we aimed to do so only when worth the benefit. Some major highlights of v3 include:</p> <ul><li>Large <a href="/docs/3.9.1/general/performance.html">performance</a> improvements including the ability to skip data parsing and render charts in parallel via webworkers</li> <li>Additional configurability and scriptable options with better defaults</li> <li>Completely rewritten animation system</li> <li>Rewritten filler plugin with numerous bug fixes</li> <li>Documentation migrated from GitBook to Vuepress</li> <li>API documentation generated and verified by TypeDoc</li> <li>No more CSS injection</li> <li>Tons of bug fixes</li> <li>Tree shaking</li></ul> <h2 id="end-user-migration"><a href="#end-user-migration" class="header-anchor">#</a> End user migration</h2> <h3 id="setup-and-installation"><a href="#setup-and-installation" class="header-anchor">#</a> Setup and installation</h3> <ul><li>Distributed files are now in lower case. For example: <code>dist/chart.js</code>.</li> <li>Chart.js is no longer providing the <code>Chart.bundle.js</code> and <code>Chart.bundle.min.js</code>. Please see the <a href="/docs/3.9.1/getting-started/installation.html">installation</a> and <a href="/docs/3.9.1/getting-started/integration.html">integration</a> docs for details on the recommended way to setup Chart.js if you were using these builds.</li> <li><code>moment</code> is no longer specified as an npm dependency. If you are using the <code>time</code> or <code>timeseries</code> scales, you must include one of <a href="https://github.com/chartjs/awesome#adapters" target="_blank" rel="noopener noreferrer">the available adapters<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a> and corresponding date library. You no longer need to exclude moment from your build.</li> <li>The <code>Chart</code> constructor will throw an error if the canvas/context provided is already in use</li> <li>Chart.js 3 is tree-shakeable. So if you are using it as an <code>npm</code> module in a project and want to make use of this feature, you need to import and register the controllers, elements, scales and plugins you want to use, for a list of all the available items to import see <a href="/docs/3.9.1/getting-started/integration.html#bundlers-webpack-rollup-etc">integration</a>. You will not have to call <code>register</code> if importing Chart.js via a <code>script</code> tag or from the <a href="/docs/3.9.1/getting-started/integration.html#bundlers-webpack-rollup-etc"><code>auto</code></a> register path as an <code>npm</code> module, in this case you will not get the tree shaking benefits. Here is an example of registering components:</li></ul> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token keyword">import</span> <span class="token punctuation">{</span> Chart<span class="token punctuation">,</span> LineController<span class="token punctuation">,</span> LineElement<span class="token punctuation">,</span> PointElement<span class="token punctuation">,</span> LinearScale<span class="token punctuation">,</span> Title <span class="token punctuation">}</span> <span class="token keyword">from</span> <span class="token template-string"><span class="token template-punctuation string">`</span><span class="token string">chart.js</span><span class="token template-punctuation string">`</span></span>
Chart<span class="token punctuation">.</span><span class="token function">register</span><span class="token punctuation">(</span>LineController<span class="token punctuation">,</span> LineElement<span class="token punctuation">,</span> PointElement<span class="token punctuation">,</span> LinearScale<span class="token punctuation">,</span> Title<span class="token punctuation">)</span><span class="token punctuation">;</span>
<span class="token keyword">const</span> chart <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Chart</span><span class="token punctuation">(</span>ctx<span class="token punctuation">,</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'line'</span><span class="token punctuation">,</span>
    <span class="token comment">// data: ...</span>
    <span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">plugins</span><span class="token operator">:</span> <span class="token punctuation">{</span>
            <span class="token literal-property property">title</span><span class="token operator">:</span> <span class="token punctuation">{</span>
                <span class="token literal-property property">display</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
                <span class="token literal-property property">text</span><span class="token operator">:</span> <span class="token string">'Chart Title'</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span><span class="token punctuation">,</span>
        <span class="token literal-property property">scales</span><span class="token operator">:</span> <span class="token punctuation">{</span>
            <span class="token literal-property property">x</span><span class="token operator">:</span> <span class="token punctuation">{</span>
                <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'linear'</span>
            <span class="token punctuation">}</span><span class="token punctuation">,</span>
            <span class="token literal-property property">y</span><span class="token operator">:</span> <span class="token punctuation">{</span>
                <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'linear'</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre></div><h3 id="chart-types"><a href="#chart-types" class="header-anchor">#</a> Chart types</h3> <ul><li><code>horizontalBar</code> chart type was removed. Horizontal bar charts can be configured using the new <a href="/docs/3.9.1/charts/bar.html#horizontal-bar-chart"><code>indexAxis</code></a> option</li></ul> <h3 id="options"><a href="#options" class="header-anchor">#</a> Options</h3> <p>A number of changes were made to the configuration options passed to the <code>Chart</code> constructor. Those changes are documented below.</p> <h4 id="generic-changes"><a href="#generic-changes" class="header-anchor">#</a> Generic changes</h4> <ul><li>Indexable options are now looping. <code>backgroundColor: ['red', 'green']</code> will result in alternating <code>'red'</code> / <code>'green'</code> if there are more than 2 data points.</li> <li>The input properties of object data can now be freely specified, see <a href="/docs/3.9.1/general/data-structures.html">data structures</a> for details.</li> <li>Most options are resolved utilizing proxies, instead of merging with defaults. In addition to easily enabling different resolution routes for different contexts, it allows using other resolved options in scriptable options.
<ul><li>Options are by default scriptable and indexable, unless disabled for some reason.</li> <li>Scriptable options receive a option resolver as second parameter for accessing other options in same context.</li> <li>Resolution falls to upper scopes, if no match is found earlier. See <a href="/docs/3.9.1/general/options.html">options</a> for details.</li></ul></li></ul> <h4 id="specific-changes"><a href="#specific-changes" class="header-anchor">#</a> Specific changes</h4> <ul><li><code>elements.rectangle</code> is now <code>elements.bar</code></li> <li><code>hover.animationDuration</code> is now configured in <code>animation.active.duration</code></li> <li><code>responsiveAnimationDuration</code> is now configured in <code>animation.resize.duration</code></li> <li>Polar area <code>elements.arc.angle</code> is now configured in degrees instead of radians.</li> <li>Polar area <code>startAngle</code> option is now consistent with <code>Radar</code>, 0 is at top and value is in degrees. Default is changed from <code>-½π</code> to  <code>0</code>.</li> <li>Doughnut <code>rotation</code> option is now in degrees and 0 is at top. Default is changed from <code>-½π</code> to  <code>0</code>.</li> <li>Doughnut <code>circumference</code> option is now in degrees. Default is changed from <code>2π</code> to <code>360</code>.</li> <li>Doughnut <code>cutoutPercentage</code> was renamed to <code>cutout</code>and accepts pixels as number and percent as string ending with <code>%</code>.</li> <li><code>scale</code> option was removed in favor of <code>options.scales.r</code> (or any other scale id, with <code>axis: 'r'</code>)</li> <li><code>scales.[x/y]Axes</code> arrays were removed. Scales are now configured directly to <code>options.scales</code> object with the object key being the scale Id.</li> <li><code>scales.[x/y]Axes.barPercentage</code> was moved to dataset option <code>barPercentage</code></li> <li><code>scales.[x/y]Axes.barThickness</code> was moved to dataset option <code>barThickness</code></li> <li><code>scales.[x/y]Axes.categoryPercentage</code> was moved to dataset option <code>categoryPercentage</code></li> <li><code>scales.[x/y]Axes.maxBarThickness</code> was moved to dataset option <code>maxBarThickness</code></li> <li><code>scales.[x/y]Axes.minBarLength</code> was moved to dataset option <code>minBarLength</code></li> <li><code>scales.[x/y]Axes.scaleLabel</code> was renamed to <code>scales[id].title</code></li> <li><code>scales.[x/y]Axes.scaleLabel.labelString</code> was renamed to <code>scales[id].title.text</code></li> <li><code>scales.[x/y]Axes.ticks.beginAtZero</code> was renamed to <code>scales[id].beginAtZero</code></li> <li><code>scales.[x/y]Axes.ticks.max</code> was renamed to <code>scales[id].max</code></li> <li><code>scales.[x/y]Axes.ticks.min</code> was renamed to <code>scales[id].min</code></li> <li><code>scales.[x/y]Axes.ticks.reverse</code> was renamed to <code>scales[id].reverse</code></li> <li><code>scales.[x/y]Axes.ticks.suggestedMax</code> was renamed to <code>scales[id].suggestedMax</code></li> <li><code>scales.[x/y]Axes.ticks.suggestedMin</code> was renamed to <code>scales[id].suggestedMin</code></li> <li><code>scales.[x/y]Axes.ticks.unitStepSize</code> was removed. Use <code>scales[id].ticks.stepSize</code></li> <li><code>scales.[x/y]Axes.ticks.userCallback</code> was renamed to <code>scales[id].ticks.callback</code></li> <li><code>scales.[x/y]Axes.time.format</code> was renamed to <code>scales[id].time.parser</code></li> <li><code>scales.[x/y]Axes.time.max</code> was renamed to <code>scales[id].max</code></li> <li><code>scales.[x/y]Axes.time.min</code> was renamed to <code>scales[id].min</code></li> <li><code>scales.[x/y]Axes.zeroLine*</code> options of axes were removed. Use scriptable scale options instead.</li> <li>The dataset option <code>steppedLine</code> was removed. Use <code>stepped</code></li> <li>The chart option <code>showLines</code> was renamed to <code>showLine</code> to match the dataset option.</li> <li>The chart option <code>startAngle</code> was moved to <code>radial</code> scale options.</li> <li>To override the platform class used in a chart instance, pass <code>platform: PlatformClass</code> in the config object. Note that the class should be passed, not an instance of the class.</li> <li><code>aspectRatio</code> defaults to 1 for doughnut, pie, polarArea, and radar charts</li> <li><code>TimeScale</code> does not read <code>t</code> from object data by default anymore. The default property is <code>x</code> or <code>y</code>, depending on the orientation. See <a href="/docs/3.9.1/general/data-structures.html">data structures</a> for details on how to change the default.</li> <li><code>tooltips</code> namespace was renamed to <code>tooltip</code> to match the plugin name</li> <li><code>legend</code>, <code>title</code> and <code>tooltip</code> namespaces were moved from <code>options</code> to <code>options.plugins</code>.</li> <li><code>tooltips.custom</code> was renamed to <code>plugins.tooltip.external</code></li></ul> <h4 id="defaults"><a href="#defaults" class="header-anchor">#</a> Defaults</h4> <ul><li><code>global</code> namespace was removed from <code>defaults</code>. So <code>Chart.defaults.global</code> is now <code>Chart.defaults</code></li> <li>Dataset controller defaults were relocate to <code>overrides</code>. For example <code>Chart.defaults.line</code> is now <code>Chart.overrides.line</code></li> <li><code>default</code> prefix was removed from defaults. For example <code>Chart.defaults.global.defaultColor</code> is now <code>Chart.defaults.color</code></li> <li><code>defaultColor</code> was split to <code>color</code>, <code>borderColor</code> and <code>backgroundColor</code></li> <li><code>defaultFontColor</code> was renamed to <code>color</code></li> <li><code>defaultFontFamily</code> was renamed to <code>font.family</code></li> <li><code>defaultFontSize</code> was renamed to <code>font.size</code></li> <li><code>defaultFontStyle</code> was renamed to <code>font.style</code></li> <li><code>defaultLineHeight</code> was renamed to <code>font.lineHeight</code></li> <li>Horizontal Bar default tooltip mode was changed from <code>'index'</code> to <code>'nearest'</code> to match vertical bar charts</li> <li><code>legend</code>, <code>title</code> and <code>tooltip</code> namespaces were moved from <code>Chart.defaults</code> to <code>Chart.defaults.plugins</code>.</li> <li><code>elements.line.fill</code> default changed from <code>true</code> to <code>false</code>.</li> <li>Line charts no longer override the default <code>interaction</code> mode. Default is changed from <code>'index'</code> to <code>'nearest'</code>.</li></ul> <h4 id="scales"><a href="#scales" class="header-anchor">#</a> Scales</h4> <p>The configuration options for scales is the largest change in v3. The <code>xAxes</code> and <code>yAxes</code> arrays were removed and axis options are individual scales now keyed by scale ID.</p> <p>The v2 configuration below is shown with it's new v3 configuration</p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">scales</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">xAxes</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token punctuation">{</span>
      <span class="token literal-property property">id</span><span class="token operator">:</span> <span class="token string">'x'</span><span class="token punctuation">,</span>
      <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'time'</span><span class="token punctuation">,</span>
      <span class="token literal-property property">display</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
      <span class="token literal-property property">title</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">display</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
        <span class="token literal-property property">text</span><span class="token operator">:</span> <span class="token string">'Date'</span>
      <span class="token punctuation">}</span><span class="token punctuation">,</span>
      <span class="token literal-property property">ticks</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">major</span><span class="token operator">:</span> <span class="token punctuation">{</span>
          <span class="token literal-property property">enabled</span><span class="token operator">:</span> <span class="token boolean">true</span>
        <span class="token punctuation">}</span><span class="token punctuation">,</span>
        <span class="token function-variable function">font</span><span class="token operator">:</span> <span class="token keyword">function</span><span class="token punctuation">(</span><span class="token parameter">context</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
          <span class="token keyword">if</span> <span class="token punctuation">(</span>context<span class="token punctuation">.</span>tick <span class="token operator">&amp;&amp;</span> context<span class="token punctuation">.</span>tick<span class="token punctuation">.</span>major<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword">return</span> <span class="token punctuation">{</span>
              <span class="token literal-property property">weight</span><span class="token operator">:</span> <span class="token string">'bold'</span><span class="token punctuation">,</span>
              <span class="token literal-property property">color</span><span class="token operator">:</span> <span class="token string">'#FF0000'</span>
            <span class="token punctuation">}</span><span class="token punctuation">;</span>
          <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token literal-property property">yAxes</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token punctuation">{</span>
      <span class="token literal-property property">id</span><span class="token operator">:</span> <span class="token string">'y'</span><span class="token punctuation">,</span>
      <span class="token literal-property property">display</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
      <span class="token literal-property property">title</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">display</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
        <span class="token literal-property property">text</span><span class="token operator">:</span> <span class="token string">'value'</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span><span class="token punctuation">]</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre></div><p>And now, in v3:</p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
  <span class="token literal-property property">scales</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">x</span><span class="token operator">:</span> <span class="token punctuation">{</span>
      <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'time'</span><span class="token punctuation">,</span>
      <span class="token literal-property property">display</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
      <span class="token literal-property property">title</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">display</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
        <span class="token literal-property property">text</span><span class="token operator">:</span> <span class="token string">'Date'</span>
      <span class="token punctuation">}</span><span class="token punctuation">,</span>
      <span class="token literal-property property">ticks</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">major</span><span class="token operator">:</span> <span class="token punctuation">{</span>
          <span class="token literal-property property">enabled</span><span class="token operator">:</span> <span class="token boolean">true</span>
        <span class="token punctuation">}</span><span class="token punctuation">,</span>
        <span class="token function-variable function">color</span><span class="token operator">:</span> <span class="token punctuation">(</span><span class="token parameter">context</span><span class="token punctuation">)</span> <span class="token operator">=&gt;</span> context<span class="token punctuation">.</span>tick <span class="token operator">&amp;&amp;</span> context<span class="token punctuation">.</span>tick<span class="token punctuation">.</span>major <span class="token operator">&amp;&amp;</span> <span class="token string">'#FF0000'</span><span class="token punctuation">,</span>
        <span class="token function-variable function">font</span><span class="token operator">:</span> <span class="token keyword">function</span><span class="token punctuation">(</span><span class="token parameter">context</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
          <span class="token keyword">if</span> <span class="token punctuation">(</span>context<span class="token punctuation">.</span>tick <span class="token operator">&amp;&amp;</span> context<span class="token punctuation">.</span>tick<span class="token punctuation">.</span>major<span class="token punctuation">)</span> <span class="token punctuation">{</span>
            <span class="token keyword">return</span> <span class="token punctuation">{</span>
              <span class="token literal-property property">weight</span><span class="token operator">:</span> <span class="token string">'bold'</span>
            <span class="token punctuation">}</span><span class="token punctuation">;</span>
          <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span><span class="token punctuation">,</span>
    <span class="token literal-property property">y</span><span class="token operator">:</span> <span class="token punctuation">{</span>
      <span class="token literal-property property">display</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
      <span class="token literal-property property">title</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">display</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
        <span class="token literal-property property">text</span><span class="token operator">:</span> <span class="token string">'value'</span>
      <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre></div><ul><li>The time scale option <code>distribution: 'series'</code> was removed and a new scale type <code>timeseries</code> was introduced in its place</li> <li>In the time scale, <code>autoSkip</code> is now enabled by default for consistency with the other scales</li></ul> <h4 id="animations"><a href="#animations" class="header-anchor">#</a> Animations</h4> <p>Animation system was completely rewritten in Chart.js v3. Each property can now be animated separately. Please see <a href="/docs/3.9.1/configuration/animations.html">animations</a> docs for details.</p> <h4 id="customizability"><a href="#customizability" class="header-anchor">#</a> Customizability</h4> <ul><li><code>custom</code> attribute of elements was removed. Please use scriptable options</li> <li>The <code>hover</code> property of scriptable options <code>context</code> object was renamed to <code>active</code> to align it with the datalabels plugin.</li></ul> <h4 id="interactions"><a href="#interactions" class="header-anchor">#</a> Interactions</h4> <ul><li>To allow DRY configuration, a root options scope for common interaction options was added. <code>options.hover</code> and <code>options.plugins.tooltip</code> now both extend from <code>options.interaction</code>. Defaults are defined at <code>defaults.interaction</code> level, so by default hover and tooltip interactions share the same mode etc.</li> <li><code>interactions</code> are now limited to the chart area + allowed overflow</li> <li><code>{mode: 'label'}</code> was replaced with <code>{mode: 'index'}</code></li> <li><code>{mode: 'single'}</code> was replaced with <code>{mode: 'nearest', intersect: true}</code></li> <li><code>modes['X-axis']</code> was replaced with <code>{mode: 'index', intersect: false}</code></li> <li><code>options.onClick</code> is now limited to the chart area</li> <li><code>options.onClick</code> and <code>options.onHover</code> now receive the <code>chart</code> instance as a 3rd argument</li> <li><code>options.onHover</code> now receives a wrapped <code>event</code> as the first parameter. The previous first parameter value is accessible via <code>event.native</code>.</li> <li><code>options.hover.onHover</code> was removed, use <code>options.onHover</code>.</li></ul> <h4 id="ticks"><a href="#ticks" class="header-anchor">#</a> Ticks</h4> <ul><li><code>options.gridLines</code> was renamed to <code>options.grid</code></li> <li><code>options.gridLines.offsetGridLines</code> was renamed to <code>options.grid.offset</code>.</li> <li><code>options.gridLines.tickMarkLength</code> was renamed to <code>options.grid.tickLength</code>.</li> <li><code>options.ticks.fixedStepSize</code> is no longer used. Use <code>options.ticks.stepSize</code>.</li> <li><code>options.ticks.major</code> and <code>options.ticks.minor</code> were replaced with scriptable options for tick fonts.</li> <li><code>Chart.Ticks.formatters.linear</code> was renamed to <code>Chart.Ticks.formatters.numeric</code>.</li> <li><code>options.ticks.backdropPaddingX</code> and <code>options.ticks.backdropPaddingY</code> were replaced with <code>options.ticks.backdropPadding</code> in the radial linear scale.</li></ul> <h4 id="tooltip"><a href="#tooltip" class="header-anchor">#</a> Tooltip</h4> <ul><li><code>xLabel</code> and <code>yLabel</code> were removed. Please use <code>label</code> and <code>formattedValue</code></li> <li>The <code>filter</code> option will now be passed additional parameters when called and should have the method signature <code>function(tooltipItem, index, tooltipItems, data)</code></li> <li>The <code>custom</code> callback now takes a context object that has <code>tooltip</code> and <code>chart</code> properties</li> <li>All properties of tooltip model related to the tooltip options have been moved to reside within the <code>options</code> property.</li> <li>The callbacks no longer are given a <code>data</code> parameter. The tooltip item parameter contains the chart and dataset instead</li> <li>The tooltip item's <code>index</code> parameter was renamed to <code>dataIndex</code> and <code>value</code> was renamed to <code>formattedValue</code></li> <li>The <code>xPadding</code> and <code>yPadding</code> options were merged into a single <code>padding</code> object</li></ul> <h2 id="developer-migration"><a href="#developer-migration" class="header-anchor">#</a> Developer migration</h2> <p>While the end-user migration for Chart.js 3 is fairly straight-forward, the developer migration can be more complicated. Please reach out for help in the #dev <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer">Slack<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a> channel if tips on migrating would be helpful.</p> <p>Some of the biggest things that have changed:</p> <ul><li>There is a completely rewritten and more performant animation system.
<ul><li><code>Element._model</code> and <code>Element._view</code> are no longer used and properties are now set directly on the elements. You will have to use the method <code>getProps</code> to access these properties inside most methods such as <code>inXRange</code>/<code>inYRange</code> and <code>getCenterPoint</code>. Please take a look at <a href="https://github.com/chartjs/Chart.js/tree/master/src/elements" target="_blank" rel="noopener noreferrer">the Chart.js-provided elements<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a> for examples.</li> <li>When building the elements in a controller, it's now suggested to call <code>updateElement</code> to provide the element properties. There are also methods such as <code>getSharedOptions</code> and <code>includeOptions</code> that have been added to skip redundant computation. Please take a look at <a href="https://github.com/chartjs/Chart.js/tree/master/src/controllers" target="_blank" rel="noopener noreferrer">the Chart.js-provided controllers<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a> for examples.</li></ul></li> <li>Scales introduced a new parsing API. This API takes user data and converts it into a more standard format. E.g. it allows users to provide numeric data as a <code>string</code> and converts it to a <code>number</code> where necessary. Previously this was done on the fly as charts were rendered. Now it's done up front with the ability to skip it for better performance if users provide data in the correct format. If you're using standard data format like <code>x</code>/<code>y</code> you may not need to do anything. If you're using a custom data format you will have to override some of the parse methods in <code>core.datasetController.js</code>. An example can be found in <a href="https://github.com/chartjs/chartjs-chart-financial" target="_blank" rel="noopener noreferrer">chartjs-chart-financial<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a>, which uses an <code>{o, h, l, c}</code> data format.</li></ul> <p>A few changes were made to controllers that are more straight-forward, but will affect all controllers:</p> <ul><li>Options:
<ul><li><code>global</code> was removed from the defaults namespace as it was unnecessary and sometimes inconsistent</li> <li>Dataset defaults are now under the chart type options instead of vice-versa. This was not able to be done when introduced in 2.x for backwards compatibility. Fixing it removes the biggest stumbling block that new chart developers encountered</li> <li>Scale default options need to be updated as described in the end user migration section (e.g. <code>x</code> instead of <code>xAxes</code> and <code>y</code> instead of <code>yAxes</code>)</li></ul></li> <li><code>updateElement</code> was changed to <code>updateElements</code> and has a new method signature as described below. This provides performance enhancements such as allowing easier reuse of computations that are common to all elements and reducing the number of function calls</li></ul> <h3 id="removed"><a href="#removed" class="header-anchor">#</a> Removed</h3> <p>The following properties and methods were removed:</p> <h4 id="removed-from-chart"><a href="#removed-from-chart" class="header-anchor">#</a> Removed from Chart</h4> <ul><li><code>Chart.animationService</code></li> <li><code>Chart.active</code></li> <li><code>Chart.borderWidth</code></li> <li><code>Chart.chart.chart</code></li> <li><code>Chart.Bar</code>. New charts are created via <code>new Chart</code> and providing the appropriate <code>type</code> parameter</li> <li><code>Chart.Bubble</code>. New charts are created via <code>new Chart</code> and providing the appropriate <code>type</code> parameter</li> <li><code>Chart.Chart</code></li> <li><code>Chart.Controller</code></li> <li><code>Chart.Doughnut</code>. New charts are created via <code>new Chart</code> and providing the appropriate <code>type</code> parameter</li> <li><code>Chart.innerRadius</code> now lives on doughnut, pie, and polarArea controllers</li> <li><code>Chart.lastActive</code></li> <li><code>Chart.Legend</code> was moved to <code>Chart.plugins.legend._element</code> and made private</li> <li><code>Chart.Line</code>. New charts are created via <code>new Chart</code> and providing the appropriate <code>type</code> parameter</li> <li><code>Chart.LinearScaleBase</code> now must be imported and cannot be accessed off the <code>Chart</code> object</li> <li><code>Chart.offsetX</code></li> <li><code>Chart.offsetY</code></li> <li><code>Chart.outerRadius</code> now lives on doughnut, pie, and polarArea controllers</li> <li><code>Chart.plugins</code> was replaced with <code>Chart.registry</code>. Plugin defaults are now in <code>Chart.defaults.plugins[id]</code>.</li> <li><code>Chart.plugins.register</code> was replaced by <code>Chart.register</code>.</li> <li><code>Chart.PolarArea</code>. New charts are created via <code>new Chart</code> and providing the appropriate <code>type</code> parameter</li> <li><code>Chart.prototype.generateLegend</code></li> <li><code>Chart.platform</code>. It only contained <code>disableCSSInjection</code>. CSS is never injected in v3.</li> <li><code>Chart.PluginBase</code></li> <li><code>Chart.Radar</code>. New charts are created via <code>new Chart</code> and providing the appropriate <code>type</code> parameter</li> <li><code>Chart.radiusLength</code></li> <li><code>Chart.scaleService</code> was replaced with <code>Chart.registry</code>. Scale defaults are now in <code>Chart.defaults.scales[type]</code>.</li> <li><code>Chart.Scatter</code>. New charts are created via <code>new Chart</code> and providing the appropriate <code>type</code> parameter</li> <li><code>Chart.types</code></li> <li><code>Chart.Title</code> was moved to <code>Chart.plugins.title._element</code> and made private</li> <li><code>Chart.Tooltip</code> is now provided by the tooltip plugin. The positioners can be accessed from <code>tooltipPlugin.positioners</code></li> <li><code>ILayoutItem.minSize</code></li></ul> <h4 id="removed-from-dataset-controllers"><a href="#removed-from-dataset-controllers" class="header-anchor">#</a> Removed from Dataset Controllers</h4> <ul><li><code>BarController.getDatasetMeta().bar</code></li> <li><code>DatasetController.addElementAndReset</code></li> <li><code>DatasetController.createMetaData</code></li> <li><code>DatasetController.createMetaDataset</code></li> <li><code>DoughnutController.getRingIndex</code></li></ul> <h4 id="removed-from-elements"><a href="#removed-from-elements" class="header-anchor">#</a> Removed from Elements</h4> <ul><li><code>Element.getArea</code></li> <li><code>Element.height</code></li> <li><code>Element.hidden</code> was replaced by chart level status, usable with <code>getDataVisibility(index)</code> / <code>toggleDataVisibility(index)</code></li> <li><code>Element.initialize</code></li> <li><code>Element.inLabelRange</code></li> <li><code>Line.calculatePointY</code></li></ul> <h4 id="removed-from-helpers"><a href="#removed-from-helpers" class="header-anchor">#</a> Removed from Helpers</h4> <ul><li><code>helpers.addEvent</code></li> <li><code>helpers.aliasPixel</code></li> <li><code>helpers.arrayEquals</code></li> <li><code>helpers.configMerge</code></li> <li><code>helpers.findIndex</code></li> <li><code>helpers.findNextWhere</code></li> <li><code>helpers.findPreviousWhere</code></li> <li><code>helpers.extend</code>. Use <code>Object.assign</code> instead</li> <li><code>helpers.getValueAtIndexOrDefault</code>. Use <code>helpers.resolve</code> instead.</li> <li><code>helpers.indexOf</code></li> <li><code>helpers.lineTo</code></li> <li><code>helpers.longestText</code> was made private</li> <li><code>helpers.max</code></li> <li><code>helpers.measureText</code> was made private</li> <li><code>helpers.min</code></li> <li><code>helpers.nextItem</code></li> <li><code>helpers.niceNum</code></li> <li><code>helpers.numberOfLabelLines</code></li> <li><code>helpers.previousItem</code></li> <li><code>helpers.removeEvent</code></li> <li><code>helpers.roundedRect</code></li> <li><code>helpers.scaleMerge</code></li> <li><code>helpers.where</code></li></ul> <h4 id="removed-from-layout"><a href="#removed-from-layout" class="header-anchor">#</a> Removed from Layout</h4> <ul><li><code>Layout.defaults</code></li></ul> <h4 id="removed-from-scales"><a href="#removed-from-scales" class="header-anchor">#</a> Removed from Scales</h4> <ul><li><code>LinearScaleBase.handleDirectionalChanges</code></li> <li><code>LogarithmicScale.minNotZero</code></li> <li><code>Scale.getRightValue</code></li> <li><code>Scale.longestLabelWidth</code></li> <li><code>Scale.longestTextCache</code> is now private</li> <li><code>Scale.margins</code> is now private</li> <li><code>Scale.mergeTicksOptions</code></li> <li><code>Scale.ticksAsNumbers</code></li> <li><code>Scale.tickValues</code> is now private</li> <li><code>TimeScale.getLabelCapacity</code> is now private</li> <li><code>TimeScale.tickFormatFunction</code> is now private</li></ul> <h4 id="removed-from-plugins-legend-title-and-tooltip"><a href="#removed-from-plugins-legend-title-and-tooltip" class="header-anchor">#</a> Removed from Plugins (Legend, Title, and Tooltip)</h4> <ul><li><code>IPlugin.afterScaleUpdate</code>. Use <code>afterLayout</code> instead</li> <li><code>Legend.margins</code> is now private</li> <li>Legend <code>onClick</code>, <code>onHover</code>, and <code>onLeave</code> options now receive the legend as the 3rd argument in addition to implicitly via <code>this</code></li> <li>Legend <code>onClick</code>, <code>onHover</code>, and <code>onLeave</code> options now receive a wrapped <code>event</code> as the first parameter. The previous first parameter value is accessible via <code>event.native</code>.</li> <li><code>Title.margins</code> is now private</li> <li>The tooltip item's <code>x</code> and <code>y</code> attributes were replaced by <code>element</code>. You can use <code>element.x</code> and <code>element.y</code> or <code>element.tooltipPosition()</code> instead.</li></ul> <h4 id="removal-of-public-apis"><a href="#removal-of-public-apis" class="header-anchor">#</a> Removal of Public APIs</h4> <p>The following public APIs were removed.</p> <ul><li><code>getElementAtEvent</code> is replaced with <code>chart.getElementsAtEventForMode(e, 'nearest', { intersect: true }, false)</code></li> <li><code>getElementsAtEvent</code> is replaced with <code>chart.getElementsAtEventForMode(e, 'index', { intersect: true }, false)</code></li> <li><code>getElementsAtXAxis</code> is replaced with <code>chart.getElementsAtEventForMode(e, 'index', { intersect: false }, false)</code></li> <li><code>getDatasetAtEvent</code> is replaced with <code>chart.getElementsAtEventForMode(e, 'dataset', { intersect: true }, false)</code></li></ul> <h4 id="removal-of-private-apis"><a href="#removal-of-private-apis" class="header-anchor">#</a> Removal of private APIs</h4> <p>The following private APIs were removed.</p> <ul><li><code>Chart._bufferedRender</code></li> <li><code>Chart._updating</code></li> <li><code>Chart.data.datasets[datasetIndex]._meta</code></li> <li><code>DatasetController._getIndexScaleId</code></li> <li><code>DatasetController._getIndexScale</code></li> <li><code>DatasetController._getValueScaleId</code></li> <li><code>DatasetController._getValueScale</code></li> <li><code>Element._ctx</code></li> <li><code>Element._model</code></li> <li><code>Element._view</code></li> <li><code>LogarithmicScale._valueOffset</code></li> <li><code>TimeScale.getPixelForOffset</code></li> <li><code>TimeScale.getLabelWidth</code></li> <li><code>Tooltip._lastActive</code></li></ul> <h3 id="renamed"><a href="#renamed" class="header-anchor">#</a> Renamed</h3> <p>The following properties were renamed during v3 development:</p> <ul><li><code>Chart.Animation.animationObject</code> was renamed to <code>Chart.Animation</code></li> <li><code>Chart.Animation.chartInstance</code> was renamed to <code>Chart.Animation.chart</code></li> <li><code>Chart.canvasHelpers</code> was merged with <code>Chart.helpers</code></li> <li><code>Chart.elements.Arc</code> was renamed to <code>Chart.elements.ArcElement</code></li> <li><code>Chart.elements.Line</code> was renamed to <code>Chart.elements.LineElement</code></li> <li><code>Chart.elements.Point</code> was renamed to <code>Chart.elements.PointElement</code></li> <li><code>Chart.elements.Rectangle</code> was renamed to <code>Chart.elements.BarElement</code></li> <li><code>Chart.layoutService</code> was renamed to <code>Chart.layouts</code></li> <li><code>Chart.pluginService</code> was renamed to <code>Chart.plugins</code></li> <li><code>helpers.callCallback</code> was renamed to <code>helpers.callback</code></li> <li><code>helpers.drawRoundedRectangle</code> was renamed to <code>helpers.roundedRect</code></li> <li><code>helpers.getValueOrDefault</code> was renamed to <code>helpers.valueOrDefault</code></li> <li><code>LayoutItem.fullWidth</code> was renamed to <code>LayoutItem.fullSize</code></li> <li><code>Point.controlPointPreviousX</code> was renamed to <code>Point.cp1x</code></li> <li><code>Point.controlPointPreviousY</code> was renamed to <code>Point.cp1y</code></li> <li><code>Point.controlPointNextX</code> was renamed to <code>Point.cp2x</code></li> <li><code>Point.controlPointNextY</code> was renamed to <code>Point.cp2y</code></li> <li><code>Scale.calculateTickRotation</code> was renamed to <code>Scale.calculateLabelRotation</code></li> <li><code>Tooltip.options.legendColorBackgroupd</code> was renamed to <code>Tooltip.options.multiKeyBackground</code></li></ul> <h4 id="renamed-private-apis"><a href="#renamed-private-apis" class="header-anchor">#</a> Renamed private APIs</h4> <p>The private APIs listed below were renamed:</p> <ul><li><code>BarController.calculateBarIndexPixels</code> was renamed to <code>BarController._calculateBarIndexPixels</code></li> <li><code>BarController.calculateBarValuePixels</code> was renamed to <code>BarController._calculateBarValuePixels</code></li> <li><code>BarController.getStackCount</code> was renamed to <code>BarController._getStackCount</code></li> <li><code>BarController.getStackIndex</code> was renamed to <code>BarController._getStackIndex</code></li> <li><code>BarController.getRuler</code> was renamed to <code>BarController._getRuler</code></li> <li><code>Chart.destroyDatasetMeta</code> was renamed to <code>Chart._destroyDatasetMeta</code></li> <li><code>Chart.drawDataset</code> was renamed to <code>Chart._drawDataset</code></li> <li><code>Chart.drawDatasets</code> was renamed to <code>Chart._drawDatasets</code></li> <li><code>Chart.eventHandler</code> was renamed to <code>Chart._eventHandler</code></li> <li><code>Chart.handleEvent</code> was renamed to <code>Chart._handleEvent</code></li> <li><code>Chart.initialize</code> was renamed to <code>Chart._initialize</code></li> <li><code>Chart.resetElements</code> was renamed to <code>Chart._resetElements</code></li> <li><code>Chart.unbindEvents</code> was renamed to <code>Chart._unbindEvents</code></li> <li><code>Chart.updateDataset</code> was renamed to <code>Chart._updateDataset</code></li> <li><code>Chart.updateDatasets</code> was renamed to <code>Chart._updateDatasets</code></li> <li><code>Chart.updateLayout</code> was renamed to <code>Chart._updateLayout</code></li> <li><code>DatasetController.destroy</code> was renamed to <code>DatasetController._destroy</code></li> <li><code>DatasetController.insertElements</code> was renamed to <code>DatasetController._insertElements</code></li> <li><code>DatasetController.onDataPop</code> was renamed to <code>DatasetController._onDataPop</code></li> <li><code>DatasetController.onDataPush</code> was renamed to <code>DatasetController._onDataPush</code></li> <li><code>DatasetController.onDataShift</code> was renamed to <code>DatasetController._onDataShift</code></li> <li><code>DatasetController.onDataSplice</code> was renamed to <code>DatasetController._onDataSplice</code></li> <li><code>DatasetController.onDataUnshift</code> was renamed to <code>DatasetController._onDataUnshift</code></li> <li><code>DatasetController.removeElements</code> was renamed to <code>DatasetController._removeElements</code></li> <li><code>DatasetController.resyncElements</code> was renamed to <code>DatasetController._resyncElements</code></li> <li><code>LayoutItem.isFullWidth</code> was renamed to <code>LayoutItem.isFullSize</code></li> <li><code>RadialLinearScale.setReductions</code> was renamed to <code>RadialLinearScale._setReductions</code></li> <li><code>RadialLinearScale.pointLabels</code> was renamed to <code>RadialLinearScale._pointLabels</code></li> <li><code>Scale.handleMargins</code> was renamed to <code>Scale._handleMargins</code></li></ul> <h3 id="changed"><a href="#changed" class="header-anchor">#</a> Changed</h3> <p>The APIs listed in this section have changed in signature or behaviour from version 2.</p> <h4 id="changed-in-scales"><a href="#changed-in-scales" class="header-anchor">#</a> Changed in Scales</h4> <ul><li><code>Scale.getLabelForIndex</code> was replaced by <code>scale.getLabelForValue</code></li> <li><code>Scale.getPixelForValue</code> now only requires one parameter. For the <code>TimeScale</code> that parameter must be millis since the epoch. As a performance optimization, it may take an optional second parameter, giving the index of the data point.</li></ul> <h5 id="changed-in-ticks"><a href="#changed-in-ticks" class="header-anchor">#</a> Changed in Ticks</h5> <ul><li><code>Scale.afterBuildTicks</code> now has no parameters like the other callbacks</li> <li><code>Scale.buildTicks</code> is now expected to return tick objects</li> <li><code>Scale.convertTicksToLabels</code> was renamed to <code>generateTickLabels</code>. It is now expected to set the label property on the ticks given as input</li> <li><code>Scale.ticks</code> now contains objects instead of strings</li> <li>When the <code>autoSkip</code> option is enabled, <code>Scale.ticks</code> now contains only the non-skipped ticks instead of all ticks.</li> <li>Ticks are now always generated in monotonically increasing order</li></ul> <h5 id="changed-in-time-scale"><a href="#changed-in-time-scale" class="header-anchor">#</a> Changed in Time Scale</h5> <ul><li><code>getValueForPixel</code> now returns milliseconds since the epoch</li></ul> <h4 id="changed-in-controllers"><a href="#changed-in-controllers" class="header-anchor">#</a> Changed in Controllers</h4> <h5 id="core-controller"><a href="#core-controller" class="header-anchor">#</a> Core Controller</h5> <ul><li>The first parameter to <code>updateHoverStyle</code> is now an array of objects containing the <code>element</code>, <code>datasetIndex</code>, and <code>index</code></li> <li>The signature or <code>resize</code> changed, the first <code>silent</code> parameter was removed.</li></ul> <h5 id="dataset-controllers"><a href="#dataset-controllers" class="header-anchor">#</a> Dataset Controllers</h5> <ul><li><code>updateElement</code> was replaced with <code>updateElements</code> now taking the elements to update, the <code>start</code> index, <code>count</code>, and <code>mode</code></li> <li><code>setHoverStyle</code> and <code>removeHoverStyle</code> now additionally take the <code>datasetIndex</code> and <code>index</code></li></ul> <h4 id="changed-in-interactions"><a href="#changed-in-interactions" class="header-anchor">#</a> Changed in Interactions</h4> <ul><li>Interaction mode methods now return an array of objects containing the <code>element</code>, <code>datasetIndex</code>, and <code>index</code></li></ul> <h4 id="changed-in-layout"><a href="#changed-in-layout" class="header-anchor">#</a> Changed in Layout</h4> <ul><li><code>ILayoutItem.update</code> no longer has a return value</li></ul> <h4 id="changed-in-helpers"><a href="#changed-in-helpers" class="header-anchor">#</a> Changed in Helpers</h4> <p>All helpers are now exposed in a flat hierarchy, e.g., <code>Chart.helpers.canvas.clipArea</code> -&gt; <code>Chart.helpers.clipArea</code></p> <h5 id="canvas-helper"><a href="#canvas-helper" class="header-anchor">#</a> Canvas Helper</h5> <ul><li>The second parameter to <code>drawPoint</code> is now the full options object, so <code>style</code>, <code>rotation</code>, and <code>radius</code> are no longer passed explicitly</li> <li><code>helpers.getMaximumHeight</code> was replaced by <code>helpers.dom.getMaximumSize</code></li> <li><code>helpers.getMaximumWidth</code> was replaced by <code>helpers.dom.getMaximumSize</code></li> <li><code>helpers.clear</code> was renamed to <code>helpers.clearCanvas</code> and now takes <code>canvas</code> and optionally <code>ctx</code> as parameter(s).</li> <li><code>helpers.retinaScale</code> accepts optional third parameter <code>forceStyle</code>, which forces overriding current canvas style. <code>forceRatio</code> no longer falls back to <code>window.devicePixelRatio</code>, instead it defaults to <code>1</code>.</li></ul> <h4 id="changed-in-platform"><a href="#changed-in-platform" class="header-anchor">#</a> Changed in Platform</h4> <ul><li><code>Chart.platform</code> is no longer the platform object used by charts. Every chart instance now has a separate platform instance.</li> <li><code>Chart.platforms</code> is an object that contains two usable platform classes, <code>BasicPlatform</code> and <code>DomPlatform</code>. It also contains <code>BasePlatform</code>, a class that all platforms must extend from.</li> <li>If the canvas passed in is an instance of <code>OffscreenCanvas</code>, the <code>BasicPlatform</code> is automatically used.</li> <li><code>isAttached</code> method was added to platform.</li></ul> <h4 id="changed-in-iplugin-interface"><a href="#changed-in-iplugin-interface" class="header-anchor">#</a> Changed in IPlugin interface</h4> <ul><li>All plugin hooks have unified signature with 3 arguments: <code>chart</code>, <code>args</code> and <code>options</code>. This means change in signature for these hooks: <code>beforeInit</code>, <code>afterInit</code>, <code>reset</code>, <code>beforeLayout</code>, <code>afterLayout</code>, <code>beforeRender</code>, <code>afterRender</code>, <code>beforeDraw</code>, <code>afterDraw</code>, <code>beforeDatasetsDraw</code>, <code>afterDatasetsDraw</code>, <code>beforeEvent</code>, <code>afterEvent</code>, <code>resize</code>, <code>destroy</code>.</li> <li><code>afterDatasetsUpdate</code>, <code>afterUpdate</code>, <code>beforeDatasetsUpdate</code>, and <code>beforeUpdate</code> now receive <code>args</code> object as second argument. <code>options</code> argument is always the last and thus was moved from 2nd to 3rd place.</li> <li><code>afterEvent</code> and <code>beforeEvent</code> now receive a wrapped <code>event</code> as the <code>event</code> property of the second argument. The native event is available via <code>args.event.native</code>.</li> <li>Initial <code>resize</code> is no longer silent. Meaning that <code>resize</code> event can fire between <code>beforeInit</code> and <code>afterInit</code></li> <li>New hooks: <code>install</code>, <code>start</code>, <code>stop</code>, and <code>uninstall</code></li> <li><code>afterEvent</code> should notify about changes that need a render by setting <code>args.changed</code> to true. Because the <code>args</code> are shared with all plugins, it should only be set to true and not false.</li></ul></div> <footer class="page-edit"><!----> <div class="last-updated"><span class="prefix">Last Updated:</span> <span class="time">8/3/2022, 12:46:38 PM</span></div></footer> <div class="page-nav"><p class="inner"><span class="prev">
      ←
      <a href="/docs/3.9.1/getting-started/usage.html" class="prev">
        Usage
      </a></span> <span class="next"><a href="/docs/3.9.1/general/accessibility.html">
        Accessibility
      </a>
      →
    </span></p></div> </main></div><div class="global-ui"></div></div>
    <script src="/docs/3.9.1/assets/js/app.7e0dc8c8.js" defer></script><script src="/docs/3.9.1/assets/js/2.3e2307f3.js" defer></script><script src="/docs/3.9.1/assets/js/183.22b2258c.js" defer></script>
  </body>
</html>
