<!DOCTYPE html>
<html lang="en-US">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <title>Tooltip | Chart.js</title>
    <meta name="generator" content="VuePress 1.8.3">
    <link rel="icon" href="/docs/3.9.1/favicon.ico">
    <meta name="description" content="Open source HTML5 Charts for your website">
    <link rel="preload" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css" as="style"><link rel="preload" href="/docs/3.9.1/assets/js/app.7e0dc8c8.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/2.3e2307f3.js" as="script"><link rel="preload" href="/docs/3.9.1/assets/js/164.7f8d8643.js" as="script"><link rel="prefetch" href="/docs/3.9.1/assets/js/10.edbecfa9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/100.f7a12d8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/101.15c56dbb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/102.33b746ea.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/103.850a4486.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/104.8b4f368b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/105.1a3d6c41.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/106.d8f8b4c1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/107.38cf9b4f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/108.a30da8c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/109.6d56b7d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/11.b98e8151.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/110.c44e533c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/111.ccb9a835.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/112.2d2a2890.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/113.5ac0921f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/114.90ef8814.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/115.12a72d27.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/116.6b780970.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/117.79604442.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/118.7752bc89.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/119.d8e76ef0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/12.433f0c7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/120.ed76595e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/121.cc3d56f2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/122.c8651871.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/123.f7d5f223.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/124.88ce07a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/125.a9572036.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/126.879447b3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/127.3b8478f3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/128.87d67ad7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/129.f6241700.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/13.b6743084.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/130.a9cb856a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/131.45e904c9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/132.abd9e4c2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/133.d5aa1db6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/134.4bfa3b5a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/135.8c7630ad.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/136.f813e062.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/137.6cab2863.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/138.03ec5265.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/139.924812c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/14.422fec10.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/140.a7967a5d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/141.34a018bb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/142.c5bfcdd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/143.05e388ba.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/144.a98bdbcc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/145.826b73e0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/146.001afadf.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/147.fcadaef3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/148.604863a4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/149.6509ed9f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/15.09a69fc3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/150.fd8575ca.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/151.67ecd4a3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/152.481a7bd4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/153.dbf3f3aa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/154.2aa9c67d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/155.e3367ebc.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/156.a5bb942d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/157.09caef4e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/158.1f82587f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/159.4cebd9d3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/16.0414f390.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/160.47df8215.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/161.fac79692.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/162.9f2f870f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/163.4f1604f9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/165.fe2baa69.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/166.c82b3c76.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/167.3c3f2ff1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/168.8d2e8e83.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/169.b10c84bd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/17.8dfcb176.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/170.b9db8312.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/171.2480fc36.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/172.81c5206a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/173.477dcbae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/174.05398ca3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/175.84ab69d0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/176.f48183c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/177.d0d35403.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/178.55d433fd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/179.9442c138.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/18.c331029e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/180.5c15d5d4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/181.911f367b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/182.72f663e8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/183.22b2258c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/184.15b21065.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/185.894ea40b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/186.f6394459.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/187.7bd9b3fe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/188.3baa9bcd.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/189.18e63b11.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/19.13bdd658.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/190.b7ffb54a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/191.13061aba.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/192.1bf9bd61.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/193.8fa44455.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/194.49c9a3c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/195.eb1e8802.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/196.9b925823.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/197.adca6c8c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/198.2ae0961a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/199.cd9dca80.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/20.5d11c294.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/200.2eb9437f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/201.6dba10c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/202.9c5057c6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/203.98a3c2b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/204.50d5cf2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/205.e56e820b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/206.6ce5c41e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/207.8aa83d91.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/208.296ee160.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/209.13279349.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/21.7b54d7d8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/210.63e30420.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/211.0b325f23.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/212.be671e2e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/213.81d2e607.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/214.ba403b5c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/215.2efcec5f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/216.e01d3100.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/217.906c8d54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/218.94e33827.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/219.21c5e01f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/22.c2daedd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/220.3ba160e4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/221.5c17138c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/222.aea004ce.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/223.cbb7b883.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/224.b130b37f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/225.32f90319.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/226.8c102c21.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/227.b3d60339.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/228.72f0ad18.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/229.3daa3b7e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/23.2c668e20.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/230.7e5a85b1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/231.b83d12f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/232.e902f42d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/233.b4f254c0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/234.d975df48.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/235.71a7be7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/236.05d87a5e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/237.d518e28b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/238.eb60e397.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/239.81fecfed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/24.af200d5b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/240.c0869bc2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/241.59dc896f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/242.6d423d39.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/243.a355eaa8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/244.ad729cf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/245.c3cd6bbe.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/246.d0708528.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/247.13d65d78.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/248.1d222543.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/249.2d7a9bf1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/25.4f97f63f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/250.09ef38e2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/251.0cb90e8a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/252.2ff0def4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/253.61c7c505.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/254.20137eeb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/255.49c937e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/256.8d709dae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/257.b3ebdce7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/258.792f66d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/259.98c809e9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/26.d1bb645c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/260.54ff10d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/261.928afea4.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/27.2d5c4a6b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/28.72dee0f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/29.8820be26.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/3.947b8d98.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/30.343676b8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/31.cff089f1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/32.56d8546e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/33.6b642a06.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/34.360a9ea9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/35.205d7fac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/36.f2765bae.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/37.6c33435e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/38.a778a6a2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/39.7dda160f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/4.ee88d25a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/40.bd778eac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/41.b79220f6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/42.c3157beb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/43.097368d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/44.2515f16e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/45.d5b4b7c3.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/46.bbab8d6e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/47.79aa575e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/48.45785af9.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/49.0569a6eb.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/5.00f814ac.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/50.14bd3ba2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/51.e0968711.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/52.fe65ddf8.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/53.faa1ff3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/54.fcfed2c5.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/55.7db1d28a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/56.8b0e82b7.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/57.9de2d983.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/58.b0f8ad0c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/59.f1ff4935.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/6.2bc86161.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/60.ac08de9a.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/61.ea4fad75.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/62.5c85853b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/63.5ac99656.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/64.c6838e95.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/65.4c1b089e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/66.379a6b45.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/67.15703e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/68.ffdefd7d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/69.bdeb7b9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/7.74f2ce90.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/70.3dadb5ed.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/71.2c97fe38.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/72.dc778e17.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/73.d88fcb57.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/74.bba2165e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/75.15562a81.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/76.21f5a94e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/77.0725268e.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/78.d6f610d1.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/79.60d67faa.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/8.8928eb8b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/80.99d71ee0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/81.f1500469.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/82.69f363a6.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/83.f1fbcb2c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/84.b76e3156.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/85.efc4bd54.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/86.85011b24.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/87.6a88d571.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/88.0f45cfe0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/89.e67ddb59.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/9.63ebb16b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/90.96ebfa9b.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/91.cdde4d3f.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/92.dc3f06d2.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/93.aaa04d52.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/94.2af5650c.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/95.1d44ec16.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/96.9545127d.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/97.8b18f487.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/98.cb259cd0.js"><link rel="prefetch" href="/docs/3.9.1/assets/js/99.6251650a.js">
    <link rel="stylesheet" href="/docs/3.9.1/assets/css/0.styles.7d07a4d1.css">
  </head>
  <body>
    <div id="app" data-server-rendered="true"><div class="theme-container"><header class="navbar"><div class="sidebar-button"><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" role="img" viewBox="0 0 448 512" class="icon"><path fill="currentColor" d="M436 124H12c-6.627 0-12-5.373-12-12V80c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12zm0 160H12c-6.627 0-12-5.373-12-12v-32c0-6.627 5.373-12 12-12h424c6.627 0 12 5.373 12 12v32c0 6.627-5.373 12-12 12z"></path></svg></div> <a href="/docs/3.9.1/" class="home-link router-link-active"><img src="/docs/3.9.1/favicon.ico" alt="Chart.js" class="logo"> <span class="site-name can-hide">Chart.js</span></a> <div class="links"><div class="search-box"><input aria-label="Search" autocomplete="off" spellcheck="false" value=""> <!----></div> <nav class="nav-links can-hide"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" class="nav-link">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav></div></header> <div class="sidebar-mask"></div> <aside class="sidebar"><nav class="nav-links"><div class="nav-item"><a href="/docs/3.9.1/" class="nav-link">
  Home
</a></div><div class="nav-item"><a href="/docs/3.9.1/api/" class="nav-link">
  API
</a></div><div class="nav-item"><a href="/docs/3.9.1/samples/" class="nav-link">
  Samples
</a></div><div class="nav-item"><div class="dropdown-wrapper"><button type="button" aria-label="Community Menu" class="dropdown-title"><span class="title">Ecosystem</span> <span class="arrow down"></span></button> <button type="button" aria-label="Community Menu" class="mobile-dropdown-title"><span class="title">Ecosystem</span> <span class="arrow right"></span></button> <ul class="nav-dropdown" style="display:none;"><li class="dropdown-item"><!----> <a href="https://github.com/chartjs/awesome" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Awesome
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://chartjs-slack.herokuapp.com/" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Slack
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li><li class="dropdown-item"><!----> <a href="https://stackoverflow.com/questions/tagged/chart.js" target="_blank" rel="noopener noreferrer" class="nav-link external">
  Stack Overflow
  <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></li></ul></div></div> <a href="https://github.com/chartjs/Chart.js" target="_blank" rel="noopener noreferrer" class="repo-link">
    GitHub
    <span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a></nav>  <ul class="sidebar-links"><li><a href="/docs/3.9.1/" aria-current="page" class="sidebar-link">Chart.js</a></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Getting Started</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>General</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading open"><span>Configuration</span> <span class="arrow down"></span></p> <ul class="sidebar-links sidebar-group-items"><li><a href="/docs/3.9.1/configuration/" aria-current="page" class="sidebar-link">Configuration</a></li><li><a href="/docs/3.9.1/configuration/animations.html" class="sidebar-link">Animations</a></li><li><a href="/docs/3.9.1/configuration/canvas-background.html" class="sidebar-link">Canvas background</a></li><li><a href="/docs/3.9.1/configuration/decimation.html" class="sidebar-link">Data Decimation</a></li><li><a href="/docs/3.9.1/configuration/device-pixel-ratio.html" class="sidebar-link">Device Pixel Ratio</a></li><li><a href="/docs/3.9.1/configuration/elements.html" class="sidebar-link">Elements</a></li><li><a href="/docs/3.9.1/configuration/interactions.html" class="sidebar-link">Interactions</a></li><li><a href="/docs/3.9.1/configuration/layout.html" class="sidebar-link">Layout</a></li><li><a href="/docs/3.9.1/configuration/legend.html" class="sidebar-link">Legend</a></li><li><a href="/docs/3.9.1/configuration/locale.html" class="sidebar-link">Locale</a></li><li><a href="/docs/3.9.1/configuration/responsive.html" class="sidebar-link">Responsive Charts</a></li><li><a href="/docs/3.9.1/configuration/subtitle.html" class="sidebar-link">Subtitle</a></li><li><a href="/docs/3.9.1/configuration/title.html" class="sidebar-link">Title</a></li><li><a href="/docs/3.9.1/configuration/tooltip.html" aria-current="page" class="active sidebar-link">Tooltip</a></li></ul></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Chart Types</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Axes</span> <span class="arrow right"></span></p> <!----></section></li><li><section class="sidebar-group collapsable depth-0"><p class="sidebar-heading"><span>Developers</span> <span class="arrow right"></span></p> <!----></section></li></ul> </aside> <main class="page"> <div class="theme-default-content content__default"><h1 id="tooltip"><a href="#tooltip" class="header-anchor">#</a> Tooltip</h1> <h2 id="tooltip-configuration"><a href="#tooltip-configuration" class="header-anchor">#</a> Tooltip Configuration</h2> <p>Namespace: <code>options.plugins.tooltip</code>, the global options for the chart tooltips is defined in <code>Chart.defaults.plugins.tooltip</code>.</p> <div class="custom-block warning"><p class="custom-block-title">WARNING</p> <p>The bubble, doughnut, pie, polar area, and scatter charts override the tooltip defaults. To change the overrides for those chart types, the options are defined in <code>Chart.overrides[type].plugins.tooltip</code>.</p></div> <table><thead><tr><th>Name</th> <th>Type</th> <th>Default</th> <th>Description</th></tr></thead> <tbody><tr><td><code>enabled</code></td> <td><code>boolean</code></td> <td><code>true</code></td> <td>Are on-canvas tooltips enabled?</td></tr> <tr><td><code>external</code></td> <td><code>function</code></td> <td><code>null</code></td> <td>See <a href="#external-custom-tooltips">external tooltip</a> section.</td></tr> <tr><td><code>mode</code></td> <td><code>string</code></td> <td><code>interaction.mode</code></td> <td>Sets which elements appear in the tooltip. <a href="/docs/3.9.1/configuration/interactions.html#modes">more...</a>.</td></tr> <tr><td><code>intersect</code></td> <td><code>boolean</code></td> <td><code>interaction.intersect</code></td> <td>If true, the tooltip mode applies only when the mouse position intersects with an element. If false, the mode will be applied at all times.</td></tr> <tr><td><code>position</code></td> <td><code>string</code></td> <td><code>'average'</code></td> <td>The mode for positioning the tooltip. <a href="#position-modes">more...</a></td></tr> <tr><td><code>callbacks</code></td> <td><code>object</code></td> <td></td> <td>See the <a href="#tooltip-callbacks">callbacks section</a>.</td></tr> <tr><td><code>itemSort</code></td> <td><code>function</code></td> <td></td> <td>Sort tooltip items. <a href="#sort-callback">more...</a></td></tr> <tr><td><code>filter</code></td> <td><code>function</code></td> <td></td> <td>Filter tooltip items. <a href="#filter-callback">more...</a></td></tr> <tr><td><code>backgroundColor</code></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td><code>'rgba(0, 0, 0, 0.8)'</code></td> <td>Background color of the tooltip.</td></tr> <tr><td><code>titleColor</code></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td><code>'#fff'</code></td> <td>Color of title text.</td></tr> <tr><td><code>titleFont</code></td> <td><code>Font</code></td> <td><code>{weight: 'bold'}</code></td> <td>See <a href="/docs/3.9.1/general/fonts.html">Fonts</a>.</td></tr> <tr><td><code>titleAlign</code></td> <td><code>string</code></td> <td><code>'left'</code></td> <td>Horizontal alignment of the title text lines. <a href="#text-alignment">more...</a></td></tr> <tr><td><code>titleSpacing</code></td> <td><code>number</code></td> <td><code>2</code></td> <td>Spacing to add to top and bottom of each title line.</td></tr> <tr><td><code>titleMarginBottom</code></td> <td><code>number</code></td> <td><code>6</code></td> <td>Margin to add on bottom of title section.</td></tr> <tr><td><code>bodyColor</code></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td><code>'#fff'</code></td> <td>Color of body text.</td></tr> <tr><td><code>bodyFont</code></td> <td><code>Font</code></td> <td><code>{}</code></td> <td>See <a href="/docs/3.9.1/general/fonts.html">Fonts</a>.</td></tr> <tr><td><code>bodyAlign</code></td> <td><code>string</code></td> <td><code>'left'</code></td> <td>Horizontal alignment of the body text lines. <a href="#text-alignment">more...</a></td></tr> <tr><td><code>bodySpacing</code></td> <td><code>number</code></td> <td><code>2</code></td> <td>Spacing to add to top and bottom of each tooltip item.</td></tr> <tr><td><code>footerColor</code></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td><code>'#fff'</code></td> <td>Color of footer text.</td></tr> <tr><td><code>footerFont</code></td> <td><code>Font</code></td> <td><code>{weight: 'bold'}</code></td> <td>See <a href="/docs/3.9.1/general/fonts.html">Fonts</a>.</td></tr> <tr><td><code>footerAlign</code></td> <td><code>string</code></td> <td><code>'left'</code></td> <td>Horizontal alignment of the footer text lines. <a href="#text-alignment">more...</a></td></tr> <tr><td><code>footerSpacing</code></td> <td><code>number</code></td> <td><code>2</code></td> <td>Spacing to add to top and bottom of each footer line.</td></tr> <tr><td><code>footerMarginTop</code></td> <td><code>number</code></td> <td><code>6</code></td> <td>Margin to add before drawing the footer.</td></tr> <tr><td><code>padding</code></td> <td><a href="/docs/3.9.1/general/padding.html"><code>Padding</code></a></td> <td><code>6</code></td> <td>Padding inside the tooltip.</td></tr> <tr><td><code>caretPadding</code></td> <td><code>number</code></td> <td><code>2</code></td> <td>Extra distance to move the end of the tooltip arrow away from the tooltip point.</td></tr> <tr><td><code>caretSize</code></td> <td><code>number</code></td> <td><code>5</code></td> <td>Size, in px, of the tooltip arrow.</td></tr> <tr><td><code>cornerRadius</code></td> <td><code>number</code>|<code>object</code></td> <td><code>6</code></td> <td>Radius of tooltip corner curves.</td></tr> <tr><td><code>multiKeyBackground</code></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td><code>'#fff'</code></td> <td>Color to draw behind the colored boxes when multiple items are in the tooltip.</td></tr> <tr><td><code>displayColors</code></td> <td><code>boolean</code></td> <td><code>true</code></td> <td>If true, color boxes are shown in the tooltip.</td></tr> <tr><td><code>boxWidth</code></td> <td><code>number</code></td> <td><code>bodyFont.size</code></td> <td>Width of the color box if displayColors is true.</td></tr> <tr><td><code>boxHeight</code></td> <td><code>number</code></td> <td><code>bodyFont.size</code></td> <td>Height of the color box if displayColors is true.</td></tr> <tr><td><code>boxPadding</code></td> <td><code>number</code></td> <td><code>1</code></td> <td>Padding between the color box and the text.</td></tr> <tr><td><code>usePointStyle</code></td> <td><code>boolean</code></td> <td><code>false</code></td> <td>Use the corresponding point style (from dataset options) instead of color boxes, ex: star, triangle etc. (size is based on the minimum value between boxWidth and boxHeight).</td></tr> <tr><td><code>borderColor</code></td> <td><a href="/docs/3.9.1/general/colors.html"><code>Color</code></a></td> <td><code>'rgba(0, 0, 0, 0)'</code></td> <td>Color of the border.</td></tr> <tr><td><code>borderWidth</code></td> <td><code>number</code></td> <td><code>0</code></td> <td>Size of the border.</td></tr> <tr><td><code>rtl</code></td> <td><code>boolean</code></td> <td></td> <td><code>true</code> for rendering the tooltip from right to left.</td></tr> <tr><td><code>textDirection</code></td> <td><code>string</code></td> <td>canvas' default</td> <td>This will force the text direction <code>'rtl' or 'ltr</code> on the canvas for rendering the tooltips, regardless of the css specified on the canvas</td></tr> <tr><td><code>xAlign</code></td> <td><code>string</code></td> <td><code>undefined</code></td> <td>Position of the tooltip caret in the X direction. <a href="#tooltip-alignment">more</a></td></tr> <tr><td><code>yAlign</code></td> <td><code>string</code></td> <td><code>undefined</code></td> <td>Position of the tooltip caret in the Y direction. <a href="#tooltip-alignment">more</a></td></tr></tbody></table> <h3 id="position-modes"><a href="#position-modes" class="header-anchor">#</a> Position Modes</h3> <p>Possible modes are:</p> <ul><li><code>'average'</code></li> <li><code>'nearest'</code></li></ul> <p><code>'average'</code> mode will place the tooltip at the average position of the items displayed in the tooltip. <code>'nearest'</code> will place the tooltip at the position of the element closest to the event position.</p> <p>You can also define <a href="#custom-position-modes">custom position modes</a>.</p> <h3 id="tooltip-alignment"><a href="#tooltip-alignment" class="header-anchor">#</a> Tooltip Alignment</h3> <p>The <code>xAlign</code> and <code>yAlign</code> options define the position of the tooltip caret. If these parameters are unset, the optimal caret position is determined.</p> <p>The following values for the <code>xAlign</code> setting are supported.</p> <ul><li><code>'left'</code></li> <li><code>'center'</code></li> <li><code>'right'</code></li></ul> <p>The following values for the <code>yAlign</code> setting are supported.</p> <ul><li><code>'top'</code></li> <li><code>'center'</code></li> <li><code>'bottom'</code></li></ul> <h3 id="text-alignment"><a href="#text-alignment" class="header-anchor">#</a> Text Alignment</h3> <p>The <code>titleAlign</code>, <code>bodyAlign</code> and <code>footerAlign</code> options define the horizontal position of the text lines with respect to the tooltip box. The following values are supported.</p> <ul><li><code>'left'</code> (default)</li> <li><code>'right'</code></li> <li><code>'center'</code></li></ul> <p>These options are only applied to text lines. Color boxes are always aligned to the left edge.</p> <h3 id="sort-callback"><a href="#sort-callback" class="header-anchor">#</a> Sort Callback</h3> <p>Allows sorting of <a href="#tooltip-item-context">tooltip items</a>. Must implement at minimum a function that can be passed to <a href="https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort" target="_blank" rel="noopener noreferrer">Array.prototype.sort<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a>. This function can also accept a third parameter that is the data object passed to the chart.</p> <h3 id="filter-callback"><a href="#filter-callback" class="header-anchor">#</a> Filter Callback</h3> <p>Allows filtering of <a href="#tooltip-item-context">tooltip items</a>. Must implement at minimum a function that can be passed to <a href="https://developer.mozilla.org/en/docs/Web/JavaScript/Reference/Global_Objects/Array/filter" target="_blank" rel="noopener noreferrer">Array.prototype.filter<span><svg xmlns="http://www.w3.org/2000/svg" aria-hidden="true" focusable="false" x="0px" y="0px" viewBox="0 0 100 100" width="15" height="15" class="icon outbound"><path fill="currentColor" d="M18.8,85.1h56l0,0c2.2,0,4-1.8,4-4v-32h-8v28h-48v-48h28v-8h-32l0,0c-2.2,0-4,1.8-4,4v56C14.8,83.3,16.6,85.1,18.8,85.1z"></path> <polygon fill="currentColor" points="45.7,48.7 51.3,54.3 77.2,28.5 77.2,37.2 85.2,37.2 85.2,14.9 62.8,14.9 62.8,22.9 71.5,22.9"></polygon></svg> <span class="sr-only">(opens new window)</span></span></a>. This function can also accept a fourth parameter that is the data object passed to the chart.</p> <h2 id="tooltip-callbacks"><a href="#tooltip-callbacks" class="header-anchor">#</a> Tooltip Callbacks</h2> <p>Namespace: <code>options.plugins.tooltip.callbacks</code>, the tooltip has the following callbacks for providing text. For all functions, <code>this</code> will be the tooltip object created from the <code>Tooltip</code> constructor.</p> <p>Namespace: <code>data.datasets[].tooltip.callbacks</code>, items marked with <code>Yes</code> in the column <code>Dataset override</code> can be overridden per dataset.</p> <p>A <a href="#tooltip-item-context">tooltip item context</a> is generated for each item that appears in the tooltip. This is the primary model that the callback methods interact with. For functions that return text, arrays of strings are treated as multiple lines of text.</p> <table><thead><tr><th>Name</th> <th>Arguments</th> <th>Return Type</th> <th>Dataset override</th> <th>Description</th></tr></thead> <tbody><tr><td><code>beforeTitle</code></td> <td><code>TooltipItem[]</code></td> <td><code>string | string[]</code></td> <td></td> <td>Returns the text to render before the title.</td></tr> <tr><td><code>title</code></td> <td><code>TooltipItem[]</code></td> <td><code>string | string[]</code></td> <td></td> <td>Returns text to render as the title of the tooltip.</td></tr> <tr><td><code>afterTitle</code></td> <td><code>TooltipItem[]</code></td> <td><code>string | string[]</code></td> <td></td> <td>Returns text to render after the title.</td></tr> <tr><td><code>beforeBody</code></td> <td><code>TooltipItem[]</code></td> <td><code>string | string[]</code></td> <td></td> <td>Returns text to render before the body section.</td></tr> <tr><td><code>beforeLabel</code></td> <td><code>TooltipItem</code></td> <td><code>string | string[]</code></td> <td>Yes</td> <td>Returns text to render before an individual label. This will be called for each item in the tooltip.</td></tr> <tr><td><code>label</code></td> <td><code>TooltipItem</code></td> <td><code>string | string[]</code></td> <td>Yes</td> <td>Returns text to render for an individual item in the tooltip. <a href="#label-callback">more...</a></td></tr> <tr><td><code>labelColor</code></td> <td><code>TooltipItem</code></td> <td><code>object</code></td> <td>Yes</td> <td>Returns the colors to render for the tooltip item. <a href="#label-color-callback">more...</a></td></tr> <tr><td><code>labelTextColor</code></td> <td><code>TooltipItem</code></td> <td><code>Color</code></td> <td>Yes</td> <td>Returns the colors for the text of the label for the tooltip item.</td></tr> <tr><td><code>labelPointStyle</code></td> <td><code>TooltipItem</code></td> <td><code>object</code></td> <td>Yes</td> <td>Returns the point style to use instead of color boxes if usePointStyle is true (object with values <code>pointStyle</code> and <code>rotation</code>). Default implementation uses the point style from the dataset points. <a href="#label-point-style-callback">more...</a></td></tr> <tr><td><code>afterLabel</code></td> <td><code>TooltipItem</code></td> <td><code>string | string[]</code></td> <td>Yes</td> <td>Returns text to render after an individual label.</td></tr> <tr><td><code>afterBody</code></td> <td><code>TooltipItem[]</code></td> <td><code>string | string[]</code></td> <td></td> <td>Returns text to render after the body section.</td></tr> <tr><td><code>beforeFooter</code></td> <td><code>TooltipItem[]</code></td> <td><code>string | string[]</code></td> <td></td> <td>Returns text to render before the footer section.</td></tr> <tr><td><code>footer</code></td> <td><code>TooltipItem[]</code></td> <td><code>string | string[]</code></td> <td></td> <td>Returns text to render as the footer of the tooltip.</td></tr> <tr><td><code>afterFooter</code></td> <td><code>TooltipItem[]</code></td> <td><code>string | string[]</code></td> <td></td> <td>Text to render after the footer section.</td></tr></tbody></table> <h3 id="label-callback"><a href="#label-callback" class="header-anchor">#</a> Label Callback</h3> <p>The <code>label</code> callback can change the text that displays for a given data point. A common example to show a unit. The example below puts a <code>'$'</code> before every row.</p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token keyword">const</span> chart <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Chart</span><span class="token punctuation">(</span>ctx<span class="token punctuation">,</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'line'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">data</span><span class="token operator">:</span> data<span class="token punctuation">,</span>
    <span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">plugins</span><span class="token operator">:</span> <span class="token punctuation">{</span>
            <span class="token literal-property property">tooltip</span><span class="token operator">:</span> <span class="token punctuation">{</span>
                <span class="token literal-property property">callbacks</span><span class="token operator">:</span> <span class="token punctuation">{</span>
                    <span class="token function-variable function">label</span><span class="token operator">:</span> <span class="token keyword">function</span><span class="token punctuation">(</span><span class="token parameter">context</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                        <span class="token keyword">let</span> label <span class="token operator">=</span> context<span class="token punctuation">.</span>dataset<span class="token punctuation">.</span>label <span class="token operator">||</span> <span class="token string">''</span><span class="token punctuation">;</span>
                        <span class="token keyword">if</span> <span class="token punctuation">(</span>label<span class="token punctuation">)</span> <span class="token punctuation">{</span>
                            label <span class="token operator">+=</span> <span class="token string">': '</span><span class="token punctuation">;</span>
                        <span class="token punctuation">}</span>
                        <span class="token keyword">if</span> <span class="token punctuation">(</span>context<span class="token punctuation">.</span>parsed<span class="token punctuation">.</span>y <span class="token operator">!==</span> <span class="token keyword">null</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                            label <span class="token operator">+=</span> <span class="token keyword">new</span> <span class="token class-name">Intl<span class="token punctuation">.</span>NumberFormat</span><span class="token punctuation">(</span><span class="token string">'en-US'</span><span class="token punctuation">,</span> <span class="token punctuation">{</span> <span class="token literal-property property">style</span><span class="token operator">:</span> <span class="token string">'currency'</span><span class="token punctuation">,</span> <span class="token literal-property property">currency</span><span class="token operator">:</span> <span class="token string">'USD'</span> <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">.</span><span class="token function">format</span><span class="token punctuation">(</span>context<span class="token punctuation">.</span>parsed<span class="token punctuation">.</span>y<span class="token punctuation">)</span><span class="token punctuation">;</span>
                        <span class="token punctuation">}</span>
                        <span class="token keyword">return</span> label<span class="token punctuation">;</span>
                    <span class="token punctuation">}</span>
                <span class="token punctuation">}</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></div><h3 id="label-color-callback"><a href="#label-color-callback" class="header-anchor">#</a> Label Color Callback</h3> <p>For example, to return a red box with a blue dashed border that has a border radius for each item in the tooltip you could do:</p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token keyword">const</span> chart <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Chart</span><span class="token punctuation">(</span>ctx<span class="token punctuation">,</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'line'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">data</span><span class="token operator">:</span> data<span class="token punctuation">,</span>
    <span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">plugins</span><span class="token operator">:</span> <span class="token punctuation">{</span>
            <span class="token literal-property property">tooltip</span><span class="token operator">:</span> <span class="token punctuation">{</span>
                <span class="token literal-property property">callbacks</span><span class="token operator">:</span> <span class="token punctuation">{</span>
                    <span class="token function-variable function">labelColor</span><span class="token operator">:</span> <span class="token keyword">function</span><span class="token punctuation">(</span><span class="token parameter">context</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                        <span class="token keyword">return</span> <span class="token punctuation">{</span>
                            <span class="token literal-property property">borderColor</span><span class="token operator">:</span> <span class="token string">'rgb(0, 0, 255)'</span><span class="token punctuation">,</span>
                            <span class="token literal-property property">backgroundColor</span><span class="token operator">:</span> <span class="token string">'rgb(255, 0, 0)'</span><span class="token punctuation">,</span>
                            <span class="token literal-property property">borderWidth</span><span class="token operator">:</span> <span class="token number">2</span><span class="token punctuation">,</span>
                            <span class="token literal-property property">borderDash</span><span class="token operator">:</span> <span class="token punctuation">[</span><span class="token number">2</span><span class="token punctuation">,</span> <span class="token number">2</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
                            <span class="token literal-property property">borderRadius</span><span class="token operator">:</span> <span class="token number">2</span><span class="token punctuation">,</span>
                        <span class="token punctuation">}</span><span class="token punctuation">;</span>
                    <span class="token punctuation">}</span><span class="token punctuation">,</span>
                    <span class="token function-variable function">labelTextColor</span><span class="token operator">:</span> <span class="token keyword">function</span><span class="token punctuation">(</span><span class="token parameter">context</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                        <span class="token keyword">return</span> <span class="token string">'#543453'</span><span class="token punctuation">;</span>
                    <span class="token punctuation">}</span>
                <span class="token punctuation">}</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></div><h3 id="label-point-style-callback"><a href="#label-point-style-callback" class="header-anchor">#</a> Label Point Style Callback</h3> <p>For example, to draw triangles instead of the regular color box for each item in the tooltip you could do:</p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token keyword">const</span> chart <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Chart</span><span class="token punctuation">(</span>ctx<span class="token punctuation">,</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'line'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">data</span><span class="token operator">:</span> data<span class="token punctuation">,</span>
    <span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">plugins</span><span class="token operator">:</span> <span class="token punctuation">{</span>
            <span class="token literal-property property">tooltip</span><span class="token operator">:</span> <span class="token punctuation">{</span>
                <span class="token literal-property property">usePointStyle</span><span class="token operator">:</span> <span class="token boolean">true</span><span class="token punctuation">,</span>
                <span class="token literal-property property">callbacks</span><span class="token operator">:</span> <span class="token punctuation">{</span>
                    <span class="token function-variable function">labelPointStyle</span><span class="token operator">:</span> <span class="token keyword">function</span><span class="token punctuation">(</span><span class="token parameter">context</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                        <span class="token keyword">return</span> <span class="token punctuation">{</span>
                            <span class="token literal-property property">pointStyle</span><span class="token operator">:</span> <span class="token string">'triangle'</span><span class="token punctuation">,</span>
                            <span class="token literal-property property">rotation</span><span class="token operator">:</span> <span class="token number">0</span>
                        <span class="token punctuation">}</span><span class="token punctuation">;</span>
                    <span class="token punctuation">}</span>
                <span class="token punctuation">}</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></div><h3 id="tooltip-item-context"><a href="#tooltip-item-context" class="header-anchor">#</a> Tooltip Item Context</h3> <p>The tooltip items passed to the tooltip callbacks implement the following interface.</p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token punctuation">{</span>
    <span class="token comment">// The chart the tooltip is being shown on</span>
    <span class="token literal-property property">chart</span><span class="token operator">:</span> Chart
    <span class="token comment">// Label for the tooltip</span>
    <span class="token literal-property property">label</span><span class="token operator">:</span> string<span class="token punctuation">,</span>
    <span class="token comment">// Parsed data values for the given `dataIndex` and `datasetIndex`</span>
    <span class="token literal-property property">parsed</span><span class="token operator">:</span> object<span class="token punctuation">,</span>
    <span class="token comment">// Raw data values for the given `dataIndex` and `datasetIndex`</span>
    <span class="token literal-property property">raw</span><span class="token operator">:</span> object<span class="token punctuation">,</span>
    <span class="token comment">// Formatted value for the tooltip</span>
    <span class="token literal-property property">formattedValue</span><span class="token operator">:</span> string<span class="token punctuation">,</span>
    <span class="token comment">// The dataset the item comes from</span>
    <span class="token literal-property property">dataset</span><span class="token operator">:</span> object
    <span class="token comment">// Index of the dataset the item comes from</span>
    <span class="token literal-property property">datasetIndex</span><span class="token operator">:</span> number<span class="token punctuation">,</span>
    <span class="token comment">// Index of this data item in the dataset</span>
    <span class="token literal-property property">dataIndex</span><span class="token operator">:</span> number<span class="token punctuation">,</span>
    <span class="token comment">// The chart element (point, arc, bar, etc.) for this tooltip item</span>
    <span class="token literal-property property">element</span><span class="token operator">:</span> Element<span class="token punctuation">,</span>
<span class="token punctuation">}</span>
</code></pre></div><h2 id="external-custom-tooltips"><a href="#external-custom-tooltips" class="header-anchor">#</a> External (Custom) Tooltips</h2> <p>External tooltips allow you to hook into the tooltip rendering process so that you can render the tooltip in your own custom way. Generally this is used to create an HTML tooltip instead of an on-canvas tooltip. The <code>external</code> option takes a function which is passed a context parameter containing the <code>chart</code> and <code>tooltip</code>. You can enable external tooltips in the global or chart configuration like so:</p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token keyword">const</span> myPieChart <span class="token operator">=</span> <span class="token keyword">new</span> <span class="token class-name">Chart</span><span class="token punctuation">(</span>ctx<span class="token punctuation">,</span> <span class="token punctuation">{</span>
    <span class="token literal-property property">type</span><span class="token operator">:</span> <span class="token string">'pie'</span><span class="token punctuation">,</span>
    <span class="token literal-property property">data</span><span class="token operator">:</span> data<span class="token punctuation">,</span>
    <span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">plugins</span><span class="token operator">:</span> <span class="token punctuation">{</span>
            <span class="token literal-property property">tooltip</span><span class="token operator">:</span> <span class="token punctuation">{</span>
                <span class="token comment">// Disable the on-canvas tooltip</span>
                <span class="token literal-property property">enabled</span><span class="token operator">:</span> <span class="token boolean">false</span><span class="token punctuation">,</span>
                <span class="token function-variable function">external</span><span class="token operator">:</span> <span class="token keyword">function</span><span class="token punctuation">(</span><span class="token parameter">context</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                    <span class="token comment">// Tooltip Element</span>
                    <span class="token keyword">let</span> tooltipEl <span class="token operator">=</span> document<span class="token punctuation">.</span><span class="token function">getElementById</span><span class="token punctuation">(</span><span class="token string">'chartjs-tooltip'</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                    <span class="token comment">// Create element on first render</span>
                    <span class="token keyword">if</span> <span class="token punctuation">(</span><span class="token operator">!</span>tooltipEl<span class="token punctuation">)</span> <span class="token punctuation">{</span>
                        tooltipEl <span class="token operator">=</span> document<span class="token punctuation">.</span><span class="token function">createElement</span><span class="token punctuation">(</span><span class="token string">'div'</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                        tooltipEl<span class="token punctuation">.</span>id <span class="token operator">=</span> <span class="token string">'chartjs-tooltip'</span><span class="token punctuation">;</span>
                        tooltipEl<span class="token punctuation">.</span>innerHTML <span class="token operator">=</span> <span class="token string">'&lt;table&gt;&lt;/table&gt;'</span><span class="token punctuation">;</span>
                        document<span class="token punctuation">.</span>body<span class="token punctuation">.</span><span class="token function">appendChild</span><span class="token punctuation">(</span>tooltipEl<span class="token punctuation">)</span><span class="token punctuation">;</span>
                    <span class="token punctuation">}</span>
                    <span class="token comment">// Hide if no tooltip</span>
                    <span class="token keyword">const</span> tooltipModel <span class="token operator">=</span> context<span class="token punctuation">.</span>tooltip<span class="token punctuation">;</span>
                    <span class="token keyword">if</span> <span class="token punctuation">(</span>tooltipModel<span class="token punctuation">.</span>opacity <span class="token operator">===</span> <span class="token number">0</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                        tooltipEl<span class="token punctuation">.</span>style<span class="token punctuation">.</span>opacity <span class="token operator">=</span> <span class="token number">0</span><span class="token punctuation">;</span>
                        <span class="token keyword">return</span><span class="token punctuation">;</span>
                    <span class="token punctuation">}</span>
                    <span class="token comment">// Set caret Position</span>
                    tooltipEl<span class="token punctuation">.</span>classList<span class="token punctuation">.</span><span class="token function">remove</span><span class="token punctuation">(</span><span class="token string">'above'</span><span class="token punctuation">,</span> <span class="token string">'below'</span><span class="token punctuation">,</span> <span class="token string">'no-transform'</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                    <span class="token keyword">if</span> <span class="token punctuation">(</span>tooltipModel<span class="token punctuation">.</span>yAlign<span class="token punctuation">)</span> <span class="token punctuation">{</span>
                        tooltipEl<span class="token punctuation">.</span>classList<span class="token punctuation">.</span><span class="token function">add</span><span class="token punctuation">(</span>tooltipModel<span class="token punctuation">.</span>yAlign<span class="token punctuation">)</span><span class="token punctuation">;</span>
                    <span class="token punctuation">}</span> <span class="token keyword">else</span> <span class="token punctuation">{</span>
                        tooltipEl<span class="token punctuation">.</span>classList<span class="token punctuation">.</span><span class="token function">add</span><span class="token punctuation">(</span><span class="token string">'no-transform'</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                    <span class="token punctuation">}</span>
                    <span class="token keyword">function</span> <span class="token function">getBody</span><span class="token punctuation">(</span><span class="token parameter">bodyItem</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                        <span class="token keyword">return</span> bodyItem<span class="token punctuation">.</span>lines<span class="token punctuation">;</span>
                    <span class="token punctuation">}</span>
                    <span class="token comment">// Set Text</span>
                    <span class="token keyword">if</span> <span class="token punctuation">(</span>tooltipModel<span class="token punctuation">.</span>body<span class="token punctuation">)</span> <span class="token punctuation">{</span>
                        <span class="token keyword">const</span> titleLines <span class="token operator">=</span> tooltipModel<span class="token punctuation">.</span>title <span class="token operator">||</span> <span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">;</span>
                        <span class="token keyword">const</span> bodyLines <span class="token operator">=</span> tooltipModel<span class="token punctuation">.</span>body<span class="token punctuation">.</span><span class="token function">map</span><span class="token punctuation">(</span>getBody<span class="token punctuation">)</span><span class="token punctuation">;</span>
                        <span class="token keyword">let</span> innerHtml <span class="token operator">=</span> <span class="token string">'&lt;thead&gt;'</span><span class="token punctuation">;</span>
                        titleLines<span class="token punctuation">.</span><span class="token function">forEach</span><span class="token punctuation">(</span><span class="token keyword">function</span><span class="token punctuation">(</span><span class="token parameter">title</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                            innerHtml <span class="token operator">+=</span> <span class="token string">'&lt;tr&gt;&lt;th&gt;'</span> <span class="token operator">+</span> title <span class="token operator">+</span> <span class="token string">'&lt;/th&gt;&lt;/tr&gt;'</span><span class="token punctuation">;</span>
                        <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                        innerHtml <span class="token operator">+=</span> <span class="token string">'&lt;/thead&gt;&lt;tbody&gt;'</span><span class="token punctuation">;</span>
                        bodyLines<span class="token punctuation">.</span><span class="token function">forEach</span><span class="token punctuation">(</span><span class="token keyword">function</span><span class="token punctuation">(</span><span class="token parameter">body<span class="token punctuation">,</span> i</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
                            <span class="token keyword">const</span> colors <span class="token operator">=</span> tooltipModel<span class="token punctuation">.</span>labelColors<span class="token punctuation">[</span>i<span class="token punctuation">]</span><span class="token punctuation">;</span>
                            <span class="token keyword">let</span> style <span class="token operator">=</span> <span class="token string">'background:'</span> <span class="token operator">+</span> colors<span class="token punctuation">.</span>backgroundColor<span class="token punctuation">;</span>
                            style <span class="token operator">+=</span> <span class="token string">'; border-color:'</span> <span class="token operator">+</span> colors<span class="token punctuation">.</span>borderColor<span class="token punctuation">;</span>
                            style <span class="token operator">+=</span> <span class="token string">'; border-width: 2px'</span><span class="token punctuation">;</span>
                            <span class="token keyword">const</span> span <span class="token operator">=</span> <span class="token string">'&lt;span style=&quot;'</span> <span class="token operator">+</span> style <span class="token operator">+</span> <span class="token string">'&quot;&gt;&lt;/span&gt;'</span><span class="token punctuation">;</span>
                            innerHtml <span class="token operator">+=</span> <span class="token string">'&lt;tr&gt;&lt;td&gt;'</span> <span class="token operator">+</span> span <span class="token operator">+</span> body <span class="token operator">+</span> <span class="token string">'&lt;/td&gt;&lt;/tr&gt;'</span><span class="token punctuation">;</span>
                        <span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                        innerHtml <span class="token operator">+=</span> <span class="token string">'&lt;/tbody&gt;'</span><span class="token punctuation">;</span>
                        <span class="token keyword">let</span> tableRoot <span class="token operator">=</span> tooltipEl<span class="token punctuation">.</span><span class="token function">querySelector</span><span class="token punctuation">(</span><span class="token string">'table'</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                        tableRoot<span class="token punctuation">.</span>innerHTML <span class="token operator">=</span> innerHtml<span class="token punctuation">;</span>
                    <span class="token punctuation">}</span>
                    <span class="token keyword">const</span> position <span class="token operator">=</span> context<span class="token punctuation">.</span>chart<span class="token punctuation">.</span>canvas<span class="token punctuation">.</span><span class="token function">getBoundingClientRect</span><span class="token punctuation">(</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
                    <span class="token keyword">const</span> bodyFont <span class="token operator">=</span> Chart<span class="token punctuation">.</span>helpers<span class="token punctuation">.</span><span class="token function">toFont</span><span class="token punctuation">(</span>tooltipModel<span class="token punctuation">.</span>options<span class="token punctuation">.</span>bodyFont<span class="token punctuation">)</span><span class="token punctuation">;</span>
                    <span class="token comment">// Display, position, and set styles for font</span>
                    tooltipEl<span class="token punctuation">.</span>style<span class="token punctuation">.</span>opacity <span class="token operator">=</span> <span class="token number">1</span><span class="token punctuation">;</span>
                    tooltipEl<span class="token punctuation">.</span>style<span class="token punctuation">.</span>position <span class="token operator">=</span> <span class="token string">'absolute'</span><span class="token punctuation">;</span>
                    tooltipEl<span class="token punctuation">.</span>style<span class="token punctuation">.</span>left <span class="token operator">=</span> position<span class="token punctuation">.</span>left <span class="token operator">+</span> window<span class="token punctuation">.</span>pageXOffset <span class="token operator">+</span> tooltipModel<span class="token punctuation">.</span>caretX <span class="token operator">+</span> <span class="token string">'px'</span><span class="token punctuation">;</span>
                    tooltipEl<span class="token punctuation">.</span>style<span class="token punctuation">.</span>top <span class="token operator">=</span> position<span class="token punctuation">.</span>top <span class="token operator">+</span> window<span class="token punctuation">.</span>pageYOffset <span class="token operator">+</span> tooltipModel<span class="token punctuation">.</span>caretY <span class="token operator">+</span> <span class="token string">'px'</span><span class="token punctuation">;</span>
                    tooltipEl<span class="token punctuation">.</span>style<span class="token punctuation">.</span>font <span class="token operator">=</span> bodyFont<span class="token punctuation">.</span>string<span class="token punctuation">;</span>
                    tooltipEl<span class="token punctuation">.</span>style<span class="token punctuation">.</span>padding <span class="token operator">=</span> tooltipModel<span class="token punctuation">.</span>padding <span class="token operator">+</span> <span class="token string">'px '</span> <span class="token operator">+</span> tooltipModel<span class="token punctuation">.</span>padding <span class="token operator">+</span> <span class="token string">'px'</span><span class="token punctuation">;</span>
                    tooltipEl<span class="token punctuation">.</span>style<span class="token punctuation">.</span>pointerEvents <span class="token operator">=</span> <span class="token string">'none'</span><span class="token punctuation">;</span>
                <span class="token punctuation">}</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span><span class="token punctuation">;</span>
</code></pre></div><p>See <a href="/docs/3.9.1/samples/tooltip/html.html">samples</a> for examples on how to get started with external tooltips.</p> <h2 id="tooltip-model"><a href="#tooltip-model" class="header-anchor">#</a> Tooltip Model</h2> <p>The tooltip model contains parameters that can be used to render the tooltip.</p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token punctuation">{</span>
    <span class="token literal-property property">chart</span><span class="token operator">:</span> Chart<span class="token punctuation">,</span>
    <span class="token comment">// The items that we are rendering in the tooltip. See Tooltip Item Interface section</span>
    <span class="token literal-property property">dataPoints</span><span class="token operator">:</span> TooltipItem<span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token comment">// Positioning</span>
    <span class="token literal-property property">xAlign</span><span class="token operator">:</span> string<span class="token punctuation">,</span>
    <span class="token literal-property property">yAlign</span><span class="token operator">:</span> string<span class="token punctuation">,</span>
    <span class="token comment">// X and Y properties are the top left of the tooltip</span>
    <span class="token literal-property property">x</span><span class="token operator">:</span> number<span class="token punctuation">,</span>
    <span class="token literal-property property">y</span><span class="token operator">:</span> number<span class="token punctuation">,</span>
    <span class="token literal-property property">width</span><span class="token operator">:</span> number<span class="token punctuation">,</span>
    <span class="token literal-property property">height</span><span class="token operator">:</span> number<span class="token punctuation">,</span>
    <span class="token comment">// Where the tooltip points to</span>
    <span class="token literal-property property">caretX</span><span class="token operator">:</span> number<span class="token punctuation">,</span>
    <span class="token literal-property property">caretY</span><span class="token operator">:</span> number<span class="token punctuation">,</span>
    <span class="token comment">// Body</span>
    <span class="token comment">// The body lines that need to be rendered</span>
    <span class="token comment">// Each object contains 3 parameters</span>
    <span class="token comment">// before: string[] // lines of text before the line with the color square</span>
    <span class="token comment">// lines: string[], // lines of text to render as the main item with color square</span>
    <span class="token comment">// after: string[], // lines of text to render after the main lines</span>
    <span class="token literal-property property">body</span><span class="token operator">:</span> object<span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token comment">// lines of text that appear after the title but before the body</span>
    <span class="token literal-property property">beforeBody</span><span class="token operator">:</span> string<span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token comment">// line of text that appear after the body and before the footer</span>
    <span class="token literal-property property">afterBody</span><span class="token operator">:</span> string<span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token comment">// Title</span>
    <span class="token comment">// lines of text that form the title</span>
    <span class="token literal-property property">title</span><span class="token operator">:</span> string<span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token comment">// Footer</span>
    <span class="token comment">// lines of text that form the footer</span>
    <span class="token literal-property property">footer</span><span class="token operator">:</span> string<span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token comment">// colors to render for each item in body[]. This is the color of the squares in the tooltip</span>
    <span class="token literal-property property">labelColors</span><span class="token operator">:</span> Color<span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token literal-property property">labelTextColors</span><span class="token operator">:</span> Color<span class="token punctuation">[</span><span class="token punctuation">]</span><span class="token punctuation">,</span>
    <span class="token comment">// 0 opacity is a hidden tooltip</span>
    <span class="token literal-property property">opacity</span><span class="token operator">:</span> number<span class="token punctuation">,</span>
    <span class="token comment">// tooltip options</span>
    <span class="token literal-property property">options</span><span class="token operator">:</span> Object
<span class="token punctuation">}</span>
</code></pre></div><h2 id="custom-position-modes"><a href="#custom-position-modes" class="header-anchor">#</a> Custom Position Modes</h2> <p>New modes can be defined by adding functions to the <code>Chart.Tooltip.positioners</code> map.</p> <p>Example:</p> <div class="language-javascript extra-class"><pre class="language-javascript"><code><span class="token keyword">import</span> <span class="token punctuation">{</span> Tooltip <span class="token punctuation">}</span> <span class="token keyword">from</span> <span class="token string">'chart.js'</span><span class="token punctuation">;</span>
<span class="token comment">/**
 * Custom positioner
 * @function Tooltip.positioners.myCustomPositioner
 * @param elements {Chart.Element[]} the tooltip elements
 * @param eventPosition {Point} the position of the event in canvas coordinates
 * @returns {TooltipPosition} the tooltip position
 */</span>
Tooltip<span class="token punctuation">.</span>positioners<span class="token punctuation">.</span><span class="token function-variable function">myCustomPositioner</span> <span class="token operator">=</span> <span class="token keyword">function</span><span class="token punctuation">(</span><span class="token parameter">elements<span class="token punctuation">,</span> eventPosition</span><span class="token punctuation">)</span> <span class="token punctuation">{</span>
    <span class="token comment">// A reference to the tooltip model</span>
    <span class="token keyword">const</span> tooltip <span class="token operator">=</span> <span class="token keyword">this</span><span class="token punctuation">;</span>
    <span class="token comment">/* ... */</span>
    <span class="token keyword">return</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">x</span><span class="token operator">:</span> <span class="token number">0</span><span class="token punctuation">,</span>
        <span class="token literal-property property">y</span><span class="token operator">:</span> <span class="token number">0</span>
        <span class="token comment">// You may also include xAlign and yAlign to override those tooltip options.</span>
    <span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token punctuation">}</span><span class="token punctuation">;</span>
<span class="token comment">// Then, to use it...</span>
<span class="token keyword">new</span> <span class="token class-name">Chart<span class="token punctuation">.</span>js</span><span class="token punctuation">(</span>ctx<span class="token punctuation">,</span> <span class="token punctuation">{</span>
    data<span class="token punctuation">,</span>
    <span class="token literal-property property">options</span><span class="token operator">:</span> <span class="token punctuation">{</span>
        <span class="token literal-property property">plugins</span><span class="token operator">:</span> <span class="token punctuation">{</span>
            <span class="token literal-property property">tooltip</span><span class="token operator">:</span> <span class="token punctuation">{</span>
                <span class="token literal-property property">position</span><span class="token operator">:</span> <span class="token string">'myCustomPositioner'</span>
            <span class="token punctuation">}</span>
        <span class="token punctuation">}</span>
    <span class="token punctuation">}</span>
<span class="token punctuation">}</span><span class="token punctuation">)</span>
</code></pre></div><p>See <a href="/docs/3.9.1/samples/tooltip/position.html">samples</a> for a more detailed example.</p> <p>If you're using TypeScript, you'll also need to register the new mode:</p> <div class="language-typescript extra-class"><pre class="language-typescript"><code><span class="token keyword">declare</span> <span class="token keyword">module</span> <span class="token string">'chart.js'</span> <span class="token punctuation">{</span>
  <span class="token keyword">interface</span> <span class="token class-name">TooltipPositionerMap</span> <span class="token punctuation">{</span>
    myCustomPositioner<span class="token operator">:</span> TooltipPositionerFunction<span class="token operator">&lt;</span>ChartType<span class="token operator">&gt;</span><span class="token punctuation">;</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span>
</code></pre></div></div> <footer class="page-edit"><!----> <div class="last-updated"><span class="prefix">Last Updated:</span> <span class="time">8/3/2022, 12:46:38 PM</span></div></footer> <div class="page-nav"><p class="inner"><span class="prev">
      ←
      <a href="/docs/3.9.1/configuration/title.html" class="prev">
        Title
      </a></span> <span class="next"><a href="/docs/3.9.1/charts/area.html">
        Area Chart
      </a>
      →
    </span></p></div> </main></div><div class="global-ui"></div></div>
    <script src="/docs/3.9.1/assets/js/app.7e0dc8c8.js" defer></script><script src="/docs/3.9.1/assets/js/2.3e2307f3.js" defer></script><script src="/docs/3.9.1/assets/js/164.7f8d8643.js" defer></script>
  </body>
</html>
