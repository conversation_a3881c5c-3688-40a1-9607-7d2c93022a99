{"name": "chart.js", "homepage": "https://www.chartjs.org", "description": "Simple HTML5 charts using the canvas element.", "version": "3.9.1", "license": "MIT", "jsdelivr": "dist/chart.min.js", "unpkg": "dist/chart.min.js", "main": "dist/chart.js", "module": "dist/chart.mjs", "types": "types/index.esm.d.ts", "keywords": ["canvas", "charts", "data", "graphs", "html5", "responsive"], "repository": {"type": "git", "url": "https://github.com/chartjs/Chart.js.git"}, "bugs": {"url": "https://github.com/chartjs/Chart.js/issues"}, "files": ["auto/**", "dist/**", "types/**", "helpers/**"], "scripts": {"autobuild": "rollup -c -w", "build": "rollup -c", "dev": "karma start --auto-watch --no-single-run --browsers chrome --grep", "dev:ff": "karma start --auto-watch --no-single-run --browsers firefox --grep", "docs": "npm run build && vuepress build docs --no-cache", "docs:dev": "npm run build && vuepress dev docs --no-cache", "lint-js": "eslint \"src/**/*.js\" \"test/**/*.js\" \"docs/**/*.js\"", "lint-md": "eslint \"**/*.md\"", "lint-tsc": "tsc", "lint-types": "eslint \"types/**/*.ts\" && node -r esm types/tests/autogen.js && tsc -p types/tests/", "lint": "concurrently \"npm:lint-*\"", "test": "npm run lint && npm run test-ci", "test-ci": "cross-env NODE_ENV=test karma start --auto-watch --single-run --coverage --grep"}, "devDependencies": {"@kurkle/color": "^0.2.1", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-inject": "^4.0.2", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.0.0", "@simonbrunel/vuepress-plugin-versions": "^0.2.0", "@types/offscreencanvas": "^2019.6.4", "@typescript-eslint/eslint-plugin": "^5.8.0", "@typescript-eslint/parser": "^5.8.0", "@vuepress/plugin-google-analytics": "^1.8.3", "@vuepress/plugin-html-redirect": "^0.1.2", "chartjs-adapter-luxon": "^1.0.0", "chartjs-adapter-moment": "^1.0.0", "chartjs-test-utils": "^0.4.0", "concurrently": "^6.0.1", "coveralls": "^3.1.0", "cross-env": "^7.0.3", "eslint": "^8.5.0", "eslint-config-chartjs": "^0.3.0", "eslint-plugin-es": "^4.1.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-markdown": "^2.2.1", "esm": "^3.2.25", "glob": "^7.1.6", "jasmine": "^3.7.0", "jasmine-core": "^3.7.1", "karma": "^6.3.2", "karma-chrome-launcher": "^3.1.0", "karma-coverage": "^2.0.3", "karma-edge-launcher": "^0.4.2", "karma-firefox-launcher": "^2.1.0", "karma-jasmine": "^4.0.1", "karma-jasmine-html-reporter": "^1.5.4", "karma-rollup-preprocessor": "^7.0.7", "karma-safari-private-launcher": "^1.0.0", "karma-spec-reporter": "0.0.32", "luxon": "^2.2.0", "markdown-it-include": "^2.0.0", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "pixelmatch": "^5.2.1", "rollup": "^2.44.0", "rollup-plugin-analyzer": "^4.0.0", "rollup-plugin-cleanup": "^3.2.1", "rollup-plugin-istanbul": "^3.0.0", "rollup-plugin-terser": "^7.0.2", "typedoc": "^0.22.10", "typedoc-plugin-markdown": "^3.6.1", "typescript": "^4.3.5", "vue-tabs-component": "^1.5.0", "vuepress": "^1.8.2", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-flexsearch": "^0.3.0", "vuepress-plugin-redirect": "^1.2.5", "vuepress-plugin-tabs": "^0.3.0", "vuepress-plugin-typedoc": "^0.10.0", "vuepress-theme-chartjs": "^0.2.0", "yargs": "^17.0.1"}}