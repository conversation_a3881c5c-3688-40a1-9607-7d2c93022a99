parser: '@typescript-eslint/parser'

plugins:
  - '@typescript-eslint'

extends:
  - chartjs
  - plugin:@typescript-eslint/recommended

rules:
  # Replace stock eslint rules with typescript-eslint equivalents for proper
  # TypeScript support.
  indent: "off"
  "@typescript-eslint/indent": ["error", 2]
  no-use-before-define: "off"
  '@typescript-eslint/no-use-before-define': "error"
  no-shadow: "off"
  '@typescript-eslint/no-shadow': "error"

  # These rules were set to warning to make the linting pass initially,
  # without making any major changes to types.
  object-curly-spacing: ["warn", "always"]
  '@typescript-eslint/no-empty-interface': "warn"
  '@typescript-eslint/ban-types': "warn"
  '@typescript-eslint/adjacent-overload-signatures': "warn"
