{"name": "simple-update-notifier", "version": "1.1.0", "description": "Simple update notifier to check for npm updates for cli applications", "main": "build/index.js", "types": "build/index.d.ts", "repository": {"type": "git", "url": "https://github.com/alexbrazier/simple-update-notifier.git"}, "homepage": "https://github.com/alexbrazier/simple-update-notifier.git", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "engines": {"node": ">=8.10.0"}, "scripts": {"test": "jest src --noStackTrace", "build": "rollup -c rollup.config.js", "prettier:check": "prettier --check src/**/*.ts", "prettier": "prettier --write src/**/*.ts", "eslint": "eslint src/**/*.ts", "lint": "yarn prettier:check && yarn eslint", "prepare": "yarn lint && yarn build", "release": "release-it"}, "dependencies": {"semver": "~7.0.0"}, "devDependencies": {"@babel/preset-env": "^7.19.1", "@babel/preset-typescript": "^7.17.12", "@release-it/conventional-changelog": "^5.1.0", "@types/jest": "^29.0.3", "@types/node": "^18.7.18", "@typescript-eslint/eslint-plugin": "^5.37.0", "@typescript-eslint/parser": "^5.37.0", "eslint": "^8.23.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.0.3", "prettier": "^2.7.1", "release-it": "^15.4.2", "rollup": "^2.79.0", "rollup-plugin-ts": "^3.0.2", "typescript": "^4.8.3"}, "publishConfig": {"registry": "https://registry.npmjs.org/"}, "files": ["build", "src"], "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular", "infile": "CHANGELOG.md"}}}, "eslintConfig": {"plugins": ["@typescript-eslint", "prettier"], "extends": ["prettier", "eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}}