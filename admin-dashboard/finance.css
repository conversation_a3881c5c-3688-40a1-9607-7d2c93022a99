.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: transform 0.2s;
}

.card:hover {
    transform: translateY(-2px);
}

.card-title {
    color: #6c757d;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.card-text {
    color: #212529;
    margin-bottom: 0.5rem;
}

.text-muted {
    font-size: 0.875rem;
}

.growth-positive {
    color: #198754;
}

.growth-negative {
    color: #dc3545;
}

.table th {
    font-weight: 500;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-pending {
    background-color: #fff3cd;
    color: #664d03;
}

.status-failed {
    background-color: #f8d7da;
    color: #842029;
}

.transaction-amount {
    font-weight: 500;
}

.amount-positive {
    color: #198754;
}

.amount-negative {
    color: #dc3545;
}

.chart-container {
    position: relative;
    height: 300px;
}

.daterangepicker {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.daterangepicker .ranges li.active {
    background-color: #0d6efd;
}

.transaction-type {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.type-income {
    background-color: #e8f5e9;
    color: #1b5e20;
}

.type-expense {
    background-color: #ffebee;
    color: #b71c1c;
}

.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-body h6 {
    color: #495057;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

.detail-table th {
    width: 30%;
    background-color: #f8f9fa;
}

.transaction-id {
    font-family: monospace;
    color: #6c757d;
}

.export-options {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1000;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    display: none;
}

.export-options.show {
    display: block;
}

.export-options .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.export-options .dropdown-item:hover {
    background-color: #f8f9fa;
}

@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .chart-container {
        height: 200px;
    }
    
    .table-responsive {
        margin: 0 -1rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
} 