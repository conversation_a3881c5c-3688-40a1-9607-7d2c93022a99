<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录日志 - WriterPro 管理后台</title>
    <link rel="stylesheet" href="dashboard.css">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <span class="logo-icon"><i class="bi bi-pen"></i></span>
                    <h1>WriterPro</h1>
                </div>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="bi bi-grid"></i>
                            <span>仪表盘</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="bi bi-people"></i>
                            <span>用户管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="agents.html">
                            <i class="bi bi-briefcase"></i>
                            <span>代理管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="finance.html">
                            <i class="bi bi-cash-stack"></i>
                            <span>财务统计</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="bi bi-gear"></i>
                            <span>系统设置</span>
                        </a>
                    </li>
                    <li>
                        <a href="content.html">
                            <i class="bi bi-file-text"></i>
                            <span>内容管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="api.html">
                            <i class="bi bi-code-square"></i>
                            <span>API配置</span>
                        </a>
                    </li>
                    <li>
                        <a href="marketing.html">
                            <i class="bi bi-megaphone"></i>
                            <span>营销工具</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="security.html">
                            <i class="bi bi-shield-check"></i>
                            <span>安全中心</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="header">
                <div class="header-left">
                    <button class="menu-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <h2>登录日志</h2>
                </div>
                
                <div class="header-right">
                    <div class="search-bar">
                        <i class="bi bi-search"></i>
                        <input type="text" placeholder="搜索用户名..." id="searchInput">
                    </div>
                    
                    <div class="header-actions">
                        <button class="action-btn" id="refreshBtn">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                        
                        <button class="action-btn" id="exportBtn">
                            <i class="bi bi-download"></i>
                        </button>
                        
                        <div class="user-dropdown">
                            <button class="user-btn">
                                <div class="avatar">A</div>
                                <span>管理员</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            
                            <div class="dropdown-menu">
                                <a href="profile.html" class="dropdown-item">
                                    <i class="bi bi-person"></i>
                                    <span>个人资料</span>
                                </a>
                                <a href="#" class="dropdown-item">
                                    <i class="bi bi-gear"></i>
                                    <span>设置</span>
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="index.html" class="dropdown-item">
                                    <i class="bi bi-box-arrow-right"></i>
                                    <span>退出登录</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 页面内容 -->
            <div class="content">
                <!-- 筛选器 -->
                <div class="filter-section">
                    <div class="filter-group">
                        <label>状态筛选:</label>
                        <select id="statusFilter">
                            <option value="">全部</option>
                            <option value="true">成功</option>
                            <option value="false">失败</option>
                        </select>
                    </div>
                    
                    <div class="filter-group">
                        <label>时间范围:</label>
                        <select id="timeFilter">
                            <option value="">全部</option>
                            <option value="today">今天</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                        </select>
                    </div>
                    
                    <button class="btn btn-primary" id="applyFilter">
                        <i class="bi bi-funnel"></i>
                        <span>应用筛选</span>
                    </button>
                </div>
                
                <!-- 统计卡片 -->
                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(82, 196, 26, 0.1); color: #52c41a;">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h4>成功登录</h4>
                            <div class="stat-value" id="successCount">0</div>
                            <div class="stat-change">
                                <span class="period">今日</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(255, 77, 79, 0.1); color: #ff4d4f;">
                            <i class="bi bi-x-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h4>失败登录</h4>
                            <div class="stat-value" id="failureCount">0</div>
                            <div class="stat-change">
                                <span class="period">今日</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(24, 144, 255, 0.1); color: #1890ff;">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="stat-info">
                            <h4>活跃用户</h4>
                            <div class="stat-value" id="activeUsers">0</div>
                            <div class="stat-change">
                                <span class="period">今日</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(250, 173, 20, 0.1); color: #faad14;">
                            <i class="bi bi-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h4>最近登录</h4>
                            <div class="stat-value" id="lastLogin">--</div>
                            <div class="stat-change">
                                <span class="period">时间</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 登录日志表格 -->
                <div class="table-section">
                    <div class="table-header">
                        <h3>登录记录</h3>
                        <div class="table-actions">
                            <button class="btn btn-outline" id="clearLogsBtn">
                                <i class="bi bi-trash"></i>
                                <span>清理日志</span>
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>用户名</th>
                                    <th>IP地址</th>
                                    <th>设备</th>
                                    <th>位置</th>
                                    <th>状态</th>
                                    <th>失败原因</th>
                                </tr>
                            </thead>
                            <tbody id="logsTableBody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <div class="pagination-section">
                        <div class="pagination-info">
                            <span id="paginationInfo">显示 0 - 0 条，共 0 条记录</span>
                        </div>
                        <div class="pagination-controls">
                            <button class="btn btn-outline" id="prevPageBtn" disabled>
                                <i class="bi bi-chevron-left"></i>
                                <span>上一页</span>
                            </button>
                            <span class="page-numbers" id="pageNumbers"></span>
                            <button class="btn btn-outline" id="nextPageBtn" disabled>
                                <span>下一页</span>
                                <i class="bi bi-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="login-logs.js"></script>
</body>
</html>
