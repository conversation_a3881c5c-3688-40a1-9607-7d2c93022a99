/* 全局样式 */
:root {
    --primary-color: #1677ff;
    --primary-hover: #4096ff;
    --secondary-color: #52c41a;
    --danger-color: #ff4d4f;
    --warning-color: #faad14;
    --text-color: #262626;
    --text-secondary: #595959;
    --text-light: #8c8c8c;
    --border-color: #d9d9d9;
    --bg-light: #f5f5f5;
    --bg-white: #ffffff;
    --shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    --radius: 6px;
    --transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    --sidebar-width: 240px;
    --sidebar-collapsed-width: 80px;
    --header-height: 64px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>, '<PERSON><PERSON>', sans-serif;
    color: var(--text-color);
    background-color: var(--bg-light);
    line-height: 1.5;
}

/* 布局 */
.admin-layout {
    display: flex;
    min-height: 100vh;
}

/* 侧边栏 */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--bg-white);
    border-right: 1px solid var(--border-color);
    transition: var(--transition);
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    overflow-y: auto;
}

.sidebar-collapsed .sidebar {
    width: var(--sidebar-collapsed-width);
}

.sidebar-header {
    height: var(--header-height);
    padding: 0 16px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.logo {
    display: flex;
    align-items: center;
}

.logo-icon {
    width: 32px;
    height: 32px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 16px;
}

.logo h1 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar-collapsed .logo h1 {
    display: none;
}

.sidebar-menu {
    padding: 16px 0;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu li {
    margin-bottom: 4px;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition);
    border-radius: 0;
}

.sidebar-menu a:hover {
    color: var(--primary-color);
    background-color: rgba(24, 144, 255, 0.1);
}

.sidebar-menu li.active a {
    color: var(--primary-color);
    background-color: rgba(24, 144, 255, 0.1);
    font-weight: 500;
}

.sidebar-menu i {
    font-size: 18px;
    margin-right: 12px;
    width: 24px;
    text-align: center;
}

.sidebar-collapsed .sidebar-menu span {
    display: none;
}

/* 主内容区 */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: var(--transition);
}

.sidebar-collapsed .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* 顶部导航 */
.header {
    height: var(--header-height);
    background-color: var(--bg-white);
    border-bottom: 1px solid var(--border-color);
    padding: 0 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 900;
}

.header-left {
    display: flex;
    align-items: center;
}

.menu-toggle {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    font-size: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: var(--text-secondary);
}

.header h2 {
    font-size: 20px;
    font-weight: 500;
}

.header-right {
    display: flex;
    align-items: center;
}

.search-bar {
    position: relative;
    margin-right: 16px;
}

.search-bar i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

.search-bar input {
    width: 240px;
    height: 36px;
    padding: 0 12px 0 36px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    background-color: var(--bg-light);
    transition: var(--transition);
}

.search-bar input:focus {
    border-color: var(--primary-color);
    background-color: var(--bg-white);
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.header-actions {
    display: flex;
    align-items: center;
}

.action-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    font-size: 20px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    color: var(--text-secondary);
    margin-right: 8px;
}

.badge {
    position: absolute;
    top: 0;
    right: 0;
    min-width: 20px;
    height: 20px;
    border-radius: 10px;
    background-color: var(--danger-color);
    color: white;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 6px;
}

.user-dropdown {
    position: relative;
}

.user-btn {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0 8px;
}

.avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    margin-right: 8px;
}

.user-btn span {
    margin-right: 8px;
    color: var(--text-color);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 180px;
    background-color: var(--bg-white);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    padding: 8px 0;
    display: none;
    z-index: 1000;
}

.user-dropdown:hover .dropdown-menu {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
}

.dropdown-item:hover {
    background-color: var(--bg-light);
}

.dropdown-item i {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

.dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 8px 0;
}

/* 内容区域 */
.content {
    padding: 24px;
}

/* 欢迎卡片 */
.welcome-card {
    background-color: var(--bg-white);
    border-radius: var(--radius);
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--shadow);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.welcome-info h3 {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 8px;
}

.welcome-info p {
    color: var(--text-secondary);
}

.btn {
    padding: 8px 16px;
    border-radius: var(--radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: inline-flex;
    align-items: center;
    border: none;
}

.btn i {
    margin-right: 8px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
}

/* 数据卡片 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    margin-bottom: 24px;
}

.stat-card {
    background-color: var(--bg-white);
    border-radius: var(--radius);
    padding: 24px;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-right: 16px;
}

.stat-info {
    flex: 1;
}

.stat-info h4 {
    font-size: 14px;
    font-weight: 400;
    color: var(--text-secondary);
    margin-bottom: 8px;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
}

.stat-change {
    display: flex;
    align-items: center;
    font-size: 12px;
}

.stat-change i {
    margin-right: 4px;
}

.stat-change.increase {
    color: var(--secondary-color);
}

.stat-change.decrease {
    color: var(--danger-color);
}

.period {
    color: var(--text-light);
    margin-left: 4px;
}

/* 图表区域 */
.chart-section {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
    margin-bottom: 24px;
}

.chart-card {
    background-color: var(--bg-white);
    border-radius: var(--radius);
    padding: 24px;
    box-shadow: var(--shadow);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.chart-header h3 {
    font-size: 18px;
    font-weight: 500;
}

.chart-actions select {
    padding: 6px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    background-color: var(--bg-white);
    font-size: 14px;
    color: var(--text-color);
    cursor: pointer;
}

.chart-container {
    height: 300px;
}

/* 活动和待办事项 */
.activity-section {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
}

.activity-card, .todo-card {
    background-color: var(--bg-white);
    border-radius: var(--radius);
    padding: 24px;
    box-shadow: var(--shadow);
}

.activity-header, .todo-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
}

.activity-header h3, .todo-header h3 {
    font-size: 18px;
    font-weight: 500;
}

.view-all {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 14px;
}

.view-all:hover {
    text-decoration: underline;
}

.add-todo-btn {
    display: flex;
    align-items: center;
    background-color: var(--bg-light);
    border: none;
    border-radius: var(--radius);
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition);
}

.add-todo-btn:hover {
    background-color: var(--border-color);
}

.add-todo-btn i {
    margin-right: 4px;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.activity-item {
    display: flex;
    align-items: flex-start;
}

.activity-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    margin-right: 12px;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 500;
    margin-bottom: 4px;
}

.activity-desc {
    color: var(--text-secondary);
    font-size: 14px;
    margin-bottom: 4px;
}

.activity-time {
    color: var(--text-light);
    font-size: 12px;
}

.todo-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.todo-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
}

.todo-item input[type="checkbox"] {
    margin-right: 12px;
}

.todo-item label {
    flex: 1;
    cursor: pointer;
}

.todo-item label.completed {
    text-decoration: line-through;
    color: var(--text-light);
}

.todo-tag {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.todo-tag.urgent {
    background-color: rgba(255, 77, 79, 0.1);
    color: var(--danger-color);
}

.todo-tag.important {
    background-color: rgba(250, 173, 20, 0.1);
    color: var(--warning-color);
}

.todo-tag.normal {
    background-color: rgba(24, 144, 255, 0.1);
    color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .stats-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .chart-section, .activity-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar-open .sidebar {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .stats-cards {
        grid-template-columns: 1fr;
    }
    
    .search-bar input {
        width: 160px;
    }

    .monitor-section {
        grid-template-columns: 1fr;
    }

    .monitor-grid {
        grid-template-columns: 1fr;
    }

    .api-stats {
        grid-template-columns: 1fr;
    }
}

/* 系统监控样式 */
.monitor-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
}

.monitor-card {
    background: var(--bg-white);
    border-radius: var(--radius);
    box-shadow: var(--shadow);
    padding: 24px;
}

.monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.monitor-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text-secondary);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-online {
    background-color: var(--secondary-color);
    box-shadow: 0 0 0 2px rgba(82, 196, 26, 0.2);
}

.status-offline {
    background-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}

.status-warning {
    background-color: var(--warning-color);
    box-shadow: 0 0 0 2px rgba(250, 173, 20, 0.2);
}

.monitor-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.monitor-item {
    text-align: center;
}

.monitor-label {
    font-size: 12px;
    color: var(--text-light);
    margin-bottom: 4px;
}

.monitor-value {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
}

.monitor-progress {
    width: 100%;
    height: 4px;
    background-color: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: 2px;
    transition: width 0.3s ease;
}

.refresh-btn {
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    color: var(--text-light);
    transition: var(--transition);
}

.refresh-btn:hover {
    color: var(--primary-color);
    background-color: rgba(22, 119, 255, 0.1);
}

.api-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.api-stat-item {
    text-align: center;
    padding: 12px;
    border-radius: var(--radius);
    background-color: #fafafa;
}

.api-stat-label {
    font-size: 12px;
    color: var(--text-light);
    margin-bottom: 4px;
}

.api-stat-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.api-stat-change {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 2px;
}

.api-stat-change.increase {
    color: var(--secondary-color);
}

.api-stat-change.decrease {
    color: var(--danger-color);
}

.chart-card.full-width {
    grid-column: 1 / -1;
}

/* 实时监控按钮 */
.btn.active {
    background-color: var(--primary-color);
    color: white;
}

.btn.active i {
    animation: spin 2s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 代理商层级结构样式 */
.hierarchy-tree {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.agent-node {
    cursor: pointer;
    transition: all 0.3s ease;
}

.agent-card {
    border: 2px solid #e8e8e8;
    border-radius: 8px;
    padding: 12px;
    background: #fff;
    transition: all 0.3s ease;
    position: relative;
}

.agent-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.agent-card.level-1 { border-color: #52c41a; }
.agent-card.level-2 { border-color: #1890ff; }
.agent-card.level-3 { border-color: #722ed1; }
.agent-card.level-4 { border-color: #fa8c16; }
.agent-card.level-5 { border-color: #f5222d; }

.agent-card.inactive {
    opacity: 0.6;
    background-color: #f5f5f5;
}

.current-agent-card {
    border-width: 3px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.agent-avatar {
    font-size: 2rem;
    color: #666;
    text-align: center;
    margin-bottom: 8px;
}

.agent-name {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 4px;
}

.agent-level {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.agent-stats {
    font-size: 0.8rem;
    color: #999;
}

.agent-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    display: none;
}

.agent-card:hover .agent-actions {
    display: block;
}

.upline-chain {
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 20px;
}

.downline-tree {
    padding-top: 20px;
}

.tree-container {
    max-height: 400px;
    overflow-y: auto;
}

.tree-node {
    margin-bottom: 8px;
    position: relative;
}

.tree-node::before {
    content: '';
    position: absolute;
    left: -15px;
    top: 20px;
    width: 10px;
    height: 1px;
    background-color: #d9d9d9;
}

.level-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    color: white;
    font-weight: bold;
    font-size: 0.8rem;
}

.level-badge.level-1 { background-color: #52c41a; }
.level-badge.level-2 { background-color: #1890ff; }
.level-badge.level-3 { background-color: #722ed1; }
.level-badge.level-4 { background-color: #fa8c16; }
.level-badge.level-5 { background-color: #f5222d; }

.hierarchy-item {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.hierarchy-item:last-child {
    border-bottom: none;
}

.stat-item {
    text-align: center;
    padding: 12px;
    background-color: #fafafa;
    border-radius: 6px;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #1890ff;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    margin-top: 4px;
}

.level-distribution {
    background-color: #fafafa;
    padding: 12px;
    border-radius: 6px;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-content {
    background: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
}