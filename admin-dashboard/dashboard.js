// 获取真实数据
async function fetchDashboardStats() {
    try {
        const response = await fetch('/ad/api/dashboard/stats');
        const result = await response.json();
        if (result.success) {
            return result.data;
        } else {
            console.error('获取统计数据失败:', result.message);
            return null;
        }
    } catch (error) {
        console.error('获取统计数据失败:', error);
        return null;
    }
}

// 更新统计卡片
function updateStatsCards(data) {
    if (!data) return;

    // 更新用户统计
    if (data.users) {
        document.querySelector('.stat-card:nth-child(1) .stat-number').textContent = data.users.totalUsers || 0;
        document.querySelector('.stat-card:nth-child(2) .stat-number').textContent = data.users.activeUsers || 0;
    }

    // 更新订单统计
    if (data.orders) {
        document.querySelector('.stat-card:nth-child(3) .stat-number').textContent = data.orders.totalOrders || 0;
        document.querySelector('.stat-card:nth-child(4) .stat-number').textContent = '¥' + (data.orders.totalRevenue || 0).toLocaleString();
    }
}

document.addEventListener('DOMContentLoaded', async () => {
    // 获取并更新真实数据
    const dashboardData = await fetchDashboardStats();
    updateStatsCards(dashboardData);

    // 侧边栏折叠/展开
    const menuToggle = document.querySelector('.menu-toggle');
    const adminLayout = document.querySelector('.admin-layout');
    
    menuToggle.addEventListener('click', () => {
        adminLayout.classList.toggle('sidebar-collapsed');
    });
    
    // 响应式处理
    function handleResponsive() {
        if (window.innerWidth <= 768) {
            adminLayout.classList.add('sidebar-collapsed');
            
            // 添加移动端点击侧边栏外部关闭侧边栏
            document.addEventListener('click', (e) => {
                if (adminLayout.classList.contains('sidebar-open') && 
                    !e.target.closest('.sidebar') && 
                    !e.target.closest('.menu-toggle')) {
                    adminLayout.classList.remove('sidebar-open');
                }
            });
            
            // 点击菜单按钮切换侧边栏
            menuToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                adminLayout.classList.toggle('sidebar-open');
            });
        } else {
            adminLayout.classList.remove('sidebar-open');
        }
    }
    
    // 初始化和窗口大小变化时处理响应式
    handleResponsive();
    window.addEventListener('resize', handleResponsive);
    
    // 用户下拉菜单
    const userBtn = document.querySelector('.user-btn');
    const dropdownMenu = document.querySelector('.dropdown-menu');
    
    document.addEventListener('click', (e) => {
        if (!userBtn.contains(e.target)) {
            dropdownMenu.style.display = 'none';
        }
    });
    
    userBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        dropdownMenu.style.display = dropdownMenu.style.display === 'block' ? 'none' : 'block';
    });
    
    // 初始化图表
    initCharts();

    // 初始化监控
    initMonitoring();

    // 待办事项复选框
    const todoCheckboxes = document.querySelectorAll('.todo-item input[type="checkbox"]');
    todoCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            const label = checkbox.nextElementSibling;
            if (checkbox.checked) {
                label.classList.add('completed');
            } else {
                label.classList.remove('completed');
            }
        });
    });
});

// 初始化图表
function initCharts() {
    // 收入趋势图表
    const revenueChart = echarts.init(document.getElementById('revenue-chart'));
    const revenueOption = {
        tooltip: {
            trigger: 'axis',
            formatter: '{b}: ¥{c}'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                formatter: '¥{value}'
            }
        },
        series: [
            {
                name: '收入',
                type: 'line',
                smooth: true,
                lineStyle: {
                    width: 3,
                    color: '#1677ff'
                },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                            offset: 0,
                            color: 'rgba(22, 119, 255, 0.3)'
                        },
                        {
                            offset: 1,
                            color: 'rgba(22, 119, 255, 0.1)'
                        }
                    ])
                },
                data: [18000, 22000, 25000, 27000, 30000, 35000, 40000, 45000, 48000, 52000, 55000, 60000]
            }
        ]
    };
    revenueChart.setOption(revenueOption);
    
    // 用户增长图表
    const userChart = echarts.init(document.getElementById('user-chart'));
    const userOption = {
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月']
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '新增用户',
                type: 'bar',
                barWidth: '60%',
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {offset: 0, color: '#83bff6'},
                        {offset: 0.5, color: '#188df0'},
                        {offset: 1, color: '#188df0'}
                    ])
                },
                data: [320, 420, 480, 520, 590, 650, 720, 820, 900, 980, 1050, 1120]
            }
        ]
    };
    userChart.setOption(userOption);
    
    // 图表响应窗口大小变化
    window.addEventListener('resize', () => {
        revenueChart.resize();
        userChart.resize();
    });
    
    // 图表周期切换
    document.getElementById('revenue-period').addEventListener('change', (e) => {
        const period = e.target.value;
        let data = [];
        
        switch(period) {
            case 'week':
                data = [9000, 10500, 12000, 11000, 13000, 14500, 15000];
                revenueOption.xAxis.data = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                break;
            case 'month':
                data = [18000, 22000, 25000, 27000, 30000, 35000, 40000, 45000, 48000, 52000, 55000, 60000];
                revenueOption.xAxis.data = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
                break;
            case 'quarter':
                data = [65000, 85000, 110000, 135000];
                revenueOption.xAxis.data = ['Q1', 'Q2', 'Q3', 'Q4'];
                break;
            case 'year':
                data = [395000, 450000, 520000, 600000, 680000];
                revenueOption.xAxis.data = ['2020', '2021', '2022', '2023', '2024'];
                break;
        }
        
        revenueOption.series[0].data = data;
        revenueChart.setOption(revenueOption);
    });
    
    document.getElementById('user-period').addEventListener('change', (e) => {
        const period = e.target.value;
        let data = [];
        
        switch(period) {
            case 'week':
                data = [120, 132, 101, 134, 90, 180, 210];
                userOption.xAxis.data = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
                break;
            case 'month':
                data = [320, 420, 480, 520, 590, 650, 720, 820, 900, 980, 1050, 1120];
                userOption.xAxis.data = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
                break;
            case 'quarter':
                data = [1220, 1960, 2500, 3050];
                userOption.xAxis.data = ['Q1', 'Q2', 'Q3', 'Q4'];
                break;
            case 'year':
                data = [3200, 4500, 6800, 9500, 12000];
                userOption.xAxis.data = ['2020', '2021', '2022', '2023', '2024'];
                break;
        }
        
        userOption.series[0].data = data;
        userChart.setOption(userOption);
    });

    // API调用趋势图表
    const apiChart = echarts.init(document.getElementById('api-chart'));
    const apiOption = {
        tooltip: {
            trigger: 'axis',
            formatter: function(params) {
                let result = params[0].name + '<br/>';
                params.forEach(param => {
                    result += param.marker + param.seriesName + ': ' + param.value + '<br/>';
                });
                return result;
            }
        },
        legend: {
            data: ['成功调用', '失败调用', '响应时间(ms)']
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
        },
        yAxis: [
            {
                type: 'value',
                name: '调用次数',
                position: 'left'
            },
            {
                type: 'value',
                name: '响应时间(ms)',
                position: 'right'
            }
        ],
        series: [
            {
                name: '成功调用',
                type: 'line',
                smooth: true,
                lineStyle: { color: '#52c41a' },
                areaStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: 'rgba(82, 196, 26, 0.3)' },
                        { offset: 1, color: 'rgba(82, 196, 26, 0.1)' }
                    ])
                },
                data: [1200, 800, 1500, 2200, 1800, 2500, 1900]
            },
            {
                name: '失败调用',
                type: 'line',
                smooth: true,
                lineStyle: { color: '#ff4d4f' },
                data: [20, 15, 35, 45, 25, 40, 30]
            },
            {
                name: '响应时间(ms)',
                type: 'line',
                smooth: true,
                yAxisIndex: 1,
                lineStyle: { color: '#faad14' },
                data: [245, 220, 280, 320, 260, 290, 245]
            }
        ]
    };
    apiChart.setOption(apiOption);

    // 图表响应窗口大小变化
    window.addEventListener('resize', () => {
        revenueChart.resize();
        userChart.resize();
        apiChart.resize();
    });
}

// 初始化监控功能
function initMonitoring() {
    let isRealTimeActive = false;
    let monitoringInterval;

    // 实时监控切换
    const realTimeToggle = document.getElementById('realTimeToggle');
    realTimeToggle.addEventListener('click', () => {
        isRealTimeActive = !isRealTimeActive;

        if (isRealTimeActive) {
            realTimeToggle.classList.add('active');
            realTimeToggle.innerHTML = '<i class="bi bi-pause-circle"></i><span>停止监控</span>';
            startRealTimeMonitoring();
        } else {
            realTimeToggle.classList.remove('active');
            realTimeToggle.innerHTML = '<i class="bi bi-play-circle"></i><span>实时监控</span>';
            stopRealTimeMonitoring();
        }
    });

    // API统计刷新
    const refreshApiStats = document.getElementById('refreshApiStats');
    refreshApiStats.addEventListener('click', () => {
        updateApiStats();
        // 添加旋转动画
        refreshApiStats.style.transform = 'rotate(360deg)';
        setTimeout(() => {
            refreshApiStats.style.transform = 'rotate(0deg)';
        }, 500);
    });

    // 初始加载系统状态
    updateSystemStatus();
    updateApiStats();

    // 定期更新系统状态（每30秒）
    setInterval(updateSystemStatus, 30000);

    function startRealTimeMonitoring() {
        monitoringInterval = setInterval(() => {
            updateSystemStatus();
            updateApiStats();
        }, 5000); // 每5秒更新一次
    }

    function stopRealTimeMonitoring() {
        if (monitoringInterval) {
            clearInterval(monitoringInterval);
        }
    }
}

// 更新系统状态
async function updateSystemStatus() {
    try {
        // 模拟API调用获取系统状态
        // 在实际应用中，这里应该调用真实的API
        const systemStatus = await getSystemStatus();

        // 更新CPU使用率
        updateMonitorValue('cpuUsage', systemStatus.cpu + '%', systemStatus.cpu);

        // 更新内存使用率
        updateMonitorValue('memoryUsage', systemStatus.memory + '%', systemStatus.memory);

        // 更新磁盘使用率
        updateMonitorValue('diskUsage', systemStatus.disk + '%', systemStatus.disk);

        // 更新网络流量
        document.getElementById('networkTraffic').textContent = systemStatus.network;

    } catch (error) {
        console.error('更新系统状态失败:', error);
    }
}

// 更新API统计
async function updateApiStats() {
    try {
        // 模拟API调用获取API统计
        const apiStats = await getApiStats();

        document.getElementById('todayApiCalls').textContent = apiStats.todayCalls.toLocaleString();
        document.getElementById('apiSuccessRate').textContent = apiStats.successRate + '%';
        document.getElementById('avgResponseTime').textContent = apiStats.avgResponseTime + 'ms';
        document.getElementById('apiErrors').textContent = apiStats.errors;

    } catch (error) {
        console.error('更新API统计失败:', error);
    }
}

// 更新监控值和进度条
function updateMonitorValue(elementId, text, percentage) {
    const element = document.getElementById(elementId);
    element.textContent = text;

    // 更新对应的进度条
    const progressBar = element.parentElement.querySelector('.progress-bar');
    if (progressBar) {
        progressBar.style.width = percentage + '%';

        // 根据使用率设置颜色
        if (percentage < 50) {
            progressBar.style.backgroundColor = '#52c41a';
        } else if (percentage < 80) {
            progressBar.style.backgroundColor = '#faad14';
        } else {
            progressBar.style.backgroundColor = '#ff4d4f';
        }
    }
}

// 获取系统状态
async function getSystemStatus() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/monitor/system-status', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('获取系统状态失败');
        }

        const data = await response.json();
        return data.success ? data.data : null;

    } catch (error) {
        console.error('获取系统状态错误:', error);
        // 返回模拟数据作为备选
        return {
            cpu: Math.floor(Math.random() * 30) + 30,
            memory: Math.floor(Math.random() * 40) + 40,
            disk: Math.floor(Math.random() * 20) + 30,
            network: (Math.random() * 2 + 0.5).toFixed(1) + 'MB/s'
        };
    }
}

// 获取API统计
async function getApiStats() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/monitor/api-stats', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('获取API统计失败');
        }

        const data = await response.json();
        return data.success ? data.data : null;

    } catch (error) {
        console.error('获取API统计错误:', error);
        // 返回模拟数据作为备选
        return {
            todayCalls: Math.floor(Math.random() * 5000) + 10000,
            successRate: (Math.random() * 2 + 98).toFixed(1),
            avgResponseTime: Math.floor(Math.random() * 100) + 200,
            errors: Math.floor(Math.random() * 50) + 10
        };
    }
}