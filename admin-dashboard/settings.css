.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-title {
    color: #495057;
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 1.5rem;
}

.form-label {
    color: #495057;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.input-group {
    margin-bottom: 1rem;
}

.input-group-text {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #495057;
    min-width: 120px;
}

.form-control {
    border-color: #dee2e6;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-check {
    margin-bottom: 0.5rem;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.nav-tabs {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 1.5rem;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 0.75rem 1rem;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #495057;
}

.nav-tabs .nav-link.active {
    border-bottom: 2px solid #0d6efd;
    color: #0d6efd;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    padding: 0.5rem 1.5rem;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.alert {
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    padding: 1rem;
}

.alert-success {
    background-color: #d1e7dd;
    border-color: #badbcc;
    color: #0f5132;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c2c7;
    color: #842029;
}

@media (max-width: 768px) {
    .input-group-text {
        min-width: auto;
    }
    
    .nav-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .nav-tabs .nav-link {
        padding: 0.5rem 0.75rem;
    }
} 