// 权限树数据结构
const permissionTree = {
    system: {
        label: '系统管理',
        permissions: {
            'system.view': '查看系统设置',
            'system.edit': '编辑系统设置',
            'system.security': '安全设置管理',
            'system.api': 'API设置管理'
        }
    },
    user: {
        label: '用户管理',
        permissions: {
            'user.view': '查看用户',
            'user.add': '添加用户',
            'user.edit': '编辑用户',
            'user.delete': '删除用户',
            'user.export': '导出用户数据',
            'user.import': '导入用户数据'
        }
    },
    role: {
        label: '角色管理',
        permissions: {
            'role.view': '查看角色',
            'role.add': '添加角色',
            'role.edit': '编辑角色',
            'role.delete': '删除角色'
        }
    },
    agent: {
        label: '代理商管理',
        permissions: {
            'agent.view': '查看代理商',
            'agent.add': '添加代理商',
            'agent.edit': '编辑代理商',
            'agent.delete': '删除代理商',
            'agent.commission': '佣金设置'
        }
    },
    finance: {
        label: '财务管理',
        permissions: {
            'finance.view': '查看财务数据',
            'finance.transaction': '交易管理',
            'finance.withdraw': '提现管理',
            'finance.report': '财务报表'
        }
    }
};

// 页面加载完成后执行
$(document).ready(function() {
    // 检查登录状态和权限
    checkAuth();
    
    // 加载角色列表
    loadRoles();
    
    // 初始化权限树
    initPermissionTree('#addPermissionTree');
    initPermissionTree('#editPermissionTree');
    
    // 绑定事件处理器
    bindEventHandlers();
});

// 检查用户登录状态和权限
function checkAuth() {
    const token = localStorage.getItem('token');
    if (!token) {
        window.location.href = 'login.html';
        return;
    }
    
    // 检查是否有角色管理权限
    $.ajax({
        url: '/api/auth/check-permission',
        headers: { 'Authorization': `Bearer ${token}` },
        data: { permission: 'role.view' },
        method: 'POST',
        success: function(response) {
            if (!response.hasPermission) {
                alert('您没有访问此页面的权限');
                window.location.href = 'dashboard.html';
            }
        },
        error: function() {
            alert('权限验证失败');
            window.location.href = 'login.html';
        }
    });
}

// 加载角色列表
function loadRoles() {
    const token = localStorage.getItem('token');
    $.ajax({
        url: '/api/roles',
        headers: { 'Authorization': `Bearer ${token}` },
        method: 'GET',
        success: function(response) {
            const roles = response.data;
            const tbody = $('#roleTableBody');
            tbody.empty();
            
            roles.forEach(role => {
                const tr = $('<tr>');
                tr.html(`
                    <td>${role.id}</td>
                    <td>${role.name}</td>
                    <td>${role.description || '-'}</td>
                    <td>${role.userCount || 0}</td>
                    <td>${new Date(role.createdAt).toLocaleString()}</td>
                    <td class="role-actions">
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="viewRoleUsers(${role.id})">
                                <i class="bi bi-people"></i>
                            </button>
                            <button class="btn btn-outline-secondary" onclick="editRole(${role.id})">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="copyRole(${role.id})">
                                <i class="bi bi-files"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteRole(${role.id})">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                `);
                tbody.append(tr);
            });
        },
        error: function() {
            alert('加载角色列表失败');
        }
    });
}

// 初始化权限树
function initPermissionTree(selector) {
    const container = $(selector);
    container.empty();
    
    const ul = $('<ul>');
    
    Object.entries(permissionTree).forEach(([key, group]) => {
        const li = $('<li>');
        const groupDiv = $('<div class="permission-group">');
        groupDiv.html(`
            <span class="toggle-icon">▼</span>
            <span>${group.label}</span>
        `);
        
        const permissionsUl = $('<ul>');
        Object.entries(group.permissions).forEach(([permKey, permLabel]) => {
            const permLi = $('<li>');
            permLi.html(`
                <div class="permission-item">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="${permKey}">
                        <label class="form-check-label">${permLabel}</label>
                    </div>
                </div>
            `);
            permissionsUl.append(permLi);
        });
        
        li.append(groupDiv, permissionsUl);
        ul.append(li);
    });
    
    container.append(ul);
    
    // 绑定展开/折叠事件
    container.find('.toggle-icon').click(function() {
        const icon = $(this);
        const ul = icon.closest('li').find('ul').first();
        ul.slideToggle();
        icon.text(icon.text() === '▼' ? '▶' : '▼');
    });
}

// 绑定事件处理器
function bindEventHandlers() {
    // 添加角色表单提交
    $('#addRoleSubmit').click(function() {
        const form = $('#addRoleForm');
        const permissions = getSelectedPermissions('#addPermissionTree');
        
        const data = {
            name: form.find('[name="name"]').val(),
            description: form.find('[name="description"]').val(),
            permissions: permissions
        };
        
        const token = localStorage.getItem('token');
        $.ajax({
            url: '/api/roles',
            headers: { 'Authorization': `Bearer ${token}` },
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function() {
                $('#addRoleModal').modal('hide');
                form[0].reset();
                loadRoles();
                alert('角色添加成功');
            },
            error: function(xhr) {
                alert(xhr.responseJSON?.message || '角色添加失败');
            }
        });
    });
    
    // 编辑角色表单提交
    $('#editRoleSubmit').click(function() {
        const form = $('#editRoleForm');
        const roleId = form.find('[name="roleId"]').val();
        const permissions = getSelectedPermissions('#editPermissionTree');
        
        const data = {
            name: form.find('[name="name"]').val(),
            description: form.find('[name="description"]').val(),
            permissions: permissions
        };
        
        const token = localStorage.getItem('token');
        $.ajax({
            url: `/api/roles/${roleId}`,
            headers: { 'Authorization': `Bearer ${token}` },
            method: 'PUT',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function() {
                $('#editRoleModal').modal('hide');
                loadRoles();
                alert('角色更新成功');
            },
            error: function(xhr) {
                alert(xhr.responseJSON?.message || '角色更新失败');
            }
        });
    });
    
    // 复制角色表单提交
    $('#copyRoleSubmit').click(function() {
        const form = $('#copyRoleForm');
        const sourceRoleId = form.find('[name="sourceRoleId"]').val();
        
        const data = {
            name: form.find('[name="name"]').val(),
            description: form.find('[name="description"]').val(),
            copyPermissions: form.find('[name="copyPermissions"]').is(':checked')
        };
        
        const token = localStorage.getItem('token');
        $.ajax({
            url: `/api/roles/${sourceRoleId}/copy`,
            headers: { 'Authorization': `Bearer ${token}` },
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(data),
            success: function() {
                $('#copyRoleModal').modal('hide');
                form[0].reset();
                loadRoles();
                alert('角色复制成功');
            },
            error: function(xhr) {
                alert(xhr.responseJSON?.message || '角色复制失败');
            }
        });
    });
}

// 获取选中的权限
function getSelectedPermissions(selector) {
    const permissions = [];
    $(selector).find('input[type="checkbox"]:checked').each(function() {
        permissions.push($(this).val());
    });
    return permissions;
}

// 编辑角色
function editRole(roleId) {
    const token = localStorage.getItem('token');
    $.ajax({
        url: `/api/roles/${roleId}`,
        headers: { 'Authorization': `Bearer ${token}` },
        method: 'GET',
        success: function(response) {
            const role = response.data;
            const form = $('#editRoleForm');
            
            form.find('[name="roleId"]').val(role.id);
            form.find('[name="name"]').val(role.name);
            form.find('[name="description"]').val(role.description);
            
            // 重置权限树
            initPermissionTree('#editPermissionTree');
            
            // 设置权限选中状态
            role.permissions.forEach(permission => {
                form.find(`input[value="${permission}"]`).prop('checked', true);
            });
            
            $('#editRoleModal').modal('show');
        },
        error: function() {
            alert('加载角色信息失败');
        }
    });
}

// 复制角色
function copyRole(roleId) {
    const form = $('#copyRoleForm');
    form.find('[name="sourceRoleId"]').val(roleId);
    $('#copyRoleModal').modal('show');
}

// 删除角色
function deleteRole(roleId) {
    if (!confirm('确定要删除这个角色吗？')) {
        return;
    }
    
    const token = localStorage.getItem('token');
    $.ajax({
        url: `/api/roles/${roleId}`,
        headers: { 'Authorization': `Bearer ${token}` },
        method: 'DELETE',
        success: function() {
            loadRoles();
            alert('角色删除成功');
        },
        error: function(xhr) {
            alert(xhr.responseJSON?.message || '角色删除失败');
        }
    });
}

// 查看角色用户
function viewRoleUsers(roleId) {
    const token = localStorage.getItem('token');
    $.ajax({
        url: `/api/roles/${roleId}/users`,
        headers: { 'Authorization': `Bearer ${token}` },
        method: 'GET',
        success: function(response) {
            const users = response.data;
            const tbody = $('#roleUsersTableBody');
            tbody.empty();
            
            users.forEach(user => {
                const tr = $('<tr>');
                tr.html(`
                    <td>${user.username}</td>
                    <td>${user.email}</td>
                    <td>${user.status === 'active' ? '活跃' : '禁用'}</td>
                    <td>${user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleString() : '-'}</td>
                `);
                tbody.append(tr);
            });
            
            $('#viewUsersModal').modal('show');
        },
        error: function() {
            alert('加载用户列表失败');
        }
    });
} 