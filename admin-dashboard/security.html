<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安全中心 - 管理后台</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="security.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.html">
                                <i class="bi bi-house-door"></i> 仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.html">
                                <i class="bi bi-people"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="roles.html">
                                <i class="bi bi-person-badge"></i> 角色管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="agents.html">
                                <i class="bi bi-person-lines-fill"></i> 代理商管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="finance.html">
                                <i class="bi bi-cash-stack"></i> 财务统计
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="monitor.html">
                                <i class="bi bi-graph-up"></i> 系统监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="security.html">
                                <i class="bi bi-shield-lock"></i> 安全中心
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主要内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- 页面标题 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">安全中心</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-sm btn-outline-secondary" id="refreshBtn">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                </div>

                <!-- 安全状态卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card security-card">
                            <div class="card-body">
                                <div class="security-icon bg-success">
                                    <i class="bi bi-shield-check"></i>
                                </div>
                                <h5 class="card-title">系统安全状态</h5>
                                <h2 class="card-text">正常</h2>
                                <small class="text-muted">最后检查: 2025-07-09 14:30</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card security-card">
                            <div class="card-body">
                                <div class="security-icon bg-warning">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                                <h5 class="card-title">待处理安全警报</h5>
                                <h2 class="card-text">3</h2>
                                <small class="text-muted">较上周 <span class="text-danger">+2</span></small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card security-card">
                            <div class="card-body">
                                <div class="security-icon bg-danger">
                                    <i class="bi bi-x-octagon"></i>
                                </div>
                                <h5 class="card-title">IP黑名单数量</h5>
                                <h2 class="card-text">27</h2>
                                <small class="text-muted">较上月 <span class="text-danger">+5</span></small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card security-card">
                            <div class="card-body">
                                <div class="security-icon bg-info">
                                    <i class="bi bi-file-earmark-lock"></i>
                                </div>
                                <h5 class="card-title">敏感操作审批</h5>
                                <h2 class="card-text">2</h2>
                                <small class="text-muted">待审批</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 快捷操作 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-lightning"></i> 快捷操作
                                </h5>
                                <div class="row">
                                    <div class="col-md-3">
                                        <a href="login-logs.html" class="btn btn-outline-primary w-100 mb-2">
                                            <i class="bi bi-clock-history"></i>
                                            <div>登录日志</div>
                                            <small class="text-muted">查看登录记录</small>
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-warning w-100 mb-2" onclick="showSecurityScan()">
                                            <i class="bi bi-shield-exclamation"></i>
                                            <div>安全扫描</div>
                                            <small class="text-muted">系统安全检查</small>
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-info w-100 mb-2" onclick="showBackupStatus()">
                                            <i class="bi bi-cloud-arrow-up"></i>
                                            <div>数据备份</div>
                                            <small class="text-muted">备份管理</small>
                                        </button>
                                    </div>
                                    <div class="col-md-3">
                                        <button class="btn btn-outline-success w-100 mb-2" onclick="showSystemHealth()">
                                            <i class="bi bi-heart-pulse"></i>
                                            <div>系统健康</div>
                                            <small class="text-muted">系统状态检查</small>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 选项卡 -->
                <ul class="nav nav-tabs" id="securityTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="risk-rules-tab" data-bs-toggle="tab" data-bs-target="#risk-rules" type="button" role="tab">
                            <i class="bi bi-exclamation-triangle"></i> 风控规则
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="ip-blacklist-tab" data-bs-toggle="tab" data-bs-target="#ip-blacklist" type="button" role="tab">
                            <i class="bi bi-shield-x"></i> IP黑名单
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="sensitive-operations-tab" data-bs-toggle="tab" data-bs-target="#sensitive-operations" type="button" role="tab">
                            <i class="bi bi-check-circle"></i> 操作审批
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="security-logs-tab" data-bs-toggle="tab" data-bs-target="#security-logs" type="button" role="tab">
                            <i class="bi bi-journal-text"></i> 安全日志
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="data-backup-tab" data-bs-toggle="tab" data-bs-target="#data-backup" type="button" role="tab">
                            <i class="bi bi-hdd"></i> 数据备份
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="securityTabsContent">
                    <!-- 风控规则 -->
                    <div class="tab-pane fade show active" id="risk-rules" role="tabpanel">
                        <div class="row mt-3">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">风控规则管理</h5>
                                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRuleModal">
                                            <i class="bi bi-plus"></i> 添加规则
                                        </button>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>规则名称</th>
                                                        <th>类型</th>
                                                        <th>风险等级</th>
                                                        <th>触发条件</th>
                                                        <th>处理动作</th>
                                                        <th>状态</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="riskRulesTableBody">
                                                    <!-- 风控规则数据将通过JavaScript动态加载 -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">风险统计</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-6 mb-3">
                                                <h4 id="highRiskCount" class="text-danger">0</h4>
                                                <small class="text-muted">高风险</small>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <h4 id="mediumRiskCount" class="text-warning">0</h4>
                                                <small class="text-muted">中风险</small>
                                            </div>
                                            <div class="col-6">
                                                <h4 id="lowRiskCount" class="text-info">0</h4>
                                                <small class="text-muted">低风险</small>
                                            </div>
                                            <div class="col-6">
                                                <h4 id="blockedCount" class="text-secondary">0</h4>
                                                <small class="text-muted">已拦截</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 风险趋势图 -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="mb-0">风险趋势</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="riskTrendChart" style="height: 200px;">
                                            <p class="text-center text-muted">风险趋势图表</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- IP黑名单 -->
                    <div class="tab-pane fade" id="ip-blacklist" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="card-title">IP黑名单管理</h5>
                                    <button class="btn btn-primary" id="addIpBtn">
                                        <i class="bi bi-plus"></i> 添加IP
                                    </button>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>IP地址</th>
                                                <th>添加原因</th>
                                                <th>添加时间</th>
                                                <th>添加人</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="ipBlacklistTable">
                                            <!-- IP黑名单数据将通过JavaScript动态加载 -->
                                            <tr>
                                                <td>*************</td>
                                                <td>多次尝试暴力登录</td>
                                                <td>2025-07-08 09:15</td>
                                                <td>系统自动</td>
                                                <td>
                                                    <button class="btn btn-sm btn-danger">移除</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>************</td>
                                                <td>恶意API调用</td>
                                                <td>2025-07-07 14:22</td>
                                                <td>admin</td>
                                                <td>
                                                    <button class="btn btn-sm btn-danger">移除</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 敏感操作审批 -->
                    <div class="tab-pane fade" id="sensitive-operations" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-body">
                                <h5 class="card-title">敏感操作审批</h5>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>操作ID</th>
                                                <th>操作类型</th>
                                                <th>申请人</th>
                                                <th>申请时间</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="sensitiveOperationsTable">
                                            <!-- 敏感操作数据将通过JavaScript动态加载 -->
                                            <tr>
                                                <td>SO-2025-0012</td>
                                                <td>批量删除用户</td>
                                                <td>operator1</td>
                                                <td>2025-07-09 10:30</td>
                                                <td><span class="badge bg-warning">待审批</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-success">批准</button>
                                                    <button class="btn btn-sm btn-danger">拒绝</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>SO-2025-0011</td>
                                                <td>修改系统配置</td>
                                                <td>operator2</td>
                                                <td>2025-07-08 16:45</td>
                                                <td><span class="badge bg-warning">待审批</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-success">批准</button>
                                                    <button class="btn btn-sm btn-danger">拒绝</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 安全日志 -->
                    <div class="tab-pane fade" id="security-logs" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="card-title">安全日志</h5>
                                    <div>
                                        <input type="text" class="form-control d-inline-block w-auto" placeholder="搜索日志...">
                                        <button class="btn btn-outline-secondary">
                                            <i class="bi bi-download"></i> 导出日志
                                        </button>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>时间</th>
                                                <th>级别</th>
                                                <th>事件类型</th>
                                                <th>IP地址</th>
                                                <th>用户</th>
                                                <th>详情</th>
                                            </tr>
                                        </thead>
                                        <tbody id="securityLogsTable">
                                            <!-- 安全日志数据将通过JavaScript动态加载 -->
                                            <tr>
                                                <td>2025-07-09 13:45:22</td>
                                                <td><span class="badge bg-danger">严重</span></td>
                                                <td>登录失败</td>
                                                <td>*************</td>
                                                <td>unknown</td>
                                                <td>多次尝试登录失败，账户已锁定</td>
                                            </tr>
                                            <tr>
                                                <td>2025-07-09 12:30:15</td>
                                                <td><span class="badge bg-warning">警告</span></td>
                                                <td>权限变更</td>
                                                <td>*************</td>
                                                <td>admin</td>
                                                <td>修改了用户 operator1 的权限</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 数据备份 -->
                    <div class="tab-pane fade" id="data-backup" role="tabpanel">
                        <div class="card mt-3">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h5 class="card-title">数据备份</h5>
                                    <button class="btn btn-primary" id="createBackupBtn">
                                        <i class="bi bi-cloud-arrow-up"></i> 创建备份
                                    </button>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>备份ID</th>
                                                <th>创建时间</th>
                                                <th>类型</th>
                                                <th>大小</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="backupsTable">
                                            <!-- 备份数据将通过JavaScript动态加载 -->
                                            <tr>
                                                <td>BK-2025-0705-001</td>
                                                <td>2025-07-05 03:00:00</td>
                                                <td>完整备份</td>
                                                <td>1.2 GB</td>
                                                <td><span class="badge bg-success">完成</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary">下载</button>
                                                    <button class="btn btn-sm btn-warning">恢复</button>
                                                    <button class="btn btn-sm btn-danger">删除</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>BK-2025-0704-001</td>
                                                <td>2025-07-04 03:00:00</td>
                                                <td>完整备份</td>
                                                <td>1.1 GB</td>
                                                <td><span class="badge bg-success">完成</span></td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary">下载</button>
                                                    <button class="btn btn-sm btn-warning">恢复</button>
                                                    <button class="btn btn-sm btn-danger">删除</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加IP黑名单模态框 -->
    <div class="modal fade" id="addIpModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加IP黑名单</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addIpForm">
                        <div class="mb-3">
                            <label for="ipAddress" class="form-label">IP地址</label>
                            <input type="text" class="form-control" id="ipAddress" required placeholder="例如: ***********">
                        </div>
                        <div class="mb-3">
                            <label for="reason" class="form-label">添加原因</label>
                            <textarea class="form-control" id="reason" rows="3" required placeholder="请输入将此IP添加到黑名单的原因..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveIpBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 创建备份模态框 -->
    <div class="modal fade" id="createBackupModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">创建数据备份</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="createBackupForm">
                        <div class="mb-3">
                            <label for="backupType" class="form-label">备份类型</label>
                            <select class="form-select" id="backupType" required>
                                <option value="full">完整备份</option>
                                <option value="incremental">增量备份</option>
                                <option value="differential">差异备份</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="backupDescription" class="form-label">备份描述</label>
                            <textarea class="form-control" id="backupDescription" rows="2" placeholder="可选: 为此备份添加描述..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="startBackupBtn">开始备份</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/security.js"></script>
    <script src="security.js"></script>
</body>
</html> 