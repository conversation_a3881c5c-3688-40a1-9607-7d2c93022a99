{"name": "admin-dashboard", "version": "1.0.0", "description": "WriterPro Admin Dashboard", "main": "server.js", "scripts": {"start": "node proxy-config.js", "start:3000": "node proxy-config.js", "start:8080": "node simple-server.js", "server": "node server.js", "dev": "nodemon server.js", "simple": "node simple-server.js", "init-db": "node scripts/initDb.js"}, "dependencies": {"bootstrap": "^5.1.3", "bootstrap-icons": "^1.8.1", "chart.js": "^3.7.1", "connect-mongo": "^5.1.0", "daterangepicker": "^3.1.0", "dotenv": "^17.1.0", "express": "^4.18.2", "express-session": "^1.18.1", "i18next": "^23.10.1", "i18next-browser-languagedetector": "^7.2.0", "jquery": "^3.6.0", "moment": "^2.29.1", "mongoose": "^8.16.2", "mysql2": "^3.6.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.0"}, "devDependencies": {"nodemon": "^2.0.15"}}