<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理 - 管理后台</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="users.css">
    <link rel="stylesheet" href="css/batch-operations.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.html">
                                <i class="bi bi-house-door"></i> 仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="users.html">
                                <i class="bi bi-people"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="roles.html">
                                <i class="bi bi-person-badge"></i> 角色管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="agents.html">
                                <i class="bi bi-person-lines-fill"></i> 代理商管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="finance.html">
                                <i class="bi bi-cash-stack"></i> 财务统计
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.html">
                                <i class="bi bi-gear"></i> 系统设置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主要内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">用户管理</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                            <i class="bi bi-person-plus"></i> 添加用户
                        </button>
                    </div>
                </div>

                <!-- 用户统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">总用户数</h5>
                                <h2 class="card-text" id="totalUsersCount">0</h2>
                                <small class="text-muted">较上月 <span id="userGrowth" class="text-success">+0%</span></small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">活跃用户</h5>
                                <h2 class="card-text" id="activeUsersCount">0</h2>
                                <small class="text-muted">7天内活跃</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">新增用户</h5>
                                <h2 class="card-text" id="newUsersCount">0</h2>
                                <small class="text-muted">本月新增</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">用户留存率</h5>
                                <h2 class="card-text" id="retentionRate">0%</h2>
                                <small class="text-muted">7天留存</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 选项卡导航 -->
                <ul class="nav nav-tabs" id="userTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="user-list-tab" data-bs-toggle="tab" data-bs-target="#user-list" type="button" role="tab">
                            <i class="bi bi-people"></i> 用户列表
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="user-analytics-tab" data-bs-toggle="tab" data-bs-target="#user-analytics" type="button" role="tab">
                            <i class="bi bi-graph-up"></i> 用户分析
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="user-behavior-tab" data-bs-toggle="tab" data-bs-target="#user-behavior" type="button" role="tab">
                            <i class="bi bi-activity"></i> 行为分析
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="userTabsContent">
                    <!-- 用户列表选项卡 -->
                    <div class="tab-pane fade show active" id="user-list" role="tabpanel">
                        <!-- 搜索和筛选 -->
                        <div class="row mb-3 mt-3">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                                    <input type="text" class="form-control" id="searchInput" placeholder="搜索用户...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="roleFilter">
                                    <option value="">所有角色</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="statusFilter">
                                    <option value="">所有状态</option>
                                    <option value="active">活跃</option>
                                    <option value="inactive">未激活</option>
                                    <option value="blocked">已封禁</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="text" class="form-control" id="dateRange" placeholder="注册时间范围">
                            </div>
                            <div class="col-md-1">
                                <button class="btn btn-outline-success export-btn" title="导出数据">
                                    <i class="bi bi-download"></i>
                                </button>
                            </div>
                        </div>

                <!-- 用户列表 -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover data-table">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" class="form-check-input select-all" title="全选">
                                </th>
                                <th>ID</th>
                                <th>用户名</th>
                                <th>邮箱</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>注册时间</th>
                                <th>最后登录</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="userTableBody">
                            <!-- 用户数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>

                <!-- 分页 -->
                <nav aria-label="用户列表分页" class="d-flex justify-content-between align-items-center">
                    <div class="pagination-info">
                        总共 <span id="totalUsers">0</span> 个用户
                    </div>
                    <ul class="pagination" id="pagination">
                        <!-- 分页按钮将通过JavaScript动态生成 -->
                    </ul>
                    <div class="pagination-size">
                        <select class="form-select" id="pageSizeSelect">
                            <option value="10">10条/页</option>
                            <option value="20">20条/页</option>
                            <option value="50">50条/页</option>
                            <option value="100">100条/页</option>
                        </select>
                    </div>
                </nav>
                    </div>

                    <!-- 用户分析选项卡 -->
                    <div class="tab-pane fade" id="user-analytics" role="tabpanel">
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">用户增长趋势</h5>
                                        <div id="userGrowthChart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">用户活跃度</h5>
                                        <div id="userActivityChart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">用户留存分析</h5>
                                        <div id="retentionChart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">用户分布</h5>
                                        <div id="userDistributionChart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 行为分析选项卡 -->
                    <div class="tab-pane fade" id="user-behavior" role="tabpanel">
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">用户行为热力图</h5>
                                        <div class="row">
                                            <div class="col-md-8">
                                                <div id="behaviorHeatmap" style="height: 400px;"></div>
                                            </div>
                                            <div class="col-md-4">
                                                <h6>行为统计</h6>
                                                <div class="behavior-stats">
                                                    <div class="stat-item">
                                                        <span class="stat-label">平均会话时长</span>
                                                        <span class="stat-value" id="avgSessionTime">0分钟</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-label">页面浏览量</span>
                                                        <span class="stat-value" id="pageViews">0</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-label">跳出率</span>
                                                        <span class="stat-value" id="bounceRate">0%</span>
                                                    </div>
                                                    <div class="stat-item">
                                                        <span class="stat-label">转化率</span>
                                                        <span class="stat-value" id="conversionRate">0%</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">功能使用排行</h5>
                                        <div id="featureUsageChart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">用户路径分析</h5>
                                        <div id="userPathChart" style="height: 300px;"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加用户模态框 -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">邮箱</label>
                            <input type="email" class="form-control" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">密码</label>
                            <input type="password" class="form-control" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">角色</label>
                            <select class="form-select" name="roleId" required>
                                <!-- 角色选项将通过JavaScript动态加载 -->
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="addUserSubmit">添加</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑用户模态框 -->
    <div class="modal fade" id="editUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">编辑用户</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editUserForm">
                        <input type="hidden" name="userId">
                        <div class="mb-3">
                            <label class="form-label">用户名</label>
                            <input type="text" class="form-control" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">邮箱</label>
                            <input type="email" class="form-control" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">角色</label>
                            <select class="form-select" name="roleId" required>
                                <!-- 角色选项将通过JavaScript动态加载 -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">状态</label>
                            <select class="form-select" name="status" required>
                                <option value="active">活跃</option>
                                <option value="inactive">未激活</option>
                                <option value="blocked">已封禁</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="editUserSubmit">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 重置密码模态框 -->
    <div class="modal fade" id="resetPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">重置密码</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="resetPasswordForm">
                        <input type="hidden" name="userId">
                        <div class="mb-3">
                            <label class="form-label">新密码</label>
                            <input type="password" class="form-control" name="newPassword" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">确认密码</label>
                            <input type="password" class="form-control" name="confirmPassword" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="resetPasswordSubmit">重置</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/moment/moment.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <script src="js/ui-enhancements.js"></script>
    <script src="js/components/batch-operations.js"></script>
    <script src="users.js"></script>
</body>
</html> 