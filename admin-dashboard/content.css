/* 内容管理页面样式 */

/* 选项卡样式 */
.tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 20px;
}

.tab {
    padding: 12px 20px;
    cursor: pointer;
    font-weight: 500;
    color: #666;
    position: relative;
    transition: all 0.3s ease;
}

.tab:hover {
    color: #1890ff;
}

.tab.active {
    color: #1890ff;
}

.tab.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #1890ff;
}

/* 选项卡内容 */
.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* 表格样式 */
.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background-color: #f9f9f9;
    padding: 12px 15px;
    text-align: left;
    font-weight: 500;
    color: #333;
    border-bottom: 1px solid #e0e0e0;
}

.data-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #e0e0e0;
}

.data-table tr:hover {
    background-color: #f5f5f5;
}

/* 状态标签 */
.status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status.active {
    background-color: rgba(82, 196, 26, 0.1);
    color: #52c41a;
}

.status.inactive {
    background-color: rgba(144, 147, 153, 0.1);
    color: #909399;
}

.status.draft {
    background-color: rgba(250, 173, 20, 0.1);
    color: #faad14;
}

/* 操作按钮 */
.actions {
    white-space: nowrap;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: none;
    background-color: transparent;
    color: #666;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-icon:hover {
    background-color: #f0f0f0;
    color: #1890ff;
}

/* 二维码管理 */
.qrcode-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.qrcode-item {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
}

.qrcode-item:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.qrcode-preview {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f9f9f9;
    border-bottom: 1px solid #e0e0e0;
}

.qrcode-preview img {
    max-width: 100%;
    max-height: 100%;
}

.qrcode-info {
    padding: 15px;
}

.qrcode-info h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
}

.qrcode-info p {
    margin: 0 0 12px 0;
    color: #666;
    font-size: 14px;
}

.qrcode-actions {
    display: flex;
    gap: 10px;
}

/* 添加二维码按钮 */
.add-qrcode {
    border: 2px dashed #d9d9d9;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
}

.add-qrcode:hover {
    border-color: #1890ff;
    color: #1890ff;
}

.add-qrcode i {
    font-size: 24px;
    margin-bottom: 8px;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
}

.modal.active {
    display: block;
}

.modal-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-dialog {
    position: relative;
    width: 90%;
    max-width: 800px;
    margin: 50px auto;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: modalFadeIn 0.3s;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    margin: 0;
    font-size: 18px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s;
}

.form-control:focus {
    border-color: #1890ff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 富文本编辑器自定义样式 */
.ql-toolbar.ql-snow {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
}

.ql-container.ql-snow {
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .qrcode-grid {
        grid-template-columns: 1fr;
    }
    
    .modal-dialog {
        width: 95%;
        margin: 20px auto;
    }
} 