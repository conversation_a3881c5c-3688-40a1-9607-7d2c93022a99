<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统日志 - WriterPro管理后台</title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/ui-enhancements.css">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- 引入Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .log-entry {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            border-left: 3px solid #dee2e6;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            background-color: #f8f9fa;
        }
        .log-entry.error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .log-entry.warning {
            border-left-color: #ffc107;
            background-color: #fff3cd;
        }
        .log-entry.info {
            border-left-color: #0dcaf0;
            background-color: #d1ecf1;
        }
        .log-entry.debug {
            border-left-color: #6c757d;
            background-color: #e2e3e5;
        }
        .log-timestamp {
            color: #6c757d;
            font-weight: bold;
        }
        .log-level {
            font-weight: bold;
            padding: 0.2rem 0.4rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
        }
        .log-level.ERROR {
            background-color: #dc3545;
            color: white;
        }
        .log-level.WARN {
            background-color: #ffc107;
            color: black;
        }
        .log-level.INFO {
            background-color: #0dcaf0;
            color: black;
        }
        .log-level.DEBUG {
            background-color: #6c757d;
            color: white;
        }
        .log-viewer {
            height: 500px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            background-color: #ffffff;
        }
        .log-stats {
            display: flex;
            gap: 1rem;
            margin-bottom: 1rem;
        }
        .log-stat {
            flex: 1;
            text-align: center;
            padding: 1rem;
            border-radius: 0.375rem;
            background-color: #f8f9fa;
        }
        .log-stat.error {
            background-color: #f8d7da;
        }
        .log-stat.warning {
            background-color: #fff3cd;
        }
        .log-stat.info {
            background-color: #d1ecf1;
        }
        .log-stat.debug {
            background-color: #e2e3e5;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="logo">
                <h1>WriterPro</h1>
                <span>管理后台</span>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li><a href="dashboard.html"><i class="bi bi-speedometer2"></i><span data-i18n="common.dashboard">仪表盘</span></a></li>
                    <li><a href="users.html"><i class="bi bi-people"></i><span data-i18n="common.users">用户管理</span></a></li>
                    <li><a href="agents.html"><i class="bi bi-person-badge"></i><span data-i18n="common.agents">代理商管理</span></a></li>
                    <li><a href="roles.html"><i class="bi bi-shield-check"></i><span data-i18n="common.roles">角色管理</span></a></li>
                    <li><a href="finance.html"><i class="bi bi-currency-dollar"></i><span data-i18n="common.finance">财务管理</span></a></li>
                    <li><a href="settings.html"><i class="bi bi-gear"></i><span data-i18n="common.settings">系统设置</span></a></li>
                    <li><a href="content.html"><i class="bi bi-file-text"></i><span data-i18n="common.content">内容管理</span></a></li>
                    <li><a href="api.html"><i class="bi bi-code-slash"></i><span data-i18n="common.api">API配置</span></a></li>
                    <li><a href="marketing.html"><i class="bi bi-megaphone"></i><span data-i18n="common.marketing">营销工具</span></a></li>
                    <li><a href="security.html"><i class="bi bi-shield-lock"></i><span data-i18n="common.security">安全中心</span></a></li>
                    <li><a href="monitor.html"><i class="bi bi-activity"></i><span data-i18n="common.monitor">系统监控</span></a></li>
                    <li><a href="analytics.html"><i class="bi bi-graph-up"></i><span>数据分析</span></a></li>
                    <li><a href="reports.html"><i class="bi bi-file-earmark-text"></i><span>报表中心</span></a></li>
                    <li class="active"><a href="logs.html"><i class="bi bi-journal-text"></i><span>系统日志</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 头部 -->
            <header class="main-header">
                <div class="header-left">
                    <button class="menu-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <div class="header-title">
                        <h2>系统日志</h2>
                        <p>实时日志监控和分析</p>
                    </div>
                </div>
                
                <div class="header-right">
                    <div class="header-actions">
                        <button class="action-btn theme-toggle" id="themeToggle" title="切换主题">
                            <i class="bi bi-moon"></i>
                        </button>
                        
                        <button class="action-btn">
                            <i class="bi bi-bell"></i>
                            <span class="badge">5</span>
                        </button>
                        
                        <div class="user-menu">
                            <button class="user-btn">
                                <div class="user-avatar">A</div>
                                <span>管理员</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <div class="user-dropdown">
                                <a href="#"><i class="bi bi-person"></i>个人资料</a>
                                <a href="#"><i class="bi bi-gear"></i>设置</a>
                                <a href="index.html"><i class="bi bi-box-arrow-right"></i>退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 日志统计 -->
                <div class="log-stats">
                    <div class="log-stat error">
                        <h3 id="errorCount">0</h3>
                        <p>错误</p>
                    </div>
                    <div class="log-stat warning">
                        <h3 id="warningCount">0</h3>
                        <p>警告</p>
                    </div>
                    <div class="log-stat info">
                        <h3 id="infoCount">0</h3>
                        <p>信息</p>
                    </div>
                    <div class="log-stat debug">
                        <h3 id="debugCount">0</h3>
                        <p>调试</p>
                    </div>
                </div>

                <!-- 日志控制面板 -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">日志控制面板</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <label class="form-label">日志级别</label>
                                <select class="form-select" id="logLevelFilter">
                                    <option value="">全部</option>
                                    <option value="ERROR">错误</option>
                                    <option value="WARN">警告</option>
                                    <option value="INFO">信息</option>
                                    <option value="DEBUG">调试</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">日志来源</label>
                                <select class="form-select" id="logSourceFilter">
                                    <option value="">全部</option>
                                    <option value="API">API</option>
                                    <option value="AUTH">认证</option>
                                    <option value="DB">数据库</option>
                                    <option value="SYSTEM">系统</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">时间范围</label>
                                <select class="form-select" id="timeRangeFilter">
                                    <option value="1h">最近1小时</option>
                                    <option value="6h">最近6小时</option>
                                    <option value="24h" selected>最近24小时</option>
                                    <option value="7d">最近7天</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">搜索关键词</label>
                                <input type="text" class="form-control" id="logSearchInput" placeholder="搜索日志内容...">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary" id="searchLogsBtn">
                                        <i class="bi bi-search"></i> 搜索
                                    </button>
                                    <button class="btn btn-outline-secondary" id="clearFiltersBtn">
                                        <i class="bi bi-x-circle"></i> 清除
                                    </button>
                                    <button class="btn btn-outline-success" id="refreshLogsBtn">
                                        <i class="bi bi-arrow-clockwise"></i> 刷新
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <div class="form-check form-switch d-inline-block me-3">
                                    <input class="form-check-input" type="checkbox" id="autoRefreshSwitch" checked>
                                    <label class="form-check-label" for="autoRefreshSwitch">自动刷新</label>
                                </div>
                                <div class="form-check form-switch d-inline-block me-3">
                                    <input class="form-check-input" type="checkbox" id="scrollToBottomSwitch" checked>
                                    <label class="form-check-label" for="scrollToBottomSwitch">自动滚动到底部</label>
                                </div>
                                <button class="btn btn-outline-primary btn-sm" id="exportLogsBtn">
                                    <i class="bi bi-download"></i> 导出日志
                                </button>
                                <button class="btn btn-outline-danger btn-sm" id="clearLogsBtn">
                                    <i class="bi bi-trash"></i> 清空日志
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 日志查看器 -->
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">实时日志</h5>
                        <div class="d-flex align-items-center gap-2">
                            <span class="badge bg-success" id="connectionStatus">已连接</span>
                            <span class="text-muted">总计: <span id="totalLogCount">0</span> 条</span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="log-viewer" id="logViewer">
                            <!-- 日志条目将在这里动态生成 -->
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 日志详情模态框 -->
    <div class="modal fade" id="logDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">日志详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>时间:</strong>
                            <p id="logDetailTime"></p>
                        </div>
                        <div class="col-md-6">
                            <strong>级别:</strong>
                            <p id="logDetailLevel"></p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <strong>来源:</strong>
                            <p id="logDetailSource"></p>
                        </div>
                        <div class="col-md-6">
                            <strong>用户:</strong>
                            <p id="logDetailUser"></p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <strong>消息:</strong>
                            <pre id="logDetailMessage" class="bg-light p-3 rounded"></pre>
                        </div>
                    </div>
                    <div class="row" id="logDetailStackTrace" style="display: none;">
                        <div class="col-md-12">
                            <strong>堆栈跟踪:</strong>
                            <pre id="logDetailStack" class="bg-light p-3 rounded"></pre>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-primary" id="copyLogDetailBtn">复制详情</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/bilingual-init.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/language-switcher.js"></script>
    <script src="js/mobile-nav.js"></script>
    <script src="js/ui-enhancements.js"></script>
    <script src="js/logs.js"></script>
</body>
</html>
