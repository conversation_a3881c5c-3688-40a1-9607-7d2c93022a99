#!/bin/bash

echo "========================================"
echo "    启动管理后台到端口3000/ad路径"
echo "========================================"
echo

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "[错误] Node.js未安装"
    echo "请先安装Node.js: https://nodejs.org"
    exit 1
fi

# 显示Node.js版本
echo "[信息] Node.js版本: $(node --version)"

# 检查当前目录
echo "[信息] 当前目录: $(pwd)"

# 检查关键文件
if [ ! -f "proxy-config.js" ]; then
    echo "[错误] 未找到proxy-config.js文件"
    echo "请确保在admin-dashboard目录中运行此脚本"
    exit 1
fi

echo "[信息] 文件检查通过"
echo

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "[信息] 安装依赖包..."
    npm install express
fi

echo "[信息] 启动服务器..."
echo "[信息] 主网站: http://localhost:3000"
echo "[信息] 管理后台: http://localhost:3000/ad"
echo "[信息] 登录信息: admin / admin123 / 101010"
echo
echo "========================================"
echo "    服务器启动中..."
echo "    按 Ctrl+C 停止服务器"
echo "========================================"
echo

# 启动服务器
node simple-port-3000.js
