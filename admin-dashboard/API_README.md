# WriterPro管理后台 API文档

## 概述

WriterPro管理后台提供完整的RESTful API，支持所有管理功能的程序化访问。本文档详细介绍了API的使用方法、认证机制、端点说明和示例代码。

## 基础信息

- **Base URL**: `https://api.writerpro.com/v1`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8
- **API版本**: v1.0.0

## 认证

### JWT令牌认证

所有API请求都需要在请求头中包含有效的JWT令牌：

```http
Authorization: Bearer <your_access_token>
```

### 获取访问令牌

```bash
curl -X POST https://api.writerpro.com/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "your_password"
  }'
```

响应示例：
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "role": "admin"
    },
    "expiresIn": 3600
  }
}
```

## API限制

- **请求频率**: 1000次/小时
- **并发连接**: 10个
- **请求大小**: 最大10MB
- **超时时间**: 30秒

## 响应格式

### 成功响应

```json
{
  "success": true,
  "data": {
    // 响应数据
  },
  "message": "操作成功"
}
```

### 错误响应

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述"
  }
}
```

## 主要API端点

### 1. 用户管理

#### 获取用户列表
```http
GET /users?page=1&limit=20&search=keyword&status=active
```

#### 创建用户
```http
POST /users
Content-Type: application/json

{
  "username": "new_user",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "user"
}
```

#### 更新用户
```http
PUT /users/{userId}
Content-Type: application/json

{
  "username": "updated_user",
  "status": "active"
}
```

#### 删除用户
```http
DELETE /users/{userId}
```

### 2. 代理商管理

#### 获取代理商列表
```http
GET /agents?page=1&limit=20&level=1&status=active
```

#### 创建代理商
```http
POST /agents
Content-Type: application/json

{
  "name": "代理商名称",
  "email": "<EMAIL>",
  "phone": "13800138000",
  "level": 1,
  "region": "华东地区"
}
```

### 3. 佣金管理

#### 获取佣金记录
```http
GET /commission/records?agentId=agent_123&status=pending
```

#### 计算订单佣金
```http
POST /commission/calculate
Content-Type: application/json

{
  "orderId": "order_123",
  "agentId": "agent_123",
  "orderAmount": 1000.00
}
```

#### 批量结算佣金
```http
POST /commission/batch-settle
Content-Type: application/json

{
  "commissionIds": ["comm_1", "comm_2", "comm_3"],
  "settlementDate": "2024-01-15"
}
```

### 4. 价格管理

#### 获取产品价格
```http
GET /pricing/products?category=basic&status=active
```

#### 计算产品价格
```http
POST /pricing/calculate
Content-Type: application/json

{
  "productId": "product_123",
  "quantity": 2,
  "memberLevel": 2
}
```

#### 批量更新价格
```http
POST /pricing/batch-update
Content-Type: application/json

{
  "updateType": "percentage",
  "adjustmentValue": 10,
  "adjustmentType": "increase",
  "updates": [
    {
      "productId": "product_123",
      "reason": "市场调整"
    }
  ]
}
```

### 5. 财务管理

#### 获取财务统计
```http
GET /finance/stats?period=30d&type=revenue
```

#### 获取交易记录
```http
GET /finance/transactions?page=1&limit=20&type=payment
```

### 6. 系统监控

#### 获取系统状态
```http
GET /monitor/system-status
```

#### 获取告警历史
```http
GET /monitor/alert-history?level=high&status=active
```

## 错误代码

| 错误代码 | HTTP状态码 | 说明 | 解决方案 |
|---------|-----------|------|----------|
| `INVALID_TOKEN` | 401 | 无效的访问令牌 | 重新登录获取新令牌 |
| `INSUFFICIENT_PERMISSIONS` | 403 | 权限不足 | 联系管理员分配权限 |
| `RESOURCE_NOT_FOUND` | 404 | 资源不存在 | 检查资源ID是否正确 |
| `VALIDATION_ERROR` | 400 | 参数验证失败 | 检查请求参数格式 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 | 降低请求频率 |
| `INTERNAL_SERVER_ERROR` | 500 | 服务器内部错误 | 联系技术支持 |

## SDK和工具

### JavaScript SDK

```bash
npm install writerpro-admin-sdk
```

```javascript
const WriterProAdmin = require('writerpro-admin-sdk');

const client = new WriterProAdmin({
  baseUrl: 'https://api.writerpro.com/v1',
  token: 'your_access_token'
});

// 获取用户列表
const users = await client.users.list({ page: 1, limit: 20 });
```

### Python SDK

```bash
pip install writerpro-admin-sdk
```

```python
from writerpro_admin import WriterProAdmin

client = WriterProAdmin(
    base_url='https://api.writerpro.com/v1',
    token='your_access_token'
)

# 获取用户列表
users = client.users.list(page=1, limit=20)
```

### Postman集合

下载预配置的Postman集合：
```http
GET /api/docs/postman
```

### OpenAPI规范

获取OpenAPI 3.0规范文档：
```http
GET /api/docs/openapi
```

## 最佳实践

### 1. 错误处理

```javascript
try {
  const response = await fetch('/api/users', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.error.message);
  }
  
  return data.data;
} catch (error) {
  console.error('API请求失败:', error.message);
  throw error;
}
```

### 2. 分页处理

```javascript
async function getAllUsers() {
  const allUsers = [];
  let page = 1;
  let hasMore = true;
  
  while (hasMore) {
    const response = await client.users.list({ page, limit: 100 });
    allUsers.push(...response.users);
    
    hasMore = page < response.pagination.pages;
    page++;
  }
  
  return allUsers;
}
```

### 3. 批量操作

```javascript
// 批量创建用户
async function batchCreateUsers(users) {
  const results = [];
  
  for (const user of users) {
    try {
      const result = await client.users.create(user);
      results.push({ success: true, data: result });
    } catch (error) {
      results.push({ success: false, error: error.message });
    }
  }
  
  return results;
}
```

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本发布
- 支持用户管理、代理商管理、佣金管理等核心功能
- 提供完整的认证和权限控制

## 支持

- **技术支持**: <EMAIL>
- **文档更新**: 每周五更新
- **状态页面**: https://status.writerpro.com
- **社区论坛**: https://community.writerpro.com

## 许可证

本API文档遵循 MIT 许可证。详情请参阅 LICENSE 文件。
