<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表盘 - WriterPro 管理后台</title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/ui-enhancements.css">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- 引入 ECharts -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <span class="logo-icon"><i class="bi bi-pen"></i></span>
                    <h1>WriterPro</h1>
                </div>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li class="active">
                        <a href="dashboard.html">
                            <i class="bi bi-grid"></i>
                            <span>仪表盘</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="bi bi-people"></i>
                            <span>用户管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="workorders.html">
                            <i class="bi bi-clipboard-check"></i>
                            <span>工单管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="experts.html">
                            <i class="bi bi-person-badge"></i>
                            <span>专家管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="agents.html">
                            <i class="bi bi-briefcase"></i>
                            <span>代理管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="finance.html">
                            <i class="bi bi-cash-stack"></i>
                            <span>财务统计</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="bi bi-gear"></i>
                            <span>系统设置</span>
                        </a>
                    </li>
                    <li>
                        <a href="content.html">
                            <i class="bi bi-file-text"></i>
                            <span>内容管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="api.html">
                            <i class="bi bi-code-square"></i>
                            <span>API配置</span>
                        </a>
                    </li>
                    <li>
                        <a href="marketing.html">
                            <i class="bi bi-megaphone"></i>
                            <span>营销工具</span>
                        </a>
                    </li>
                    <li>
                        <a href="security.html">
                            <i class="bi bi-shield-check"></i>
                            <span>安全中心</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="header">
                <div class="header-left">
                    <button class="menu-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <h2 data-i18n="nav.dashboard">仪表盘</h2>
                </div>
                
                <div class="header-right">
                    <div class="search-bar">
                        <i class="bi bi-search"></i>
                        <input type="text" placeholder="搜索...">
                    </div>
                    
                    <div class="header-actions">
                        <button class="action-btn theme-toggle" id="themeToggle" title="切换主题">
                            <i class="bi bi-moon"></i>
                        </button>

                        <button class="action-btn">
                            <i class="bi bi-bell"></i>
                            <span class="badge">5</span>
                        </button>
                        
                        <div class="user-dropdown">
                            <button class="user-btn">
                                <div class="avatar">A</div>
                                <span>管理员</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            
                            <div class="dropdown-menu">
                                <a href="profile.html" class="dropdown-item">
                                    <i class="bi bi-person"></i>
                                    <span>个人资料</span>
                                </a>
                                <a href="#" class="dropdown-item">
                                    <i class="bi bi-gear"></i>
                                    <span>设置</span>
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="login.html" class="dropdown-item">
                                    <i class="bi bi-box-arrow-right"></i>
                                    <span>退出登录</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 页面内容 -->
            <div class="content">
                <!-- 欢迎卡片 -->
                <div class="welcome-card">
                    <div class="welcome-info">
                        <h3>欢迎回来，管理员</h3>
                        <p>这里是今日数据概览</p>
                    </div>
                    <div class="welcome-actions">
                        <button class="btn btn-primary">
                            <i class="bi bi-download"></i>
                            <span>导出报告</span>
                        </button>
                    </div>
                </div>
                
                <!-- 数据卡片 -->
                <div class="stats-cards">
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(24, 144, 255, 0.1); color: #1890ff;">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="stat-info">
                            <h4>总用户数</h4>
                            <div class="stat-value">12,345</div>
                            <div class="stat-change increase">
                                <i class="bi bi-arrow-up"></i>
                                <span>8.5%</span>
                                <span class="period">较上月</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(82, 196, 26, 0.1); color: #52c41a;">
                            <i class="bi bi-currency-yen"></i>
                        </div>
                        <div class="stat-info">
                            <h4>本月收入</h4>
                            <div class="stat-value">¥45,678</div>
                            <div class="stat-change increase">
                                <i class="bi bi-arrow-up"></i>
                                <span>12.3%</span>
                                <span class="period">较上月</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(250, 173, 20, 0.1); color: #faad14;">
                            <i class="bi bi-file-text"></i>
                        </div>
                        <div class="stat-info">
                            <h4>处理文档</h4>
                            <div class="stat-value">2,567</div>
                            <div class="stat-change increase">
                                <i class="bi bi-arrow-up"></i>
                                <span>5.2%</span>
                                <span class="period">较上周</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(255, 77, 79, 0.1); color: #ff4d4f;">
                            <i class="bi bi-briefcase"></i>
                        </div>
                        <div class="stat-info">
                            <h4>代理数量</h4>
                            <div class="stat-value">128</div>
                            <div class="stat-change decrease">
                                <i class="bi bi-arrow-down"></i>
                                <span>2.1%</span>
                                <span class="period">较上月</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 系统监控区域 -->
                <div class="monitor-section">
                    <div class="monitor-card">
                        <div class="monitor-header">
                            <h3>
                                <i class="bi bi-activity"></i>
                                系统状态
                            </h3>
                            <div class="status-indicator">
                                <span class="status-dot status-online"></span>
                                <span>正常运行</span>
                            </div>
                        </div>
                        <div class="monitor-grid">
                            <div class="monitor-item">
                                <div class="monitor-label">CPU使用率</div>
                                <div class="monitor-value" id="cpuUsage">45%</div>
                                <div class="monitor-progress">
                                    <div class="progress-bar" style="width: 45%; background-color: #52c41a;"></div>
                                </div>
                            </div>
                            <div class="monitor-item">
                                <div class="monitor-label">内存使用率</div>
                                <div class="monitor-value" id="memoryUsage">62%</div>
                                <div class="monitor-progress">
                                    <div class="progress-bar" style="width: 62%; background-color: #faad14;"></div>
                                </div>
                            </div>
                            <div class="monitor-item">
                                <div class="monitor-label">磁盘使用率</div>
                                <div class="monitor-value" id="diskUsage">38%</div>
                                <div class="monitor-progress">
                                    <div class="progress-bar" style="width: 38%; background-color: #52c41a;"></div>
                                </div>
                            </div>
                            <div class="monitor-item">
                                <div class="monitor-label">网络流量</div>
                                <div class="monitor-value" id="networkTraffic">1.2MB/s</div>
                                <div class="monitor-progress">
                                    <div class="progress-bar" style="width: 30%; background-color: #1677ff;"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="monitor-card">
                        <div class="monitor-header">
                            <h3>
                                <i class="bi bi-graph-up"></i>
                                API监控
                            </h3>
                            <div class="refresh-btn" id="refreshApiStats">
                                <i class="bi bi-arrow-clockwise"></i>
                            </div>
                        </div>
                        <div class="api-stats">
                            <div class="api-stat-item">
                                <div class="api-stat-label">今日调用</div>
                                <div class="api-stat-value" id="todayApiCalls">12,456</div>
                                <div class="api-stat-change increase">
                                    <i class="bi bi-arrow-up"></i>
                                    <span>+8.5%</span>
                                </div>
                            </div>
                            <div class="api-stat-item">
                                <div class="api-stat-label">成功率</div>
                                <div class="api-stat-value" id="apiSuccessRate">99.2%</div>
                                <div class="api-stat-change increase">
                                    <i class="bi bi-arrow-up"></i>
                                    <span>+0.3%</span>
                                </div>
                            </div>
                            <div class="api-stat-item">
                                <div class="api-stat-label">平均响应</div>
                                <div class="api-stat-value" id="avgResponseTime">245ms</div>
                                <div class="api-stat-change decrease">
                                    <i class="bi bi-arrow-down"></i>
                                    <span>-12ms</span>
                                </div>
                            </div>
                            <div class="api-stat-item">
                                <div class="api-stat-label">错误数</div>
                                <div class="api-stat-value" id="apiErrors">23</div>
                                <div class="api-stat-change increase">
                                    <i class="bi bi-arrow-up"></i>
                                    <span>+5</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="chart-section">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>收入趋势</h3>
                            <div class="chart-actions">
                                <select id="revenue-period">
                                    <option value="week">本周</option>
                                    <option value="month" selected>本月</option>
                                    <option value="quarter">本季度</option>
                                    <option value="year">本年</option>
                                </select>
                            </div>
                        </div>
                        <div id="revenue-chart" class="chart-container"></div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>用户增长</h3>
                            <div class="chart-actions">
                                <select id="user-period">
                                    <option value="week">本周</option>
                                    <option value="month" selected>本月</option>
                                    <option value="quarter">本季度</option>
                                    <option value="year">本年</option>
                                </select>
                            </div>
                        </div>
                        <div id="user-chart" class="chart-container"></div>
                    </div>
                </div>

                <!-- API调用趋势图 -->
                <div class="chart-section">
                    <div class="chart-card full-width">
                        <div class="chart-header">
                            <h3>API调用趋势</h3>
                            <div class="chart-actions">
                                <button class="btn btn-sm" id="realTimeToggle">
                                    <i class="bi bi-play-circle"></i>
                                    <span>实时监控</span>
                                </button>
                                <select id="api-period">
                                    <option value="hour">最近24小时</option>
                                    <option value="day" selected>最近7天</option>
                                    <option value="week">最近4周</option>
                                </select>
                            </div>
                        </div>
                        <div id="api-chart" class="chart-container"></div>
                    </div>
                </div>
                
                <!-- 最近活动和待办事项 -->
                <div class="activity-section">
                    <div class="activity-card">
                        <div class="activity-header">
                            <h3>最近活动</h3>
                            <a href="#" class="view-all">查看全部</a>
                        </div>
                        <div class="activity-list">
                            <div class="activity-item">
                                <div class="activity-icon" style="background-color: rgba(24, 144, 255, 0.1); color: #1890ff;">
                                    <i class="bi bi-person-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">新用户注册</div>
                                    <div class="activity-desc">用户 张三 完成了注册</div>
                                    <div class="activity-time">10分钟前</div>
                                </div>
                            </div>
                            
                            <div class="activity-item">
                                <div class="activity-icon" style="background-color: rgba(82, 196, 26, 0.1); color: #52c41a;">
                                    <i class="bi bi-cash"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">新订单支付</div>
                                    <div class="activity-desc">用户 李四 支付了 ¥199 的订单</div>
                                    <div class="activity-time">30分钟前</div>
                                </div>
                            </div>
                            
                            <div class="activity-item">
                                <div class="activity-icon" style="background-color: rgba(250, 173, 20, 0.1); color: #faad14;">
                                    <i class="bi bi-file-earmark-text"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">文档处理完成</div>
                                    <div class="activity-desc">用户 王五 的文档优化已完成</div>
                                    <div class="activity-time">1小时前</div>
                                </div>
                            </div>
                            
                            <div class="activity-item">
                                <div class="activity-icon" style="background-color: rgba(255, 77, 79, 0.1); color: #ff4d4f;">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">系统警告</div>
                                    <div class="activity-desc">API调用次数接近限制</div>
                                    <div class="activity-time">2小时前</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="todo-card">
                        <div class="todo-header">
                            <h3>待办事项</h3>
                            <button class="add-todo-btn">
                                <i class="bi bi-plus"></i>
                                <span>添加</span>
                            </button>
                        </div>
                        <div class="todo-list">
                            <div class="todo-item">
                                <input type="checkbox" id="todo1">
                                <label for="todo1">审核新代理申请 (3)</label>
                                <span class="todo-tag urgent">紧急</span>
                            </div>
                            
                            <div class="todo-item">
                                <input type="checkbox" id="todo2">
                                <label for="todo2">更新系统价格配置</label>
                                <span class="todo-tag important">重要</span>
                            </div>
                            
                            <div class="todo-item">
                                <input type="checkbox" id="todo3">
                                <label for="todo3">回复用户反馈邮件</label>
                            </div>
                            
                            <div class="todo-item">
                                <input type="checkbox" id="todo4">
                                <label for="todo4">准备月度财务报表</label>
                                <span class="todo-tag normal">普通</span>
                            </div>
                            
                            <div class="todo-item">
                                <input type="checkbox" id="todo5" checked>
                                <label for="todo5" class="completed">更新客服二维码</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="js/bilingual-init.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/language-switcher.js"></script>
    <script src="js/mobile-nav.js"></script>
    <script src="js/ui-enhancements.js"></script>
    <script src="dashboard.js"></script>
</body>
</html>