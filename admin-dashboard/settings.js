// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 加载所有设置
    loadAllSettings();

    // 加载价格设置
    loadPricingSettings();

    // 加载系统配置
    loadSystemConfig();

    // 加载操作日志
    loadOperationLogs();

    // 绑定表单提交事件
    bindFormSubmitEvents();

    // 绑定其他事件
    bindOtherEvents();
});

// 加载所有设置
async function loadAllSettings() {
    try {
        const response = await fetch('/api/settings');
        const settings = await response.json();
        
        if (!response.ok) {
            throw new Error(settings.message || '加载设置失败');
        }
        
        // 填充表单数据
        fillFormData(settings);
    } catch (error) {
        console.error('加载设置失败:', error);
        showToast('错误', error.message);
    }
}

// 填充表单数据
function fillFormData(settings) {
    // 基本设置
    document.querySelector('[name="systemName"]').value = settings.systemName || '';
    document.querySelector('[name="systemDescription"]').value = settings.systemDescription || '';
    document.querySelector('[name="contactEmail"]').value = settings.contactEmail || '';
    document.querySelector('[name="supportPhone"]').value = settings.supportPhone || '';
    
    // 支付设置
    document.querySelector('[name="alipayAppId"]').value = settings.alipayAppId || '';
    document.querySelector('[name="alipayPrivateKey"]').value = settings.alipayPrivateKey || '';
    document.querySelector('[name="alipayPublicKey"]').value = settings.alipayPublicKey || '';
    document.querySelector('[name="wechatMchId"]').value = settings.wechatMchId || '';
    document.querySelector('[name="wechatMchKey"]').value = settings.wechatMchKey || '';
    document.querySelector('[name="wechatApiKey"]').value = settings.wechatApiKey || '';
    
    // 通知设置
    document.querySelector('[name="smtpHost"]').value = settings.smtpHost || '';
    document.querySelector('[name="smtpPort"]').value = settings.smtpPort || '';
    document.querySelector('[name="smtpUsername"]').value = settings.smtpUsername || '';
    document.querySelector('[name="smtpPassword"]').value = settings.smtpPassword || '';
    document.querySelector('[name="notifyOnRegistration"]').checked = settings.notifyOnRegistration || false;
    document.querySelector('[name="notifyOnPayment"]').checked = settings.notifyOnPayment || false;
    document.querySelector('[name="notifyOnRefund"]').checked = settings.notifyOnRefund || false;
    
    // 安全设置
    document.querySelector('[name="requireUppercase"]').checked = settings.requireUppercase || false;
    document.querySelector('[name="requireNumber"]').checked = settings.requireNumber || false;
    document.querySelector('[name="requireSpecialChar"]').checked = settings.requireSpecialChar || false;
    document.querySelector('[name="minLength"]').value = settings.minLength || 8;
    document.querySelector('[name="enableTwoFactor"]').checked = settings.enableTwoFactor || false;
    document.querySelector('[name="maxLoginAttempts"]').value = settings.maxLoginAttempts || 5;
    document.querySelector('[name="lockoutDuration"]').value = settings.lockoutDuration || 30;
    
    // API设置
    document.querySelector('[name="enableRateLimit"]').checked = settings.enableRateLimit || false;
    document.querySelector('[name="requestsPerMinute"]').value = settings.requestsPerMinute || 60;
    document.querySelector('[name="apiKeyExpiration"]').value = settings.apiKeyExpiration || 30;
    document.querySelector('[name="allowedOrigins"]').value = settings.allowedOrigins || '';
}

// 绑定表单提交事件
function bindFormSubmitEvents() {
    // 基本设置表单
    document.getElementById('basicSettingsForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        await saveSettings('basic', new FormData(e.target));
    });
    
    // 支付设置表单
    document.getElementById('paymentSettingsForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        await saveSettings('payment', new FormData(e.target));
    });
    
    // 通知设置表单
    document.getElementById('notificationSettingsForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        await saveSettings('notification', new FormData(e.target));
    });
    
    // 安全设置表单
    document.getElementById('securitySettingsForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        await saveSettings('security', new FormData(e.target));
    });
    
    // API设置表单
    document.getElementById('apiSettingsForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        await saveSettings('api', new FormData(e.target));
    });
}

// 保存设置
async function saveSettings(type, formData) {
    try {
        const data = {};
        formData.forEach((value, key) => {
            // 处理复选框
            if (formData.getAll(key).length > 1) {
                data[key] = formData.getAll(key);
            } else {
                data[key] = value;
            }
        });
        
        const response = await fetch(`/api/settings/${type}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (!response.ok) {
            throw new Error(result.message || '保存设置失败');
        }
        
        showToast('成功', '设置已保存');
    } catch (error) {
        console.error('保存设置失败:', error);
        showToast('错误', error.message);
    }
}

// 绑定文件上传事件
function bindFileUploadEvent() {
    const logoInput = document.querySelector('[name="systemLogo"]');
    logoInput.addEventListener('change', async (e) => {
        const file = e.target.files[0];
        if (!file) return;
        
        try {
            const formData = new FormData();
            formData.append('logo', file);
            
            const response = await fetch('/api/settings/logo', {
                method: 'POST',
                body: formData
            });
            
            const result = await response.json();
            
            if (!response.ok) {
                throw new Error(result.message || '上传Logo失败');
            }
            
            showToast('成功', 'Logo已更新');
        } catch (error) {
            console.error('上传Logo失败:', error);
            showToast('错误', error.message);
        }
    });
}

// 显示提示消息
function showToast(title, message) {
    // 创建提示框
    const toast = document.createElement('div');
    toast.className = `alert alert-${title === '成功' ? 'success' : 'danger'} position-fixed top-0 end-0 m-3`;
    toast.style.zIndex = '1050';
    toast.innerHTML = `
        <strong>${title}：</strong> ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    // 添加到页面
    document.body.appendChild(toast);
    
    // 3秒后自动关闭
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 验证表单数据
function validateFormData(type, data) {
    switch (type) {
        case 'basic':
            if (!data.systemName) return '系统名称不能为空';
            if (!data.contactEmail) return '联系邮箱不能为空';
            if (data.contactEmail && !isValidEmail(data.contactEmail)) return '联系邮箱格式无效';
            break;
            
        case 'payment':
            if (data.alipayAppId && !data.alipayPrivateKey) return '请填写支付宝商户私钥';
            if (data.wechatMchId && !data.wechatMchKey) return '请填写微信支付商户密钥';
            break;
            
        case 'notification':
            if (data.smtpHost && !data.smtpPort) return '请填写SMTP端口';
            if (data.smtpHost && !data.smtpUsername) return '请填写SMTP用户名';
            break;
            
        case 'security':
            if (data.minLength && data.minLength < 6) return '密码最小长度不能小于6位';
            if (data.maxLoginAttempts && data.maxLoginAttempts < 1) return '登录失败次数限制必须大于0';
            break;
            
        case 'api':
            if (data.requestsPerMinute && data.requestsPerMinute < 1) return '每分钟请求限制必须大于0';
            if (data.apiKeyExpiration && data.apiKeyExpiration < 1) return 'API密钥有效期必须大于0';
            break;
    }
    
    return null;
}

// 验证邮箱格式
function isValidEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

// 格式化表单数据
function formatFormData(type, data) {
    switch (type) {
        case 'basic':
            return {
                ...data,
                systemName: data.systemName.trim(),
                systemDescription: data.systemDescription.trim(),
                contactEmail: data.contactEmail.trim(),
                supportPhone: data.supportPhone.trim()
            };
            
        case 'payment':
            return {
                ...data,
                alipayAppId: data.alipayAppId.trim(),
                wechatMchId: data.wechatMchId.trim()
            };
            
        case 'notification':
            return {
                ...data,
                smtpHost: data.smtpHost.trim(),
                smtpUsername: data.smtpUsername.trim()
            };
            
        case 'api':
            return {
                ...data,
                allowedOrigins: data.allowedOrigins.split(',').map(origin => origin.trim()).filter(Boolean)
            };
            
        default:
            return data;
    }
}

// 加载价格设置
async function loadPricingSettings() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/settings/pricing', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                renderPricingTable(data.data);
            }
        }
    } catch (error) {
        console.error('加载价格设置失败:', error);
        // 使用模拟数据
        renderPricingTable([
            {
                _id: 'plan-basic',
                productName: '基础版月度订阅',
                productType: 'monthly',
                originalPrice: 99,
                discountPrice: 79,
                status: 'active'
            },
            {
                _id: 'plan-pro',
                productName: '专业版月度订阅',
                productType: 'monthly',
                originalPrice: 299,
                discountPrice: 249,
                status: 'active'
            }
        ]);
    }
}

// 渲染价格表格
function renderPricingTable(plans) {
    const tbody = document.getElementById('pricingTableBody');
    if (!tbody) return;

    tbody.innerHTML = '';

    plans.forEach(plan => {
        const statusBadge = plan.status === 'active'
            ? '<span class="badge bg-success">启用</span>'
            : '<span class="badge bg-secondary">禁用</span>';

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${plan.productName}</td>
            <td>${getProductTypeText(plan.productType)}</td>
            <td>¥${plan.originalPrice}</td>
            <td>¥${plan.discountPrice || '-'}</td>
            <td>${statusBadge}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="editPricingPlan('${plan._id}')">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deletePricingPlan('${plan._id}')">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 获取产品类型文本
function getProductTypeText(type) {
    const typeMap = {
        'monthly': '月度订阅',
        'yearly': '年度订阅',
        'one-time': '一次性购买'
    };
    return typeMap[type] || type;
}

// 加载系统配置
async function loadSystemConfig() {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/settings/system-config', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                fillSystemConfigForm(data.data);
            }
        }
    } catch (error) {
        console.error('加载系统配置失败:', error);
        // 使用默认值
        fillSystemConfigForm({
            systemName: 'WriterPro',
            systemVersion: '1.0.0',
            systemDescription: '智能写作助手平台',
            contactEmail: '<EMAIL>',
            contactPhone: '************',
            apiRateLimit: 100,
            registerRateLimit: 10,
            loginRateLimit: 5,
            cacheExpiry: 30,
            enableRedis: true,
            enableMemoryCache: true
        });
    }
}

// 填充系统配置表单
function fillSystemConfigForm(config) {
    const elements = {
        'systemName': config.systemName,
        'systemVersion': config.systemVersion,
        'systemDescription': config.systemDescription,
        'contactEmail': config.contactEmail,
        'contactPhone': config.contactPhone,
        'apiRateLimit': config.apiRateLimit,
        'registerRateLimit': config.registerRateLimit,
        'loginRateLimit': config.loginRateLimit,
        'cacheExpiry': config.cacheExpiry,
        'enableRedis': config.enableRedis,
        'enableMemoryCache': config.enableMemoryCache
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            if (element.type === 'checkbox') {
                element.checked = value;
            } else {
                element.value = value;
            }
        }
    });
}

// 加载操作日志
async function loadOperationLogs(page = 1) {
    try {
        const token = localStorage.getItem('token');
        const response = await fetch(`/api/settings/operation-logs?page=${page}&limit=20`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                renderLogsTable(data.data);
                updateLogsPagination(data.pagination);
                updateLogsStats(data.stats);
            }
        }
    } catch (error) {
        console.error('加载操作日志失败:', error);
        // 使用模拟数据
        renderLogsTable([
            {
                _id: 'log-001',
                timestamp: new Date(),
                username: 'admin',
                module: 'system',
                operation: 'login',
                level: 'info',
                ip: '127.0.0.1',
                details: '管理员登录系统'
            }
        ]);
        updateLogsStats({
            totalLogs: 156,
            todayLogs: 23,
            errorLogs: 5,
            warningLogs: 12
        });
    }
}