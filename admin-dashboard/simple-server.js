const express = require('express');
const path = require('path');

// 创建Express应用
const app = express();
const PORT = process.env.PORT || 8080;

// 提供静态文件
app.use(express.static(__dirname));

// 页面路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

app.get('/dashboard', (req, res) => {
    res.sendFile(path.join(__dirname, 'dashboard.html'));
});

// 模拟API路由
app.post('/api/auth/login', express.json(), (req, res) => {
    const { username, password } = req.body;
    
    // 简单的验证
    if (username === 'admin' && password === 'admin123') {
        // 返回需要双因素认证
        res.json({
            success: true,
            requiresTwoFactor: true,
            message: '请输入双因素认证码'
        });
    } else {
        res.status(401).json({
            success: false,
            message: '用户名或密码错误'
        });
    }
});

app.post('/api/auth/verify-2fa', express.json(), (req, res) => {
    const { code } = req.body;
    
    // 简单的验证
    if (code === '101010') {
        res.json({
            success: true,
            user: {
                id: '1',
                username: 'admin',
                name: '系统管理员',
                email: '<EMAIL>',
                role: 'admin'
            }
        });
    } else {
        res.status(401).json({
            success: false,
            message: '验证码错误'
        });
    }
});

app.post('/api/auth/resend-2fa', (req, res) => {
    res.json({
        success: true,
        message: '验证码已重新发送'
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`服务器运行在端口 ${PORT}`);
    console.log(`访问 http://localhost:${PORT} 查看管理后台`);
}); 