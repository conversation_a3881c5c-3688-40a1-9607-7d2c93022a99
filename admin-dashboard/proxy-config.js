/**
 * 代理配置文件
 * 将管理后台集成到主网站的 /ad 路径下
 */

const express = require('express');
const path = require('path');

// 创建Express应用
const app = express();
const PORT = 3000;

// 静态文件中间件 - 为管理后台提供静态资源
app.use('/ad', express.static(__dirname));

// 管理后台页面路由
app.get('/ad', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

app.get('/ad/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

app.get('/ad/dashboard', (req, res) => {
    res.sendFile(path.join(__dirname, 'dashboard.html'));
});

app.get('/ad/users', (req, res) => {
    res.sendFile(path.join(__dirname, 'users.html'));
});

app.get('/ad/agents', (req, res) => {
    res.sendFile(path.join(__dirname, 'agents.html'));
});

app.get('/ad/commission', (req, res) => {
    res.sendFile(path.join(__dirname, 'commission.html'));
});

app.get('/ad/pricing', (req, res) => {
    res.sendFile(path.join(__dirname, 'pricing.html'));
});

app.get('/ad/roles', (req, res) => {
    res.sendFile(path.join(__dirname, 'roles.html'));
});

app.get('/ad/finance', (req, res) => {
    res.sendFile(path.join(__dirname, 'finance.html'));
});

app.get('/ad/analytics', (req, res) => {
    res.sendFile(path.join(__dirname, 'analytics.html'));
});

app.get('/ad/reports', (req, res) => {
    res.sendFile(path.join(__dirname, 'reports.html'));
});

app.get('/ad/settings', (req, res) => {
    res.sendFile(path.join(__dirname, 'settings.html'));
});

app.get('/ad/content', (req, res) => {
    res.sendFile(path.join(__dirname, 'content.html'));
});

app.get('/ad/api-docs', (req, res) => {
    res.sendFile(path.join(__dirname, 'api-docs.html'));
});

app.get('/ad/marketing', (req, res) => {
    res.sendFile(path.join(__dirname, 'marketing.html'));
});

app.get('/ad/security', (req, res) => {
    res.sendFile(path.join(__dirname, 'security.html'));
});

app.get('/ad/monitor', (req, res) => {
    res.sendFile(path.join(__dirname, 'monitor.html'));
});

app.get('/ad/logs', (req, res) => {
    res.sendFile(path.join(__dirname, 'logs.html'));
});

app.get('/ad/tasks', (req, res) => {
    res.sendFile(path.join(__dirname, 'tasks.html'));
});

app.get('/ad/notifications', (req, res) => {
    res.sendFile(path.join(__dirname, 'notifications.html'));
});

app.get('/ad/backup', (req, res) => {
    res.sendFile(path.join(__dirname, 'backup.html'));
});

// 管理后台API路由
app.use(express.json());

// 登录API
app.post('/ad/api/auth/login', (req, res) => {
    const { username, password } = req.body;
    
    if (username === 'admin' && password === 'admin123') {
        res.json({
            success: true,
            requiresTwoFactor: true,
            message: '请输入双因素认证码'
        });
    } else {
        res.status(401).json({
            success: false,
            message: '用户名或密码错误'
        });
    }
});

// 双因素认证API
app.post('/ad/api/auth/verify-2fa', (req, res) => {
    const { code } = req.body;
    
    if (code === '101010') {
        res.json({
            success: true,
            user: {
                id: '1',
                username: 'admin',
                name: '系统管理员',
                email: '<EMAIL>',
                role: 'admin'
            }
        });
    } else {
        res.status(401).json({
            success: false,
            message: '验证码错误'
        });
    }
});

// 重发验证码API
app.post('/ad/api/auth/resend-2fa', (req, res) => {
    res.json({
        success: true,
        message: '验证码已重新发送'
    });
});

// 主网站路由（示例）
app.get('/', (req, res) => {
    res.send(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>主网站</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .admin-link { 
                    display: inline-block; 
                    padding: 10px 20px; 
                    background: #007bff; 
                    color: white; 
                    text-decoration: none; 
                    border-radius: 5px; 
                    margin-top: 20px;
                }
                .admin-link:hover { background: #0056b3; }
            </style>
        </head>
        <body>
            <h1>欢迎访问主网站</h1>
            <p>这是运行在端口3000的主网站。</p>
            <a href="/ad" class="admin-link">进入管理后台</a>
            <p><small>管理后台地址: <code>http://localhost:3000/ad</code></small></p>
        </body>
        </html>
    `);
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`🚀 服务器运行在端口 ${PORT}`);
    console.log(`📱 主网站: http://localhost:${PORT}`);
    console.log(`🔧 管理后台: http://localhost:${PORT}/ad`);
    console.log(`👤 登录信息: admin / admin123 / 101010`);
});

module.exports = app;
