<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统监控 - 管理后台</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/apexcharts/dist/apexcharts.css">
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="monitor.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.html">
                                <i class="bi bi-house-door"></i> 仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.html">
                                <i class="bi bi-people"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="roles.html">
                                <i class="bi bi-person-badge"></i> 角色管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="monitor.html">
                                <i class="bi bi-graph-up"></i> 系统监控
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="settings.html">
                                <i class="bi bi-gear"></i> 系统设置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主要内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">系统监控</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-outline-secondary" id="refreshBtn">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="exportBtn">
                                <i class="bi bi-download"></i> 导出
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="timeRangeDropdown" data-bs-toggle="dropdown">
                                <i class="bi bi-calendar3"></i> 最近24小时
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="#" data-range="24">最近24小时</a></li>
                                <li><a class="dropdown-item" href="#" data-range="72">最近3天</a></li>
                                <li><a class="dropdown-item" href="#" data-range="168">最近7天</a></li>
                                <li><a class="dropdown-item" href="#" data-range="720">最近30天</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 告警容器 -->
                <div id="alertContainer" class="mb-3"></div>

                <!-- 标签页导航 -->
                <ul class="nav nav-tabs mb-4" id="monitorTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
                            <i class="bi bi-speedometer2"></i> 系统概览
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="alerts-tab" data-bs-toggle="tab" data-bs-target="#alerts" type="button" role="tab">
                            <i class="bi bi-exclamation-triangle"></i> 告警管理
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="performance-tab" data-bs-toggle="tab" data-bs-target="#performance" type="button" role="tab">
                            <i class="bi bi-graph-up"></i> 性能分析
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                            <i class="bi bi-gear"></i> 监控设置
                        </button>
                    </li>
                </ul>

                <!-- 标签页内容 -->
                <div class="tab-content" id="monitorTabContent">
                    <!-- 系统概览 -->
                    <div class="tab-pane fade show active" id="overview" role="tabpanel">
                        <!-- 系统状态概览 -->
                        <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">CPU使用率</h5>
                                <div class="d-flex align-items-center">
                                    <div class="display-4 me-3" id="cpuUsage">0%</div>
                                    <div id="cpuTrend"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">内存使用率</h5>
                                <div class="d-flex align-items-center">
                                    <div class="display-4 me-3" id="memoryUsage">0%</div>
                                    <div id="memoryTrend"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">在线用户</h5>
                                <div class="d-flex align-items-center">
                                    <div class="display-4 me-3" id="onlineUsers">0</div>
                                    <div id="userTrend"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">系统负载</h5>
                                <div class="d-flex align-items-center">
                                    <div class="display-4 me-3" id="systemLoad">0</div>
                                    <div id="loadTrend"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 性能监控图表 -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">CPU & 内存使用趋势</h5>
                                <div id="resourceChart"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">网络流量</h5>
                                <div id="networkChart"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 用户活动和系统日志 -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">实时用户活动</h5>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>用户</th>
                                                <th>操作</th>
                                                <th>IP地址</th>
                                                <th>时间</th>
                                            </tr>
                                        </thead>
                                        <tbody id="userActivityTable">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">系统日志</h5>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>级别</th>
                                                <th>消息</th>
                                                <th>来源</th>
                                                <th>时间</th>
                                            </tr>
                                        </thead>
                                        <tbody id="systemLogTable">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                    </div>

                    <!-- 告警管理 -->
                    <div class="tab-pane fade" id="alerts" role="tabpanel">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card">
                                    <div class="card-header">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <h5 class="card-title mb-0">告警历史</h5>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex gap-2 justify-content-end">
                                                    <select class="form-select form-select-sm" id="alertLevelFilter" style="width: auto;">
                                                        <option value="">全部级别</option>
                                                        <option value="low">低</option>
                                                        <option value="medium">中</option>
                                                        <option value="high">高</option>
                                                        <option value="critical">严重</option>
                                                    </select>
                                                    <select class="form-select form-select-sm" id="alertStatusFilter" style="width: auto;">
                                                        <option value="">全部状态</option>
                                                        <option value="active">活跃</option>
                                                        <option value="acknowledge">已确认</option>
                                                        <option value="resolve">已解决</option>
                                                        <option value="ignore">已忽略</option>
                                                    </select>
                                                    <button class="btn btn-outline-primary btn-sm" onclick="loadAlertHistory()">
                                                        <i class="bi bi-arrow-clockwise"></i> 刷新
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table" id="alertHistoryTable">
                                                <thead>
                                                    <tr>
                                                        <th>级别</th>
                                                        <th>类型</th>
                                                        <th>消息</th>
                                                        <th>状态</th>
                                                        <th>时间</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- 动态生成 -->
                                                </tbody>
                                            </table>
                                        </div>
                                        <nav>
                                            <ul class="pagination justify-content-center" id="alertPagination">
                                                <!-- 动态生成 -->
                                            </ul>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 性能分析 -->
                    <div class="tab-pane fade" id="performance" role="tabpanel">
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="d-flex gap-2">
                                    <button class="btn btn-outline-primary btn-sm" onclick="loadPerformanceHistory('1h')">1小时</button>
                                    <button class="btn btn-outline-primary btn-sm" onclick="loadPerformanceHistory('6h')">6小时</button>
                                    <button class="btn btn-primary btn-sm" onclick="loadPerformanceHistory('24h')">24小时</button>
                                    <button class="btn btn-outline-primary btn-sm" onclick="loadPerformanceHistory('7d')">7天</button>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">CPU使用率趋势</h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="cpuTrendChart" height="300"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">内存使用率趋势</h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="memoryTrendChart" height="300"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 监控设置 -->
                    <div class="tab-pane fade" id="settings" role="tabpanel">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">告警阈值设置</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="alertConfigForm">
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label class="form-label">CPU使用率阈值 (%)</label>
                                                    <div class="input-group">
                                                        <input type="number" class="form-control" id="cpuThreshold" min="1" max="100" value="80">
                                                        <div class="input-group-text">
                                                            <input class="form-check-input" type="checkbox" id="cpuEnabled" checked>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">内存使用率阈值 (%)</label>
                                                    <div class="input-group">
                                                        <input type="number" class="form-control" id="memoryThreshold" min="1" max="100" value="85">
                                                        <div class="input-group-text">
                                                            <input class="form-check-input" type="checkbox" id="memoryEnabled" checked>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label class="form-label">磁盘使用率阈值 (%)</label>
                                                    <div class="input-group">
                                                        <input type="number" class="form-control" id="diskThreshold" min="1" max="100" value="90">
                                                        <div class="input-group-text">
                                                            <input class="form-check-input" type="checkbox" id="diskEnabled" checked>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">响应时间阈值 (ms)</label>
                                                    <div class="input-group">
                                                        <input type="number" class="form-control" id="responseTimeThreshold" min="100" value="2000">
                                                        <div class="input-group-text">
                                                            <input class="form-check-input" type="checkbox" id="responseTimeEnabled" checked>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label class="form-label">错误率阈值 (%)</label>
                                                    <div class="input-group">
                                                        <input type="number" class="form-control" id="errorRateThreshold" min="0" max="100" step="0.1" value="5">
                                                        <div class="input-group-text">
                                                            <input class="form-check-input" type="checkbox" id="errorRateEnabled" checked>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label">检查间隔 (秒)</label>
                                                    <input type="number" class="form-control" id="checkInterval" min="10" value="60">
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <label class="form-label">告警冷却时间 (秒)</label>
                                                    <input type="number" class="form-control" id="alertCooldown" min="60" value="300">
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">通知方式</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="emailNotification" checked>
                                                    <label class="form-check-label" for="emailNotification">邮件通知</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="webhookNotification">
                                                    <label class="form-check-label" for="webhookNotification">Webhook通知</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="smsNotification">
                                                    <label class="form-check-label" for="smsNotification">短信通知</label>
                                                </div>
                                            </div>
                                            <button type="button" class="btn btn-primary" onclick="saveAlertConfig()">保存配置</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">监控状态</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <span>实时监控</span>
                                            <span class="badge bg-success">运行中</span>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <span>告警检查</span>
                                            <span class="badge bg-success">启用</span>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <span>数据收集</span>
                                            <span class="badge bg-success">正常</span>
                                        </div>
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <span>存储使用</span>
                                            <span class="badge bg-info">65%</span>
                                        </div>
                                        <hr>
                                        <div class="text-center">
                                            <button class="btn btn-outline-primary btn-sm me-2">重启监控</button>
                                            <button class="btn btn-outline-secondary btn-sm">清理数据</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/jquery/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts/dist/apexcharts.min.js"></script>
    <script src="monitor.js"></script>
</body>
</html> 