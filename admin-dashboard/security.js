// 全局变量
let ipBlacklist = [];
let sensitiveOperations = [];
let securityLogs = [];
let backups = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 加载数据
    loadIpBlacklist();
    loadSensitiveOperations();
    loadSecurityLogs();
    loadBackups();
    
    // 绑定事件处理器
    bindEventHandlers();
});

// 加载IP黑名单数据
async function loadIpBlacklist() {
    try {
        // 模拟API调用
        // const response = await fetch('/api/security/ip-blacklist');
        // const data = await response.json();
        
        // 模拟数据
        const data = [
            { id: 1, ip: '*************', reason: '多次尝试暴力登录', createdAt: '2025-07-08 09:15', createdBy: '系统自动' },
            { id: 2, ip: '************', reason: '恶意API调用', createdAt: '2025-07-07 14:22', createdBy: 'admin' },
            { id: 3, ip: '*************', reason: '扫描漏洞', createdAt: '2025-07-06 18:05', createdBy: '系统自动' },
            { id: 4, ip: '************', reason: '异常访问模式', createdAt: '2025-07-05 22:30', createdBy: 'admin' },
            { id: 5, ip: '*********', reason: '恶意脚本注入', createdAt: '2025-07-04 11:45', createdBy: 'security_admin' }
        ];
        
        ipBlacklist = data;
        renderIpBlacklist();
    } catch (error) {
        console.error('加载IP黑名单失败:', error);
        showToast('错误', '加载IP黑名单失败');
    }
}

// 渲染IP黑名单表格
function renderIpBlacklist() {
    const tableBody = document.getElementById('ipBlacklistTable');
    tableBody.innerHTML = '';
    
    ipBlacklist.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.ip}</td>
            <td>${item.reason}</td>
            <td>${item.createdAt}</td>
            <td>${item.createdBy}</td>
            <td>
                <button class="btn btn-sm btn-danger remove-ip" data-id="${item.id}">移除</button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// 加载敏感操作审批数据
async function loadSensitiveOperations() {
    try {
        // 模拟API调用
        // const response = await fetch('/api/security/sensitive-operations');
        // const data = await response.json();
        
        // 模拟数据
        const data = [
            { id: 'SO-2025-0012', type: '批量删除用户', requester: 'operator1', requestTime: '2025-07-09 10:30', status: 'pending' },
            { id: 'SO-2025-0011', type: '修改系统配置', requester: 'operator2', requestTime: '2025-07-08 16:45', status: 'pending' },
            { id: 'SO-2025-0010', type: '导出用户数据', requester: 'operator3', requestTime: '2025-07-08 09:20', status: 'approved' },
            { id: 'SO-2025-0009', type: '修改支付接口', requester: 'operator1', requestTime: '2025-07-07 14:15', status: 'rejected' }
        ];
        
        sensitiveOperations = data;
        renderSensitiveOperations();
    } catch (error) {
        console.error('加载敏感操作审批数据失败:', error);
        showToast('错误', '加载敏感操作审批数据失败');
    }
}

// 渲染敏感操作审批表格
function renderSensitiveOperations() {
    const tableBody = document.getElementById('sensitiveOperationsTable');
    tableBody.innerHTML = '';
    
    sensitiveOperations.forEach(item => {
        const row = document.createElement('tr');
        
        let statusBadge = '';
        switch(item.status) {
            case 'pending':
                statusBadge = '<span class="badge bg-warning">待审批</span>';
                break;
            case 'approved':
                statusBadge = '<span class="badge bg-success">已批准</span>';
                break;
            case 'rejected':
                statusBadge = '<span class="badge bg-danger">已拒绝</span>';
                break;
        }
        
        let actions = '';
        if (item.status === 'pending') {
            actions = `
                <button class="btn btn-sm btn-success approve-operation" data-id="${item.id}">批准</button>
                <button class="btn btn-sm btn-danger reject-operation" data-id="${item.id}">拒绝</button>
            `;
        } else {
            actions = `
                <button class="btn btn-sm btn-secondary view-operation" data-id="${item.id}">查看</button>
            `;
        }
        
        row.innerHTML = `
            <td>${item.id}</td>
            <td>${item.type}</td>
            <td>${item.requester}</td>
            <td>${item.requestTime}</td>
            <td>${statusBadge}</td>
            <td>${actions}</td>
        `;
        tableBody.appendChild(row);
    });
}

// 加载安全日志数据
async function loadSecurityLogs() {
    try {
        // 模拟API调用
        // const response = await fetch('/api/security/logs');
        // const data = await response.json();
        
        // 模拟数据
        const data = [
            { time: '2025-07-09 13:45:22', level: 'critical', type: '登录失败', ip: '*************', user: 'unknown', details: '多次尝试登录失败，账户已锁定' },
            { time: '2025-07-09 12:30:15', level: 'warning', type: '权限变更', ip: '*************', user: 'admin', details: '修改了用户 operator1 的权限' },
            { time: '2025-07-09 10:15:33', level: 'info', type: '系统配置', ip: '*************', user: 'admin', details: '更新了系统配置参数' },
            { time: '2025-07-08 18:22:45', level: 'critical', type: '异常访问', ip: '************', user: 'unknown', details: '尝试访问未授权API' },
            { time: '2025-07-08 15:10:05', level: 'warning', type: '文件上传', ip: '*************', user: 'operator2', details: '上传了可疑文件，已被系统拦截' }
        ];
        
        securityLogs = data;
        renderSecurityLogs();
    } catch (error) {
        console.error('加载安全日志失败:', error);
        showToast('错误', '加载安全日志失败');
    }
}

// 渲染安全日志表格
function renderSecurityLogs() {
    const tableBody = document.getElementById('securityLogsTable');
    tableBody.innerHTML = '';
    
    securityLogs.forEach(log => {
        const row = document.createElement('tr');
        
        let levelBadge = '';
        switch(log.level) {
            case 'critical':
                levelBadge = '<span class="badge bg-danger">严重</span>';
                break;
            case 'warning':
                levelBadge = '<span class="badge bg-warning">警告</span>';
                break;
            case 'info':
                levelBadge = '<span class="badge bg-info">信息</span>';
                break;
        }
        
        row.innerHTML = `
            <td>${log.time}</td>
            <td>${levelBadge}</td>
            <td>${log.type}</td>
            <td>${log.ip}</td>
            <td>${log.user}</td>
            <td>${log.details}</td>
        `;
        tableBody.appendChild(row);
    });
}

// 加载备份数据
async function loadBackups() {
    try {
        // 模拟API调用
        // const response = await fetch('/api/security/backups');
        // const data = await response.json();
        
        // 模拟数据
        const data = [
            { id: 'BK-2025-0705-001', createdAt: '2025-07-05 03:00:00', type: '完整备份', size: '1.2 GB', status: 'completed' },
            { id: 'BK-2025-0704-001', createdAt: '2025-07-04 03:00:00', type: '完整备份', size: '1.1 GB', status: 'completed' },
            { id: 'BK-2025-0703-001', createdAt: '2025-07-03 03:00:00', type: '完整备份', size: '1.1 GB', status: 'completed' },
            { id: 'BK-2025-0702-001', createdAt: '2025-07-02 03:00:00', type: '完整备份', size: '1.0 GB', status: 'completed' },
            { id: 'BK-2025-0701-001', createdAt: '2025-07-01 03:00:00', type: '完整备份', size: '1.0 GB', status: 'completed' }
        ];
        
        backups = data;
        renderBackups();
    } catch (error) {
        console.error('加载备份数据失败:', error);
        showToast('错误', '加载备份数据失败');
    }
}

// 渲染备份表格
function renderBackups() {
    const tableBody = document.getElementById('backupsTable');
    tableBody.innerHTML = '';
    
    backups.forEach(backup => {
        const row = document.createElement('tr');
        
        let statusBadge = '';
        switch(backup.status) {
            case 'completed':
                statusBadge = '<span class="badge bg-success">完成</span>';
                break;
            case 'in_progress':
                statusBadge = '<span class="badge bg-info">进行中</span>';
                break;
            case 'failed':
                statusBadge = '<span class="badge bg-danger">失败</span>';
                break;
        }
        
        row.innerHTML = `
            <td>${backup.id}</td>
            <td>${backup.createdAt}</td>
            <td>${backup.type}</td>
            <td>${backup.size}</td>
            <td>${statusBadge}</td>
            <td>
                <button class="btn btn-sm btn-primary download-backup" data-id="${backup.id}">下载</button>
                <button class="btn btn-sm btn-warning restore-backup" data-id="${backup.id}">恢复</button>
                <button class="btn btn-sm btn-danger delete-backup" data-id="${backup.id}">删除</button>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

// 添加IP到黑名单
async function addIpToBlacklist() {
    const ipAddress = document.getElementById('ipAddress').value;
    const reason = document.getElementById('reason').value;
    
    if (!ipAddress || !reason) {
        showToast('错误', 'IP地址和原因不能为空');
        return;
    }
    
    try {
        // 模拟API调用
        // const response = await fetch('/api/security/ip-blacklist', {
        //     method: 'POST',
        //     headers: {
        //         'Content-Type': 'application/json'
        //     },
        //     body: JSON.stringify({ ip: ipAddress, reason })
        // });
        // const data = await response.json();
        
        // 模拟成功响应
        const newItem = {
            id: ipBlacklist.length + 1,
            ip: ipAddress,
            reason: reason,
            createdAt: new Date().toISOString().replace('T', ' ').substring(0, 19),
            createdBy: 'admin'
        };
        
        ipBlacklist.unshift(newItem);
        renderIpBlacklist();
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('addIpModal'));
        modal.hide();
        
        // 清空表单
        document.getElementById('addIpForm').reset();
        
        showToast('成功', 'IP已成功添加到黑名单');
    } catch (error) {
        console.error('添加IP到黑名单失败:', error);
        showToast('错误', '添加IP到黑名单失败');
    }
}

// 从黑名单中移除IP
async function removeIpFromBlacklist(id) {
    try {
        // 模拟API调用
        // const response = await fetch(`/api/security/ip-blacklist/${id}`, {
        //     method: 'DELETE'
        // });
        
        // 模拟成功响应
        ipBlacklist = ipBlacklist.filter(item => item.id !== id);
        renderIpBlacklist();
        
        showToast('成功', 'IP已从黑名单中移除');
    } catch (error) {
        console.error('从黑名单中移除IP失败:', error);
        showToast('错误', '从黑名单中移除IP失败');
    }
}

// 审批敏感操作
async function approveOperation(id, approved) {
    try {
        // 模拟API调用
        // const response = await fetch(`/api/security/sensitive-operations/${id}`, {
        //     method: 'PATCH',
        //     headers: {
        //         'Content-Type': 'application/json'
        //     },
        //     body: JSON.stringify({ status: approved ? 'approved' : 'rejected' })
        // });
        
        // 模拟成功响应
        const operation = sensitiveOperations.find(op => op.id === id);
        if (operation) {
            operation.status = approved ? 'approved' : 'rejected';
            renderSensitiveOperations();
            
            showToast('成功', `操作已${approved ? '批准' : '拒绝'}`);
        }
    } catch (error) {
        console.error('审批敏感操作失败:', error);
        showToast('错误', '审批敏感操作失败');
    }
}

// 创建备份
async function createBackup() {
    const backupType = document.getElementById('backupType').value;
    const backupDescription = document.getElementById('backupDescription').value;
    
    try {
        // 模拟API调用
        // const response = await fetch('/api/security/backups', {
        //     method: 'POST',
        //     headers: {
        //         'Content-Type': 'application/json'
        //     },
        //     body: JSON.stringify({ type: backupType, description: backupDescription })
        // });
        // const data = await response.json();
        
        // 模拟成功响应
        const date = new Date();
        const dateStr = date.toISOString().split('T')[0].replace(/-/g, '');
        const newBackup = {
            id: `BK-${dateStr}-001`,
            createdAt: date.toISOString().replace('T', ' ').substring(0, 19),
            type: backupType === 'full' ? '完整备份' : backupType === 'incremental' ? '增量备份' : '差异备份',
            size: '待计算',
            status: 'in_progress'
        };
        
        backups.unshift(newBackup);
        renderBackups();
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('createBackupModal'));
        modal.hide();
        
        // 清空表单
        document.getElementById('createBackupForm').reset();
        
        showToast('成功', '备份创建中，请稍候...');
        
        // 模拟备份完成
        setTimeout(() => {
            newBackup.status = 'completed';
            newBackup.size = '1.3 GB';
            renderBackups();
            showToast('成功', '备份已完成');
        }, 5000);
    } catch (error) {
        console.error('创建备份失败:', error);
        showToast('错误', '创建备份失败');
    }
}

// 绑定事件处理器
function bindEventHandlers() {
    // 刷新按钮
    document.getElementById('refreshBtn').addEventListener('click', () => {
        loadIpBlacklist();
        loadSensitiveOperations();
        loadSecurityLogs();
        loadBackups();
    });
    
    // 添加IP按钮
    document.getElementById('addIpBtn').addEventListener('click', () => {
        const modal = new bootstrap.Modal(document.getElementById('addIpModal'));
        modal.show();
    });
    
    // 保存IP按钮
    document.getElementById('saveIpBtn').addEventListener('click', addIpToBlacklist);
    
    // IP黑名单表格事件委托
    document.getElementById('ipBlacklistTable').addEventListener('click', (e) => {
        if (e.target.classList.contains('remove-ip')) {
            const id = parseInt(e.target.dataset.id);
            removeIpFromBlacklist(id);
        }
    });
    
    // 敏感操作表格事件委托
    document.getElementById('sensitiveOperationsTable').addEventListener('click', (e) => {
        if (e.target.classList.contains('approve-operation')) {
            const id = e.target.dataset.id;
            approveOperation(id, true);
        } else if (e.target.classList.contains('reject-operation')) {
            const id = e.target.dataset.id;
            approveOperation(id, false);
        }
    });
    
    // 创建备份按钮
    document.getElementById('createBackupBtn').addEventListener('click', () => {
        const modal = new bootstrap.Modal(document.getElementById('createBackupModal'));
        modal.show();
    });
    
    // 开始备份按钮
    document.getElementById('startBackupBtn').addEventListener('click', createBackup);
    
    // 备份表格事件委托
    document.getElementById('backupsTable').addEventListener('click', (e) => {
        if (e.target.classList.contains('download-backup')) {
            const id = e.target.dataset.id;
            showToast('信息', `正在下载备份 ${id}`);
        } else if (e.target.classList.contains('restore-backup')) {
            const id = e.target.dataset.id;
            if (confirm(`确定要恢复备份 ${id}？这将覆盖当前数据。`)) {
                showToast('信息', `正在恢复备份 ${id}，请稍候...`);
            }
        } else if (e.target.classList.contains('delete-backup')) {
            const id = e.target.dataset.id;
            if (confirm(`确定要删除备份 ${id}？此操作不可撤销。`)) {
                backups = backups.filter(backup => backup.id !== id);
                renderBackups();
                showToast('成功', `备份 ${id} 已删除`);
            }
        }
    });
}

// 显示提示消息
function showToast(title, message) {
    // 如果页面中有Toast组件，可以使用它
    console.log(`${title}: ${message}`);
    alert(`${title}: ${message}`);
} 