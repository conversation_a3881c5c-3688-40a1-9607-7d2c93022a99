const express = require('express');
const router = express.Router();

// 导入控制器
const authController = require('../controllers/authController');
const userController = require('../controllers/userController');
const couponController = require('../controllers/couponController');
const contentController = require('../controllers/contentController');
const apiKeyController = require('../controllers/apiKeyController');
const securityController = require('../controllers/securityController');
const monitorController = require('../controllers/monitorController');
const userAnalyticsController = require('../controllers/userAnalyticsController');
const agentController = require('../controllers/agentController');
const financeController = require('../controllers/financeController');
const settingsController = require('../controllers/settingsController');
const contentController = require('../controllers/contentController');
const apiController = require('../controllers/apiController');
const marketingController = require('../controllers/marketingController');
const securityController = require('../controllers/securityController');
const roleController = require('../controllers/roleController');
const analyticsController = require('../controllers/analyticsController');
const commissionController = require('../controllers/commissionController');
const pricingController = require('../controllers/pricingController');
const apiDocsController = require('../controllers/apiDocsController');

// 中间件
const { isAuthenticated, hasRole } = require('../middleware/auth');

// 认证路由
router.post('/auth/login', authController.login);
router.post('/auth/verify-2fa', authController.verify2FA);
router.post('/auth/resend-2fa', authController.resend2FA);
router.post('/auth/check-permission', authController.checkPermission);
router.get('/auth/login-logs', isAuthenticated, hasRole(['admin']), authController.getLoginLogs);

// 用户路由
router.get('/users', isAuthenticated, hasRole(['admin', 'finance_admin']), userController.getAllUsers);
router.get('/users/:id', isAuthenticated, userController.getUserById);
router.post('/users', isAuthenticated, hasRole(['admin']), userController.createUser);
router.put('/users/:id', isAuthenticated, hasRole(['admin']), userController.updateUser);
router.delete('/users/:id', isAuthenticated, hasRole(['admin']), userController.deleteUser);

// 优惠券路由
router.get('/coupons', isAuthenticated, couponController.getAllCoupons);
router.get('/coupons/:id', isAuthenticated, couponController.getCouponById);
router.post('/coupons', isAuthenticated, hasRole(['admin', 'marketing_admin']), couponController.createCoupon);
router.put('/coupons/:id', isAuthenticated, hasRole(['admin', 'marketing_admin']), couponController.updateCoupon);
router.delete('/coupons/:id', isAuthenticated, hasRole(['admin', 'marketing_admin']), couponController.deleteCoupon);

// 内容路由
router.get('/content', isAuthenticated, contentController.getAllContent);
router.get('/content/:id', isAuthenticated, contentController.getContentById);
router.post('/content', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.createContent);
router.put('/content/:id', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.updateContent);
router.delete('/content/:id', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.deleteContent);

// API密钥路由
router.get('/api-keys', isAuthenticated, hasRole(['admin']), apiKeyController.getAllApiKeys);
router.get('/api-keys/:id', isAuthenticated, hasRole(['admin']), apiKeyController.getApiKeyById);
router.post('/api-keys', isAuthenticated, hasRole(['admin']), apiKeyController.createApiKey);
router.put('/api-keys/:id', isAuthenticated, hasRole(['admin']), apiKeyController.updateApiKey);
router.delete('/api-keys/:id', isAuthenticated, hasRole(['admin']), apiKeyController.deleteApiKey);

// 安全路由
router.get('/security/ip-blacklist', isAuthenticated, hasRole(['admin']), securityController.getIpBlacklist);
router.post('/security/ip-blacklist', isAuthenticated, hasRole(['admin']), securityController.addToIpBlacklist);
router.delete('/security/ip-blacklist/:id', isAuthenticated, hasRole(['admin']), securityController.removeFromIpBlacklist);

// 统计数据路由
router.get('/statistics/dashboard', isAuthenticated, require('../controllers/statisticsController').getDashboardStats);
router.get('/statistics/users', isAuthenticated, hasRole(['admin']), require('../controllers/statisticsController').getUserStats);
router.get('/statistics/finance', isAuthenticated, hasRole(['admin', 'finance_admin']), require('../controllers/statisticsController').getFinanceStats);

// 监控路由
router.get('/monitor/system-status', isAuthenticated, hasRole(['admin']), monitorController.getSystemStatus);
router.get('/monitor/api-stats', isAuthenticated, hasRole(['admin']), monitorController.getApiStats);
router.get('/monitor/system-health', isAuthenticated, hasRole(['admin']), monitorController.getSystemHealth);

// 用户分析路由
router.get('/analytics/user-stats', isAuthenticated, hasRole(['admin']), userAnalyticsController.getUserStats);
router.get('/analytics/user-growth', isAuthenticated, hasRole(['admin']), userAnalyticsController.getUserGrowthTrend);
router.get('/analytics/user-activity', isAuthenticated, hasRole(['admin']), userAnalyticsController.getUserActivity);
router.get('/analytics/retention', isAuthenticated, hasRole(['admin']), userAnalyticsController.getRetentionAnalysis);
router.get('/analytics/user-distribution', isAuthenticated, hasRole(['admin']), userAnalyticsController.getUserDistribution);
router.get('/analytics/user-behavior', isAuthenticated, hasRole(['admin']), userAnalyticsController.getUserBehavior);

// 代理管理路由
router.get('/agents', isAuthenticated, hasRole(['admin']), agentController.getAgents);
router.post('/agents', isAuthenticated, hasRole(['admin']), agentController.createAgent);
router.put('/agents/:id', isAuthenticated, hasRole(['admin']), agentController.updateAgent);
router.delete('/agents/:id', isAuthenticated, hasRole(['admin']), agentController.deleteAgent);
router.get('/agents/levels', isAuthenticated, hasRole(['admin']), agentController.getAgentLevels);
router.post('/agents/levels', isAuthenticated, hasRole(['admin']), agentController.createAgentLevel);
router.get('/agents/commission-settings', isAuthenticated, hasRole(['admin']), agentController.getCommissionSettings);
router.put('/agents/commission-settings', isAuthenticated, hasRole(['admin']), agentController.updateCommissionSettings);
router.get('/agents/recruitment-settings', isAuthenticated, hasRole(['admin']), agentController.getRecruitmentSettings);
router.put('/agents/recruitment-settings', isAuthenticated, hasRole(['admin']), agentController.updateRecruitmentSettings);

// 财务管理路由
router.get('/finance/overview', isAuthenticated, hasRole(['admin', 'finance_admin']), financeController.getFinanceOverview);
router.get('/finance/transactions', isAuthenticated, hasRole(['admin', 'finance_admin']), financeController.getTransactions);
router.get('/finance/refunds', isAuthenticated, hasRole(['admin', 'finance_admin']), financeController.getRefunds);
router.post('/finance/refunds/:id/process', isAuthenticated, hasRole(['admin', 'finance_admin']), financeController.processRefund);
router.get('/finance/consumption-analysis', isAuthenticated, hasRole(['admin', 'finance_admin']), financeController.getConsumptionAnalysis);

// 系统设置路由
router.get('/settings/pricing', isAuthenticated, hasRole(['admin']), settingsController.getPricingSettings);
router.post('/settings/pricing', isAuthenticated, hasRole(['admin']), settingsController.createPricingPlan);
router.put('/settings/pricing/:id', isAuthenticated, hasRole(['admin']), settingsController.updatePricingPlan);
router.post('/settings/pricing/batch-adjust', isAuthenticated, hasRole(['admin']), settingsController.batchAdjustPricing);
router.get('/settings/system-config', isAuthenticated, hasRole(['admin']), settingsController.getSystemConfig);
router.put('/settings/system-config/:configType', isAuthenticated, hasRole(['admin']), settingsController.updateSystemConfig);
router.get('/settings/operation-logs', isAuthenticated, hasRole(['admin']), settingsController.getOperationLogs);
router.post('/settings/operation-logs/clear', isAuthenticated, hasRole(['admin']), settingsController.clearOperationLogs);

// 内容管理路由
router.get('/content/agreements', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.getAgreements);
router.post('/content/agreements', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.createAgreement);
router.get('/content/agreements/:id', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.getAgreementDetail);
router.put('/content/agreements/:id', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.updateAgreement);
router.post('/content/agreements/:id/publish', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.publishAgreement);
router.get('/content/announcements', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.getAnnouncements);
router.post('/content/announcements', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.createAnnouncement);
router.get('/content/announcements/:id', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.getAnnouncementDetail);
router.put('/content/announcements/:id', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.updateAnnouncement);
router.post('/content/announcements/:id/publish', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.publishAnnouncement);
router.post('/content/announcements/:id/unpublish', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.unpublishAnnouncement);
router.delete('/content/announcements/:id', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.deleteAnnouncement);
router.get('/content/qrcode-settings', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.getQRCodeSettings);
router.put('/content/qrcode-settings', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.updateQRCodeSettings);
router.get('/content/pages', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.getPages);
router.put('/content/pages/:id', isAuthenticated, hasRole(['admin', 'content_admin']), contentController.updatePage);

// API配置路由
router.get('/api-config/google', isAuthenticated, hasRole(['admin']), apiController.getGoogleApiConfig);
router.put('/api-config/google', isAuthenticated, hasRole(['admin']), apiController.updateGoogleApiConfig);
router.get('/api-config/payment', isAuthenticated, hasRole(['admin']), apiController.getPaymentConfig);
router.put('/api-config/payment/:provider', isAuthenticated, hasRole(['admin']), apiController.updatePaymentConfig);
router.get('/api-config/usage-stats', isAuthenticated, hasRole(['admin']), apiController.getApiUsageStats);
router.get('/api-config/keys', isAuthenticated, hasRole(['admin']), apiController.getApiKeys);
router.post('/api-config/keys', isAuthenticated, hasRole(['admin']), apiController.createApiKey);
router.get('/api-config/keys/:id', isAuthenticated, hasRole(['admin']), apiController.getApiKeyDetail);
router.put('/api-config/keys/:id', isAuthenticated, hasRole(['admin']), apiController.updateApiKey);
router.post('/api-config/keys/:id/rotate', isAuthenticated, hasRole(['admin']), apiController.rotateApiKey);
router.delete('/api-config/keys/:id', isAuthenticated, hasRole(['admin']), apiController.deleteApiKey);
router.get('/api-config/webhooks', isAuthenticated, hasRole(['admin']), apiController.getWebhooks);
router.post('/api-config/webhooks', isAuthenticated, hasRole(['admin']), apiController.createWebhook);
router.get('/api-config/webhooks/:id', isAuthenticated, hasRole(['admin']), apiController.getWebhookDetail);
router.put('/api-config/webhooks/:id', isAuthenticated, hasRole(['admin']), apiController.updateWebhook);
router.post('/api-config/webhooks/:id/test', isAuthenticated, hasRole(['admin']), apiController.testWebhook);
router.delete('/api-config/webhooks/:id', isAuthenticated, hasRole(['admin']), apiController.deleteWebhook);
router.post('/api-config/test-connection', isAuthenticated, hasRole(['admin']), apiController.testApiConnection);

// 营销工具路由
router.get('/marketing/overview', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.getMarketingOverview);
router.get('/marketing/coupons', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.getCoupons);
router.post('/marketing/coupons', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.createCoupon);
router.get('/marketing/coupons/:id', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.getCouponDetail);
router.put('/marketing/coupons/:id', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.updateCoupon);
router.post('/marketing/coupons/:id/toggle', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.toggleCouponStatus);
router.delete('/marketing/coupons/:id', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.deleteCoupon);
router.get('/marketing/campaigns', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.getCampaigns);
router.post('/marketing/campaigns', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.createCampaign);
router.get('/marketing/campaigns/:id', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.getCampaignDetail);
router.put('/marketing/campaigns/:id', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.updateCampaign);
router.post('/marketing/campaigns/:id/toggle', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.toggleCampaignStatus);
router.delete('/marketing/campaigns/:id', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.deleteCampaign);
router.get('/marketing/referrals', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.getReferrals);
router.get('/marketing/referral-settings', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.getReferralSettings);
router.put('/marketing/referral-settings', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.updateReferralSettings);
router.get('/marketing/points-settings', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.getPointsSettings);
router.put('/marketing/points-settings', isAuthenticated, hasRole(['admin', 'marketing_admin']), marketingController.updatePointsSettings);

// 安全中心路由
router.get('/security/overview', isAuthenticated, hasRole(['admin', 'security_admin']), securityController.getSecurityOverview);
router.get('/security/risk-rules', isAuthenticated, hasRole(['admin', 'security_admin']), securityController.getRiskRules);
router.post('/security/risk-rules', isAuthenticated, hasRole(['admin', 'security_admin']), securityController.createRiskRule);
router.get('/security/approvals', isAuthenticated, hasRole(['admin', 'security_admin']), securityController.getApprovals);
router.post('/security/approvals/:id/process', isAuthenticated, hasRole(['admin', 'security_admin']), securityController.processApproval);
router.get('/security/backups', isAuthenticated, hasRole(['admin', 'security_admin']), securityController.getBackups);
router.post('/security/backups', isAuthenticated, hasRole(['admin', 'security_admin']), securityController.createBackup);
router.get('/security/ip-blacklist', isAuthenticated, hasRole(['admin', 'security_admin']), securityController.getIpBlacklist);

// 代理管理路由
router.get('/agents/overview', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.getAgentOverview);
router.get('/agents', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.getAgents);
router.post('/agents', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.createAgent);
router.put('/agents/:id', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.updateAgent);
router.get('/agents/levels', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.getAgentLevels);
router.post('/agents/levels', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.createAgentLevel);
router.put('/agents/levels/:id', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.updateAgentLevel);
router.get('/agents/commission-settings', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.getCommissionSettings);
router.put('/agents/commission-settings', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.updateCommissionSettings);
router.get('/agents/recruitment-settings', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.getRecruitmentSettings);
router.put('/agents/recruitment-settings', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.updateRecruitmentSettings);

// 多级代理商体系路由
router.get('/agents/:agentId/hierarchy', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.getAgentHierarchy);
router.put('/agents/:agentId/upline', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.setAgentUpline);
router.post('/agents/calculate-commission', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.calculateMultiLevelCommission);
router.put('/agents/:agentId/upgrade', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.upgradeAgent);
router.get('/agents/:agentId/team-performance', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.getTeamPerformance);

// 招商管理路由
router.get('/recruitment/applications', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.getRecruitmentApplications);
router.post('/recruitment/applications', agentController.createRecruitmentApplication); // 公开接口，不需要认证
router.put('/recruitment/applications/:applicationId/review', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.reviewRecruitmentApplication);
router.post('/recruitment/applications/batch-review', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.batchReviewApplications);
router.get('/recruitment/stats', isAuthenticated, hasRole(['admin', 'agent_admin']), agentController.getRecruitmentStats);

// 财务管理路由
router.get('/finance/overview', isAuthenticated, hasRole(['admin', 'finance_admin']), financeController.getFinanceOverview);
router.get('/finance/transactions', isAuthenticated, hasRole(['admin', 'finance_admin']), financeController.getTransactions);
router.get('/finance/refunds', isAuthenticated, hasRole(['admin', 'finance_admin']), financeController.getRefunds);
router.post('/finance/refunds/:id/process', isAuthenticated, hasRole(['admin', 'finance_admin']), financeController.processRefund);
router.get('/finance/reports', isAuthenticated, hasRole(['admin', 'finance_admin']), financeController.getFinanceReports);
router.get('/finance/consumption-analysis', isAuthenticated, hasRole(['admin', 'finance_admin']), financeController.getConsumptionAnalysis);

// 系统设置路由
router.get('/settings/basic', isAuthenticated, hasRole(['admin']), settingsController.getBasicSettings);
router.put('/settings/basic', isAuthenticated, hasRole(['admin']), settingsController.updateBasicSettings);
router.get('/settings/pricing', isAuthenticated, hasRole(['admin']), settingsController.getPricingSettings);
router.put('/settings/pricing', isAuthenticated, hasRole(['admin']), settingsController.updatePricingSettings);
router.get('/settings/payment', isAuthenticated, hasRole(['admin']), settingsController.getPaymentSettings);
router.put('/settings/payment', isAuthenticated, hasRole(['admin']), settingsController.updatePaymentSettings);
router.get('/settings/system', isAuthenticated, hasRole(['admin']), settingsController.getSystemParameters);
router.put('/settings/system', isAuthenticated, hasRole(['admin']), settingsController.updateSystemParameters);
router.get('/settings/notification', isAuthenticated, hasRole(['admin']), settingsController.getNotificationSettings);
router.put('/settings/notification', isAuthenticated, hasRole(['admin']), settingsController.updateNotificationSettings);
router.get('/settings/operation-logs', isAuthenticated, hasRole(['admin']), settingsController.getOperationLogs);

// 角色管理路由
router.get('/roles', isAuthenticated, hasRole(['admin']), roleController.getRoles);
router.post('/roles', isAuthenticated, hasRole(['admin']), roleController.createRole);
router.get('/roles/:id', isAuthenticated, hasRole(['admin']), roleController.getRoleDetail);
router.put('/roles/:id', isAuthenticated, hasRole(['admin']), roleController.updateRole);
router.delete('/roles/:id', isAuthenticated, hasRole(['admin']), roleController.deleteRole);
router.post('/roles/:id/copy', isAuthenticated, hasRole(['admin']), roleController.copyRole);
router.post('/roles/batch-status', isAuthenticated, hasRole(['admin']), roleController.batchUpdateRoleStatus);
router.get('/permissions/tree', isAuthenticated, hasRole(['admin']), roleController.getPermissionTree);

// 数据分析路由
router.get('/analytics/comprehensive', isAuthenticated, hasRole(['admin', 'analyst']), analyticsController.getComprehensiveAnalytics);
router.get('/analytics/user-behavior', isAuthenticated, hasRole(['admin', 'analyst']), analyticsController.getUserBehaviorAnalytics);
router.get('/analytics/revenue', isAuthenticated, hasRole(['admin', 'analyst']), analyticsController.getRevenueAnalytics);
router.get('/analytics/agent-performance', isAuthenticated, hasRole(['admin', 'analyst']), analyticsController.getAgentPerformanceAnalytics);
router.get('/analytics/marketing-effectiveness', isAuthenticated, hasRole(['admin', 'analyst']), analyticsController.getMarketingEffectivenessAnalytics);
router.post('/analytics/custom-report', isAuthenticated, hasRole(['admin', 'analyst']), analyticsController.generateCustomReport);

// 佣金管理路由
router.get('/commission/config', isAuthenticated, hasRole(['admin', 'finance']), commissionController.getCommissionConfig);
router.put('/commission/config', isAuthenticated, hasRole(['admin']), commissionController.updateCommissionConfig);
router.get('/commission/stats', isAuthenticated, hasRole(['admin', 'finance']), commissionController.getAgentCommissionStats);
router.get('/commission/records', isAuthenticated, hasRole(['admin', 'finance']), commissionController.getCommissionRecords);
router.post('/commission/calculate', isAuthenticated, hasRole(['admin', 'finance']), commissionController.calculateOrderCommission);
router.post('/commission/batch-settle', isAuthenticated, hasRole(['admin', 'finance']), commissionController.batchSettleCommissions);
router.get('/commission/withdraw-requests', isAuthenticated, hasRole(['admin', 'finance']), commissionController.getWithdrawRequests);
router.put('/commission/withdraw-requests/:requestId', isAuthenticated, hasRole(['admin', 'finance']), commissionController.processWithdrawRequest);

// 佣金计算引擎路由
router.post('/commission/calculate-order', isAuthenticated, hasRole(['admin', 'finance']), commissionController.calculateOrderCommission);
router.post('/commission/batch-calculate', isAuthenticated, hasRole(['admin', 'finance']), commissionController.batchCalculateCommission);
router.post('/commission/auto-settle', isAuthenticated, hasRole(['admin', 'finance']), commissionController.autoSettleCommissions);
router.get('/commission/performance-bonus', isAuthenticated, hasRole(['admin', 'finance']), commissionController.calculatePerformanceBonus);
router.get('/commission/analysis', isAuthenticated, hasRole(['admin', 'finance']), commissionController.getCommissionAnalysis);

// 价格管理路由
router.get('/pricing/products', isAuthenticated, hasRole(['admin', 'finance']), pricingController.getProductPrices);
router.get('/pricing/products/:productId', isAuthenticated, hasRole(['admin', 'finance']), pricingController.getProductPrice);
router.put('/pricing/products/:productId', isAuthenticated, hasRole(['admin', 'finance']), pricingController.updateProductPrice);
router.post('/pricing/batch-update', isAuthenticated, hasRole(['admin', 'finance']), pricingController.batchUpdatePrices);
router.get('/pricing/discounts', isAuthenticated, hasRole(['admin', 'finance']), pricingController.getDiscountConfig);
router.put('/pricing/discounts/:discountId', isAuthenticated, hasRole(['admin', 'finance']), pricingController.updateDiscount);
router.delete('/pricing/discounts/:discountId', isAuthenticated, hasRole(['admin']), pricingController.deleteDiscount);
router.get('/pricing/member-pricing', isAuthenticated, hasRole(['admin', 'finance']), pricingController.getMemberPricing);
router.put('/pricing/member-pricing', isAuthenticated, hasRole(['admin']), pricingController.updateMemberPricing);
router.get('/pricing/stats', isAuthenticated, hasRole(['admin', 'finance']), pricingController.getPricingStats);
router.post('/pricing/calculate', isAuthenticated, hasRole(['admin', 'finance']), pricingController.calculatePrice);

// 监控管理路由
router.get('/monitor/alert-config', isAuthenticated, hasRole(['admin']), monitorController.getAlertConfig);
router.put('/monitor/alert-config', isAuthenticated, hasRole(['admin']), monitorController.updateAlertConfig);
router.get('/monitor/alert-history', isAuthenticated, hasRole(['admin']), monitorController.getAlertHistory);
router.put('/monitor/alerts/:alertId', isAuthenticated, hasRole(['admin']), monitorController.handleAlert);
router.get('/monitor/performance-history', isAuthenticated, hasRole(['admin']), monitorController.getPerformanceHistory);

// API文档路由
router.get('/docs', apiDocsController.getApiDocumentation);
router.get('/docs/openapi', apiDocsController.getOpenApiSpec);
router.get('/docs/stats', apiDocsController.getApiStats);
router.get('/docs/postman', apiDocsController.generatePostmanCollection);
router.post('/docs/test', isAuthenticated, hasRole(['admin']), apiDocsController.testApiEndpoint);

module.exports = router;