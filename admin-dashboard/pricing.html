<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>价格管理 - WriterPro管理后台</title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="css/responsive.css">
    <link rel="stylesheet" href="css/ui-enhancements.css">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <!-- 引入Bootstrap -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .pricing-card {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1.5rem;
            margin-bottom: 1rem;
            background-color: #ffffff;
            transition: all 0.2s ease;
        }
        .pricing-card:hover {
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-color: #0d6efd;
        }
        .price-display {
            font-size: 1.5rem;
            font-weight: bold;
            color: #198754;
        }
        .price-old {
            text-decoration: line-through;
            color: #6c757d;
            font-size: 1rem;
        }
        .discount-badge {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .member-level {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
        }
        .batch-actions {
            position: sticky;
            top: 0;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 1rem;
            margin: -1rem -1rem 1rem -1rem;
            border-radius: 0.375rem 0.375rem 0 0;
            display: none;
        }
        .batch-actions.show {
            display: block;
        }
        .price-history {
            max-height: 300px;
            overflow-y: auto;
        }
        .price-change {
            padding: 0.5rem;
            border-left: 3px solid #dee2e6;
            margin-bottom: 0.5rem;
        }
        .price-change.increase {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .price-change.decrease {
            border-left-color: #198754;
            background-color: #d1e7dd;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="logo">
                <h1>WriterPro</h1>
                <span>管理后台</span>
            </div>

            <nav class="sidebar-menu">
                <ul>
                    <li><a href="dashboard.html"><i class="bi bi-speedometer2"></i><span data-i18n="common.dashboard">仪表盘</span></a></li>
                    <li><a href="users.html"><i class="bi bi-people"></i><span data-i18n="common.users">用户管理</span></a></li>
                    <li><a href="agents.html"><i class="bi bi-person-badge"></i><span data-i18n="common.agents">代理商管理</span></a></li>
                    <li><a href="commission.html"><i class="bi bi-currency-dollar"></i><span>佣金管理</span></a></li>
                    <li class="active"><a href="pricing.html"><i class="bi bi-tags"></i><span>价格管理</span></a></li>
                    <li><a href="roles.html"><i class="bi bi-shield-check"></i><span data-i18n="common.roles">角色管理</span></a></li>
                    <li><a href="finance.html"><i class="bi bi-currency-dollar"></i><span data-i18n="common.finance">财务管理</span></a></li>
                    <li><a href="settings.html"><i class="bi bi-gear"></i><span data-i18n="common.settings">系统设置</span></a></li>
                    <li><a href="content.html"><i class="bi bi-file-text"></i><span data-i18n="common.content">内容管理</span></a></li>
                    <li><a href="api.html"><i class="bi bi-code-slash"></i><span data-i18n="common.api">API配置</span></a></li>
                    <li><a href="marketing.html"><i class="bi bi-megaphone"></i><span data-i18n="common.marketing">营销工具</span></a></li>
                    <li><a href="security.html"><i class="bi bi-shield-lock"></i><span data-i18n="common.security">安全中心</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 头部 -->
            <header class="main-header">
                <div class="header-left">
                    <button class="menu-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <div class="header-title">
                        <h2>价格管理</h2>
                        <p>产品价格设置、折扣配置和会员价格管理</p>
                    </div>
                </div>

                <div class="header-right">
                    <div class="header-actions">
                        <button class="action-btn theme-toggle" id="themeToggle" title="切换主题">
                            <i class="bi bi-moon"></i>
                        </button>

                        <button class="action-btn">
                            <i class="bi bi-bell"></i>
                            <span class="badge">5</span>
                        </button>

                        <div class="user-menu">
                            <button class="user-btn">
                                <div class="user-avatar">A</div>
                                <span>管理员</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            <div class="user-dropdown">
                                <a href="#"><i class="bi bi-person"></i>个人资料</a>
                                <a href="#"><i class="bi bi-gear"></i>设置</a>
                                <a href="index.html"><i class="bi bi-box-arrow-right"></i>退出登录</a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 价格统计 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-primary">
                                <i class="bi bi-box"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="totalProducts">0</h3>
                                <p>产品总数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-success">
                                <i class="bi bi-currency-dollar"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="avgPrice">¥0</h3>
                                <p>平均价格</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-warning">
                                <i class="bi bi-percent"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="activeDiscounts">0</h3>
                                <p>活跃折扣</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="metric-card">
                            <div class="metric-icon bg-info">
                                <i class="bi bi-arrow-up-down"></i>
                            </div>
                            <div class="metric-content">
                                <h3 id="recentChanges">0</h3>
                                <p>近期调价</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 标签页导航 -->
                <ul class="nav nav-tabs mb-4" id="pricingTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="products-tab" data-bs-toggle="tab" data-bs-target="#products" type="button" role="tab">
                            <i class="bi bi-box"></i> 产品价格
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="discounts-tab" data-bs-toggle="tab" data-bs-target="#discounts" type="button" role="tab">
                            <i class="bi bi-percent"></i> 折扣管理
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="member-pricing-tab" data-bs-toggle="tab" data-bs-target="#member-pricing" type="button" role="tab">
                            <i class="bi bi-star"></i> 会员价格
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="batch-operations-tab" data-bs-toggle="tab" data-bs-target="#batch-operations" type="button" role="tab">
                            <i class="bi bi-gear"></i> 批量操作
                        </button>
                    </li>
                </ul>

                <!-- 标签页内容 -->
                <div class="tab-content" id="pricingTabContent">
                    <!-- 产品价格 -->
                    <div class="tab-pane fade show active" id="products" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h5 class="card-title mb-0">产品价格列表</h5>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex gap-2 justify-content-end">
                                            <button class="btn btn-success" id="addProductBtn">
                                                <i class="bi bi-plus"></i> 添加产品
                                            </button>
                                            <button class="btn btn-primary" id="importPricesBtn">
                                                <i class="bi bi-upload"></i> 导入价格
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- 批量操作栏 -->
                                <div class="batch-actions" id="batchActions">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span>已选择 <strong id="selectedCount">0</strong> 个产品</span>
                                        <div class="d-flex gap-2">
                                            <button class="btn btn-warning btn-sm" id="batchAdjustPriceBtn">批量调价</button>
                                            <button class="btn btn-danger btn-sm" id="batchDeleteBtn">批量删除</button>
                                            <button class="btn btn-outline-secondary btn-sm" id="clearSelectionBtn">取消选择</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- 筛选器 -->
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <select class="form-select" id="categoryFilter">
                                            <option value="">全部分类</option>
                                            <option value="basic">基础版</option>
                                            <option value="premium">高级版</option>
                                            <option value="enterprise">企业版</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" id="statusFilter">
                                            <option value="">全部状态</option>
                                            <option value="active">启用</option>
                                            <option value="inactive">禁用</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <input type="text" class="form-control" id="searchInput" placeholder="搜索产品名称或SKU...">
                                    </div>
                                    <div class="col-md-2">
                                        <button class="btn btn-outline-primary w-100" id="searchBtn">
                                            <i class="bi bi-search"></i> 搜索
                                        </button>
                                    </div>
                                </div>

                                <!-- 产品价格表格 -->
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>
                                                    <input type="checkbox" class="form-check-input" id="selectAll">
                                                </th>
                                                <th>产品名称</th>
                                                <th>SKU</th>
                                                <th>分类</th>
                                                <th>基础价格</th>
                                                <th>会员价格</th>
                                                <th>状态</th>
                                                <th>更新时间</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="productsTable">
                                            <!-- 动态生成 -->
                                        </tbody>
                                    </table>
                                </div>

                                <!-- 分页 -->
                                <nav>
                                    <ul class="pagination justify-content-center" id="productsPagination">
                                        <!-- 动态生成 -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>

                    <!-- 折扣管理 -->
                    <div class="tab-pane fade" id="discounts" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h5 class="card-title mb-0">折扣配置</h5>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex gap-2 justify-content-end">
                                            <button class="btn btn-primary" id="addDiscountBtn">
                                                <i class="bi bi-plus"></i> 创建折扣
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="discountsList">
                                    <!-- 动态生成折扣列表 -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 会员价格 -->
                    <div class="tab-pane fade" id="member-pricing" role="tabpanel">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">会员价格配置</h5>
                            </div>
                            <div class="card-body">
                                <div id="memberLevels">
                                    <!-- 动态生成会员等级配置 -->
                                </div>
                                <div class="mt-3">
                                    <button class="btn btn-outline-primary" id="addMemberLevelBtn">
                                        <i class="bi bi-plus"></i> 添加会员等级
                                    </button>
                                    <button class="btn btn-success ms-2" id="saveMemberPricingBtn">
                                        <i class="bi bi-check"></i> 保存配置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 批量操作 -->
                    <div class="tab-pane fade" id="batch-operations" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">批量调价</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="batchPriceForm">
                                            <div class="mb-3">
                                                <label class="form-label">调价方式</label>
                                                <select class="form-select" id="adjustmentType">
                                                    <option value="percentage">按百分比</option>
                                                    <option value="fixed">按固定金额</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">调价类型</label>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="priceAction" id="increasePrice" value="increase" checked>
                                                    <label class="form-check-label" for="increasePrice">涨价</label>
                                                </div>
                                                <div class="form-check">
                                                    <input class="form-check-input" type="radio" name="priceAction" id="decreasePrice" value="decrease">
                                                    <label class="form-check-label" for="decreasePrice">降价</label>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">调价幅度</label>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" id="adjustmentValue" step="0.01" min="0" required>
                                                    <span class="input-group-text" id="adjustmentUnit">%</span>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">筛选条件</label>
                                                <select class="form-select" id="batchCategory">
                                                    <option value="">全部分类</option>
                                                    <option value="basic">基础版</option>
                                                    <option value="premium">高级版</option>
                                                    <option value="enterprise">企业版</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">调价原因</label>
                                                <textarea class="form-control" id="batchReason" rows="3" placeholder="请输入调价原因..."></textarea>
                                            </div>
                                            <button type="submit" class="btn btn-primary w-100">执行批量调价</button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">价格变更历史</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="price-history" id="priceHistory">
                                            <!-- 动态生成价格变更历史 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 产品价格编辑模态框 -->
    <div class="modal fade" id="productPriceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="productPriceModalTitle">编辑产品价格</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="productPriceForm">
                        <input type="hidden" id="productId">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">产品名称</label>
                                    <input type="text" class="form-control" id="productName" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">SKU</label>
                                    <input type="text" class="form-control" id="productSku" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">分类</label>
                                    <select class="form-select" id="productCategory">
                                        <option value="basic">基础版</option>
                                        <option value="premium">高级版</option>
                                        <option value="enterprise">企业版</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">状态</label>
                                    <select class="form-select" id="productStatus">
                                        <option value="active">启用</option>
                                        <option value="inactive">禁用</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">基础价格</label>
                                    <div class="input-group">
                                        <span class="input-group-text">¥</span>
                                        <input type="number" class="form-control" id="basePrice" step="0.01" min="0" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">成本价格</label>
                                    <div class="input-group">
                                        <span class="input-group-text">¥</span>
                                        <input type="number" class="form-control" id="costPrice" step="0.01" min="0">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">产品描述</label>
                            <textarea class="form-control" id="productDescription" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">调价原因</label>
                            <input type="text" class="form-control" id="changeReason" placeholder="请输入调价原因...">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveProductPriceBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 折扣编辑模态框 -->
    <div class="modal fade" id="discountModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="discountModalTitle">创建折扣</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="discountForm">
                        <input type="hidden" id="discountId">
                        <div class="mb-3">
                            <label class="form-label">折扣名称</label>
                            <input type="text" class="form-control" id="discountName" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">折扣类型</label>
                                    <select class="form-select" id="discountType">
                                        <option value="percentage">百分比折扣</option>
                                        <option value="fixed">固定金额折扣</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">折扣值</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="discountValue" step="0.01" min="0" required>
                                        <span class="input-group-text" id="discountUnit">%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">开始时间</label>
                                    <input type="datetime-local" class="form-control" id="startDate">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">结束时间</label>
                                    <input type="datetime-local" class="form-control" id="endDate">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">适用范围</label>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="applicableScope" id="allProducts" value="all" checked>
                                <label class="form-check-label" for="allProducts">全部产品</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="applicableScope" id="specificProducts" value="specific">
                                <label class="form-check-label" for="specificProducts">指定产品</label>
                            </div>
                        </div>
                        <div class="mb-3" id="productSelectionGroup" style="display: none;">
                            <label class="form-label">选择产品</label>
                            <select class="form-select" id="selectedProducts" multiple>
                                <!-- 动态填充产品选项 -->
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">状态</label>
                            <select class="form-select" id="discountStatus">
                                <option value="active">启用</option>
                                <option value="inactive">禁用</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">描述</label>
                            <textarea class="form-control" id="discountDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveDiscountBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/bilingual-init.js"></script>
    <script src="js/i18n.js"></script>
    <script src="js/language-switcher.js"></script>
    <script src="js/mobile-nav.js"></script>
    <script src="js/ui-enhancements.js"></script>
    <script src="js/components/batch-operations.js"></script>
    <script src="js/pricing.js"></script>
</body>
</html>