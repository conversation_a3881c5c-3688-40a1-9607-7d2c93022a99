/**
 * 数据库初始化脚本
 * 用于创建初始管理员账户和基本数据
 */

const mongoose = require('mongoose');
const User = require('../models/User');
const Content = require('../models/Content');
const connectDB = require('../config/db');
require('dotenv').config();

// 初始化管理员用户
const initAdminUser = async () => {
    try {
        // 检查是否已存在管理员用户
        const adminExists = await User.findOne({ username: 'admin' });
        
        if (adminExists) {
            console.log('管理员用户已存在，跳过创建');
            return;
        }
        
        // 创建管理员用户
        const adminUser = new User({
            username: 'admin',
            password: 'admin123', // 在实际应用中应该使用加密密码
            name: '系统管理员',
            email: '<EMAIL>',
            role: 'admin',
            twoFactorEnabled: true,
            twoFactorSecret: '101010' // 在实际应用中应该使用随机生成的密钥
        });
        
        await adminUser.save();
        console.log('管理员用户创建成功');
    } catch (error) {
        console.error('创建管理员用户失败:', error);
    }
};

// 初始化基本内容
const initBasicContent = async () => {
    try {
        // 检查是否已存在用户协议
        const agreementExists = await Content.findOne({ 
            type: 'agreement', 
            title: '用户服务协议' 
        });
        
        if (!agreementExists) {
            // 创建用户协议
            const agreement = new Content({
                title: '用户服务协议',
                type: 'agreement',
                content: `
                    <h1>WriterPro用户服务协议</h1>
                    <p>欢迎使用WriterPro服务！</p>
                    <p>本协议是您与WriterPro平台之间关于使用WriterPro服务的协议。</p>
                    <h2>1. 服务内容</h2>
                    <p>WriterPro提供在线写作、内容优化、AI辅助创作等服务。</p>
                    <h2>2. 用户权利和义务</h2>
                    <p>用户在使用本服务时应遵守中华人民共和国相关法律法规。</p>
                    <h2>3. 知识产权</h2>
                    <p>用户通过WriterPro创作的内容知识产权归用户所有。</p>
                    <h2>4. 隐私保护</h2>
                    <p>我们重视用户隐私保护，详情请参阅《隐私政策》。</p>
                    <h2>5. 免责声明</h2>
                    <p>WriterPro不对用户创作内容的准确性、完整性负责。</p>
                    <h2>6. 协议修改</h2>
                    <p>WriterPro有权在必要时修改本协议条款，修改后的协议会在平台上公布。</p>
                    <h2>7. 适用法律</h2>
                    <p>本协议适用中华人民共和国法律。</p>
                `,
                version: 'v1.0',
                status: 'published',
                publishedAt: new Date()
            });
            
            await agreement.save();
            console.log('用户协议创建成功');
        }
        
        // 检查是否已存在隐私政策
        const privacyExists = await Content.findOne({ 
            type: 'agreement', 
            title: '隐私政策' 
        });
        
        if (!privacyExists) {
            // 创建隐私政策
            const privacy = new Content({
                title: '隐私政策',
                type: 'agreement',
                content: `
                    <h1>WriterPro隐私政策</h1>
                    <p>本隐私政策描述了WriterPro如何收集、使用和保护您的个人信息。</p>
                    <h2>1. 信息收集</h2>
                    <p>我们会收集您的注册信息、使用记录等数据。</p>
                    <h2>2. 信息使用</h2>
                    <p>我们使用收集的信息提供、维护和改进我们的服务。</p>
                    <h2>3. 信息共享</h2>
                    <p>除非法律要求或您同意，我们不会与第三方共享您的个人信息。</p>
                    <h2>4. 信息安全</h2>
                    <p>我们采取多种安全措施保护您的个人信息安全。</p>
                    <h2>5. Cookie使用</h2>
                    <p>我们使用Cookie技术来改善用户体验。</p>
                    <h2>6. 隐私政策更新</h2>
                    <p>我们可能会不时更新本隐私政策，更新后会在平台上公布。</p>
                `,
                version: 'v1.0',
                status: 'published',
                publishedAt: new Date()
            });
            
            await privacy.save();
            console.log('隐私政策创建成功');
        }
        
    } catch (error) {
        console.error('创建基本内容失败:', error);
    }
};

// 主函数
const init = async () => {
    try {
        // 连接数据库
        await connectDB();
        
        // 初始化数据
        await initAdminUser();
        await initBasicContent();
        
        console.log('数据库初始化完成');
        process.exit(0);
    } catch (error) {
        console.error('数据库初始化失败:', error);
        process.exit(1);
    }
};

// 执行初始化
init(); 