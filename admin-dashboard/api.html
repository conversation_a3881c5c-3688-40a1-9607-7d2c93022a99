<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API配置 - WriterPro 管理后台</title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="api.css">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <span class="logo-icon"><i class="bi bi-pen"></i></span>
                    <h1>WriterPro</h1>
                </div>
            </div>
            
            <nav class="sidebar-menu">
                <ul>
                    <li>
                        <a href="dashboard.html">
                            <i class="bi bi-grid"></i>
                            <span>仪表盘</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.html">
                            <i class="bi bi-people"></i>
                            <span>用户管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="agents.html">
                            <i class="bi bi-briefcase"></i>
                            <span>代理管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="finance.html">
                            <i class="bi bi-cash-stack"></i>
                            <span>财务统计</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.html">
                            <i class="bi bi-gear"></i>
                            <span>系统设置</span>
                        </a>
                    </li>
                    <li>
                        <a href="content.html">
                            <i class="bi bi-file-text"></i>
                            <span>内容管理</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="api.html">
                            <i class="bi bi-code-square"></i>
                            <span>API配置</span>
                        </a>
                    </li>
                    <li>
                        <a href="marketing.html">
                            <i class="bi bi-megaphone"></i>
                            <span>营销工具</span>
                        </a>
                    </li>
                    <li>
                        <a href="security.html">
                            <i class="bi bi-shield-check"></i>
                            <span>安全中心</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="header">
                <div class="header-left">
                    <button class="menu-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <h2>API配置</h2>
                </div>
                
                <div class="header-right">
                    <div class="search-bar">
                        <i class="bi bi-search"></i>
                        <input type="text" placeholder="搜索...">
                    </div>
                    
                    <div class="header-actions">
                        <button class="action-btn">
                            <i class="bi bi-bell"></i>
                            <span class="badge">2</span>
                        </button>
                        
                        <div class="user-dropdown">
                            <button class="user-btn">
                                <div class="avatar">A</div>
                                <span>管理员</span>
                                <i class="bi bi-chevron-down"></i>
                            </button>
                            
                            <div class="dropdown-menu">
                                <a href="profile.html" class="dropdown-item">
                                    <i class="bi bi-person"></i>
                                    <span>个人资料</span>
                                </a>
                                <a href="#" class="dropdown-item">
                                    <i class="bi bi-gear"></i>
                                    <span>设置</span>
                                </a>
                                <div class="dropdown-divider"></div>
                                <a href="login.html" class="dropdown-item">
                                    <i class="bi bi-box-arrow-right"></i>
                                    <span>退出登录</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- 页面内容 -->
            <div class="content">
                <div class="content-header">
                    <div class="content-title">
                        <h3>API配置</h3>
                        <p>管理系统API密钥和第三方接口配置</p>
                    </div>
                    <div class="content-actions">
                        <button class="btn btn-primary" id="addApiKeyBtn">
                            <i class="bi bi-plus"></i>
                            <span>添加API密钥</span>
                        </button>
                    </div>
                </div>

                <!-- 选项卡导航 -->
                <div class="tabs">
                    <div class="tab active" data-tab="google-api">谷歌API配置</div>
                    <div class="tab" data-tab="payment-api">支付接口配置</div>
                    <div class="tab" data-tab="api-usage">API使用统计</div>
                    <div class="tab" data-tab="client-api">客户端API密钥</div>
                    <div class="tab" data-tab="webhooks">Webhook配置</div>
                </div>

                <!-- 选项卡内容 -->
                <div class="tab-content">
                    <!-- 谷歌API配置 -->
                    <div class="tab-pane active" id="google-api">
                        <div class="card">
                            <div class="card-body">
                                <h4 class="card-title">谷歌API密钥管理</h4>
                                <p class="card-description">管理用于谷歌服务的API密钥和凭证</p>
                                
                                <div class="api-key-list">
                                    <div class="api-key-item">
                                        <div class="api-key-info">
                                            <div class="api-key-header">
                                                <h5>主要API密钥</h5>
                                                <span class="status active">已启用</span>
                                            </div>
                                            <div class="api-key-details">
                                                <div class="api-key-field">
                                                    <span class="field-label">API密钥:</span>
                                                    <div class="masked-field">
                                                        <span class="masked-value">●●●●●●●●●●●●●●●●●●●●●●●</span>
                                                        <button class="toggle-visibility" data-visible="false">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                        <button class="copy-btn" title="复制">
                                                            <i class="bi bi-clipboard"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="api-key-field">
                                                    <span class="field-label">项目ID:</span>
                                                    <span>writer-pro-325412</span>
                                                </div>
                                                <div class="api-key-field">
                                                    <span class="field-label">创建日期:</span>
                                                    <span>2025-06-15</span>
                                                </div>
                                                <div class="api-key-field">
                                                    <span class="field-label">上次使用:</span>
                                                    <span>2025-07-09 15:32</span>
                                                </div>
                                                <div class="api-key-field">
                                                    <span class="field-label">API限制:</span>
                                                    <span>10,000次/日</span>
                                                </div>
                                                <div class="api-key-field">
                                                    <span class="field-label">已启用服务:</span>
                                                    <div class="api-services">
                                                        <span class="service-tag">Cloud Translation API</span>
                                                        <span class="service-tag">Document AI</span>
                                                        <span class="service-tag">Cloud Vision API</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="api-key-actions">
                                            <button class="btn btn-sm btn-outline">
                                                <i class="bi bi-pencil"></i> 编辑
                                            </button>
                                            <button class="btn btn-sm btn-outline">
                                                <i class="bi bi-arrow-clockwise"></i> 轮换密钥
                                            </button>
                                            <button class="btn btn-sm btn-danger-outline">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        </div>
                                    </div>

                                    <div class="api-key-item">
                                        <div class="api-key-info">
                                            <div class="api-key-header">
                                                <h5>备用API密钥</h5>
                                                <span class="status inactive">已禁用</span>
                                            </div>
                                            <div class="api-key-details">
                                                <div class="api-key-field">
                                                    <span class="field-label">API密钥:</span>
                                                    <div class="masked-field">
                                                        <span class="masked-value">●●●●●●●●●●●●●●●●●●●●●●●</span>
                                                        <button class="toggle-visibility" data-visible="false">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                        <button class="copy-btn" title="复制">
                                                            <i class="bi bi-clipboard"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="api-key-field">
                                                    <span class="field-label">项目ID:</span>
                                                    <span>writer-pro-backup-325413</span>
                                                </div>
                                                <div class="api-key-field">
                                                    <span class="field-label">创建日期:</span>
                                                    <span>2025-06-20</span>
                                                </div>
                                                <div class="api-key-field">
                                                    <span class="field-label">上次使用:</span>
                                                    <span>从未使用</span>
                                                </div>
                                                <div class="api-key-field">
                                                    <span class="field-label">API限制:</span>
                                                    <span>10,000次/日</span>
                                                </div>
                                                <div class="api-key-field">
                                                    <span class="field-label">已启用服务:</span>
                                                    <div class="api-services">
                                                        <span class="service-tag">Cloud Translation API</span>
                                                        <span class="service-tag">Document AI</span>
                                                        <span class="service-tag">Cloud Vision API</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="api-key-actions">
                                            <button class="btn btn-sm btn-outline">
                                                <i class="bi bi-pencil"></i> 编辑
                                            </button>
                                            <button class="btn btn-sm btn-success-outline">
                                                <i class="bi bi-check-circle"></i> 启用
                                            </button>
                                            <button class="btn btn-sm btn-danger-outline">
                                                <i class="bi bi-trash"></i> 删除
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-20">
                            <div class="card-body">
                                <h4 class="card-title">API用量监控</h4>
                                <div class="api-usage-chart">
                                    <canvas id="googleApiUsageChart" height="250"></canvas>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-20">
                            <div class="card-body">
                                <h4 class="card-title">费用统计</h4>
                                <div class="api-cost-summary">
                                    <div class="cost-item">
                                        <div class="cost-label">本月费用</div>
                                        <div class="cost-value">$157.83</div>
                                        <div class="cost-trend increase">
                                            <i class="bi bi-arrow-up"></i>
                                            <span>12.5%</span>
                                        </div>
                                    </div>
                                    <div class="cost-item">
                                        <div class="cost-label">预估下月费用</div>
                                        <div class="cost-value">$175.50</div>
                                    </div>
                                    <div class="cost-item">
                                        <div class="cost-label">年度预算</div>
                                        <div class="cost-value">$2,400.00</div>
                                        <div class="cost-progress">
                                            <div class="progress">
                                                <div class="progress-bar" style="width: 45%"></div>
                                            </div>
                                            <span class="progress-text">已使用 45%</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="api-cost-chart">
                                    <canvas id="googleApiCostChart" height="250"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 支付接口配置 -->
                    <div class="tab-pane" id="payment-api">
                        <div class="card">
                            <div class="card-body">
                                <h4 class="card-title">支付渠道管理</h4>
                                <p class="card-description">管理系统支持的支付渠道和配置</p>
                                
                                <div class="payment-channels">
                                    <!-- 支付宝配置 -->
                                    <div class="payment-channel">
                                        <div class="channel-header">
                                            <div class="channel-icon alipay">
                                                <i class="bi bi-credit-card"></i>
                                            </div>
                                            <div class="channel-title">
                                                <h5>支付宝</h5>
                                                <span class="status active">已启用</span>
                                            </div>
                                            <div class="channel-toggle">
                                                <label class="switch">
                                                    <input type="checkbox" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="channel-body">
                                            <div class="channel-field">
                                                <span class="field-label">应用ID (APPID):</span>
                                                <span>202507095423512</span>
                                            </div>
                                            <div class="channel-field">
                                                <span class="field-label">应用私钥:</span>
                                                <div class="masked-field">
                                                    <span class="masked-value">●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●</span>
                                                    <button class="toggle-visibility" data-visible="false">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="channel-field">
                                                <span class="field-label">支付宝公钥:</span>
                                                <div class="masked-field">
                                                    <span class="masked-value">●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●</span>
                                                    <button class="toggle-visibility" data-visible="false">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="channel-field">
                                                <span class="field-label">签名方式:</span>
                                                <span>RSA2</span>
                                            </div>
                                            <div class="channel-field">
                                                <span class="field-label">网关地址:</span>
                                                <span>https://openapi.alipay.com/gateway.do</span>
                                            </div>
                                            <div class="channel-field">
                                                <span class="field-label">手续费率:</span>
                                                <span>0.6%</span>
                                            </div>
                                            <div class="channel-actions">
                                                <button class="btn btn-sm btn-outline">
                                                    <i class="bi bi-pencil"></i> 编辑
                                                </button>
                                                <button class="btn btn-sm btn-outline">
                                                    <i class="bi bi-arrow-repeat"></i> 测试连接
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 微信支付配置 -->
                                    <div class="payment-channel">
                                        <div class="channel-header">
                                            <div class="channel-icon wechat">
                                                <i class="bi bi-chat-dots"></i>
                                            </div>
                                            <div class="channel-title">
                                                <h5>微信支付</h5>
                                                <span class="status active">已启用</span>
                                            </div>
                                            <div class="channel-toggle">
                                                <label class="switch">
                                                    <input type="checkbox" checked>
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="channel-body">
                                            <div class="channel-field">
                                                <span class="field-label">商户ID:</span>
                                                <span>1425367890</span>
                                            </div>
                                            <div class="channel-field">
                                                <span class="field-label">商户密钥:</span>
                                                <div class="masked-field">
                                                    <span class="masked-value">●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●</span>
                                                    <button class="toggle-visibility" data-visible="false">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="channel-field">
                                                <span class="field-label">AppID:</span>
                                                <span>wx2507095423512</span>
                                            </div>
                                            <div class="channel-field">
                                                <span class="field-label">AppSecret:</span>
                                                <div class="masked-field">
                                                    <span class="masked-value">●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●</span>
                                                    <button class="toggle-visibility" data-visible="false">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="channel-field">
                                                <span class="field-label">API密钥:</span>
                                                <div class="masked-field">
                                                    <span class="masked-value">●●●●●●●●●●●●●●●●●●●●●●●●●●●●●●</span>
                                                    <button class="toggle-visibility" data-visible="false">
                                                        <i class="bi bi-eye"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div class="channel-field">
                                                <span class="field-label">手续费率:</span>
                                                <span>0.6%</span>
                                            </div>
                                            <div class="channel-actions">
                                                <button class="btn btn-sm btn-outline">
                                                    <i class="bi bi-pencil"></i> 编辑
                                                </button>
                                                <button class="btn btn-sm btn-outline">
                                                    <i class="bi bi-arrow-repeat"></i> 测试连接
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- PayPal配置 -->
                                    <div class="payment-channel">
                                        <div class="channel-header">
                                            <div class="channel-icon paypal">
                                                <i class="bi bi-paypal"></i>
                                            </div>
                                            <div class="channel-title">
                                                <h5>PayPal</h5>
                                                <span class="status inactive">未启用</span>
                                            </div>
                                            <div class="channel-toggle">
                                                <label class="switch">
                                                    <input type="checkbox">
                                                    <span class="slider"></span>
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="channel-body">
                                            <div class="empty-state">
                                                <i class="bi bi-gear"></i>
                                                <p>尚未配置PayPal接口</p>
                                                <button class="btn btn-primary">
                                                    <i class="bi bi-plus"></i> 配置接口
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-20">
                            <div class="card-body">
                                <h4 class="card-title">支付通知设置</h4>
                                <div class="form-group">
                                    <label for="notifyUrl">通知URL</label>
                                    <div class="input-with-copy">
                                        <input type="text" id="notifyUrl" class="form-control" value="https://api.writerpro.com/payment/notify" readonly>
                                        <button class="copy-btn" title="复制">
                                            <i class="bi bi-clipboard"></i>
                                        </button>
                                    </div>
                                    <small class="form-hint">支付完成后，支付服务商将向此URL发送异步通知</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="returnUrl">前端跳转URL</label>
                                    <div class="input-with-copy">
                                        <input type="text" id="returnUrl" class="form-control" value="https://writerpro.com/payment/result" readonly>
                                        <button class="copy-btn" title="复制">
                                            <i class="bi bi-clipboard"></i>
                                        </button>
                                    </div>
                                    <small class="form-hint">用户支付完成后，将跳转到此URL</small>
                                </div>
                                
                                <div class="form-group">
                                    <label>错误通知接收邮箱</label>
                                    <div class="tag-input">
                                        <div class="tag-item">
                                            <EMAIL>
                                            <button class="tag-remove"><i class="bi bi-x"></i></button>
                                        </div>
                                        <div class="tag-item">
                                            <EMAIL>
                                            <button class="tag-remove"><i class="bi bi-x"></i></button>
                                        </div>
                                        <input type="text" placeholder="输入邮箱地址并按回车">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- API使用统计 -->
                    <div class="tab-pane" id="api-usage">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="card-header-actions">
                                            <h4 class="card-title">API调用趋势</h4>
                                            <div class="date-filter">
                                                <select class="form-control form-control-sm">
                                                    <option value="7">近7天</option>
                                                    <option value="30" selected>近30天</option>
                                                    <option value="90">近90天</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="api-trend-chart">
                                            <canvas id="apiTrendChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body">
                                        <h4 class="card-title">API调用分布</h4>
                                        <div class="api-distribution-chart">
                                            <canvas id="apiDistributionChart" height="260"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mt-20">
                            <div class="card-body">
                                <div class="card-header-actions">
                                    <h4 class="card-title">API调用明细</h4>
                                    <div class="card-actions">
                                        <button class="btn btn-sm btn-outline">
                                            <i class="bi bi-download"></i> 导出数据
                                        </button>
                                        <button class="btn btn-sm btn-outline">
                                            <i class="bi bi-arrow-clockwise"></i> 刷新
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="table-responsive">
                                    <table class="table api-table">
                                        <thead>
                                            <tr>
                                                <th>API端点</th>
                                                <th>总调用次数</th>
                                                <th>成功率</th>
                                                <th>平均响应时间</th>
                                                <th>错误率</th>
                                                <th>趋势</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>/api/documents/analyze</td>
                                                <td>12,587</td>
                                                <td>
                                                    <div class="progress-bar-sm">
                                                        <div class="progress-fill success" style="width: 98.7%"></div>
                                                    </div>
                                                    <span>98.7%</span>
                                                </td>
                                                <td>245ms</td>
                                                <td>1.3%</td>
                                                <td>
                                                    <div class="trend-chart">
                                                        <svg width="100" height="30">
                                                            <polyline points="0,25 15,20 30,22 45,15 60,18 75,10 90,5 100,8" 
                                                                    fill="none" stroke="#52c41a" stroke-width="2"></polyline>
                                                        </svg>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>/api/documents/translate</td>
                                                <td>8,423</td>
                                                <td>
                                                    <div class="progress-bar-sm">
                                                        <div class="progress-fill success" style="width: 99.2%"></div>
                                                    </div>
                                                    <span>99.2%</span>
                                                </td>
                                                <td>320ms</td>
                                                <td>0.8%</td>
                                                <td>
                                                    <div class="trend-chart">
                                                        <svg width="100" height="30">
                                                            <polyline points="0,15 15,12 30,18 45,10 60,8 75,12 90,5 100,3" 
                                                                    fill="none" stroke="#52c41a" stroke-width="2"></polyline>
                                                        </svg>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>/api/users/auth</td>
                                                <td>25,142</td>
                                                <td>
                                                    <div class="progress-bar-sm">
                                                        <div class="progress-fill success" style="width: 99.8%"></div>
                                                    </div>
                                                    <span>99.8%</span>
                                                </td>
                                                <td>87ms</td>
                                                <td>0.2%</td>
                                                <td>
                                                    <div class="trend-chart">
                                                        <svg width="100" height="30">
                                                            <polyline points="0,10 15,12 30,10 45,8 60,12 75,10 90,9 100,8" 
                                                                    fill="none" stroke="#52c41a" stroke-width="2"></polyline>
                                                        </svg>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>/api/payment/process</td>
                                                <td>3,254</td>
                                                <td>
                                                    <div class="progress-bar-sm">
                                                        <div class="progress-fill warning" style="width: 95.4%"></div>
                                                    </div>
                                                    <span>95.4%</span>
                                                </td>
                                                <td>178ms</td>
                                                <td>4.6%</td>
                                                <td>
                                                    <div class="trend-chart">
                                                        <svg width="100" height="30">
                                                            <polyline points="0,15 15,18 30,20 45,18 60,15 75,22 90,25 100,18" 
                                                                    fill="none" stroke="#faad14" stroke-width="2"></polyline>
                                                        </svg>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>/api/documents/ocr</td>
                                                <td>5,873</td>
                                                <td>
                                                    <div class="progress-bar-sm">
                                                        <div class="progress-fill danger" style="width: 92.1%"></div>
                                                    </div>
                                                    <span>92.1%</span>
                                                </td>
                                                <td>530ms</td>
                                                <td>7.9%</td>
                                                <td>
                                                    <div class="trend-chart">
                                                        <svg width="100" height="30">
                                                            <polyline points="0,15 15,18 30,20 45,25 60,20 75,22 90,15 100,18" 
                                                                    fill="none" stroke="#ff4d4f" stroke-width="2"></polyline>
                                                        </svg>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                
                                <div class="pagination">
                                    <button class="btn-page disabled">
                                        <i class="bi bi-chevron-left"></i>
                                    </button>
                                    <button class="btn-page active">1</button>
                                    <button class="btn-page">2</button>
                                    <button class="btn-page">3</button>
                                    <span class="page-ellipsis">...</span>
                                    <button class="btn-page">10</button>
                                    <button class="btn-page">
                                        <i class="bi bi-chevron-right"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mt-20">
                            <div class="card-body">
                                <h4 class="card-title">错误率最高的API端点</h4>
                                <div class="api-error-list">
                                    <div class="api-error-item">
                                        <div class="api-error-info">
                                            <h5>/api/documents/ocr</h5>
                                            <div class="api-error-stats">
                                                <div class="stat-item">
                                                    <span class="stat-label">错误率:</span>
                                                    <span class="stat-value danger">7.9%</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">错误次数:</span>
                                                    <span class="stat-value">464</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">主要错误:</span>
                                                    <span class="stat-value">超时 (68%)</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="api-error-actions">
                                            <button class="btn btn-sm btn-outline">查看详情</button>
                                        </div>
                                    </div>
                                    
                                    <div class="api-error-item">
                                        <div class="api-error-info">
                                            <h5>/api/payment/process</h5>
                                            <div class="api-error-stats">
                                                <div class="stat-item">
                                                    <span class="stat-label">错误率:</span>
                                                    <span class="stat-value warning">4.6%</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">错误次数:</span>
                                                    <span class="stat-value">150</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">主要错误:</span>
                                                    <span class="stat-value">参数错误 (53%)</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="api-error-actions">
                                            <button class="btn btn-sm btn-outline">查看详情</button>
                                        </div>
                                    </div>
                                    
                                    <div class="api-error-item">
                                        <div class="api-error-info">
                                            <h5>/api/documents/analyze</h5>
                                            <div class="api-error-stats">
                                                <div class="stat-item">
                                                    <span class="stat-label">错误率:</span>
                                                    <span class="stat-value">1.3%</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">错误次数:</span>
                                                    <span class="stat-value">164</span>
                                                </div>
                                                <div class="stat-item">
                                                    <span class="stat-label">主要错误:</span>
                                                    <span class="stat-value">格式不支持 (75%)</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="api-error-actions">
                                            <button class="btn btn-sm btn-outline">查看详情</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 客户端API密钥 -->
                    <div class="tab-pane" id="client-api">
                        <div class="card">
                            <div class="card-body">
                                <div class="card-header-actions">
                                    <h4 class="card-title">客户端API密钥</h4>
                                    <div class="card-actions">
                                        <button class="btn btn-primary" id="createClientApiBtn">
                                            <i class="bi bi-plus"></i> 创建密钥
                                        </button>
                                    </div>
                                </div>
                                <p class="card-description">管理第三方应用访问API的密钥</p>
                                
                                <div class="table-responsive">
                                    <table class="table api-table">
                                        <thead>
                                            <tr>
                                                <th>名称</th>
                                                <th>API密钥</th>
                                                <th>创建时间</th>
                                                <th>到期时间</th>
                                                <th>权限范围</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>移动应用</td>
                                                <td>
                                                    <div class="masked-field sm">
                                                        <span class="masked-value">●●●●●●●●●●●●●●●●</span>
                                                        <button class="toggle-visibility" data-visible="false">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                        <button class="copy-btn" title="复制">
                                                            <i class="bi bi-clipboard"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                                <td>2025-06-01</td>
                                                <td>2026-06-01</td>
                                                <td>
                                                    <div class="permission-tags">
                                                        <span class="permission-tag">文档读写</span>
                                                        <span class="permission-tag">用户数据</span>
                                                    </div>
                                                </td>
                                                <td><span class="status active">有效</span></td>
                                                <td>
                                                    <div class="table-actions">
                                                        <button class="btn-icon" title="编辑">
                                                            <i class="bi bi-pencil"></i>
                                                        </button>
                                                        <button class="btn-icon" title="重新生成">
                                                            <i class="bi bi-arrow-repeat"></i>
                                                        </button>
                                                        <button class="btn-icon" title="撤销">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>Web应用</td>
                                                <td>
                                                    <div class="masked-field sm">
                                                        <span class="masked-value">●●●●●●●●●●●●●●●●</span>
                                                        <button class="toggle-visibility" data-visible="false">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                        <button class="copy-btn" title="复制">
                                                            <i class="bi bi-clipboard"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                                <td>2025-06-05</td>
                                                <td>2026-06-05</td>
                                                <td>
                                                    <div class="permission-tags">
                                                        <span class="permission-tag">文档读写</span>
                                                        <span class="permission-tag">用户数据</span>
                                                        <span class="permission-tag">支付接口</span>
                                                    </div>
                                                </td>
                                                <td><span class="status active">有效</span></td>
                                                <td>
                                                    <div class="table-actions">
                                                        <button class="btn-icon" title="编辑">
                                                            <i class="bi bi-pencil"></i>
                                                        </button>
                                                        <button class="btn-icon" title="重新生成">
                                                            <i class="bi bi-arrow-repeat"></i>
                                                        </button>
                                                        <button class="btn-icon" title="撤销">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>合作伙伴API</td>
                                                <td>
                                                    <div class="masked-field sm">
                                                        <span class="masked-value">●●●●●●●●●●●●●●●●</span>
                                                        <button class="toggle-visibility" data-visible="false">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                        <button class="copy-btn" title="复制">
                                                            <i class="bi bi-clipboard"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                                <td>2025-06-10</td>
                                                <td>2026-06-10</td>
                                                <td>
                                                    <div class="permission-tags">
                                                        <span class="permission-tag">文档读</span>
                                                        <span class="permission-tag">翻译API</span>
                                                    </div>
                                                </td>
                                                <td><span class="status active">有效</span></td>
                                                <td>
                                                    <div class="table-actions">
                                                        <button class="btn-icon" title="编辑">
                                                            <i class="bi bi-pencil"></i>
                                                        </button>
                                                        <button class="btn-icon" title="重新生成">
                                                            <i class="bi bi-arrow-repeat"></i>
                                                        </button>
                                                        <button class="btn-icon" title="撤销">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>测试应用</td>
                                                <td>
                                                    <div class="masked-field sm">
                                                        <span class="masked-value">●●●●●●●●●●●●●●●●</span>
                                                        <button class="toggle-visibility" data-visible="false">
                                                            <i class="bi bi-eye"></i>
                                                        </button>
                                                        <button class="copy-btn" title="复制">
                                                            <i class="bi bi-clipboard"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                                <td>2025-06-20</td>
                                                <td>2025-09-20</td>
                                                <td>
                                                    <div class="permission-tags">
                                                        <span class="permission-tag">文档读</span>
                                                        <span class="permission-tag">测试环境</span>
                                                    </div>
                                                </td>
                                                <td><span class="status inactive">测试中</span></td>
                                                <td>
                                                    <div class="table-actions">
                                                        <button class="btn-icon" title="编辑">
                                                            <i class="bi bi-pencil"></i>
                                                        </button>
                                                        <button class="btn-icon" title="重新生成">
                                                            <i class="bi bi-arrow-repeat"></i>
                                                        </button>
                                                        <button class="btn-icon" title="撤销">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-20">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h4 class="card-title">API使用限制</h4>
                                        <div class="form-group">
                                            <label for="rateLimit">请求速率限制</label>
                                            <div class="input-suffix">
                                                <input type="number" id="rateLimit" class="form-control" value="100">
                                                <span class="suffix">请求/分钟</span>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="dailyLimit">每日请求限制</label>
                                            <div class="input-suffix">
                                                <input type="number" id="dailyLimit" class="form-control" value="10000">
                                                <span class="suffix">请求/天</span>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="maxFileSize">最大文件大小</label>
                                            <div class="input-suffix">
                                                <input type="number" id="maxFileSize" class="form-control" value="10">
                                                <span class="suffix">MB</span>
                                            </div>
                                        </div>
                                        <button class="btn btn-primary">保存设置</button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-body">
                                        <h4 class="card-title">API文档</h4>
                                        <p>为开发者提供API文档和开发指南</p>
                                        <div class="doc-links">
                                            <a href="#" class="doc-link">
                                                <i class="bi bi-file-earmark-text"></i>
                                                <div class="doc-link-info">
                                                    <h5>API参考文档</h5>
                                                    <p>全面的API端点、参数和响应格式说明</p>
                                                </div>
                                            </a>
                                            <a href="#" class="doc-link">
                                                <i class="bi bi-book"></i>
                                                <div class="doc-link-info">
                                                    <h5>开发指南</h5>
                                                    <p>从零开始接入API的详细教程</p>
                                                </div>
                                            </a>
                                            <a href="#" class="doc-link">
                                                <i class="bi bi-code-square"></i>
                                                <div class="doc-link-info">
                                                    <h5>代码示例</h5>
                                                    <p>多种编程语言的实现示例</p>
                                                </div>
                                            </a>
                                            <a href="#" class="doc-link">
                                                <i class="bi bi-question-circle"></i>
                                                <div class="doc-link-info">
                                                    <h5>常见问题</h5>
                                                    <p>API使用过程中的常见问题解答</p>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Webhook配置 -->
                    <div class="tab-pane" id="webhooks">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="card-header-actions">
                                            <h4 class="card-title">Webhook端点管理</h4>
                                            <button class="btn btn-primary" id="addWebhookBtn">
                                                <i class="bi bi-plus"></i> 添加Webhook
                                            </button>
                                        </div>

                                        <div class="webhook-list">
                                            <div class="webhook-item">
                                                <div class="webhook-header">
                                                    <div class="webhook-info">
                                                        <h5>支付成功通知</h5>
                                                        <span class="webhook-url">https://api.example.com/webhooks/payment</span>
                                                    </div>
                                                    <div class="webhook-status">
                                                        <span class="status active">已启用</span>
                                                        <label class="switch">
                                                            <input type="checkbox" checked>
                                                            <span class="slider"></span>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="webhook-details">
                                                    <div class="webhook-events">
                                                        <span class="event-tag">payment.success</span>
                                                        <span class="event-tag">payment.failed</span>
                                                        <span class="event-tag">payment.refund</span>
                                                    </div>
                                                    <div class="webhook-stats">
                                                        <div class="stat-item">
                                                            <span class="stat-label">成功率:</span>
                                                            <span class="stat-value success">98.5%</span>
                                                        </div>
                                                        <div class="stat-item">
                                                            <span class="stat-label">最后调用:</span>
                                                            <span class="stat-value">2分钟前</span>
                                                        </div>
                                                        <div class="stat-item">
                                                            <span class="stat-label">平均响应:</span>
                                                            <span class="stat-value">245ms</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="webhook-actions">
                                                    <button class="btn btn-sm btn-outline">
                                                        <i class="bi bi-pencil"></i> 编辑
                                                    </button>
                                                    <button class="btn btn-sm btn-outline">
                                                        <i class="bi bi-arrow-repeat"></i> 测试
                                                    </button>
                                                    <button class="btn btn-sm btn-outline">
                                                        <i class="bi bi-clock-history"></i> 日志
                                                    </button>
                                                    <button class="btn btn-sm btn-danger-outline">
                                                        <i class="bi bi-trash"></i> 删除
                                                    </button>
                                                </div>
                                            </div>

                                            <div class="webhook-item">
                                                <div class="webhook-header">
                                                    <div class="webhook-info">
                                                        <h5>用户注册通知</h5>
                                                        <span class="webhook-url">https://api.example.com/webhooks/user</span>
                                                    </div>
                                                    <div class="webhook-status">
                                                        <span class="status active">已启用</span>
                                                        <label class="switch">
                                                            <input type="checkbox" checked>
                                                            <span class="slider"></span>
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="webhook-details">
                                                    <div class="webhook-events">
                                                        <span class="event-tag">user.created</span>
                                                        <span class="event-tag">user.updated</span>
                                                        <span class="event-tag">user.deleted</span>
                                                    </div>
                                                    <div class="webhook-stats">
                                                        <div class="stat-item">
                                                            <span class="stat-label">成功率:</span>
                                                            <span class="stat-value success">99.2%</span>
                                                        </div>
                                                        <div class="stat-item">
                                                            <span class="stat-label">最后调用:</span>
                                                            <span class="stat-value">5分钟前</span>
                                                        </div>
                                                        <div class="stat-item">
                                                            <span class="stat-label">平均响应:</span>
                                                            <span class="stat-value">180ms</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="webhook-actions">
                                                    <button class="btn btn-sm btn-outline">
                                                        <i class="bi bi-pencil"></i> 编辑
                                                    </button>
                                                    <button class="btn btn-sm btn-outline">
                                                        <i class="bi bi-arrow-repeat"></i> 测试
                                                    </button>
                                                    <button class="btn btn-sm btn-outline">
                                                        <i class="bi bi-clock-history"></i> 日志
                                                    </button>
                                                    <button class="btn btn-sm btn-danger-outline">
                                                        <i class="bi bi-trash"></i> 删除
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body">
                                        <h4 class="card-title">Webhook统计</h4>
                                        <div class="webhook-stats-summary">
                                            <div class="stat-card">
                                                <div class="stat-number">2</div>
                                                <div class="stat-label">活跃端点</div>
                                            </div>
                                            <div class="stat-card">
                                                <div class="stat-number">1,247</div>
                                                <div class="stat-label">今日调用</div>
                                            </div>
                                            <div class="stat-card">
                                                <div class="stat-number">98.8%</div>
                                                <div class="stat-label">平均成功率</div>
                                            </div>
                                            <div class="stat-card">
                                                <div class="stat-number">210ms</div>
                                                <div class="stat-label">平均响应时间</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-20">
                                    <div class="card-body">
                                        <h4 class="card-title">可用事件类型</h4>
                                        <div class="event-types">
                                            <div class="event-category">
                                                <h6>支付事件</h6>
                                                <div class="event-list">
                                                    <span class="event-item">payment.success</span>
                                                    <span class="event-item">payment.failed</span>
                                                    <span class="event-item">payment.refund</span>
                                                    <span class="event-item">payment.chargeback</span>
                                                </div>
                                            </div>
                                            <div class="event-category">
                                                <h6>用户事件</h6>
                                                <div class="event-list">
                                                    <span class="event-item">user.created</span>
                                                    <span class="event-item">user.updated</span>
                                                    <span class="event-item">user.deleted</span>
                                                    <span class="event-item">user.login</span>
                                                </div>
                                            </div>
                                            <div class="event-category">
                                                <h6>订阅事件</h6>
                                                <div class="event-list">
                                                    <span class="event-item">subscription.created</span>
                                                    <span class="event-item">subscription.updated</span>
                                                    <span class="event-item">subscription.cancelled</span>
                                                    <span class="event-item">subscription.expired</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- API密钥添加/编辑模态框 -->
    <div class="modal" id="apiKeyModal">
        <div class="modal-backdrop"></div>
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">添加API密钥</h4>
                    <button class="close-btn" id="closeModal">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="apiName">密钥名称</label>
                        <input type="text" id="apiName" class="form-control" placeholder="输入密钥名称">
                    </div>
                    <div class="form-group">
                        <label for="apiKey">API密钥</label>
                        <input type="text" id="apiKey" class="form-control" placeholder="输入API密钥">
                    </div>
                    <div class="form-group">
                        <label for="projectId">项目ID</label>
                        <input type="text" id="projectId" class="form-control" placeholder="输入项目ID">
                    </div>
                    <div class="form-group">
                        <label for="apiLimit">API限制</label>
                        <input type="text" id="apiLimit" class="form-control" placeholder="例如：10000次/日">
                    </div>
                    <div class="form-group">
                        <label>启用的服务</label>
                        <div class="checkbox-group">
                            <label class="checkbox">
                                <input type="checkbox" value="translation">
                                <span>Cloud Translation API</span>
                            </label>
                            <label class="checkbox">
                                <input type="checkbox" value="document">
                                <span>Document AI</span>
                            </label>
                            <label class="checkbox">
                                <input type="checkbox" value="vision">
                                <span>Cloud Vision API</span>
                            </label>
                            <label class="checkbox">
                                <input type="checkbox" value="speech">
                                <span>Speech-to-Text API</span>
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="apiStatus">状态</label>
                        <select id="apiStatus" class="form-control">
                            <option value="active">启用</option>
                            <option value="inactive">禁用</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" id="cancelBtn">取消</button>
                    <button class="btn btn-primary" id="saveApiKeyBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/api.js"></script>
    <script src="api.js"></script>
</body>
</html> 