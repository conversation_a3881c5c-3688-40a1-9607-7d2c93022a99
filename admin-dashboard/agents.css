/* 代理统计卡片 */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
    margin-bottom: 24px;
}

.stat-card {
    background-color: var(--bg-white);
    border-radius: var(--radius);
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: var(--shadow);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    background-color: rgba(24, 144, 255, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    margin-right: 16px;
}

.stat-info {
    flex: 1;
}

.stat-value {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 4px;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 14px;
}

.stat-change {
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 500;
}

.stat-change.increase {
    color: var(--secondary-color);
}

.stat-change.decrease {
    color: var(--danger-color);
}

/* 图表容器 */
.charts-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
}

.chart-card {
    background-color: var(--bg-white);
    border-radius: var(--radius);
    padding: 20px;
    box-shadow: var(--shadow);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.chart-header h3 {
    font-size: 18px;
    font-weight: 500;
    margin: 0;
}

.chart-actions {
    display: flex;
    gap: 8px;
}

.chart {
    height: 300px;
}

/* 工具栏样式继承自users.css */

/* 表格样式继承自users.css */

/* 代理详情模态框 */
.agent-detail-header {
    display: flex;
    gap: 24px;
    margin-bottom: 24px;
    padding-bottom: 24px;
    border-bottom: 1px solid var(--border-color);
}

.agent-detail-avatar {
    position: relative;
}

.agent-status {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid white;
}

.agent-status.active {
    background-color: var(--secondary-color);
}

.agent-status.pending {
    background-color: var(--warning-color);
}

.agent-status.suspended {
    background-color: var(--danger-color);
}

.agent-detail-info {
    flex: 1;
}

.agent-detail-info h2 {
    font-size: 24px;
    margin-bottom: 4px;
}

.agent-detail-info p {
    color: var(--text-secondary);
    margin-bottom: 12px;
}

.agent-detail-meta {
    display: flex;
    gap: 16px;
    color: var(--text-light);
    font-size: 14px;
}

.agent-detail-level {
    padding: 2px 8px;
    border-radius: 12px;
    background-color: rgba(24, 144, 255, 0.1);
    color: var(--primary-color);
}

.agent-detail-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* 输入组 */
.input-group {
    display: flex;
    align-items: center;
}

.input-group input {
    flex: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group-text {
    padding: 8px 12px;
    background-color: #f0f0f0;
    border: 1px solid var(--border-color);
    border-left: none;
    border-top-right-radius: var(--radius);
    border-bottom-right-radius: var(--radius);
}

/* 摘要卡片 */
.commission-summary,
.sub-agents-summary,
.users-summary {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 24px;
    margin-bottom: 24px;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1rem;
}

.card-title {
    color: #495057;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.card-text {
    color: #212529;
    margin-bottom: 0;
}

/* 状态标签样式 */
.status-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-active {
    background-color: #d1e7dd;
    color: #0f5132;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-suspended {
    background-color: #f8d7da;
    color: #721c24;
}

/* 表格样式优化 */
.table {
    font-size: 0.875rem;
}

.table th {
    font-weight: 500;
    background-color: #f8f9fa;
    white-space: nowrap;
}

.table td {
    vertical-align: middle;
}

/* 操作按钮组 */
.agent-actions {
    white-space: nowrap;
}

.agent-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    margin-right: 0.25rem;
}

.agent-actions .btn:last-child {
    margin-right: 0;
}

/* 搜索框样式 */
.input-group .form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 筛选下拉框样式 */
.form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* 模态框样式优化 */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
}

/* 表单样式优化 */
.form-label {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    font-size: 0.875rem;
}

/* 业绩图表容器 */
#performanceChart {
    height: 300px;
    margin-bottom: 1rem;
}

/* 分页样式 */
.pagination {
    margin-bottom: 0;
}

.page-link {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .btn-toolbar {
        margin-top: 1rem;
    }
    
    .agent-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
    }
    
    .agent-actions .btn {
        margin-right: 0;
    }
    
    .table td {
        white-space: normal;
    }
    
    #performanceChart {
        height: 200px;
    }
}

/* 加载动画 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.75rem;
}

/* 表格行悬停效果 */
.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
    cursor: pointer;
}

/* 数字金额样式 */
.amount {
    font-family: 'Roboto Mono', monospace;
    font-weight: 500;
}

/* 佣金比例进度条 */
.commission-rate {
    width: 100px;
    height: 6px;
    background-color: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.commission-rate-bar {
    height: 100%;
    background-color: #0d6efd;
    transition: width 0.3s ease;
}

/* 代理商层级标识 */
.agent-level {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    color: #6c757d;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    margin-left: 0.5rem;
}

/* 搜索结果高亮 */
.highlight {
    background-color: #fff3cd;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
} 