<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统设置 - 管理后台</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="settings.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav id="sidebar" class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="dashboard.html">
                                <i class="bi bi-house-door"></i> 仪表盘
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="users.html">
                                <i class="bi bi-people"></i> 用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="roles.html">
                                <i class="bi bi-person-badge"></i> 角色管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="agents.html">
                                <i class="bi bi-person-lines-fill"></i> 代理商管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="finance.html">
                                <i class="bi bi-cash-stack"></i> 财务统计
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="settings.html">
                                <i class="bi bi-gear"></i> 系统设置
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主要内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">系统设置</h1>
                </div>

                <!-- 设置选项卡 -->
                <ul class="nav nav-tabs" id="settingsTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="basic-tab" data-bs-toggle="tab" data-bs-target="#basic" type="button" role="tab">
                            <i class="bi bi-gear"></i> 基本设置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="pricing-tab" data-bs-toggle="tab" data-bs-target="#pricing" type="button" role="tab">
                            <i class="bi bi-currency-dollar"></i> 价格设置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system" type="button" role="tab">
                            <i class="bi bi-cpu"></i> 系统参数
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="payment-tab" data-bs-toggle="tab" data-bs-target="#payment" type="button" role="tab">
                            <i class="bi bi-credit-card"></i> 支付设置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="notification-tab" data-bs-toggle="tab" data-bs-target="#notification" type="button" role="tab">
                            <i class="bi bi-bell"></i> 通知设置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab">
                            <i class="bi bi-journal-text"></i> 操作日志
                        </button>
                    </li>
                </ul>

                <!-- 设置内容 -->
                <div class="tab-content mt-4" id="settingsTabContent">
                    <!-- 基本设置 -->
                    <div class="tab-pane fade show active" id="basic" role="tabpanel">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">基本信息</h5>
                                <form id="basicSettingsForm">
                                    <div class="mb-3">
                                        <label class="form-label">系统名称</label>
                                        <input type="text" class="form-control" name="systemName" value="WriterPro">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">系统描述</label>
                                        <textarea class="form-control" name="systemDescription" rows="3"></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">系统Logo</label>
                                        <input type="file" class="form-control" name="systemLogo">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">联系邮箱</label>
                                        <input type="email" class="form-control" name="contactEmail">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">技术支持电话</label>
                                        <input type="tel" class="form-control" name="supportPhone">
                                    </div>
                                    <button type="submit" class="btn btn-primary">保存设置</button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 价格设置 -->
                    <div class="tab-pane fade" id="pricing" role="tabpanel">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">产品价格管理</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>产品名称</th>
                                                        <th>类型</th>
                                                        <th>价格 (¥)</th>
                                                        <th>折扣价 (¥)</th>
                                                        <th>状态</th>
                                                        <th>操作</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="pricingTableBody">
                                                    <!-- 价格数据将通过JavaScript动态加载 -->
                                                </tbody>
                                            </table>
                                        </div>
                                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPricingModal">
                                            <i class="bi bi-plus"></i> 添加价格方案
                                        </button>
                                    </div>
                                </div>

                                <!-- 批量价格调整 -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="mb-0">批量价格调整</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="batchPricingForm">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <label class="form-label">调整类型</label>
                                                    <select class="form-select" id="adjustmentType">
                                                        <option value="percentage">按百分比</option>
                                                        <option value="fixed">固定金额</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-4">
                                                    <label class="form-label">调整值</label>
                                                    <input type="number" class="form-control" id="adjustmentValue" placeholder="输入调整值">
                                                </div>
                                                <div class="col-md-4">
                                                    <label class="form-label">应用范围</label>
                                                    <select class="form-select" id="adjustmentScope">
                                                        <option value="all">全部产品</option>
                                                        <option value="basic">基础版</option>
                                                        <option value="pro">专业版</option>
                                                        <option value="enterprise">企业版</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="mt-3">
                                                <button type="submit" class="btn btn-warning">
                                                    <i class="bi bi-calculator"></i> 批量调整价格
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <!-- 折扣活动配置 -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">折扣活动</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="discountForm">
                                            <div class="mb-3">
                                                <label class="form-label">活动名称</label>
                                                <input type="text" class="form-control" id="discountName" placeholder="如：新年特惠">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">折扣类型</label>
                                                <select class="form-select" id="discountType">
                                                    <option value="percentage">百分比折扣</option>
                                                    <option value="fixed">固定金额减免</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">折扣值</label>
                                                <input type="number" class="form-control" id="discountValue" placeholder="如：20 (表示20%或20元)">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">开始时间</label>
                                                <input type="datetime-local" class="form-control" id="discountStartTime">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">结束时间</label>
                                                <input type="datetime-local" class="form-control" id="discountEndTime">
                                            </div>
                                            <button type="submit" class="btn btn-success w-100">
                                                <i class="bi bi-percent"></i> 创建折扣活动
                                            </button>
                                        </form>
                                    </div>
                                </div>

                                <!-- 会员价格设置 -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="mb-0">会员价格</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="memberPricingForm">
                                            <div class="mb-3">
                                                <label class="form-label">VIP会员折扣 (%)</label>
                                                <input type="number" class="form-control" id="vipDiscount" value="10" min="0" max="50">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">SVIP会员折扣 (%)</label>
                                                <input type="number" class="form-control" id="svipDiscount" value="20" min="0" max="50">
                                            </div>
                                            <button type="submit" class="btn btn-primary w-100">
                                                <i class="bi bi-star"></i> 保存会员价格
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 系统参数 -->
                    <div class="tab-pane fade" id="system" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">基础配置</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="systemConfigForm">
                                            <div class="mb-3">
                                                <label class="form-label">系统名称</label>
                                                <input type="text" class="form-control" id="systemName" value="WriterPro">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">系统版本</label>
                                                <input type="text" class="form-control" id="systemVersion" value="1.0.0" readonly>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">系统描述</label>
                                                <textarea class="form-control" id="systemDescription" rows="3">智能写作助手平台</textarea>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">联系邮箱</label>
                                                <input type="email" class="form-control" id="contactEmail" value="<EMAIL>">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">客服电话</label>
                                                <input type="tel" class="form-control" id="contactPhone" value="************">
                                            </div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="bi bi-check"></i> 保存基础配置
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <!-- 限流设置 -->
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="mb-0">限流设置</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="rateLimitForm">
                                            <div class="mb-3">
                                                <label class="form-label">API请求限制 (次/分钟)</label>
                                                <input type="number" class="form-control" id="apiRateLimit" value="100">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">用户注册限制 (次/小时)</label>
                                                <input type="number" class="form-control" id="registerRateLimit" value="10">
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">登录尝试限制 (次/小时)</label>
                                                <input type="number" class="form-control" id="loginRateLimit" value="5">
                                            </div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="bi bi-shield"></i> 保存限流设置
                                            </button>
                                        </form>
                                    </div>
                                </div>

                                <!-- 缓存策略 -->
                                <div class="card mt-3">
                                    <div class="card-header">
                                        <h5 class="mb-0">缓存策略</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="cacheForm">
                                            <div class="mb-3">
                                                <label class="form-label">缓存过期时间 (分钟)</label>
                                                <input type="number" class="form-control" id="cacheExpiry" value="30">
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="enableRedis" checked>
                                                    <label class="form-check-label" for="enableRedis">
                                                        启用Redis缓存
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="enableMemoryCache" checked>
                                                    <label class="form-check-label" for="enableMemoryCache">
                                                        启用内存缓存
                                                    </label>
                                                </div>
                                            </div>
                                            <button type="submit" class="btn btn-primary">
                                                <i class="bi bi-hdd"></i> 保存缓存设置
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 支付设置 -->
                    <div class="tab-pane fade" id="payment" role="tabpanel">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">支付配置</h5>
                                <form id="paymentSettingsForm">
                                    <div class="mb-3">
                                        <label class="form-label">支付宝配置</label>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text">AppID</span>
                                            <input type="text" class="form-control" name="alipayAppId">
                                        </div>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text">商户私钥</span>
                                            <textarea class="form-control" name="alipayPrivateKey" rows="3"></textarea>
                                        </div>
                                        <div class="input-group">
                                            <span class="input-group-text">支付宝公钥</span>
                                            <textarea class="form-control" name="alipayPublicKey" rows="3"></textarea>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">微信支付配置</label>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text">商户号</span>
                                            <input type="text" class="form-control" name="wechatMchId">
                                        </div>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text">商户密钥</span>
                                            <input type="text" class="form-control" name="wechatMchKey">
                                        </div>
                                        <div class="input-group">
                                            <span class="input-group-text">API密钥</span>
                                            <input type="text" class="form-control" name="wechatApiKey">
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">保存设置</button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 通知设置 -->
                    <div class="tab-pane fade" id="notification" role="tabpanel">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">通知配置</h5>
                                <form id="notificationSettingsForm">
                                    <div class="mb-3">
                                        <label class="form-label">邮件服务器配置</label>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text">SMTP服务器</span>
                                            <input type="text" class="form-control" name="smtpHost">
                                        </div>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text">SMTP端口</span>
                                            <input type="number" class="form-control" name="smtpPort">
                                        </div>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text">用户名</span>
                                            <input type="text" class="form-control" name="smtpUsername">
                                        </div>
                                        <div class="input-group">
                                            <span class="input-group-text">密码</span>
                                            <input type="password" class="form-control" name="smtpPassword">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">通知模板</label>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" name="notifyOnRegistration">
                                            <label class="form-check-label">新用户注册通知</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" name="notifyOnPayment">
                                            <label class="form-check-label">支付成功通知</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" name="notifyOnRefund">
                                            <label class="form-check-label">退款通知</label>
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">保存设置</button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 安全设置 -->
                    <div class="tab-pane fade" id="security" role="tabpanel">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">安全配置</h5>
                                <form id="securitySettingsForm">
                                    <div class="mb-3">
                                        <label class="form-label">密码策略</label>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" name="requireUppercase">
                                            <label class="form-check-label">必须包含大写字母</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" name="requireNumber">
                                            <label class="form-check-label">必须包含数字</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" name="requireSpecialChar">
                                            <label class="form-check-label">必须包含特殊字符</label>
                                        </div>
                                        <div class="input-group">
                                            <span class="input-group-text">最小长度</span>
                                            <input type="number" class="form-control" name="minLength" min="6">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">登录安全</label>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" name="enableTwoFactor">
                                            <label class="form-check-label">启用两步验证</label>
                                        </div>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text">登录失败次数限制</span>
                                            <input type="number" class="form-control" name="maxLoginAttempts">
                                        </div>
                                        <div class="input-group">
                                            <span class="input-group-text">锁定时间(分钟)</span>
                                            <input type="number" class="form-control" name="lockoutDuration">
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">保存设置</button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- API设置 -->
                    <div class="tab-pane fade" id="api" role="tabpanel">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">API配置</h5>
                                <form id="apiSettingsForm">
                                    <div class="mb-3">
                                        <label class="form-label">API访问控制</label>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" name="enableRateLimit">
                                            <label class="form-check-label">启用访问频率限制</label>
                                        </div>
                                        <div class="input-group mb-2">
                                            <span class="input-group-text">每分钟请求限制</span>
                                            <input type="number" class="form-control" name="requestsPerMinute">
                                        </div>
                                        <div class="input-group">
                                            <span class="input-group-text">API密钥有效期(天)</span>
                                            <input type="number" class="form-control" name="apiKeyExpiration">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">CORS设置</label>
                                        <div class="input-group">
                                            <span class="input-group-text">允许的域名</span>
                                            <input type="text" class="form-control" name="allowedOrigins" placeholder="用逗号分隔多个域名">
                                        </div>
                                    </div>
                                    <button type="submit" class="btn btn-primary">保存设置</button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- 操作日志 -->
                    <div class="tab-pane fade" id="logs" role="tabpanel">
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <!-- 日志筛选 -->
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-3">
                                                <input type="text" class="form-control" id="logSearch" placeholder="搜索用户/操作">
                                            </div>
                                            <div class="col-md-2">
                                                <select class="form-select" id="logLevel">
                                                    <option value="">全部级别</option>
                                                    <option value="info">信息</option>
                                                    <option value="warning">警告</option>
                                                    <option value="error">错误</option>
                                                    <option value="critical">严重</option>
                                                </select>
                                            </div>
                                            <div class="col-md-2">
                                                <select class="form-select" id="logModule">
                                                    <option value="">全部模块</option>
                                                    <option value="user">用户管理</option>
                                                    <option value="finance">财务管理</option>
                                                    <option value="agent">代理管理</option>
                                                    <option value="system">系统设置</option>
                                                    <option value="security">安全中心</option>
                                                </select>
                                            </div>
                                            <div class="col-md-3">
                                                <input type="text" class="form-control" id="logDateRange" placeholder="选择时间范围">
                                            </div>
                                            <div class="col-md-2">
                                                <button class="btn btn-primary" id="searchLogs">
                                                    <i class="bi bi-search"></i> 搜索
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 日志列表 -->
                                <div class="card">
                                    <div class="card-header d-flex justify-content-between align-items-center">
                                        <h5 class="mb-0">操作日志</h5>
                                        <div>
                                            <button class="btn btn-sm btn-outline-primary" id="exportLogs">
                                                <i class="bi bi-download"></i> 导出日志
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" id="clearLogs">
                                                <i class="bi bi-trash"></i> 清理日志
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>时间</th>
                                                        <th>用户</th>
                                                        <th>模块</th>
                                                        <th>操作</th>
                                                        <th>级别</th>
                                                        <th>IP地址</th>
                                                        <th>详情</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="logsTableBody">
                                                    <!-- 日志数据将通过JavaScript动态加载 -->
                                                </tbody>
                                            </table>
                                        </div>

                                        <!-- 分页 -->
                                        <nav aria-label="日志分页" class="mt-3">
                                            <ul class="pagination justify-content-center" id="logsPagination">
                                                <!-- 分页将通过JavaScript动态加载 -->
                                            </ul>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 日志统计 -->
                        <div class="row mt-3">
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h4 id="totalLogs">0</h4>
                                        <small class="text-muted">总日志数</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h4 id="todayLogs">0</h4>
                                        <small class="text-muted">今日日志</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h4 id="errorLogs">0</h4>
                                        <small class="text-muted">错误日志</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <h4 id="warningLogs">0</h4>
                                        <small class="text-muted">警告日志</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- 添加价格方案模态框 -->
    <div class="modal fade" id="addPricingModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加价格方案</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addPricingForm">
                        <div class="mb-3">
                            <label class="form-label">产品名称</label>
                            <input type="text" class="form-control" id="productName" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">产品类型</label>
                            <select class="form-select" id="productType" required>
                                <option value="">选择类型</option>
                                <option value="monthly">月度订阅</option>
                                <option value="yearly">年度订阅</option>
                                <option value="one-time">一次性购买</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">原价 (¥)</label>
                            <input type="number" class="form-control" id="originalPrice" step="0.01" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">折扣价 (¥)</label>
                            <input type="number" class="form-control" id="discountPrice" step="0.01">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">产品描述</label>
                            <textarea class="form-control" id="productDescription" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="savePricing">保存</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery/dist/jquery.min.js"></script>
    <script src="js/settings.js"></script>
    <script src="settings.js"></script>
</body>
</html> 