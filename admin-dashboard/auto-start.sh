#!/bin/bash

# WriterPro 管理后台全自动启动脚本
# 自动检测环境、安装依赖、启动服务

echo "🤖 WriterPro 管理后台全自动启动脚本"
echo "========================================"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查并进入正确目录
check_directory() {
    log_info "检查当前目录..."
    
    # 如果当前目录有index.html，说明已经在admin-dashboard目录
    if [ -f "index.html" ]; then
        log_success "已在admin-dashboard目录中"
        return 0
    fi
    
    # 如果当前目录有admin-dashboard子目录，进入它
    if [ -d "admin-dashboard" ]; then
        log_info "进入admin-dashboard目录..."
        cd admin-dashboard
        if [ -f "index.html" ]; then
            log_success "成功进入admin-dashboard目录"
            return 0
        fi
    fi
    
    # 尝试查找admin-dashboard目录
    log_info "搜索admin-dashboard目录..."
    ADMIN_DIR=$(find . -name "admin-dashboard" -type d 2>/dev/null | head -1)
    
    if [ -n "$ADMIN_DIR" ]; then
        log_info "找到目录: $ADMIN_DIR"
        cd "$ADMIN_DIR"
        if [ -f "index.html" ]; then
            log_success "成功进入admin-dashboard目录"
            return 0
        fi
    fi
    
    log_error "未找到admin-dashboard目录或index.html文件"
    log_error "请确保在包含admin-dashboard的目录中运行此脚本"
    exit 1
}

# 检查并安装Node.js
check_nodejs() {
    log_info "检查Node.js..."

    if command -v node &> /dev/null; then
        NODE_VERSION=$(node --version)
        log_success "Node.js已安装: $NODE_VERSION"
        return 0
    else
        log_warning "Node.js未安装，正在尝试自动安装..."

        # 检测操作系统并安装Node.js
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            if command -v brew &> /dev/null; then
                log_info "使用Homebrew安装Node.js..."
                brew install node
            else
                log_info "安装Homebrew..."
                /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
                brew install node
            fi
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            # Linux
            if command -v apt-get &> /dev/null; then
                log_info "使用apt-get安装Node.js..."
                curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
                sudo apt-get install -y nodejs
            elif command -v yum &> /dev/null; then
                log_info "使用yum安装Node.js..."
                curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
                sudo yum install -y nodejs npm
            elif command -v dnf &> /dev/null; then
                log_info "使用dnf安装Node.js..."
                curl -fsSL https://rpm.nodesource.com/setup_lts.x | sudo bash -
                sudo dnf install -y nodejs npm
            else
                log_error "无法识别的Linux发行版"
                install_nodejs_manual
            fi
        else
            install_nodejs_manual
        fi

        # 验证安装
        if command -v node &> /dev/null; then
            NODE_VERSION=$(node --version)
            log_success "Node.js安装成功: $NODE_VERSION"
        else
            log_error "Node.js安装失败"
            install_nodejs_manual
        fi
    fi
}

# 手动安装Node.js指导
install_nodejs_manual() {
    log_error "自动安装失败，请手动安装Node.js:"
    echo
    echo "1. 访问 https://nodejs.org"
    echo "2. 下载LTS版本"
    echo "3. 安装后重新运行此脚本"
    echo
    echo "或者使用以下命令:"
    echo "curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash"
    echo "source ~/.bashrc"
    echo "nvm install --lts"
    echo
    exit 1
}

# 安装npm依赖
install_dependencies() {
    log_info "检查并安装项目依赖..."

    # 检查package.json是否存在
    if [ ! -f "package.json" ]; then
        log_info "创建package.json..."
        cat > package.json << 'EOF'
{
  "name": "writerpro-admin-dashboard",
  "version": "1.0.0",
  "description": "WriterPro管理后台系统",
  "main": "simple-port-3000.js",
  "scripts": {
    "start": "node simple-port-3000.js",
    "dev": "node simple-port-3000.js"
  },
  "dependencies": {},
  "devDependencies": {},
  "keywords": ["admin", "dashboard", "management"],
  "author": "WriterPro Team",
  "license": "MIT"
}
EOF
        log_success "package.json创建完成"
    fi

    # 检查node_modules是否存在
    if [ ! -d "node_modules" ]; then
        log_info "初始化npm项目..."
        npm init -y > /dev/null 2>&1
    fi

    log_success "依赖检查完成"
}

# 检查Python（备用方案）
check_python() {
    log_info "检查Python..."
    
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 --version)
        log_success "Python3已安装: $PYTHON_VERSION"
        return 0
    elif command -v python &> /dev/null; then
        PYTHON_VERSION=$(python --version)
        log_success "Python已安装: $PYTHON_VERSION"
        return 0
    else
        log_warning "Python未安装，将仅使用Node.js"
        return 1
    fi
}

# 停止占用端口的进程
stop_port_processes() {
    log_info "检查端口占用情况..."
    
    # 检查端口3000
    if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口3000被占用，正在停止相关进程..."
        lsof -ti:3000 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    # 检查端口8080
    if lsof -Pi :8080 -sTCP:LISTEN -t >/dev/null 2>&1; then
        log_warning "端口8080被占用，正在停止相关进程..."
        lsof -ti:8080 | xargs kill -9 2>/dev/null || true
        sleep 2
    fi
    
    log_success "端口清理完成"
}

# 创建简化服务器文件（如果不存在）
create_server_file() {
    if [ ! -f "simple-port-3000.js" ]; then
        log_info "创建服务器文件..."
        cat > simple-port-3000.js << 'EOF'
const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 3000;

const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml'
};

function getContentType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    return mimeTypes[ext] || 'application/octet-stream';
}

const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    let pathname = parsedUrl.pathname;
    
    // 处理API请求
    if (pathname.startsWith('/ad/api/')) {
        res.setHeader('Content-Type', 'application/json');
        
        if (pathname === '/ad/api/auth/login' && req.method === 'POST') {
            let body = '';
            req.on('data', chunk => { body += chunk.toString(); });
            req.on('end', () => {
                try {
                    const { username, password } = JSON.parse(body);
                    if (username === 'admin' && password === 'admin123') {
                        res.writeHead(200);
                        res.end(JSON.stringify({
                            success: true,
                            requiresTwoFactor: true,
                            message: '请输入双因素认证码'
                        }));
                    } else {
                        res.writeHead(401);
                        res.end(JSON.stringify({
                            success: false,
                            message: '用户名或密码错误'
                        }));
                    }
                } catch (error) {
                    res.writeHead(400);
                    res.end(JSON.stringify({ success: false, message: '请求格式错误' }));
                }
            });
            return;
        }
        
        if (pathname === '/ad/api/auth/verify-2fa' && req.method === 'POST') {
            let body = '';
            req.on('data', chunk => { body += chunk.toString(); });
            req.on('end', () => {
                try {
                    const { code } = JSON.parse(body);
                    if (code === '101010') {
                        res.writeHead(200);
                        res.end(JSON.stringify({
                            success: true,
                            user: {
                                id: '1',
                                username: 'admin',
                                name: '系统管理员',
                                email: '<EMAIL>',
                                role: 'admin'
                            }
                        }));
                    } else {
                        res.writeHead(401);
                        res.end(JSON.stringify({ success: false, message: '验证码错误' }));
                    }
                } catch (error) {
                    res.writeHead(400);
                    res.end(JSON.stringify({ success: false, message: '请求格式错误' }));
                }
            });
            return;
        }
        
        res.writeHead(404);
        res.end(JSON.stringify({ success: false, message: 'API接口不存在' }));
        return;
    }
    
    // 处理静态文件
    let filePath;
    
    if (pathname === '/') {
        res.writeHead(200, { 'Content-Type': 'text/html' });
        res.end(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>主网站</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
                    .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                    .admin-link { display: inline-block; padding: 15px 30px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px; font-weight: bold; }
                    .admin-link:hover { background: #0056b3; }
                    .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin-top: 20px; }
                </style>
            </head>
            <body>
                <div class="container">
                    <h1>🎉 欢迎访问主网站</h1>
                    <p>管理后台已成功集成到 /ad 路径下。</p>
                    <a href="/ad" class="admin-link">🔧 进入管理后台</a>
                    <div class="info">
                        <h3>📋 登录信息</h3>
                        <p><strong>管理后台:</strong> <code>http://localhost:3000/ad</code></p>
                        <p><strong>用户名:</strong> <code>admin</code></p>
                        <p><strong>密码:</strong> <code>admin123</code></p>
                        <p><strong>验证码:</strong> <code>101010</code></p>
                    </div>
                </div>
            </body>
            </html>
        `);
        return;
    }
    
    if (pathname === '/ad' || pathname === '/ad/') {
        filePath = path.join(__dirname, 'index.html');
    } else if (pathname.startsWith('/ad/')) {
        const actualPath = pathname.substring(3);
        const pageMap = {
            '/dashboard': 'dashboard.html',
            '/users': 'users.html',
            '/agents': 'agents.html',
            '/commission': 'commission.html',
            '/pricing': 'pricing.html',
            '/roles': 'roles.html',
            '/finance': 'finance.html',
            '/analytics': 'analytics.html',
            '/reports': 'reports.html',
            '/settings': 'settings.html',
            '/content': 'content.html',
            '/api-docs': 'api-docs.html',
            '/marketing': 'marketing.html',
            '/security': 'security.html',
            '/monitor': 'monitor.html',
            '/logs': 'logs.html',
            '/tasks': 'tasks.html',
            '/notifications': 'notifications.html',
            '/backup': 'backup.html'
        };
        
        if (pageMap[actualPath]) {
            filePath = path.join(__dirname, pageMap[actualPath]);
        } else {
            filePath = path.join(__dirname, actualPath);
        }
    } else {
        res.writeHead(404, { 'Content-Type': 'text/html' });
        res.end('<h1>404 - 页面不存在</h1><p><a href="/">返回首页</a> | <a href="/ad">进入管理后台</a></p>');
        return;
    }
    
    fs.readFile(filePath, (err, data) => {
        if (err) {
            res.writeHead(404, { 'Content-Type': 'text/html' });
            res.end('<h1>404 - 文件不存在</h1><p><a href="/">返回首页</a> | <a href="/ad">进入管理后台</a></p>');
        } else {
            const contentType = getContentType(filePath);
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(data);
        }
    });
});

server.listen(PORT, () => {
    console.log('========================================');
    console.log('🚀 服务器运行在端口', PORT);
    console.log('📱 主网站: http://localhost:' + PORT);
    console.log('🔧 管理后台: http://localhost:' + PORT + '/ad');
    console.log('👤 登录信息: admin / admin123 / 101010');
    console.log('========================================');
});
EOF
        log_success "服务器文件创建完成"
    fi
}

# 启动服务器
start_server() {
    log_info "启动服务器..."
    
    # 尝试使用Node.js启动
    if command -v node &> /dev/null; then
        log_info "使用Node.js启动服务器..."
        
        # 自动打开浏览器（后台运行）
        if command -v open &> /dev/null; then
            # macOS
            (sleep 3 && open "http://localhost:3000/ad") &
        elif command -v xdg-open &> /dev/null; then
            # Linux
            (sleep 3 && xdg-open "http://localhost:3000/ad") &
        fi
        
        node simple-port-3000.js
    else
        log_error "无法启动服务器：Node.js不可用"
        exit 1
    fi
}

# 主函数
main() {
    echo "🚀 开始全自动启动流程..."
    echo

    check_directory
    check_nodejs
    install_dependencies
    check_python
    stop_port_processes
    create_server_file

    echo
    log_success "所有检查和安装完成，准备启动服务器..."
    echo

    start_server
}

# 运行主函数
main
