.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1rem;
}

.card-title {
    color: #495057;
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.display-4 {
    font-size: 2rem;
    font-weight: 500;
    line-height: 1.2;
    color: #212529;
}

/* 趋势小图表容器 */
#cpuTrend, #memoryTrend, #userTrend, #loadTrend {
    width: 100px;
    height: 40px;
}

/* 大图表容器 */
#resourceChart, #networkChart {
    height: 300px;
}

/* 状态指示器 */
.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.status-normal {
    background-color: #28a745;
}

.status-warning {
    background-color: #ffc107;
}

.status-danger {
    background-color: #dc3545;
}

/* 日志级别标签 */
.log-level {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.log-level-info {
    background-color: #cce5ff;
    color: #004085;
}

.log-level-warning {
    background-color: #fff3cd;
    color: #856404;
}

.log-level-error {
    background-color: #f8d7da;
    color: #721c24;
}

/* 表格样式优化 */
.table {
    font-size: 0.875rem;
}

.table th {
    font-weight: 500;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

/* 时间范围选择器 */
.dropdown-menu {
    font-size: 0.875rem;
}

.dropdown-item {
    padding: 0.5rem 1rem;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

/* 按钮组样式 */
.btn-group .btn {
    font-size: 0.875rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .display-4 {
        font-size: 1.5rem;
    }
    
    #cpuTrend, #memoryTrend, #userTrend, #loadTrend {
        width: 60px;
        height: 30px;
    }
    
    #resourceChart, #networkChart {
        height: 200px;
    }
    
    .card-title {
        font-size: 0.875rem;
    }
}

/* 实时更新动画 */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.value-update {
    animation: pulse 0.5s ease-in-out;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.75rem;
}

/* 图表legend样式 */
.apexcharts-legend {
    font-size: 0.875rem;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 加载动画 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 数据刷新提示 */
.refresh-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 0.875rem;
    z-index: 1000;
    display: none;
}

/* 图表交互提示 */
.chart-tooltip {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px;
    border-radius: 4px;
    font-size: 0.75rem;
} 