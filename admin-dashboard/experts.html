<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专家管理 - WriterPro 管理后台</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="css/ui-enhancements.css">
</head>
<body>
    <div class="admin-layout">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="bi bi-pen"></i>
                    <span>WriterPro</span>
                </div>
                <button class="menu-toggle">
                    <i class="bi bi-list"></i>
                </button>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="dashboard.html"><i class="bi bi-grid"></i><span>仪表盘</span></a></li>
                    <li><a href="users.html"><i class="bi bi-people"></i><span>用户管理</span></a></li>
                    <li><a href="workorders.html"><i class="bi bi-clipboard-check"></i><span>工单管理</span></a></li>
                    <li class="active"><a href="experts.html"><i class="bi bi-person-badge"></i><span>专家管理</span></a></li>
                    <li><a href="finance.html"><i class="bi bi-cash-stack"></i><span>财务统计</span></a></li>
                    <li><a href="settings.html"><i class="bi bi-gear"></i><span>系统设置</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部导航 -->
            <header class="top-header">
                <div class="header-left">
                    <button class="menu-toggle">
                        <i class="bi bi-list"></i>
                    </button>
                    <h1>专家管理</h1>
                </div>
                
                <div class="header-right">
                    <button class="btn btn-primary" onclick="showAddExpertModal()">
                        <i class="bi bi-plus"></i>
                        添加专家
                    </button>
                    <div class="user-menu">
                        <button class="user-btn">
                            <i class="bi bi-person-circle"></i>
                            <span>管理员</span>
                            <i class="bi bi-chevron-down"></i>
                        </button>
                        <div class="dropdown-menu">
                            <a href="#" class="dropdown-item">
                                <i class="bi bi-person"></i>
                                <span>个人资料</span>
                            </a>
                            <a href="#" class="dropdown-item">
                                <i class="bi bi-gear"></i>
                                <span>设置</span>
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="index.html" class="dropdown-item">
                                <i class="bi bi-box-arrow-right"></i>
                                <span>退出登录</span>
                            </a>
                        </div>
                    </div>
                </div>
            </header>

            <!-- 页面内容 -->
            <div class="page-content">
                <!-- 筛选和搜索 -->
                <div class="content-header">
                    <div class="row">
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter">
                                <option value="">全部状态</option>
                                <option value="active">在线</option>
                                <option value="busy">忙碌</option>
                                <option value="offline">离线</option>
                                <option value="suspended">暂停</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="specialtyFilter">
                                <option value="">全部专业</option>
                                <option value="文学">文学</option>
                                <option value="法学">法学</option>
                                <option value="经济学">经济学</option>
                                <option value="管理学">管理学</option>
                                <option value="工学">工学</option>
                                <option value="理学">理学</option>
                                <option value="医学">医学</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="sortBy">
                                <option value="createdAt">注册时间</option>
                                <option value="averageRating">平均评分</option>
                                <option value="completedOrders">完成订单</option>
                                <option value="currentOrders">当前订单</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchInput" placeholder="搜索专家...">
                                <button class="btn btn-outline-secondary" type="button" id="searchBtn">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计卡片 -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-primary">
                                <i class="bi bi-person-badge"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="totalExperts">0</h3>
                                <p>总专家数</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-success">
                                <i class="bi bi-person-check"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="activeExperts">0</h3>
                                <p>在线专家</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-warning">
                                <i class="bi bi-person-dash"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="busyExperts">0</h3>
                                <p>忙碌专家</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-card">
                            <div class="stat-icon bg-info">
                                <i class="bi bi-star"></i>
                            </div>
                            <div class="stat-info">
                                <h3 id="averageRating">0.0</h3>
                                <p>平均评分</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 专家列表 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">专家列表</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>专家编号</th>
                                        <th>姓名</th>
                                        <th>职称</th>
                                        <th>专业领域</th>
                                        <th>状态</th>
                                        <th>当前/最大订单</th>
                                        <th>完成订单</th>
                                        <th>平均评分</th>
                                        <th>注册时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="expertsTable">
                                    <tr>
                                        <td colspan="10" class="text-center">
                                            <div class="spinner-border" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- 分页 -->
                        <nav aria-label="专家分页">
                            <ul class="pagination justify-content-center" id="pagination">
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 添加专家模态框 -->
    <div class="modal fade" id="addExpertModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加专家</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addExpertForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="userId" class="form-label">用户ID</label>
                                    <input type="number" class="form-control" id="userId" required>
                                    <div class="form-text">请输入要设为专家的用户ID</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="realName" class="form-label">真实姓名</label>
                                    <input type="text" class="form-control" id="realName" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="title" class="form-label">职称</label>
                                    <input type="text" class="form-control" id="title" placeholder="如：教授、副教授、讲师">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="education" class="form-label">学历</label>
                                    <select class="form-select" id="education">
                                        <option value="">请选择</option>
                                        <option value="博士">博士</option>
                                        <option value="硕士">硕士</option>
                                        <option value="学士">学士</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="specialties" class="form-label">专业领域</label>
                                    <select class="form-select" id="specialties" multiple>
                                        <option value="文学">文学</option>
                                        <option value="法学">法学</option>
                                        <option value="经济学">经济学</option>
                                        <option value="管理学">管理学</option>
                                        <option value="工学">工学</option>
                                        <option value="理学">理学</option>
                                        <option value="医学">医学</option>
                                        <option value="教育学">教育学</option>
                                        <option value="历史学">历史学</option>
                                        <option value="哲学">哲学</option>
                                    </select>
                                    <div class="form-text">可多选</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="languages" class="form-label">语言能力</label>
                                    <select class="form-select" id="languages" multiple>
                                        <option value="中文">中文</option>
                                        <option value="英文">英文</option>
                                        <option value="日文">日文</option>
                                        <option value="韩文">韩文</option>
                                        <option value="法文">法文</option>
                                        <option value="德文">德文</option>
                                        <option value="俄文">俄文</option>
                                    </select>
                                    <div class="form-text">可多选</div>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="maxConcurrentOrders" class="form-label">最大并发订单数</label>
                            <input type="number" class="form-control" id="maxConcurrentOrders" value="3" min="1" max="10">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="addExpert()">添加专家</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 专家详情模态框 -->
    <div class="modal fade" id="expertModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">专家详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="expertDetails">
                    <!-- 专家详情内容 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-warning" id="updateStatusBtn">更新状态</button>
                    <button type="button" class="btn btn-primary" id="editExpertBtn">编辑信息</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/experts.js"></script>
</body>
</html>
